<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppShareTag;
use App\Helpers\ResponseHelper;
use Illuminate\Http\Request;

/**
 * 应用分享标签管理控制器
 * @package App\Http\Controllers\Admin
 */
class AppShareTagController extends Controller
{
    /**
     * 获取标签列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'status'    => 'nullable|in:0,1',
            'sort_name' => 'nullable|in:id,created_at,updated_at,use_count,sort_order',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);

        $records = AppShareTag::getList($request->all());
        return ResponseHelper::success($records);
    }

    /**
     * 获取标签详情
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $record = AppShareTag::getDetail($request->input('id'));
        if (!$record) {
            return ResponseHelper::error('标签不存在');
        }

        return ResponseHelper::success($record);
    }

    /**
     * 保存标签详情
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'          => 'sometimes|integer|gte:0',
            'name'        => 'required|max:50',
            'color'       => 'nullable|regex:/^#[0-9A-Fa-f]{6}$/',
            'description' => 'nullable|max:500',
            'sort_order'  => 'nullable|integer|gte:0',
            'status'      => 'required|in:0,1',
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        
        // 检查标签名称是否重复
        $id = $formData['id'] ?? 0;
        $existingTag = AppShareTag::where('name', $formData['name'])
            ->when($id, function($query) use ($id) {
                return $query->where('id', '!=', $id);
            })
            ->first();

        if ($existingTag) {
            return ResponseHelper::error('标签名称已存在');
        }

        $result = AppShareTag::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * 删除标签
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $result = AppShareTag::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }





    /**
     * 批量更新标签状态
     */
    public function batchUpdateStatus(Request $request)
    {
        $request->validate([
            'ids'    => 'required|array',
            'ids.*'  => 'integer|gt:0',
            'status' => 'required|in:0,1',
        ]);

        try {
            $count = AppShareTag::whereIn('id', $request->input('ids'))
                ->update(['status' => $request->input('status')]);

            return ResponseHelper::success(['updated_count' => $count], '批量更新成功');
        } catch (\Exception $e) {
            logErr('批量更新标签状态失败：' . $e->getMessage());
            return ResponseHelper::error('批量更新失败');
        }
    }
}
