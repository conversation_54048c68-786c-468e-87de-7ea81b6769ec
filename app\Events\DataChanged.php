<?php

namespace App\Events;

use App\Providers\OperateLinkModelProvider;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

//后台内容变更事件
class DataChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    const OPERATE_TYPE_ADD = 1;
    const OPERATE_TYPE_EDIT = 2;
    const OPERATE_TYPE_DEL = 3;
    const OPERATE_TYPE_TEXT_LIST = [self::OPERATE_TYPE_ADD => '添加', self::OPERATE_TYPE_EDIT => '编辑', self::OPERATE_TYPE_DEL => '删除'];

    private array|null $newData;
    private array|null $oldData;
    private array $tableInfo;
    private array $ignoreFields = ['id', 'created_at', 'updated_at', 'deleted_at', 'password', 'weight_by_age', 'month_ranges'];
    private array $nameFields = ['name', 'name_tc', 'name_en', 'districtName'];

    public function __construct(string $modelClass, $newData, $oldData = null)
    {
        $this->tableInfo = $this->getTableInfo($modelClass);
        $this->newData = $newData;
        $this->oldData = $oldData;
    }

    public function getOperateType()
    {
        if ($this->newData) {
            return $this->oldData ? self::OPERATE_TYPE_EDIT : self::OPERATE_TYPE_ADD;
        } else {
            return self::OPERATE_TYPE_DEL;
        }
    }

    public function getOperateTypeText()
    {
        return self::OPERATE_TYPE_TEXT_LIST[$this->getOperateType()];
    }

    public function getTableInfo($modelClass)
    {
        /**
         * @var Model $model
         */
        $model = with(new $modelClass());
        $pk = $model->getKeyName();
        $tableName = $model->getTable();
        $tableInfo = DB::select("SELECT COLUMN_NAME, COLUMN_COMMENT FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = '$tableName'");
        $tableComment = DB::select("SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '$tableName'");
        $comment = $tableComment[0]->TABLE_COMMENT;
        if (empty($comment)) {
            throw new \Exception("[$tableName]未设置表注释");
        }
        $fields = [];
        foreach ($tableInfo as $column) {
            $fieldName = $column->COLUMN_NAME;
            $fieldComment = $column->COLUMN_COMMENT;
            if (empty($fieldComment) && !in_array($fieldName, $this->ignoreFields)) {
                throw new \Exception("[$fieldName]未设置字段注释");
            }
            $fields[$fieldName] = $fieldComment;
        }
        return ['model' => $modelClass, 'pk' => $pk, 'table' => $tableName, 'comment' => $comment, 'fields' => $fields];
    }

    private function parseValueText($data, $fieldName, $fieldComment)
    {
        $notAllow = ['，', '、', '：'];
        foreach ($notAllow as $char) {
            if (str_contains($fieldComment, $char)) {
                $this->throwParseError($fieldName, '注释出现特殊符号：' . $char);
            }
        }
        if (preg_match('/\s+/', $fieldComment)) {
            $this->throwParseError($fieldName, '注释出现空白字符');
        }
        $value = $data[$fieldName];
        $arr = explode(':', $fieldComment);
        if (!isset($arr[1])) {
            return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value; //普通文本更新
        }
        //状态/类型更新
        $list = explode(',', $arr[1]);

        try {
            $map = [];
            foreach ($list as $v) {
                list($enum, $text) = explode('=', $v);
                $map[$enum] = $text;
            }
        } catch (\Exception $e) {
            //格式：  状态:1=正常,2=异常
            $this->throwParseError($fieldName, '注释解析错误：' . $fieldComment);
        }
        if (!isset($map[$value])) {
            $this->throwParseError($fieldName, '不存在枚举值：' . $value);
        }
        return $map[$value];
    }

    private function throwParseError($fieldName, $errMsg)
    {
        throw new \Exception('表：[' . $this->tableInfo['table'] . ']，字段：[' . $fieldName . ']，' . $errMsg);
    }

    private function getNameRow()
    {
        $fields = $this->tableInfo['fields'];
        foreach ($this->nameFields as $nameField) {
            if (isset($this->oldData[$nameField])) {
                $fieldComment = $fields[$nameField];
                $newValue = $this->parseValueText($this->oldData, $nameField, $fieldComment);
                return $fieldComment . '：【' . $newValue . '】' . PHP_EOL;
            }
        }
        return '';
    }

    private function buildContent()
    {
        $type = $this->getOperateType();
        $content = '';
        if ($type == self::OPERATE_TYPE_DEL) {
            $content .= 'ID：【' . $this->oldData['id'] . '】' . PHP_EOL . $this->getNameRow();
        } else {
            $isEdit = $type == self::OPERATE_TYPE_EDIT;
            $fields = $this->tableInfo['fields'];
            foreach ($fields as $fieldName => $fieldComment) {
                if (in_array($fieldName, $this->ignoreFields)) {
                    continue;
                }
                if (!isset($this->newData[$fieldName])) {
                    continue;
                }
                $newValue = $this->parseValueText($this->newData, $fieldName, $fieldComment);
                $row = current(explode(':', $fieldComment)) . '：';
                if ($isEdit) {
                    $oldValue = $this->parseValueText($this->oldData, $fieldName, $fieldComment);
                    if ($newValue == $oldValue) {
                        continue;
                    }
                    $row .= "【{$oldValue}】=>【{$newValue}】";
                } else {
                    $row .= "【{$newValue}】";
                }
                $content .= $row . PHP_EOL;
            }
        }

        return rtrim($content, PHP_EOL);
    }

    public function buildLog()
    {
        $operate_type = $this->getOperateType();
        $operate_type_text = $this->getOperateTypeText();
        $table_info = $this->tableInfo;
        $title = $table_info['comment'];
        $content = $this->buildContent();
        $data = $this->newData ?: $this->oldData;
        return [
            'operate_type' => $operate_type,
            'link_type'    => OperateLinkModelProvider::getLinkType($table_info['model']),
            'link_id'      => $data[$table_info['pk']],
            'title'        => $operate_type_text . $title,
            'content'      => $content,
        ];
    }
}
