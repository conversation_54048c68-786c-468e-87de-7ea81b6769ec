<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\GoodCollect;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * collect
 * 商品收藏
 * Class CollectController
 * @package App\Http\Controllers\Mobile
 */
class GoodCollectController extends Controller
{
    /**
     * CollectList
     * 收藏列表
     * @param Request $request
     * @queryParam  limit int 每页显示条数
     * @queryParam  sort string 排序
     * @queryParam  page string 页码
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = GoodCollect::getList($formData);
        return ResponseHelper::success($records);
    }

    public function collect(Request $request)
    {
        $request->validate([
            'good_id' => 'required|integer|gt:0',
        ]);
        $result = GoodCollect::collect($request->user()->id, $request->good_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelCollect(Request $request)
    {
        $request->validate([
            'good_id' => 'required|integer|gt:0',
        ]);
        $result = GoodCollect::cancelCollect($request->user()->id, $request->good_id);
        return ResponseHelper::result(...$result);
    }
}
