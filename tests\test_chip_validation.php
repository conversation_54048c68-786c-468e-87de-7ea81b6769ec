<?php

/**
 * 晶片编号验证测试脚本
 */

echo "🧪 晶片编号验证测试\n";
echo "==================\n\n";

// 模拟晶片编号验证函数
function validateChipCodeData(array $data): array
{
    $result = [
        'chip_number' => null
    ];

    // 验证晶片编号
    if (isset($data['chip_number']) && !empty($data['chip_number']) && $data['chip_number'] !== 'null') {
        $chipNumber = trim($data['chip_number']);
        
        // 晶片编号验证规则：
        // 1. 纯数字格式：6-20位数字（支持各种长度的晶片编号）
        // 2. 字母数字混合格式：支持常见的晶片编号格式
        if (preg_match('/^\d{6,20}$/', $chipNumber) || 
            preg_match('/^[A-Za-z0-9]{6,20}$/', $chipNumber)) {
            $result['chip_number'] = $chipNumber;
            
            echo "✅ 晶片编号验证成功: {$chipNumber} (长度: " . strlen($chipNumber) . ")\n";
        } else {
            echo "❌ 晶片编号格式不符合要求: {$chipNumber} (长度: " . strlen($chipNumber) . ")\n";
        }
    }

    return $result;
}

// 测试用例
$testCases = [
    // 有效的晶片编号
    ['chip_number' => '100999998'],           // 9位数字 (您遇到的问题)
    ['chip_number' => '123456789012345'],     // 15位数字 (标准格式)
    ['chip_number' => '900248699963399'],     // 15位数字
    ['chip_number' => '123456'],              // 6位数字 (最短)
    ['chip_number' => '12345678901234567890'], // 20位数字 (最长)
    ['chip_number' => 'ABC123456789'],        // 字母数字混合
    ['chip_number' => '1A2B3C4D5E'],          // 字母数字混合
    
    // 无效的晶片编号
    ['chip_number' => '12345'],               // 5位数字 (太短)
    ['chip_number' => '123456789012345678901'], // 21位数字 (太长)
    ['chip_number' => 'ABC-123-456'],         // 包含特殊字符
    ['chip_number' => ''],                    // 空字符串
    ['chip_number' => 'null'],                // 字符串null
    ['chip_number' => null],                  // null值
];

echo "测试各种晶片编号格式:\n";
echo str_repeat('-', 50) . "\n";

foreach ($testCases as $index => $testData) {
    echo "测试 " . ($index + 1) . ": ";
    if (isset($testData['chip_number'])) {
        $displayValue = $testData['chip_number'] === null ? 'null' : "'{$testData['chip_number']}'";
        echo "输入: {$displayValue} ";
    }
    
    $result = validateChipCodeData($testData);
    
    if ($result['chip_number'] !== null) {
        echo "-> 结果: '{$result['chip_number']}'\n";
    } else {
        echo "-> 结果: null\n";
    }
}

echo "\n" . str_repeat('-', 50) . "\n";

// 特别测试您遇到的问题
echo "\n🎯 特别测试您遇到的问题:\n";
$problemCase = ['chip_number' => '100999998'];
echo "AI解析结果: " . json_encode($problemCase) . "\n";

$result = validateChipCodeData($problemCase);
echo "验证结果: " . json_encode($result) . "\n";

if ($result['chip_number'] === '100999998') {
    echo "✅ 问题已修复！晶片编号 '100999998' 现在可以正确验证\n";
} else {
    echo "❌ 问题仍然存在，需要进一步调试\n";
}

echo "\n📋 修复说明:\n";
echo "1. 将晶片编号长度要求从 10-20位 改为 6-20位\n";
echo "2. 支持纯数字格式: /^\d{6,20}$/\n";
echo "3. 支持字母数字混合: /^[A-Za-z0-9]{6,20}$/\n";
echo "4. 添加了详细的验证日志\n";
echo "5. 更新了AI提示词，说明晶片编号的实际格式\n";

echo "\n✅ 晶片编号验证修复完成！\n";
