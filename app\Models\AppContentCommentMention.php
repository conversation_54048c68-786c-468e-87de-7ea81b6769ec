<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 评论@提及模型
 * @package App\Models
 */
class AppContentCommentMention extends Model
{
    use HasFactory;

    protected $table = 'app_contents_comments_mentions';

    protected $fillable = [
        'comment_id',
        'content_id',
        'mention_user_id',
        'comment_user_id',
        'read_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 关联评论
     */
    public function comment()
    {
        return $this->belongsTo(AppContentComment::class, 'comment_id');
    }

    /**
     * 关联分享内容
     */
    public function content()
    {
        return $this->belongsTo(AppContent::class, 'content_id');
    }

    /**
     * 关联被@的用户
     */
    public function mentionUser()
    {
        return $this->belongsTo(AppUser::class, 'mention_user_id');
    }

    /**
     * 关联评论者
     */
    public function commentUser()
    {
        return $this->belongsTo(AppUser::class, 'comment_user_id');
    }

    /**
     * 创建@提及记录
     */
    public static function createMentions($commentId, $contentId, $commentUserId, $mentionUserIds)
    {
        if (empty($mentionUserIds)) {
            return true;
        }

        $data = [];
        foreach ($mentionUserIds as $mentionUserId) {
            // 不能@自己
            if ($mentionUserId == $commentUserId) {
                continue;
            }

            $data[] = [
                'comment_id' => $commentId,
                'content_id' => $contentId,
                'mention_user_id' => $mentionUserId,
                'comment_user_id' => $commentUserId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        if (!empty($data)) {
            self::insert($data);
        }

        return true;
    }

    /**
     * 获取未读@提及数量
     */
    public static function getUnreadCount($userId)
    {
        return self::where('mention_user_id', $userId)
            ->whereNull('read_at')
            ->count();
    }


}
