<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserFollowedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 被关注的用户 ID
     *
     * @var int
     */
    public $followedUserId;

    /**
     * 关注者 ID
     *
     * @var int
     */
    public $followerId;

    /**
     * Create a new event instance.
     *
     * @param int $followedUserId 被关注的用户 ID
     * @param int $followerId 关注者 ID
     * @return void
     */
    public function __construct($followedUserId, $followerId)
    {
        $this->followedUserId = $followedUserId;
        $this->followerId = $followerId;
    }
}
