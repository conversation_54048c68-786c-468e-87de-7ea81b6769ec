<?php

namespace App\Models;

use App\Helpers\QianFang;
use App\Helpers\RedisLock;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PaymentLog extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'trade_no',
        'syssn',
        'transaction_id',
        'money',
        'data',
        'state',
        'pay_id',
        'pay_type',
        'type',
        'platform',
    ];

    const PAYMENT_LOG_STATE_CREATE = 0; //状态:生成
    const PAYMENT_LOG_STATE_COMPLETE = 1; //状态:完成
    const PAYMENT_LOG_STATE_FAILURE = 2; //状态:失败
    const PAYMENT_LOG_TYPE_GOODS_INDENT = 'goodIndent'; //支付类型:商品订单支付
    const PAYMENT_LOG_TYPE_GOODS_INDENT_REFUND = 'goodIndentRefund'; //支付类型:商品订单退款
    const PAYMENT_LOG_TYPE_REFUND = 'refund'; //支付类型:退款
    const PAYMENT_LOG_PLATFORM_WEIXIN = 'weixin'; //支付平台:微信

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 用户
     */
    public function user()
    {
        return $this->hasOne(AppUser::class, 'id', 'user_id');
    }

    public static function getList($search_data = array())
    {
        //遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('transaction_id', 'like', "%$keyword%");
            });
        })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('time', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->where('state', 1)   //只查看有效的资金日志
            ->with([
                'user' => function (Builder $query) {
                    $query->select('id', 'username', 'phone', 'avatar');
                },
            ])
            ->orderBy('created_at', 'desc');
        if ($limit == -1) {
            return $query->get()->toArray();
        }
        return $query->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)->first();
    }

    public static function getInfo($trade_no)
    {
        return self::where('trade_no', $trade_no)->first();
    }

    public static function saveByGoodIndent($GoodIndent)
    {
        if ($info = self::getInfo($GoodIndent->identification)) {
            return json_decode($info->data, true);
        }
        $paymentLog = new self();
        $paymentLog->state = self::PAYMENT_LOG_STATE_CREATE;
        $paymentLog->user_id = $GoodIndent->user_id;
        $paymentLog->trade_no = $GoodIndent->identification;
        $paymentLog->money = $GoodIndent->pay_amount;
        $paymentLog->name = '对商品订单[' . $GoodIndent->identification . ']的付款';
        $paymentLog->type = self::PAYMENT_LOG_TYPE_GOODS_INDENT;
        $paymentLog->pay_type = GoodIndent::class;
        $paymentLog->pay_id = $GoodIndent->id;
        $payment = self::getPayment($GoodIndent, $paymentLog);
        $paymentLog->data = json_encode($payment, JSON_UNESCAPED_UNICODE);
        $paymentLog->save() or throw_if(true, 'RuntimeException', '资金日志保存失败');
        return $payment;
    }

    public static function getPayment($GoodIndent, $paymentLog)
    {
        $sdk = new QianFang;
        $notify_url = config('app.url') . '/api/payment/qfpay_notify';
        switch ($GoodIndent->pay_method) {
            case 1:
                $httpQuery = http_build_query([
                    'trade_no' => $paymentLog->trade_no,
                    'money'    => $paymentLog->money,
                    'type'     => $paymentLog->type,
                    'pay_id'   => $paymentLog->pay_id,
                ]);
                return $sdk->checkstand(
                    $paymentLog->type . '@' . $paymentLog->trade_no,
                    $paymentLog->trade_no,
                    $paymentLog->money,
                    $notify_url,
                    GoodIndent::ORDER_OVER_TIME,
                    config('payment.success_url') . '?' . $httpQuery,
                    config('payment.failed_url') . '?' . $httpQuery
                );
            case 2:
                return $sdk->create_payment_intent(
                    $paymentLog->trade_no,
                    $paymentLog->money,
                    $notify_url,
                    $GoodIndent->pay_type
                );
            case 3:
                return $sdk->payment(
                    $paymentLog->type . '@' . $paymentLog->trade_no,
                    $paymentLog->trade_no,
                    $paymentLog->money,
                    $notify_url,
                    GoodIndent::ORDER_OVER_TIME,
                    $GoodIndent->pay_type
                );
        }
        return throw_if(true, 'RuntimeException', '支付方式错误');
    }

    public static function refundByGoodIndent($GoodIndentRefund)
    {
        $paymentLog = PaymentLog::getInfo($GoodIndentRefund->good_indent_identification);
        if (!$paymentLog) {
            return;  //没有交易记录
        }
        $refundData = (new QianFang)->refund(
            $paymentLog->syssn,
            $GoodIndentRefund->identification,
            $GoodIndentRefund->total,
        );
        throw_if(!$refundData['code'], 'RuntimeException', $refundData['err_code_des']);
        $paymentLog = new self();
        $paymentLog->user_id = $GoodIndentRefund->user_id;
        $paymentLog->trade_no = $GoodIndentRefund->identification;
        $paymentLog->syssn = $paymentLog->identification;
        $paymentLog->transaction_id = $paymentLog->identification;
        $paymentLog->money = $GoodIndentRefund->total;
        $paymentLog->name = '对商品订单[' . $GoodIndentRefund->good_indent_identification . ']的退款';
        $paymentLog->type = self::PAYMENT_LOG_TYPE_GOODS_INDENT_REFUND;
        $paymentLog->pay_type = GoodIndent::class;
        $paymentLog->pay_id = $GoodIndentRefund->good_indent_id;
        $paymentLog->data = json_encode($refundData, JSON_UNESCAPED_UNICODE);
        $paymentLog->time = date('Y-m-d H:i:s');
        $paymentLog->save() or throw_if(true, 'RuntimeException', '资金日志保存失败');
    }

    public static function notify($notify_type, $trade_no, $chnlsn, $payTime, $syssn = '')
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $paymentLog = self::getInfo($trade_no) or throw_if(true, 'RuntimeException', '资金日志不存在');
            if ($paymentLog->state != self::PAYMENT_LOG_STATE_COMPLETE) {
                //'UpdateGoodIndent_' . $id
                $lockKey = 'Update' . ucfirst($paymentLog->type) . '_' . $paymentLog->pay_id;
                $lock = RedisLock::lock($redis, $lockKey);
                if (!$lock) {
                    throw new Exception('业务繁忙，请稍后再试');
                }
                $paymentLog->state = self::PAYMENT_LOG_STATE_COMPLETE;
                $paymentLog->transaction_id = $chnlsn;
                $paymentLog->time = $payTime;
                $syssn and $paymentLog->syssn = $syssn;
                $paymentLog->save() or throw_if(true, 'RuntimeException', '资金日志保存失败');
                /**
                 * @var GoodIndent $info
                 */
                $model = new $paymentLog->pay_type;
                $info = $model->findOrFail($paymentLog->pay_id);
                if ($notify_type == 'payment') {
                    call_user_func_array([$info, 'setPaid'], [$info, $paymentLog->time]);
                } else {
                    call_user_func_array([$info, 'setRefund'], [$info, $paymentLog->time]);
                }
                $info->save() or throw_if(true, 'RuntimeException', '关联更新订单失败');
            }
            DB::commit();
            $success = true;
            $message = '';
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
            logErr($trade_no . ($notify_type == 'payment' ? '支付' : '退款') . '回调失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, $lockKey);
        }
        return [$success, [], $message];
    }
}
