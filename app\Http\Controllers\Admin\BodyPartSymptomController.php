<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AnalysisBodyPart;
use App\Models\BodyPartSymptom;
use Illuminate\Http\Request;

class BodyPartSymptomController extends Controller
{
    /**
     * 获取症状列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'         => 'sometimes|integer|gt:0',
            'per_page'     => 'sometimes|integer|between:1,200',
            'keyword'      => 'nullable|max:255',
            'sort_name'    => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'      => 'nullable|in:asc,desc',
            'body_part_id' => 'nullable|integer|gt:0',
            'pet_type'     => 'nullable|in:general,dog,cat',
            'status'       => 'nullable|in:0,1',
        ]);

        $query = BodyPartSymptom::with('bodyPart');

        // 关键词搜索
        if ($request->has('keyword') && $request->input('keyword')) {
            $keyword = $request->input('keyword');
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }

        // 部位筛选
        if ($request->has('body_part_id') && $request->input('body_part_id')) {
            $query->where('body_part_id', $request->input('body_part_id'));
        }

        // 宠物类型筛选
        if ($request->has('pet_type') && $request->input('pet_type')) {
            $query->where('pet_type', $request->input('pet_type'));
        }

        // 状态筛选
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('status', $request->input('status'));
        }

        // 排序
        $sortName = $request->input('sort_name', 'sort_order');
        $sortBy = $request->input('sort_by', 'asc');
        $query->orderBy($sortName, $sortBy);

        // 使用标准分页
        $perPage = $request->input('per_page', 15);
        $records = $query->paginate($perPage);

        return ResponseHelper::success($records);
    }

    /**
     * 获取症状详情
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $detail = BodyPartSymptom::with('bodyPart')->find($id);

        if (!$detail) {
            return ResponseHelper::error('症状不存在');
        }

        return ResponseHelper::success($detail);
    }

    /**
     * 保存症状信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'           => 'sometimes|integer|gte:0',
            'body_part_id' => 'required|integer|gt:0|exists:analysis_body_parts,id',
            'code'         => 'required|string|max:50',
            'name'         => 'required|string|max:100',
            'description'  => 'nullable|string',
            'icon'         => 'nullable|string|max:255',
            'pet_type'     => 'required|in:general,dog,cat',
            'sort_order'   => 'nullable|integer|gte:0',
            'status'       => 'required|in:0,1',
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));

        // 检查部位是否存在
        $bodyPart = AnalysisBodyPart::find($formData['body_part_id']);
        if (!$bodyPart) {
            return ResponseHelper::error('部位不存在');
        }

        // 检查唯一性
        $query = BodyPartSymptom::where('code', $formData['code'])
            ->where('pet_type', $formData['pet_type']);

        if (isset($formData['id']) && $formData['id'] > 0) {
            $query->where('id', '!=', $formData['id']);
        }

        $exists = $query->exists();
        if ($exists) {
            return ResponseHelper::error('该宠物类型下已存在相同代码的症状');
        }

        // 保存数据
        if (isset($formData['id']) && $formData['id'] > 0) {
            $model = BodyPartSymptom::find($formData['id']);
            if (!$model) {
                return ResponseHelper::error('症状不存在');
            }
            $model->update($formData);
        } else {
            $model = BodyPartSymptom::create($formData);
        }

        return ResponseHelper::success($model);
    }

    /**
     * 删除症状
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $model = BodyPartSymptom::find($id);

        if (!$model) {
            return ResponseHelper::error('症状不存在');
        }

        $model->delete();

        return ResponseHelper::success(true);
    }
}
