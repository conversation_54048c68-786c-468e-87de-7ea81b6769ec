<?php

namespace App\Services\PetCalculator;

/**
 * 狗营养计算器
 * 基于AAFCO标准计算狗的营养需求
 */
class DogNutritionCalculator extends NutritionCalculator
{
    /**
     * 获取营养素功能说明
     *
     * @param string $nutrient
     * @return string
     */
    protected function getNutrientFunction(string $nutrient): string
    {
        $functions = [
            // 宏量营养素
            'protein' => '构建和修复肌肉组织，维持免疫系统功能，提供能量',
            'fat' => '提供必需脂肪酸，促进脂溶性维生素吸收，维持皮肤和毛发健康',
            'carbohydrate' => '提供快速能量，支持大脑和神经系统功能',
            'fiber' => '促进消化健康，维持肠道菌群平衡，帮助体重管理',

            // 维生素
            'vitamin_a' => '维持视力健康，支持免疫系统，促进细胞生长和分化',
            'vitamin_d' => '促进钙磷吸收，维持骨骼和牙齿健康',
            'vitamin_e' => '抗氧化作用，保护细胞膜，维持肌肉和神经系统健康',
            'vitamin_k' => '参与血液凝固，维持骨骼健康',
            'thiamine' => '能量代谢，神经系统功能，心脏健康',
            'riboflavin' => '能量代谢，细胞生长，维持皮肤和眼睛健康',
            'niacin' => '能量代谢，神经系统功能，皮肤健康',
            'pantothenic_acid' => '能量代谢，激素合成，神经系统功能',
            'pyridoxine' => '蛋白质代谢，神经递质合成，免疫功能',
            'folic_acid' => 'DNA合成，红细胞形成，神经系统发育',
            'vitamin_b12' => '红细胞形成，神经系统功能，DNA合成',
            'choline' => '大脑发育，神经传导，脂肪代谢',

            // 矿物质
            'calcium' => '骨骼和牙齿发育，肌肉收缩，神经传导',
            'phosphorus' => '骨骼和牙齿健康，能量代谢，细胞膜结构',
            'potassium' => '肌肉和神经功能，维持体液平衡',
            'sodium' => '维持体液平衡，神经传导，肌肉功能',
            'chloride' => '维持酸碱平衡，消化液成分',
            'magnesium' => '骨骼健康，肌肉和神经功能，酶活性',
            'iron' => '血红蛋白合成，氧气运输，能量代谢',
            'zinc' => '免疫功能，伤口愈合，蛋白质合成，皮肤健康',
            'copper' => '铁代谢，结缔组织形成，抗氧化酶活性',
            'manganese' => '骨骼发育，酶活性，抗氧化功能',
            'selenium' => '抗氧化功能，免疫系统支持，甲状腺功能',
            'iodine' => '甲状腺激素合成，代谢调节',

            // 氨基酸
            'arginine' => '免疫功能，伤口愈合，蛋白质合成',
            'histidine' => '组织修复，血红蛋白合成，神经保护',
            'isoleucine' => '肌肉代谢，能量产生，血糖调节',
            'leucine' => '肌肉蛋白质合成，血糖调节',
            'lysine' => '蛋白质合成，钙吸收，胶原蛋白形成',
            'methionine_cystine' => '蛋白质合成，毛发健康，抗氧化功能',
            'phenylalanine_tyrosine' => '神经递质合成，蛋白质合成',
            'threonine' => '蛋白质合成，免疫功能，脂肪代谢',
            'tryptophan' => '血清素合成，睡眠调节，情绪稳定',
            'valine' => '肌肉代谢，组织修复，能量产生',
        ];

        return $functions[$nutrient] ?? '维持正常生理功能';
    }

    /**
     * 获取食物来源
     *
     * @param string $nutrient
     * @return array
     */
    protected function getFoodSources(string $nutrient): array
    {
        $sources = [
            // 宏量营养素
            'protein' => ['鸡肉', '牛肉', '鱼肉', '鸡蛋', '豆类'],
            'fat' => ['鱼油', '鸡脂', '植物油', '坚果'],
            'carbohydrate' => ['糙米', '燕麦', '红薯', '土豆'],
            'fiber' => ['南瓜', '胡萝卜', '苹果', '燕麦'],

            // 维生素
            'vitamin_a' => ['胡萝卜', '菠菜', '肝脏', '鸡蛋'],
            'vitamin_d' => ['鱼油', '蛋黄', '肝脏'],
            'vitamin_e' => ['植物油', '坚果', '绿叶蔬菜'],
            'vitamin_k' => ['绿叶蔬菜', '肝脏', '鱼类'],
            'thiamine' => ['全谷物', '豆类', '肉类'],
            'riboflavin' => ['肉类', '蛋类', '奶制品'],
            'niacin' => ['肉类', '鱼类', '全谷物'],
            'pantothenic_acid' => ['肉类', '蛋类', '全谷物'],
            'pyridoxine' => ['肉类', '鱼类', '全谷物'],
            'folic_acid' => ['绿叶蔬菜', '豆类', '肝脏'],
            'vitamin_b12' => ['肉类', '鱼类', '蛋类'],
            'choline' => ['蛋类', '肉类', '鱼类'],

            // 矿物质
            'calcium' => ['骨粉', '奶制品', '绿叶蔬菜'],
            'phosphorus' => ['肉类', '鱼类', '骨粉'],
            'potassium' => ['香蕉', '土豆', '肉类'],
            'sodium' => ['食盐', '肉类', '海藻'],
            'chloride' => ['食盐', '海藻'],
            'magnesium' => ['全谷物', '坚果', '绿叶蔬菜'],
            'iron' => ['肝脏', '红肉', '菠菜'],
            'zinc' => ['肉类', '海鲜', '全谷物'],
            'copper' => ['肝脏', '坚果', '海鲜'],
            'manganese' => ['全谷物', '坚果', '绿叶蔬菜'],
            'selenium' => ['鱼类', '肉类', '全谷物'],
            'iodine' => ['海藻', '鱼类', '碘化盐'],

            // 氨基酸
            'arginine' => ['肉类', '鱼类', '坚果'],
            'histidine' => ['肉类', '鱼类', '全谷物'],
            'isoleucine' => ['肉类', '蛋类', '豆类'],
            'leucine' => ['肉类', '蛋类', '豆类'],
            'lysine' => ['肉类', '鱼类', '豆类'],
            'methionine_cystine' => ['肉类', '鱼类', '蛋类'],
            'phenylalanine_tyrosine' => ['肉类', '鱼类', '蛋类'],
            'threonine' => ['肉类', '蛋类', '豆类'],
            'tryptophan' => ['火鸡肉', '鸡肉', '鱼类'],
            'valine' => ['肉类', '蛋类', '豆类'],
        ];

        return $sources[$nutrient] ?? ['均衡饮食'];
    }

    /**
     * 获取推荐建议
     *
     * @return array
     */
    protected function getRecommendations(): array
    {
        $recommendations = [
            'feeding_guidelines' => [
                '选择高质量的商业狗粮，确保符合AAFCO标准',
                '根据年龄、体重和活动量调整食量',
                '定时定量喂食，避免自由采食',
                '提供充足的清洁饮水'
            ],
            'special_considerations' => [
                '幼犬需要更高的蛋白质和脂肪含量',
                '老年犬可能需要易消化的食物',
                '工作犬或高活动量犬需要更多能量',
                '超重犬需要控制热量摄入'
            ],
            'foods_to_avoid' => [
                '巧克力 - 含有可可碱，对狗有毒',
                '葡萄和葡萄干 - 可能导致肾衰竭',
                '洋葱和大蒜 - 可能导致贫血',
                '木糖醇 - 人工甜味剂，可能导致低血糖',
                '酒精 - 对狗的神经系统有害',
                '咖啡因 - 可能导致心律不齐',
                '牛油果 - 含有对狗有害的化合物',
                '生面团 - 可能在胃中发酵产生酒精'
            ],
            'supplementation_notes' => [
                '均衡的商业狗粮通常不需要额外补充',
                '特殊情况下可考虑补充鱼油（Omega-3）',
                '老年犬可能需要关节保健品',
                '任何补充剂使用前请咨询兽医'
            ],
            'monitoring_tips' => [
                '定期监测体重和身体状况评分',
                '观察食欲、精神状态和排便情况',
                '注意皮肤和毛发的健康状况',
                '定期进行兽医检查和血液检测'
            ]
        ];

        // 根据年龄阶段添加特殊建议
        $ageStage = $this->getAgeStage();
        if ($ageStage === 'puppy') {
            $recommendations['age_specific'] = [
                '幼犬期营养对终生健康至关重要',
                '选择专门的幼犬粮，营养密度更高',
                '少量多餐，每天3-4次',
                '确保充足的DHA支持大脑发育'
            ];
        } elseif ($ageStage === 'senior') {
            $recommendations['age_specific'] = [
                '老年犬代谢较慢，需要易消化的食物',
                '可能需要增加抗氧化剂摄入',
                '注意关节健康，考虑葡萄糖胺补充',
                '定期监测肾脏和心脏功能'
            ];
        }

        return $recommendations;
    }
}
