<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use App\Events\PetAlbumLikedEvent;

/**
 * @mixin Builder
 */
class PetAlbumLike extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_albums_likes';

    protected $fillable = [
        'album_id',
        'album_user_id',
        'like_user_id',
        'is_like',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($albumId)
    {
        //宠物相册点赞列表
        $cacheAlbumLikeCount = "pet_album:{$albumId}:like_count";
        redis()->del($cacheAlbumLikeCount);
        if ($albumLikeList = self::where('album_id', $albumId)->select('id', 'like_user_id', 'created_at')->get()->toArray()) {
            redis()->incrBy($cacheAlbumLikeCount, count($albumLikeList));
            //用户相册点赞-用于判断某相册是否已点赞
            foreach ($albumLikeList as $v) {
                $appUserId = $v['like_user_id'];
                $cacheMyAlbumLikeId = "app_user:{$appUserId}:album_like_id:{$albumId}";
                redis()->set($cacheMyAlbumLikeId, $v['id']);
            }
        }
        //更新为初始化
        $cacheInitKey = "pet_album:{$albumId}:init_like";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($albumId)
    {
        $cacheInitKey = "pet_album:{$albumId}:init_like";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($albumId); //数据有问题时把key删除，重新初始化
    }

    public static function getAlbumLikeCount($albumId)
    {
        self::ensureInitRedis($albumId);
        $cacheAlbumLikeCount = "pet_album:{$albumId}:like_count";
        return (int)redis()->get($cacheAlbumLikeCount);
    }

    public static function isLike($appUserId, $albumId)
    {
        self::ensureInitRedis($albumId);
        $cacheMyAlbumLikeId = "app_user:{$appUserId}:album_like_id:{$albumId}";
        return redis()->get($cacheMyAlbumLikeId);
    }

    public static function likeAlbum($appUserId, $albumId)
    {
        self::ensureInitRedis($albumId);
        if (self::isLike($appUserId, $albumId)) {
            return [false, [], '不能重複點贊該相册'];
        }
        $today = date('Y-m-d');
        $cacheAlbumLikeCountToday = "app_user:{$appUserId}:album_like_count:{$today}";
        if (!redis()->exists($cacheAlbumLikeCountToday)) {
            redis()->setex($cacheAlbumLikeCountToday, 86400, self::withTrashed()->where('like_user_id', $appUserId)->where('created_at', '>=', $today)->count());
        }
        if (redis()->get($cacheAlbumLikeCountToday) >= 1000) {
            logErr('相册点赞数量告警：' . $appUserId);
            return [false, [], '今日點贊次數已達上限'];
        }
        if (!$album = PetAlbum::where('id', $albumId)->first()) {
            return [false, [], '相册不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            //用户今日相册次数
            redis()->incr($cacheAlbumLikeCountToday);
            //更新到数据库
            $albumLike = new self();
            $albumLike->album_id = $album->id;
            $albumLike->album_user_id = $album->user_id;
            $albumLike->like_user_id = $appUserId;
            $albumLike->is_like = 1;
            $albumLike->save();
            //相册点赞次数
            $cacheAlbumLikeCount = "pet_album:{$albumId}:like_count";
            redis()->incr($cacheAlbumLikeCount);
            //用户相册点赞-用于判断某相册是否已点赞
            $cacheMyAlbumLikeId = "app_user:{$appUserId}:album_like_id:{$albumId}";
            redis()->set($cacheMyAlbumLikeId, $albumLike->id);
            //更新统计
            PetAlbum::incrementLike($albumId);

            // 触发点赞事件（只有当点赞者和相册所有者不是同一人时才触发）
            if ($appUserId != $album->user_id) {
                event(new PetAlbumLikedEvent($albumId, $album->user_id, $appUserId));
            }

            DB::commit();
            return [true, ['album_like_id' => $albumLike->id], '點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '點贊失敗'];
        }
    }

    public static function cancelLikeAlbum($appUserId, $albumId)
    {
        if (!$albumLikeId = self::isLike($appUserId, $albumId)) {
            return [false, [], '還沒點贊該相册'];
        }
        DB::beginTransaction();
        try {
            //相册点赞次数
            $cacheAlbumLikeCount = "pet_album:{$albumId}:like_count";
            redis()->decr($cacheAlbumLikeCount);
            //用户相册点赞
            $cacheMyAlbumLikeId = "app_user:{$appUserId}:album_like_id:{$albumId}";
            redis()->del($cacheMyAlbumLikeId);
            //更新到数据库
            self::where('id', $albumLikeId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            //更新统计
            PetAlbum::decrementLike($albumId);
            DB::commit();
            return [true, [], '取消點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取消点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取消點贊失敗'];
        }
    }
}
