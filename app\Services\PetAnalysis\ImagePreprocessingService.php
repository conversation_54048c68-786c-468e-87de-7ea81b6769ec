<?php

namespace App\Services\PetAnalysis;

use Illuminate\Support\Facades\Log;

/**
 * 图片预处理服务
 * 专门处理复杂格式的化验报告图片，提高AI识别成功率
 */
class ImagePreprocessingService
{
    /**
     * 分析图片质量并提供优化建议
     * 
     * @param array $images 图片URL数组
     * @return array 分析结果和建议
     */
    public function analyzeImageQuality(array $images): array
    {
        $analysis = [
            'total_images' => count($images),
            'quality_score' => 0,
            'issues' => [],
            'suggestions' => [],
            'processing_recommendations' => []
        ];

        if (empty($images)) {
            $analysis['issues'][] = '没有提供图片';
            $analysis['suggestions'][] = '请上传至少一张化验报告图片';
            return $analysis;
        }

        // 基础质量评估
        $qualityScore = 70; // 基础分数

        // 图片数量评估
        if (count($images) > 5) {
            $analysis['issues'][] = '图片数量过多可能影响处理效率';
            $analysis['suggestions'][] = '建议将相关的检测项目合并到1-3张图片中';
            $qualityScore -= 10;
        } elseif (count($images) >= 2) {
            $qualityScore += 10; // 多张图片可以提供更多信息
        }

        // 处理建议
        $analysis['processing_recommendations'] = $this->getProcessingRecommendations($images);
        
        $analysis['quality_score'] = max(0, min(100, $qualityScore));

        return $analysis;
    }

    /**
     * 获取处理建议
     */
    protected function getProcessingRecommendations(array $images): array
    {
        $recommendations = [];

        // 基础建议
        $recommendations[] = [
            'type' => 'ai_prompt_optimization',
            'title' => 'AI识别优化',
            'description' => '针对复杂表格格式优化AI提示词',
            'priority' => 'high'
        ];

        $recommendations[] = [
            'type' => 'error_tolerance',
            'title' => '容错机制',
            'description' => '降低完整性检测阈值，允许部分指标识别失败',
            'priority' => 'high'
        ];

        $recommendations[] = [
            'type' => 'region_focus',
            'title' => '区域聚焦',
            'description' => '重点识别表格区域，忽略图表干扰',
            'priority' => 'medium'
        ];

        if (count($images) > 1) {
            $recommendations[] = [
                'type' => 'multi_image_fusion',
                'title' => '多图融合',
                'description' => '综合多张图片信息，提高识别准确率',
                'priority' => 'medium'
            ];
        }

        return $recommendations;
    }

    /**
     * 生成优化的AI提示词片段
     * 根据图片特征动态调整提示词
     */
    public function generateOptimizedPromptFragment(array $images): string
    {
        $fragment = '';

        // 基础优化
        $fragment .= "【圖片處理優化】：\n";
        $fragment .= "1. 重點關注表格區域的文字和數字\n";
        $fragment .= "2. 忽略圖表、線條等視覺元素\n";
        $fragment .= "3. 允許部分指標因圖片質量問題無法識別\n";
        $fragment .= "4. 確保識別成功的指標100%準確\n\n";

        // 多图处理
        if (count($images) > 1) {
            $fragment .= "【多圖處理策略】：\n";
            $fragment .= "1. 綜合所有圖片信息\n";
            $fragment .= "2. 優先選擇清晰度最高的指標數據\n";
            $fragment .= "3. 避免重複指標，選擇最完整的版本\n\n";
        }

        // 容错建议
        $fragment .= "【容錯要求】：\n";
        $fragment .= "1. 最低識別目標：3-5個有效指標\n";
        $fragment .= "2. 質量優於數量：寧可少量準確，不要大量錯誤\n";
        $fragment .= "3. 不確定的信息寧可不識別\n\n";

        return $fragment;
    }

    /**
     * 检查图片是否适合AI识别
     */
    public function isImageSuitableForAI(array $images): array
    {
        $result = [
            'suitable' => true,
            'confidence' => 80,
            'issues' => [],
            'recommendations' => []
        ];

        if (empty($images)) {
            $result['suitable'] = false;
            $result['confidence'] = 0;
            $result['issues'][] = '没有提供图片';
            return $result;
        }

        // 图片数量检查
        if (count($images) > 10) {
            $result['suitable'] = false;
            $result['confidence'] = 20;
            $result['issues'][] = '图片数量过多，可能影响处理效果';
            $result['recommendations'][] = '建议减少到5张以内的关键图片';
        }

        // 基础适用性评估
        if (count($images) >= 1 && count($images) <= 5) {
            $result['confidence'] = 85;
            $result['recommendations'][] = '图片数量适中，建议使用容错识别模式';
        }

        return $result;
    }

    /**
     * 生成图片处理报告
     */
    public function generateProcessingReport(array $images, array $analysisResult = null): array
    {
        $report = [
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'image_count' => count($images),
            'processing_strategy' => 'enhanced_tolerance',
            'expected_indicators' => '3-15个',
            'quality_threshold' => 'lowered_for_complex_format',
            'recommendations' => []
        ];

        // 基础建议
        $report['recommendations'][] = '使用增强容错模式处理复杂表格格式';
        $report['recommendations'][] = '重点识别清晰可见的指标，允许部分识别失败';
        $report['recommendations'][] = '优先保证识别准确性，而非完整性';

        if ($analysisResult) {
            $report['analysis_result'] = $analysisResult;
        }

        return $report;
    }

    /**
     * 获取针对复杂格式的特殊处理建议
     */
    public function getComplexFormatHandlingAdvice(): array
    {
        return [
            'ai_strategy' => [
                'use_region_focus' => true,
                'ignore_chart_elements' => true,
                'lower_completeness_threshold' => true,
                'prioritize_accuracy' => true
            ],
            'error_handling' => [
                'allow_partial_recognition' => true,
                'minimum_indicators' => 3,
                'quality_over_quantity' => true
            ],
            'fallback_options' => [
                'manual_input_suggestion' => true,
                'image_quality_improvement' => true,
                'region_cropping' => true
            ]
        ];
    }
}
