<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AnalysisBodyPart;
use App\Models\BodyPartSymptom;

class AnalysisBodyPartsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 通用部位
        $generalParts = [
            [
                'code' => 'general',
                'name' => '全身/综合',
                'description' => '全面评估宠物的整体健康状况',
                'icon' => '/images/parts/general.png',
                'pet_type' => 'general',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'head',
                'name' => '头部',
                'description' => '头部健康状况分析',
                'icon' => '/images/parts/head.png',
                'pet_type' => 'general',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'eyes',
                'name' => '眼睛',
                'description' => '眼睛健康状况分析',
                'icon' => '/images/parts/eyes.png',
                'pet_type' => 'general',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'ears',
                'name' => '耳朵',
                'description' => '耳朵健康状况分析',
                'icon' => '/images/parts/ears.png',
                'pet_type' => 'general',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'code' => 'mouth',
                'name' => '口腔/牙齿',
                'description' => '口腔和牙齿健康状况分析',
                'icon' => '/images/parts/mouth.png',
                'pet_type' => 'general',
                'sort_order' => 5,
                'status' => 1
            ],
            [
                'code' => 'nose',
                'name' => '鼻子',
                'description' => '鼻子健康状况分析',
                'icon' => '/images/parts/nose.png',
                'pet_type' => 'general',
                'sort_order' => 6,
                'status' => 1
            ],
            [
                'code' => 'skin',
                'name' => '皮肤/被毛',
                'description' => '皮肤和被毛健康状况分析',
                'icon' => '/images/parts/skin.png',
                'pet_type' => 'general',
                'sort_order' => 7,
                'status' => 1
            ],
            [
                'code' => 'limbs',
                'name' => '四肢/关节',
                'description' => '四肢和关节健康状况分析',
                'icon' => '/images/parts/limbs.png',
                'pet_type' => 'general',
                'sort_order' => 8,
                'status' => 1
            ],
            [
                'code' => 'digestive',
                'name' => '消化系统',
                'description' => '消化系统健康状况分析',
                'icon' => '/images/parts/digestive.png',
                'pet_type' => 'general',
                'sort_order' => 9,
                'status' => 1
            ],
            [
                'code' => 'respiratory',
                'name' => '呼吸系统',
                'description' => '呼吸系统健康状况分析',
                'icon' => '/images/parts/respiratory.png',
                'pet_type' => 'general',
                'sort_order' => 10,
                'status' => 1
            ],
            [
                'code' => 'urinary',
                'name' => '泌尿系统',
                'description' => '泌尿系统健康状况分析',
                'icon' => '/images/parts/urinary.png',
                'pet_type' => 'general',
                'sort_order' => 11,
                'status' => 1
            ],
            [
                'code' => 'reproductive',
                'name' => '生殖系统',
                'description' => '生殖系统健康状况分析',
                'icon' => '/images/parts/reproductive.png',
                'pet_type' => 'general',
                'sort_order' => 12,
                'status' => 1
            ],
            [
                'code' => 'behavior',
                'name' => '行为/精神状态',
                'description' => '行为和精神状态分析',
                'icon' => '/images/parts/behavior.png',
                'pet_type' => 'general',
                'sort_order' => 13,
                'status' => 1
            ]
        ];
        
        // 狗特有部位
        $dogParts = [
            [
                'code' => 'paws',
                'name' => '爪子/足垫',
                'description' => '爪子和足垫健康状况分析',
                'icon' => '/images/parts/dog_paws.png',
                'pet_type' => 'dog',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'back',
                'name' => '背部/脊椎',
                'description' => '背部和脊椎健康状况分析',
                'icon' => '/images/parts/dog_back.png',
                'pet_type' => 'dog',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'tail',
                'name' => '尾巴',
                'description' => '尾巴健康状况分析',
                'icon' => '/images/parts/dog_tail.png',
                'pet_type' => 'dog',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'anal',
                'name' => '肛门/肛门腺',
                'description' => '肛门和肛门腺健康状况分析',
                'icon' => '/images/parts/dog_anal.png',
                'pet_type' => 'dog',
                'sort_order' => 4,
                'status' => 1
            ]
        ];
        
        // 猫特有部位
        $catParts = [
            [
                'code' => 'paws',
                'name' => '爪子/足垫',
                'description' => '爪子和足垫健康状况分析',
                'icon' => '/images/parts/cat_paws.png',
                'pet_type' => 'cat',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'back',
                'name' => '背部/脊椎',
                'description' => '背部和脊椎健康状况分析',
                'icon' => '/images/parts/cat_back.png',
                'pet_type' => 'cat',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'tail',
                'name' => '尾巴',
                'description' => '尾巴健康状况分析',
                'icon' => '/images/parts/cat_tail.png',
                'pet_type' => 'cat',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'grooming',
                'name' => '清洁行为',
                'description' => '猫咪清洁行为分析',
                'icon' => '/images/parts/cat_grooming.png',
                'pet_type' => 'cat',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'code' => 'whiskers',
                'name' => '胡须/触须',
                'description' => '猫咪胡须和触须状态分析',
                'icon' => '/images/parts/cat_whiskers.png',
                'pet_type' => 'cat',
                'sort_order' => 5,
                'status' => 1
            ]
        ];
        
        // 合并所有部位并添加到数据库
        $allParts = array_merge($generalParts, $dogParts, $catParts);
        
        foreach ($allParts as $part) {
            $bodyPart = AnalysisBodyPart::updateOrCreate(
                ['code' => $part['code'], 'pet_type' => $part['pet_type']],
                $part
            );
            
            // 为每个部位添加一些常见症状
            $this->addSymptomsForPart($bodyPart);
        }
    }
    
    /**
     * 为部位添加常见症状
     *
     * @param AnalysisBodyPart $bodyPart
     * @return void
     */
    private function addSymptomsForPart(AnalysisBodyPart $bodyPart)
    {
        $symptoms = [];
        
        // 通用症状
        $generalSymptoms = [
            'pain' => '疼痛',
            'swelling' => '肿胀',
            'redness' => '发红',
            'itching' => '瘙痒',
            'discharge' => '分泌物',
            'abnormal_behavior' => '异常行为'
        ];
        
        // 根据部位代码添加特定症状
        switch ($bodyPart->code) {
            case 'head':
                $symptoms = [
                    'head_tilt' => '头部倾斜',
                    'head_shaking' => '摇头',
                    'head_pressing' => '头部顶压',
                    'asymmetry' => '不对称'
                ];
                break;
                
            case 'eyes':
                $symptoms = [
                    'redness' => '发红',
                    'discharge' => '分泌物',
                    'squinting' => '眯眼',
                    'cloudiness' => '混浊',
                    'tearing' => '流泪',
                    'third_eyelid_showing' => '第三眼睑外露'
                ];
                break;
                
            case 'ears':
                $symptoms = [
                    'head_shaking' => '摇头',
                    'ear_scratching' => '抓耳朵',
                    'discharge' => '分泌物',
                    'odor' => '异味',
                    'redness' => '发红',
                    'swelling' => '肿胀'
                ];
                break;
                
            case 'mouth':
                $symptoms = [
                    'bad_breath' => '口臭',
                    'drooling' => '流口水',
                    'difficulty_eating' => '进食困难',
                    'bleeding_gums' => '牙龈出血',
                    'tartar' => '牙垢',
                    'loose_teeth' => '牙齿松动'
                ];
                break;
                
            case 'nose':
                $symptoms = [
                    'discharge' => '鼻涕',
                    'sneezing' => '打喷嚏',
                    'dryness' => '干燥',
                    'bleeding' => '流血',
                    'breathing_difficulty' => '呼吸困难'
                ];
                break;
                
            case 'skin':
                $symptoms = [
                    'itching' => '瘙痒',
                    'redness' => '发红',
                    'hair_loss' => '脱毛',
                    'dandruff' => '皮屑',
                    'rash' => '皮疹',
                    'lumps' => '肿块',
                    'parasites' => '寄生虫'
                ];
                break;
                
            case 'paws':
                $symptoms = [
                    'limping' => '跛行',
                    'licking_paws' => '舔爪子',
                    'swelling' => '肿胀',
                    'broken_nails' => '指甲断裂',
                    'pad_injuries' => '足垫受伤'
                ];
                break;
                
            case 'limbs':
                $symptoms = [
                    'limping' => '跛行',
                    'stiffness' => '僵硬',
                    'swelling' => '肿胀',
                    'reluctance_to_move' => '不愿活动',
                    'abnormal_gait' => '步态异常'
                ];
                break;
                
            case 'back':
                $symptoms = [
                    'arched_back' => '拱背',
                    'stiffness' => '僵硬',
                    'pain_when_touched' => '触摸时疼痛',
                    'reluctance_to_jump' => '不愿跳跃',
                    'difficulty_standing' => '站立困难'
                ];
                break;
                
            case 'tail':
                $symptoms = [
                    'tail_chasing' => '追尾巴',
                    'limp_tail' => '尾巴无力',
                    'hair_loss' => '尾巴脱毛',
                    'pain_when_touched' => '触摸时疼痛'
                ];
                break;
                
            case 'digestive':
                $symptoms = [
                    'vomiting' => '呕吐',
                    'diarrhea' => '腹泻',
                    'constipation' => '便秘',
                    'loss_of_appetite' => '食欲不振',
                    'excessive_gas' => '胀气',
                    'abdominal_pain' => '腹痛',
                    'weight_loss' => '体重减轻'
                ];
                break;
                
            case 'respiratory':
                $symptoms = [
                    'coughing' => '咳嗽',
                    'sneezing' => '打喷嚏',
                    'wheezing' => '喘息',
                    'labored_breathing' => '呼吸困难',
                    'nasal_discharge' => '鼻涕',
                    'rapid_breathing' => '呼吸急促'
                ];
                break;
                
            case 'urinary':
                $symptoms = [
                    'frequent_urination' => '频繁排尿',
                    'straining_to_urinate' => '排尿困难',
                    'blood_in_urine' => '尿血',
                    'inappropriate_urination' => '不当排尿',
                    'excessive_thirst' => '过度饮水'
                ];
                break;
                
            case 'reproductive':
                $symptoms = [
                    'discharge' => '分泌物',
                    'swelling' => '肿胀',
                    'excessive_licking' => '过度舔舐',
                    'behavioral_changes' => '行为改变'
                ];
                break;
                
            case 'behavior':
                $symptoms = [
                    'lethargy' => '嗜睡',
                    'aggression' => '攻击性',
                    'anxiety' => '焦虑',
                    'excessive_vocalization' => '过度发声',
                    'hiding' => '躲藏',
                    'changes_in_sleep' => '睡眠改变',
                    'disorientation' => '迷失方向'
                ];
                break;
                
            case 'anal':
                if ($bodyPart->pet_type === 'dog') {
                    $symptoms = [
                        'scooting' => '蹭地',
                        'licking' => '舔肛门',
                        'swelling' => '肿胀',
                        'discharge' => '分泌物',
                        'odor' => '异味'
                    ];
                }
                break;
                
            case 'grooming':
                if ($bodyPart->pet_type === 'cat') {
                    $symptoms = [
                        'excessive_grooming' => '过度舔毛',
                        'lack_of_grooming' => '不舔毛',
                        'hair_loss' => '脱毛',
                        'dandruff' => '皮屑',
                        'matted_fur' => '毛发打结'
                    ];
                }
                break;
                
            case 'whiskers':
                if ($bodyPart->pet_type === 'cat') {
                    $symptoms = [
                        'broken_whiskers' => '胡须断裂',
                        'asymmetrical_whiskers' => '胡须不对称',
                        'whisker_fatigue' => '胡须疲劳',
                        'behavioral_changes' => '行为改变'
                    ];
                }
                break;
                
            case 'general':
                $symptoms = [
                    'lethargy' => '嗜睡',
                    'fever' => '发热',
                    'loss_of_appetite' => '食欲不振',
                    'weight_loss' => '体重减轻',
                    'increased_thirst' => '饮水增加',
                    'weakness' => '虚弱',
                    'behavioral_changes' => '行为改变'
                ];
                break;
        }
        
        // 合并通用症状和特定症状
        if ($bodyPart->code !== 'general') {
            $symptoms = array_merge($symptoms, $generalSymptoms);
        }
        
        // 添加症状到数据库
        $sortOrder = 1;
        foreach ($symptoms as $code => $name) {
            BodyPartSymptom::updateOrCreate(
                ['body_part_id' => $bodyPart->id, 'code' => $code],
                [
                    'name' => $name,
                    'description' => $name . '症状描述',
                    'pet_type' => $bodyPart->pet_type,
                    'sort_order' => $sortOrder++,
                    'status' => 1
                ]
            );
        }
    }
}
