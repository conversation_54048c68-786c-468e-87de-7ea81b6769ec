<?php

require_once 'vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 测试userid筛选修复效果 ===\n";

try {
    // 测试1: 直接调用模型方法（只传userid）
    echo "\n1. 直接测试模型方法（只传userid=2）:\n";
    $formData1 = [
        'page' => 1,
        'per_page' => 10,
        'userid' => 2
    ];
    
    // 使用数据库查询模拟模型方法
    $query = DB::table('pets_albums')
        ->where('status', 1)
        ->where('user_id', 2)
        ->select('id', 'user_id', 'pet_ids', 'description')
        ->orderBy('created_at', 'desc');
    
    $albums = $query->get();
    echo "数据库直接查询userid=2的相册数量: " . $albums->count() . "\n";
    
    foreach ($albums as $album) {
        echo "- ID: {$album->id}, user_id: {$album->user_id}, pet_ids: '{$album->pet_ids}'\n";
    }
    
    // 测试2: 模拟修复后的控制器逻辑
    echo "\n2. 模拟修复后的控制器逻辑:\n";
    $formData2 = [
        'page' => 1,
        'per_page' => 10,
        'userid' => 2
    ];
    
    $userId = 1; // 当前登录用户ID
    
    // 修复后的逻辑：如果指定了userid，不添加宠物权限限制
    if (!empty($formData2['pet_id'])) {
        echo "指定了pet_id参数\n";
    } elseif (empty($formData2['userid'])) {
        echo "没有指定userid，会添加宠物权限限制\n";
    } else {
        echo "指定了userid={$formData2['userid']}，不添加宠物权限限制\n";
    }
    
    echo "最终传递给模型的参数:\n";
    print_r($formData2);
    
    // 测试3: 对比修复前后的差异
    echo "\n3. 对比修复前后的差异:\n";
    
    echo "修复前逻辑（会添加宠物权限限制）:\n";
    $beforeFormData = [
        'page' => 1,
        'per_page' => 10,
        'userid' => 2,
        'pet_id' => [4 => 0] // 模拟添加的宠物权限限制
    ];
    print_r($beforeFormData);
    
    echo "修复后逻辑（不添加宠物权限限制）:\n";
    $afterFormData = [
        'page' => 1,
        'per_page' => 10,
        'userid' => 2
        // 不添加pet_id限制
    ];
    print_r($afterFormData);
    
    // 测试4: 验证不同场景
    echo "\n4. 验证不同场景:\n";
    
    echo "场景1: 只传userid=2 -> 应该返回4条记录\n";
    $scenario1 = ['userid' => 2];
    $count1 = DB::table('pets_albums')->where('status', 1)->where('user_id', 2)->count();
    echo "预期结果: {$count1} 条\n";
    
    echo "场景2: 传userid=2和pet_id=4 -> 应该返回包含宠物4的记录\n";
    $count2 = DB::table('pets_albums')
        ->where('status', 1)
        ->where('user_id', 2)
        ->where(function($q) {
            $q->whereRaw("FIND_IN_SET(?, pet_ids)", [4]);
        })
        ->count();
    echo "预期结果: {$count2} 条\n";
    
    echo "场景3: 不传任何参数 -> 应该根据用户权限返回\n";
    echo "这种情况下会添加宠物权限限制\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试完成 ===\n";
