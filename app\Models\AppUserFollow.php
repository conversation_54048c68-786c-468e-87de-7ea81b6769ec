<?php

namespace App\Models;

use App\Events\UserFollowedEvent;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class AppUserFollow extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_users_follows';

    protected $fillable = [
        'app_user_id',
        'follow_user_id',
        'is_mutual',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($appUserId)
    {
        //我的关注列表
        $cacheFollowingOfMine = "app_user:{$appUserId}:following";
        $followingList = self::where('app_user_id', $appUserId)->select('follow_user_id', 'is_mutual', 'created_at')->get()->toArray();
        redis()->del($cacheFollowingOfMine);
        //相互关注关系
        $cacheMutualFollowKeyOfMine = "app_user:{$appUserId}:mutual_following";
        redis()->del($cacheMutualFollowKeyOfMine);
        foreach ($followingList as $v) {
            $timeOfMine = strtotime($v['created_at']);
            redis()->zAdd($cacheFollowingOfMine, $timeOfMine, $v['follow_user_id']);
            if ($v['is_mutual'] == 1) {
                redis()->zAdd($cacheMutualFollowKeyOfMine, $timeOfMine, $v['follow_user_id']);
            }
        }
        //我的粉丝列表
        $cacheFollowersOfMine = "app_user:{$appUserId}:followers";
        redis()->del($cacheFollowersOfMine);
        $followerUserIds = self::where('follow_user_id', $appUserId)->select('app_user_id', 'created_at')->get()->toArray();
        $followingUserIds = array_column($followingList, null, 'follow_user_id');
        foreach ($followerUserIds as $v) {
            $timeOfIt = strtotime($v['created_at']);
            if (array_key_exists($v['app_user_id'], $followingUserIds)) {
                //我和粉丝互关
                $cacheMutualFollowKeyOfIt = "app_user:{$v['app_user_id']}:mutual_following";
                $timeOfMine = strtotime($followingUserIds[$v['app_user_id']]['created_at']);
                $mutualTime = max($timeOfIt, $timeOfMine);
                redis()->zAdd($cacheMutualFollowKeyOfIt, $mutualTime, $appUserId);
            }
        }
        //更新为初始化
        $cacheInitKey = "app_user:{$appUserId}:init_following";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($appUserId)
    {
        $cacheInitKey = "app_user:{$appUserId}:init_following";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($appUserId); //数据有问题时把key删除，重新初始化
    }

    //判断是否关注
    public static function isFollowing($appUserId, $followingUserId)
    {
        self::ensureInitRedis($appUserId);
        $cacheFollowingOfMine = "app_user:{$appUserId}:following";
        return redis()->zScore($cacheFollowingOfMine, $followingUserId) > 0;
    }

    //判断是否互相关注
    public static function isMutualFollowing($appUserId, $followingUserId)
    {
        self::ensureInitRedis($appUserId);
        $cacheMutualFollowKeyOfMine = "app_user:{$appUserId}:mutual_following";
        return redis()->zScore($cacheMutualFollowKeyOfMine, $followingUserId) > 0;
    }

    public static function setFollowing($appUserId, $followingUserId)
    {
        if ($appUserId == $followingUserId) {
            return [false, [], '不能關注自己'];
        }
        if (self::isFollowing($appUserId, $followingUserId)) {
            return [false, [], '不能重複關注'];
        }
        //我的关注列表
        $cacheFollowingOfMine = "app_user:{$appUserId}:following";
        if (redis()->zCard($cacheFollowingOfMine) >= 1000) {
            logErr('关注数量告警：' . $appUserId);
            return [false, [], '關注數量已達上限'];
        }
        if (!AppUser::where('id', $followingUserId)->value('id')) {
            return [false, [], '用戶不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            $time = time();
            redis()->zAdd($cacheFollowingOfMine, $time, $followingUserId);
            //TA的粉丝列表
            $cacheFollowersOfIt = "app_user:{$followingUserId}:followers";
            redis()->zAdd($cacheFollowersOfIt, $time, $appUserId);
            //检查是否相互关注
            $cacheFollowingOfIt = "app_user:{$followingUserId}:following";
            if ($isMutual = redis()->zScore($cacheFollowingOfIt, $appUserId)) {
                //记录相互关注关系（方便快速查找）
                list($cacheMutualFollowKeyOfMine, $cacheMutualFollowKeyOfIt) = [
                    "app_user:{$appUserId}:mutual_following",
                    "app_user:{$followingUserId}:mutual_following"
                ];
                redis()->zAdd($cacheMutualFollowKeyOfMine, $time, $followingUserId);
                redis()->zAdd($cacheMutualFollowKeyOfIt, $time, $appUserId);
            }
            //更新到数据库
            $follow = new self();
            $follow->app_user_id = $appUserId;
            $follow->follow_user_id = $followingUserId;
            $follow->is_mutual = $isMutual ? 1 : 0;
            $follow->save();
            if ($isMutual) {
                self::where('app_user_id', $followingUserId)->where('follow_user_id', $appUserId)->update(['is_mutual' => 1]);
            }
            //更新统计
            AppUser::incrementFollower($appUserId, $followingUserId);

            // 触发用户关注事件
            event(new UserFollowedEvent($followingUserId, $appUserId));

            DB::commit();
            return [true, [], '關注成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('关注失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '關注失敗'];
        }
    }

    public static function cancelFollowing($appUserId, $followingUserId)
    {
        if ($appUserId == $followingUserId) {
            return [false, [], '不能取消關注自己'];
        }
        if (!self::isFollowing($appUserId, $followingUserId)) {
            return [false, [], '還沒关注TA'];
        }
        DB::beginTransaction();
        try {
            //我的关注列表
            $cacheFollowingOfMine = "app_user:{$appUserId}:following";
            redis()->zRem($cacheFollowingOfMine, $followingUserId);
            //TA的粉丝列表
            $cacheFollowersOfIt = "app_user:{$followingUserId}:followers";
            redis()->zRem($cacheFollowersOfIt, $appUserId);
            //检查相互关注关系
            $cacheFollowingOfIt = "app_user:{$followingUserId}:following";
            if ($isMutual = redis()->zScore($cacheFollowingOfIt, $appUserId)) {
                //删除相互关注关系（方便快速查找）
                list($cacheMutualFollowKeyOfMine, $cacheMutualFollowKeyOfIt) = [
                    "app_user:{$appUserId}:mutual_following",
                    "app_user:{$followingUserId}:mutual_following"
                ];
                redis()->zRem($cacheMutualFollowKeyOfMine, $followingUserId);
                redis()->zRem($cacheMutualFollowKeyOfIt, $appUserId);
            }
            //更新到数据库
            self::where('app_user_id', $appUserId)->where('follow_user_id', $followingUserId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if ($isMutual) {
                self::where('app_user_id', $followingUserId)->where('follow_user_id', $appUserId)->update(['is_mutual' => 0]);
            }
            //更新统计
            AppUser::decrementFollower($appUserId, $followingUserId);
            DB::commit();
            return [true, [], '取關成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取关失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取關失敗'];
        }
    }

    public static function getFollowingList($search_data = array(), $currentUserId = 0)
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $appUserId = $search_data['user_id'];
        if (!$appUserId) {
            return [false, [], '用戶未登录'];
        }
        self::ensureInitRedis($appUserId);
        if (!AppUser::where('id', $appUserId)->value('id')) {
            return [false, [], '用戶不存在或已刪除'];
        }
        $cacheFollowingOfMine = "app_user:{$appUserId}:following";
        return [true, self::buildList($cacheFollowingOfMine, $page, $limit, $currentUserId), '獲取成功'];
    }

    public static function getFansList($search_data = array(), $currentUserId = 0)
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $appUserId = $search_data['user_id'];
        self::ensureInitRedis($appUserId);
        if (!AppUser::where('id', $appUserId)->value('id')) {
            return [false, [], '用戶不存在或已刪除'];
        }
        $cacheFollowersOfMine = "app_user:{$appUserId}:followers";
        return [true, self::buildList($cacheFollowersOfMine, $page, $limit, $currentUserId), '獲取成功'];
    }

    protected static function buildList($redisKey, $page, $limit, $currentUserId = 0)
    {
        $totalCount = redis()->zCard($redisKey);
        $startIndex = max(0, $totalCount - ($page * $limit));
        $endIndex = min($totalCount, $startIndex + $limit);

        $userIds = redis()->zRevRange($redisKey, $startIndex, $endIndex);
        //todo redis存储用户基本信息
        $users = !$userIds ? [] : AppUser::select(['id', 'username', 'avatar','uid'])->whereIn('id', $userIds)->get()->toArray();
        $users = array_column($users, null, 'id');

        $list = [];
        foreach ($userIds as $id) {
            $userInfo = $users[$id] ?? [];
            if (empty($userInfo)) {
                $userInfo = ['id' => 0, 'username' => '已注销', 'avatar' => null];
            } else {
                // 添加“是否被关注”字段
                if ($currentUserId > 0 && $id != $currentUserId) {
                    $userInfo['is_followed'] = self::isFollowing($currentUserId, $id);
                } else {
                    $userInfo['is_followed'] = false;
                }
            }
            $list[] = $userInfo;
        }

        return [
            'total'        => $totalCount,
            'per_page'     => $limit,
            'current_page' => $page,
            'last_page'    => max((int)ceil($totalCount / $limit), 1),
            'data'         => $list,
        ];
    }

    /**
     * 获取我关注的用户ID列表
     */
    public static function getMyFollowingUserIds($appUserId)
    {
        self::ensureInitRedis($appUserId);
        $cacheFollowingOfMine = "app_user:{$appUserId}:following";
        return redis()->zRange($cacheFollowingOfMine, 0, -1);
    }

    /**
     * 获取我关注的用户ID列表（排除家人）
     * 用于朋友动态筛选，确保家人不出现在朋友列表中
     */
    public static function getMyFollowingUserIdsExcludeFamily($appUserId)
    {
        // 获取所有关注的用户ID
        $followingUserIds = self::getMyFollowingUserIds($appUserId);

        if (empty($followingUserIds)) {
            return [];
        }

        // 获取家人用户ID
        $familyUserIds = PetMaster::getMyFamilyUserIds($appUserId);

        if (empty($familyUserIds)) {
            return $followingUserIds;
        }

        // 排除家人，只返回非家人的关注用户
        return array_values(array_diff($followingUserIds, $familyUserIds));
    }
}
