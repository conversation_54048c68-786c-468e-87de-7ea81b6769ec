<?php

namespace App\Listeners;

use App\Events\ConsultBookWillStartEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultBookWillStartNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultBookWillStartEvent $event
     * @return void
     */
    public function handle(ConsultBookWillStartEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_CONSULT_BOOK_WILL_START,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定用户
            $channel = GoEasyServiceProvider::getChannelForUser($consult->user_id);
            $notification = ConsultNotificationService::getConsultBookWillStartMessage();
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to user failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            //推送给指定营养师
            $channel = GoEasyServiceProvider::getChannelForDietitian($consult->dietitian_id);
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            Log::info('Consult book will start notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult book will start notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
