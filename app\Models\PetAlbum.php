<?php

namespace App\Models;

use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use DateTimeInterface;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetAlbum extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_albums';

    protected $fillable = [
        'user_id',
        'pet_ids',
        'images',
        'description',

        'source_type',
        'source_id',
        'status',
    ];

    protected $casts = [
        'images' => 'array',
        'status' => 'integer',
    ];

    // pet_ids 不使用自动转换，手动处理以确保字符串格式

    /**
     * 自定义JSON编码，避免斜杠转义
     */
    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    // 注意：合并表设计中，宠物关联通过pet_ids JSON字段处理，不再使用Eloquent关联

    /**
     * 获取相册列表（合并表结构）
     */
    public static function getList($formData, $requestAppUserId = null)
    {
        $page = $formData['page'] ?? 1;
        $limit = $formData['per_page'] ?? 20;
        $petId = $formData['pet_id'] ?? null;
        $isNoPetIds = $formData['is_no_pet_ids'] ?? null; // 1=筛选没有宠物ID的相册
        $isPostSource = $formData['is_post_source'] ?? null; // 0=非帖子来源, 1=帖子来源
        $userid = $formData['userid'] ?? null; // 按用户ID筛选相册
        $familyUserIds = $formData['family_user_ids'] ?? null; // 按家人用户ID筛选相册

        $showGeneralOnly = $formData['show_general_only'] ?? false;
        $strictFilter = $formData['strict_filter'] ?? false; // 严格筛选模式，不包含通用相册

        $query = self::query()
            ->where('status', 1)
            ->when($showGeneralOnly, function (Builder $query) {
                // 只查看通用相册（pet_ids为空）
                $query->where(function ($q) {
                    $q->whereNull('pet_ids')
                        ->orWhere('pet_ids', '')
                        ->orWhere('pet_ids', '0');
                });
            })
            ->when(!empty($petId) && !$showGeneralOnly, function (Builder $query) use ($petId, $strictFilter) {
                if (is_array($petId)) {
                    // 多个宠物ID查询 - 适配字符串格式
                    $query->where(function ($q) use ($petId, $strictFilter) {
                        $hasValidPetId = false;
                        foreach ($petId as $id) {
                            if (!empty($id) && is_numeric($id)) {
                                // 使用FIND_IN_SET查询字符串格式的pet_ids
                                $q->orWhereRaw("FIND_IN_SET(?, pet_ids)", [$id]);
                                $hasValidPetId = true;
                            }
                        }

                        // 如果不是严格筛选模式，也包含通用相册（pet_ids为空）
                        if (!$strictFilter && $hasValidPetId) {
                            $q->orWhereNull('pet_ids')
                                ->orWhere('pet_ids', '')
                                ->orWhere('pet_ids', '0');
                        }
                    });
                } else {
                    // 单个宠物ID查询 - 适配字符串格式
                    $query->where(function ($q) use ($petId, $strictFilter) {
                        if (!empty($petId) && is_numeric($petId)) {
                            $q->whereRaw("FIND_IN_SET(?, pet_ids)", [$petId]);

                            // 如果不是严格筛选模式，也包含通用相册
                            if (!$strictFilter) {
                                $q->orWhereNull('pet_ids')
                                    ->orWhere('pet_ids', '')
                                    ->orWhere('pet_ids', '0');
                            }
                        }
                    });
                }
            })
            ->when($isNoPetIds == 1, function (Builder $query) {
                // 筛选没有宠物ID的相册记录（pet_ids为空或null）
                $query->where(function ($q) {
                    $q->whereNull('pet_ids')
                        ->orWhere('pet_ids', '')
                        ->orWhere('pet_ids', '0');
                });
            })
            ->when($isPostSource !== null, function (Builder $query) use ($isPostSource) {
                // 根据is_post_source参数筛选：0=非帖子来源, 1=帖子来源
                if ($isPostSource == 1) {
                    // 筛选来源于帖子的相册记录
                    $query->where('source_type', 'post');
                } else {
                    // 筛选非帖子来源的相册记录
                    $query->where(function ($q) {
                        $q->where('source_type', '!=', 'post')
                            ->orWhereNull('source_type');
                    });
                }
            })
            ->when($familyUserIds, function (Builder $query) use ($familyUserIds) {
                // 按家人用户ID筛选相册（包含自己）
                if (!empty($familyUserIds)) {
                    $query->whereIn('user_id', $familyUserIds);
                }
            })
            ->orderBy('created_at', 'desc');

        $result = $query->paginate($limit, ['*'], 'page', $page);

        // 处理返回数据，添加关联的宠物信息、帖子信息和发布者信息
        $result->getCollection()->transform(function ($album) use ($requestAppUserId) {
            if (!empty($album->pet_ids) && trim($album->pet_ids) !== '' && $album->pet_ids !== '0') {
                // 将字符串格式的pet_ids转换为数组，过滤无效值
                $petIdsArray = array_filter(array_map('trim', explode(',', $album->pet_ids)), function ($id) {
                    return !empty($id) && is_numeric($id) && $id > 0;
                });

                if (!empty($petIdsArray)) {
                    $album->pets = Pet::whereIn('id', $petIdsArray)
                        ->select('id', 'name', 'avatar')
                        ->get();
                } else {
                    $album->pets = collect([]);
                }
            } else {
                $album->pets = collect([]);
            }

            // 添加帖子信息（如果来源于帖子）
            if ($album->source_type === 'post' && !empty($album->source_id)) {
                $post = AppContent::where('id', $album->source_id)
                    ->select('id', 'title', 'content', 'post_type', 'created_at', 'visibility')
                    ->first();

                if ($post) {
                    $album->post_info = [
                        'post_id'         => $post->id,
                        'post_title'      => $post->title,
                        'post_content'    => mb_substr($post->content, 0, 100) . (mb_strlen($post->content) > 100 ? '...' : ''),
                        'post_type'       => $post->post_type,
                        'post_created_at' => $post->created_at,
                        'visibility'      => $post->visibility,
                    ];
                } else {
                    $album->post_info = null;
                }
            } else {
                $album->post_info = null;
            }


            // 添加发布者信息
            $publisher = AppUser::where('id', $album->user_id)
                ->select('id', 'username', 'chinese_name', 'english_name', 'avatar')
                ->first();

            $album = $album->toArray();

            // 添加发布者信息到相册数据
            if ($publisher) {
                $album['publisher'] = [
                    'id'           => $publisher->id,
                    'username'     => $publisher->username,
                    'chinese_name' => $publisher->chinese_name,
                    'english_name' => $publisher->english_name,
                    'avatar'       => $publisher->avatar,
                    'display_name' => $publisher->chinese_name ?: $publisher->english_name ?: $publisher->username
                ];

                // 判断是否是家人发布的相册
                if ($requestAppUserId && $publisher->id != $requestAppUserId) {
                    $album['is_family_post'] = PetMaster::isFamily($requestAppUserId, $publisher->id);
                } else {
                    $album['is_family_post'] = false;
                }
            } else {
                $album['publisher'] = null;
                $album['is_family_post'] = false;
            }

            self::renderItem($album, $requestAppUserId);
            return $album;
        });

        return $result;
    }


    //增加访问数量
    public static function incrementView($contentId)
    {
        return self::where('id', $contentId)->increment('view_count', 1);
    }

    //增加点赞内容数量
    public static function incrementLike($contentId)
    {
        return self::where('id', $contentId)->increment('like_count', 1);
    }

    //减少点赞内容数量
    public static function decrementLike($contentId)
    {
        return self::withTrashed()->where('id', $contentId)->decrement('like_count', 1);
    }

    public static function renderItem(&$item, $requestAppUserId)
    {
        //app用户访问，追加收藏状态
        if ($requestAppUserId) {
            self::renderRealtimeDetail($item, $requestAppUserId);
        }
    }

    //todo 宠物相册列表应该需要缓存，缓存的view_count肯定是需要实时读取redis的
    public static function renderRealtimeDetail(&$album, $appUserId)
    {
        if (!$album['id']) {
            return;
        }
        $album['like_count'] = PetAlbumLike::getAlbumLikeCount($album['id']);
        $album['is_like'] = (bool)PetAlbumLike::isLike($appUserId, $album['id']);
    }

    /**
     * 保存多宠物多图片相册记录（严格数据结构控制）
     */
    public static function saveMultiAlbum($formData)
    {
        try {
            // 严格控制数据结构
            $albumData = [
                'user_id'     => $formData['user_id'],
                'description' => $formData['description'] ?? null,
                'category'    => $formData['category'] ?? null,
                'source_type' => $formData['source_type'] ?? null,
                'source_id'   => $formData['source_id'] ?? null,
                'status'      => 1,
                'created_at'  => now(),
                'updated_at'  => now(),
            ];

            // 处理pet_ids：直接使用字符串格式（如"4,5,6"）
            if (!empty($formData['pet_ids'])) {
                if (is_array($formData['pet_ids'])) {
                    // 如果传入的是数组，转换为字符串格式
                    $petIds = array_map('strval', array_unique($formData['pet_ids']));
                    $albumData['pet_ids'] = implode(',', $petIds);
                } else {
                    // 如果传入的是字符串，直接使用
                    $albumData['pet_ids'] = trim($formData['pet_ids']);
                }
            } else {
                // 允许为空，表示通用相册
                $albumData['pet_ids'] = null;
            }

            // 处理images：直接使用数组格式，Laravel会自动转换为JSON
            if (!empty($formData['images']) && is_array($formData['images'])) {
                // 确保所有URL都是字符串
                $imageUrls = array_map('strval', $formData['images']);
                $albumData['images'] = $imageUrls; // 直接使用数组，不手动json_encode
            } else {
                return [false, [], '图片URL不能为空'];
            }

            // 验证必需字段
            if (empty($albumData['user_id'])) {
                return [false, [], '用户ID不能为空'];
            }

            if (empty($albumData['images'])) {
                return [false, [], '至少需要一张图片'];
            }

            // 检查是否是编辑模式
            if (!empty($formData['id'])) {
                // 编辑模式：更新现有记录
                $album = self::find($formData['id']);
                if (!$album) {
                    return [false, [], '要更新的相册记录不存在'];
                }

                // 更新数据
                $album->update($albumData);
                $operationType = '更新';
            } else {
                // 新建模式：创建新记录
                $album = self::create($albumData);
                $operationType = '创建';
            }

            // 计算统计信息
            $petCount = !empty($albumData['pet_ids']) ? count(explode(',', $albumData['pet_ids'])) : 0;
            $imageCount = count($albumData['images']); // 直接计算数组长度

            //添加提醒
            if (empty($formData['id']) && $formData['source_type'] == 'manual') {
                $petIds = explode(',', $albumData['pet_ids']);
                $familyUserIds = PetMaster::whereIn('pet_id', $petIds)->where('user_id', '!=', $formData['user_id'])->pluck('user_id')->toArray();
                $visitedUserIds = AppUserLog::getTodayVisitedUsers($familyUserIds);
                foreach ($visitedUserIds as $familyUserId) {
                    PetReminder::create([
                        'user_id'      => $familyUserId,
                        'pet_id'       => $petCount ? $petIds[0] : 0,
                        'type'         => PetReminder::TYPE_FAMILY_UPLOAD_ALBUM_REMINDER,
                        'title'        => PetReminder::MAPPING[PetReminder::TYPE_FAMILY_UPLOAD_ALBUM_REMINDER]['label'],
                        'content'      => "您的家人上傳了相冊，请查看",
                        'trigger_date' => Carbon::today(),
                        'extra'        => json_encode(['id' => $album->id]),
                    ]);
                }
            }

            $message = $petCount > 0
                ? "{$operationType}成功，{$imageCount}张图片关联到{$petCount}只宠物"
                : "{$operationType}成功，{$imageCount}张图片保存为通用相册";

            return [true, $album, $message];
        } catch (\Exception $e) {
            return [false, [], $e->getMessage()];
        }
    }

    /**
     * 批量编辑相册记录（智能图片分离模式）
     */
    public static function batchEditAlbums($albumsData, $userId)
    {
        try {
            if (empty($albumsData) || !is_array($albumsData)) {
                return [false, [], '请提供要编辑的相册数据'];
            }

            $successCount = 0;
            $failedAlbums = [];
            $updatedAlbums = [];
            $createdAlbums = [];

            foreach ($albumsData as $albumData) {
                if (!isset($albumData['id'])) {
                    $failedAlbums[] = [
                        'id'     => null,
                        'reason' => '缺少相册ID'
                    ];
                    continue;
                }

                $albumId = $albumData['id'];
                $album = self::where('id', $albumId)->first();

                if (!$album) {
                    $failedAlbums[] = [
                        'id'     => $albumId,
                        'reason' => '相册不存在'
                    ];
                    continue;
                }

                if ($album['user_id'] != $userId) {
                    $failedAlbums[] = [
                        'id'     => $albumId,
                        'reason' => '无权修改该相册'
                    ];
                    continue;
                }

                // 获取原相册数据
                $originalImages = $album->images ?? [];
                $originalPetIds = $album->pet_ids;

                // 新的图片和宠物ID
                $newImages = isset($albumData['images']) ? array_map('strval', $albumData['images']) : $originalImages;
                $newPetIds = '';

                if (isset($albumData['pet_ids'])) {
                    if (is_array($albumData['pet_ids'])) {
                        $petIds = array_map('strval', array_unique($albumData['pet_ids']));
                        $newPetIds = implode(',', $petIds);
                    } else {
                        $newPetIds = trim($albumData['pet_ids']);
                    }
                } else {
                    $newPetIds = $originalPetIds;
                }

                // 检查是否需要图片分离
                $needsSeparation = false;
                $remainingImages = [];

                if (isset($albumData['images']) && count($originalImages) > count($newImages)) {
                    // 有图片被移除，需要检查是否需要分离
                    $remainingImages = array_diff($originalImages, $newImages);
                    if (!empty($remainingImages) && $originalPetIds !== $newPetIds) {
                        $needsSeparation = true;
                    }
                }

                if ($needsSeparation && !empty($remainingImages)) {
                    // 智能分离模式：创建新相册保留剩余图片
                    $remainingAlbumData = [
                        'user_id'     => $userId,
                        'pet_ids'     => $originalPetIds, // 保持原来的宠物归属
                        'images'      => array_values($remainingImages),
                        'description' => $album->description,
                        'category'    => $album->category,
                        'source_type' => $album->source_type,
                        'source_id'   => $album->source_id,
                        'status'      => 1,
                    ];

                    $remainingAlbum = self::create($remainingAlbumData);
                    $createdAlbums[] = $remainingAlbum;

                    // 更新原相册为新的图片和宠物归属
                    $updateData = [
                        'images'     => $newImages,
                        'pet_ids'    => $newPetIds,
                        'updated_at' => now(),
                    ];

                    if (isset($albumData['description'])) {
                        $updateData['description'] = $albumData['description'];
                    }
                    if (isset($albumData['category'])) {
                        $updateData['category'] = $albumData['category'];
                    }

                    $album->update($updateData);
                    $updatedAlbums[] = $album->fresh();
                    $successCount++;

                } else {
                    // 常规更新模式
                    $updateData = [];

                    // 更新图片URL列表
                    if (isset($albumData['images'])) {
                        if (empty($albumData['images']) || !is_array($albumData['images'])) {
                            $failedAlbums[] = [
                                'id'     => $albumId,
                                'reason' => '至少需要一张图片'
                            ];
                            continue;
                        }
                        $updateData['images'] = $newImages;
                    }

                    // 更新关联的宠物ID
                    if (isset($albumData['pet_ids'])) {
                        $updateData['pet_ids'] = $newPetIds;
                    }

                    // 更新描述
                    if (isset($albumData['description'])) {
                        $updateData['description'] = $albumData['description'];
                    }
                    // 更新分类
                    if (isset($albumData['category'])) {
                        $updateData['category'] = $albumData['category'];
                    }

                    if (!empty($updateData)) {
                        $updateData['updated_at'] = now();
                        $album->update($updateData);
                        $updatedAlbums[] = $album->fresh();
                        $successCount++;
                    } else {
                        $failedAlbums[] = [
                            'id'     => $albumId,
                            'reason' => '没有需要更新的数据'
                        ];
                    }
                }
            }

            $message = "批量编辑完成：成功 {$successCount} 条";
            if (!empty($createdAlbums)) {
                $message .= "，智能分离创建 " . count($createdAlbums) . " 条新相册";
            }
            if (!empty($failedAlbums)) {
                $message .= "，失败 " . count($failedAlbums) . " 条";
            }

            return [
                true,
                [
                    'updated_albums' => $updatedAlbums,
                    'created_albums' => $createdAlbums,
                    'failed_albums'  => $failedAlbums,
                    'success_count'  => $successCount,
                    'created_count'  => count($createdAlbums),
                    'failed_count'   => count($failedAlbums)
                ],
                $message
            ];
        } catch (\Exception $e) {
            return [false, [], $e->getMessage()];
        }
    }


    /**
     * 删除相册图片
     */
    public static function deleteAlbum($id, $userId)
    {
        DB::commit();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "相册：{$id}不存在或已删除");
            $info['user_id'] == $userId or throw_if(true, 'RuntimeException', "您没有权限删除该相册");

            // 如果有关联的图片资源，减少引用计数
            if ($info->pet_image_id && $info->petImage) {
                $info->petImage->decrementReference();
            }

            $success = $info->where('id', $id)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if ($success) {
                $success = true;
                $message = '删除成功';
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }


    //判断是否发布过相册
    public static function getPostedPetIds($userId, $petIds, $days = 0)
    {
        $list = self::where('user_id', $userId)
            ->when($petIds !== "", function (Builder $query) use ($petIds) {
                $query->where(function ($query) use ($petIds) {
                    foreach ($petIds as $id) {
                        $query->orWhereRaw("FIND_IN_SET(?, pet_ids)", [$id]);
                    }
                });
            })
            ->when($days, function (Builder $query) use ($days) {
                $query->where('created_at', '>', Carbon::today()->subDays($days));
            })
            ->distinct()->pluck('pet_ids')->toArray();
        $allPetIds = [];
        foreach ($list as $v) {
            $allPetIds = array_merge($allPetIds, explode(',', $v));
        }
        return array_unique($allPetIds);
    }

}
