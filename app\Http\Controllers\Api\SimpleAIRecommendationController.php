<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Helpers\ResponseHelper;
use App\Models\ChatSession;
use App\Services\SimpleAIRecommendationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SimpleAIRecommendationController extends Controller
{
    protected $recommendationService;

    public function __construct(SimpleAIRecommendationService $recommendationService)
    {
        $this->recommendationService = $recommendationService;
    }

    /**
     * 根据session_id获取AI分析结果的商品推荐
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecommendationsBySession(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|max:100',
            'limit' => 'nullable|integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            return ResponseHelper::success([
                'recommendations' => [],
                'total_count' => 0,
                'message_tc' => '參數驗證失敗，請檢查輸入參數'
            ], '參數驗證失敗');
        }

        $sessionId = $request->input('session_id');
        $limit = $request->input('limit', 5);

        try {
            // 查找对应的AI分析会话记录
            $chatSession = ChatSession::where('session_id', $sessionId)
                ->whereIn('analysis_type', ['food', 'health', 'stool', 'ingredient'])
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$chatSession) {
                return ResponseHelper::success([
                    'recommendations' => [],
                    'total_count' => 0,
                    'session_id' => $sessionId,
                    'message_tc' => '未找到對應的AI分析記錄，暫無相關商品推薦'
                ], '未找到對應的AI分析記錄');
            }

            // 获取最新的分析内容
            $analysisContent = $this->getLatestAnalysisContent($chatSession);

            if (empty($analysisContent)) {
                return ResponseHelper::success([
                    'recommendations' => [],
                    'message_tc' => '暫無相關商品推薦',
                    'analysis_type' => $chatSession->analysis_type,
                    'session_id' => $sessionId,
                    'total_count' => 0
                ], '分析內容為空');
            }

            // 生成商品推荐
            $recommendationResult = $this->recommendationService->generateRecommendations(
                $analysisContent,
                $chatSession->analysis_type,
                $chatSession->pet_ids ? (is_array($chatSession->pet_ids) ? $chatSession->pet_ids[0] : $chatSession->pet_ids) : null,
                $limit
            );

            if ($recommendationResult['status'] === 'success') {
                $recommendations = $recommendationResult['recommendations'];

                return ResponseHelper::success([
                    'recommendations' => $recommendations,
                    'total_count' => count($recommendations),
                    'analysis_type' => $chatSession->analysis_type,
                    'session_id' => $sessionId,
                    'matched_keywords' => $recommendationResult['matched_keywords'] ?? [],
                    'message_tc' => count($recommendations) > 0
                        ? '根據AI分析結果，為您推薦以下商品：'
                        : '暫無相關商品推薦，建議諮詢專業獸醫'
                ], '推薦生成成功');
            } else {
                return ResponseHelper::success([
                    'recommendations' => [],
                    'total_count' => 0,
                    'session_id' => $sessionId,
                    'analysis_type' => $chatSession->analysis_type,
                    'message_tc' => '推薦系統暫時不可用，請稍後再試'
                ], '推薦生成失敗');
            }

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('AI推荐接口错误: ' . $e->getMessage());

            return ResponseHelper::success([
                'recommendations' => [],
                'total_count' => 0,
                'session_id' => $data['session_id'] ?? '',
                'message_tc' => '系統暫時不可用，請稍後再試'
            ], '系統錯誤');
        }
    }



    /**
     * 获取最新的分析内容
     *
     * @param ChatSession $chatSession
     * @return string
     */
    private function getLatestAnalysisContent(ChatSession $chatSession): string
    {
        // 获取会话的最新消息内容
        $messages = $chatSession->messages()
            ->where('role', 'assistant')
            ->orderBy('created_at', 'desc')
            ->first();

        return $messages ? $messages->content : '';
    }
}
