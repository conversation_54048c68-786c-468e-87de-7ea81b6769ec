<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id', 64)->unique()->comment('会话唯一标识');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->unsignedBigInteger('pet_id')->nullable()->comment('宠物ID，可为空');
            $table->string('title', 255)->nullable()->comment('会话标题');
            $table->tinyInteger('analysis_type')->default(0)->comment('分析类型：0=普通聊天，1=食物分析，2=排泄物分析，3=健康分析');
            $table->tinyInteger('status')->default(1)->comment('状态：0=已删除，1=正常');
            $table->timestamp('last_message_at')->nullable()->comment('最后一条消息时间');
            $table->timestamps();
            $table->softDeletes();

            $table->index('user_id');
            $table->index('pet_id');
            $table->index(['user_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_sessions');
    }
};
