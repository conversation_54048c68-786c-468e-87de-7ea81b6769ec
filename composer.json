{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "ext-bcmath": "*", "ext-curl": "*", "ext-intl": "*", "ext-json": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-redis": "*", "alibabacloud/dysmsapi-20180501": "^1.0", "aliyuncs/oss-sdk-php": "^2.6", "aws/aws-sdk-php": "^3.303", "firebase/php-jwt": "^6.11", "getuilaboratory/getui-pushapi-php-client-v2": "dev-master", "guzzlehttp/guzzle": "^7.2", "hightman/xunsearch": "^1.4", "laravel/framework": "^9.21", "laravel/passport": "^11.8", "laravel/sanctum": "^3.0", "laravel/scout": "^10.11", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "predis/predis": "^2.2", "simplesoftwareio/simple-qrcode": "~4.2.0"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.15", "doctrine/dbal": " ~2.3", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "Illuminate\\Foundation\\ComposerScripts::postUpdate", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["barryvdh/laravel-ide-helper"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}