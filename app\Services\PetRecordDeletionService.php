<?php

namespace App\Services;

use App\Models\Pet;
use App\Models\PetCertificate;
use App\Models\PetVaccineRecord;
use App\Models\PetMedicalRecord;
use Illuminate\Support\Facades\DB;

/**
 * 宠物记录删除权限服务
 * 统一处理疫苗记录、出生证明、晶片号码、医疗档案的删除权限控制
 */
class PetRecordDeletionService
{

    /**
     * 检查疫苗针卡是否可以删除（通过证件ID）
     * 规则：疫苗针卡审核通过后不可删除，审核未通过可以删除
     *
     * @param int $certificateId 证件ID
     * @param int $userId        用户ID
     * @return array ['can_delete' => bool, 'message' => string, 'certificate' => object|null, 'pet' => object|null]
     */
    public function canDeleteVaccineCardById(int $certificateId, int $userId): array
    {
        // 获取疫苗针卡证件
        $vaccineCard = PetCertificate::with('pet')
            ->where('id', $certificateId)
            ->where('certificate_type', PetCertificate::TYPE_VACCINE_CARD)
            ->first();

        if (!$vaccineCard) {
            return [
                'can_delete'  => false,
                'message'     => '疫苗針卡不存在',
                'certificate' => null,
                'pet'         => null
            ];
        }

        // 检查宠物编辑权限
        if (!Pet::hasEditPermission($vaccineCard->pet->id, $userId)) {
            return [
                'can_delete'  => false,
                'message'     => '沒有刪除權限',
                'certificate' => $vaccineCard,
                'pet'         => $vaccineCard->pet
            ];
        }

        // 检查审核状态
        if ($vaccineCard->status === PetCertificate::STATUS_APPROVED) {
            return [
                'can_delete'  => false,
                'message'     => '疫苗針卡已審核通過，無法刪除',
                'certificate' => $vaccineCard,
                'pet'         => $vaccineCard->pet
            ];
        }

        return [
            'can_delete'  => true,
            'message'     => '可以刪除',
            'certificate' => $vaccineCard,
            'pet'         => $vaccineCard->pet
        ];
    }

    /**
     * 检查医疗档案是否可以删除
     * 规则：医疗档案无论审核状态都可以删除
     *
     * @param int $medicalRecordId 医疗档案ID
     * @param int $userId          用户ID
     * @return array ['can_delete' => bool, 'message' => string, 'record' => object|null]
     */
    public function canDeleteMedicalRecord(int $medicalRecordId, int $userId): array
    {
        // 获取医疗档案
        $medicalRecord = PetMedicalRecord::with('pet')->find($medicalRecordId);
        if (!$medicalRecord) {
            return [
                'can_delete' => false,
                'message'    => '醫療檔案不存在',
                'record'     => null
            ];
        }

        // 检查宠物编辑权限
        if (!Pet::hasEditPermission($medicalRecord->pet->id, $userId)) {
            return [
                'can_delete' => false,
                'message'    => '沒有刪除權限',
                'record'     => $medicalRecord
            ];
        }

        // 医疗档案无论审核状态都可以删除
        return [
            'can_delete' => true,
            'message'    => '可以刪除',
            'record'     => $medicalRecord
        ];
    }

    /**
     * 检查出生证明是否可以删除
     * 规则：出生证明审核通过后不可删除，审核未通过可以删除
     *
     * @param int $petId  宠物ID
     * @param int $userId 用户ID
     * @return array ['can_delete' => bool, 'message' => string, 'certificate' => object|null, 'pet' => object|null]
     */
    public function canDeleteBirthCertificate(int $petId, int $userId): array
    {
        // 获取宠物信息
        $pet = Pet::find($petId);
        if (!$pet) {
            return [
                'can_delete'  => false,
                'message'     => '寵物不存在',
                'certificate' => null,
                'pet'         => null
            ];
        }

        // 检查宠物编辑权限
        if (!Pet::hasEditPermission($petId, $userId)) {
            return [
                'can_delete'  => false,
                'message'     => '沒有刪除權限',
                'certificate' => null,
                'pet'         => null
            ];
        }

        // 获取出生证明
        $birthCertificate = PetCertificate::where('pet_id', $petId)
            ->where('certificate_type', PetCertificate::TYPE_BIRTH_CERTIFICATE)
            ->latest()
            ->first();

        if (!$birthCertificate) {
            return [
                'can_delete'  => false,
                'message'     => '出生證明不存在',
                'certificate' => null,
                'pet'         => $pet
            ];
        }

        // 检查审核状态
        if ($birthCertificate->status === PetCertificate::STATUS_APPROVED) {
            return [
                'can_delete'  => false,
                'message'     => '出生證明已審核通過，無法刪除',
                'certificate' => $birthCertificate,
                'pet'         => $pet
            ];
        }

        return [
            'can_delete'  => true,
            'message'     => '可以刪除',
            'certificate' => $birthCertificate,
            'pet'         => $pet
        ];
    }

    /**
     * 检查晶片编码是否可以删除
     * 规则：晶片编码审核通过后不可删除，审核未通过可以删除
     *
     * @param int $petId  宠物ID
     * @param int $userId 用户ID
     * @return array ['can_delete' => bool, 'message' => string, 'certificate' => object|null, 'pet' => object|null]
     */
    public function canDeleteChipCode(int $petId, int $userId): array
    {
        // 获取宠物信息
        $pet = Pet::find($petId);
        if (!$pet) {
            return [
                'can_delete'  => false,
                'message'     => '寵物不存在',
                'certificate' => null,
                'pet'         => null
            ];
        }

        // 检查宠物编辑权限
        if (!Pet::hasEditPermission($petId, $userId)) {
            return [
                'can_delete'  => false,
                'message'     => '沒有刪除權限',
                'certificate' => null,
                'pet'         => null
            ];
        }

        // 获取晶片编码证件
        $chipCode = PetCertificate::where('pet_id', $petId)
            ->where('certificate_type', PetCertificate::TYPE_CHIP_CODE)
            ->latest()
            ->first();

        if (!$chipCode) {
            return [
                'can_delete'  => false,
                'message'     => '晶片編碼不存在',
                'certificate' => null,
                'pet'         => $pet
            ];
        }

        // 检查审核状态
        if ($chipCode->status === PetCertificate::STATUS_APPROVED) {
            return [
                'can_delete'  => false,
                'message'     => '晶片編碼已審核通過，無法刪除',
                'certificate' => $chipCode,
                'pet'         => $pet
            ];
        }

        return [
            'can_delete'  => true,
            'message'     => '可以刪除',
            'certificate' => $chipCode,
            'pet'         => $pet
        ];
    }


    /**
     * 删除疫苗针卡
     * 删除疫苗针卡时同时删除该针卡下的所有疫苗记录
     *
     * @param PetCertificate $vaccineCard
     * @return array ['success' => bool, 'message' => string]
     */
    public function deleteVaccineCard(PetCertificate $vaccineCard): array
    {
        try {
            DB::transaction(function () use ($vaccineCard) {
                // 删除该宠物的所有疫苗记录
                $deletedRecordsCount = PetVaccineRecord::where('pet_id', $vaccineCard->pet_id)->count();
                PetVaccineRecord::where('pet_id', $vaccineCard->pet_id)->delete();

                // 删除疫苗针卡证件
                $vaccineCard->delete();

                // 记录删除日志
                \Log::info('疫苗针卡删除成功', [
                    'certificate_id'          => $vaccineCard->id,
                    'pet_id'                  => $vaccineCard->pet_id,
                    'deleted_vaccine_records' => $deletedRecordsCount
                ]);
            });

            return [
                'success' => true,
                'message' => '疫苗針卡及相關疫苗記錄刪除成功'
            ];
        } catch (\Exception $e) {
            \Log::error('疫苗针卡删除失败', [
                'certificate_id' => $vaccineCard->id,
                'pet_id'         => $vaccineCard->pet_id,
                'error'          => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '刪除失敗：' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除医疗档案
     *
     * @param PetMedicalRecord $medicalRecord
     * @return array ['success' => bool, 'message' => string]
     */
    public function deleteMedicalRecord(PetMedicalRecord $medicalRecord): array
    {
        try {
            $medicalRecord->delete();
            return [
                'success' => true,
                'message' => '醫療檔案刪除成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '刪除失敗：' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除出生证明
     *
     * @param PetCertificate $birthCertificate
     * @return array ['success' => bool, 'message' => string]
     */
    public function deleteBirthCertificate(PetCertificate $birthCertificate): array
    {
        try {
            $birthCertificate->delete();
            return [
                'success' => true,
                'message' => '出生證明刪除成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '刪除失敗：' . $e->getMessage()
            ];
        }
    }

    /**
     * 删除晶片编码
     *
     * @param PetCertificate $chipCode
     * @param Pet            $pet
     * @return array ['success' => bool, 'message' => string]
     */
    public function deleteChipCode(PetCertificate $chipCode, Pet $pet): array
    {
        try {
            DB::transaction(function () use ($chipCode, $pet) {
                // 删除证件记录
                $chipCode->delete();

                // 清空宠物表中的晶片相关字段
                $pet->update([
                    'chip_code'         => null,
                    'chip_implant_date' => null,
                    'chip_code_status'  => null
                ]);
            });

            return [
                'success' => true,
                'message' => '晶片編碼刪除成功'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '刪除失敗：' . $e->getMessage()
            ];
        }
    }


}
