<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\NotificationLog;
use Illuminate\Http\Request;

class NotificationLogController extends Controller
{
    /**
     * 获取通知列表
     */
    public function getList(Request $request)
    {
        $validated = $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
            'type' => 'nullable|string'
        ]);

        $result = NotificationLog::getList(
            $request->user()->id,
            $validated['type'] ?? null,
            $validated['page'] ?? 1,
            $validated['per_page'] ?? 20
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount(Request $request)
    {
        $result = NotificationLog::getUnreadCount($request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 标记消息已读
     */
    public function markRead(Request $request)
    {
        $validated = $request->validate([
            'notification_id' => 'nullable|integer',  // 为空则标记所有消息已读
            'type' => 'nullable|string'  // 可选择按类型标记已读
        ]);

        $result = NotificationLog::markRead(
            $request->user()->id,
            $validated['notification_id'] ?? null,
            $validated['type'] ?? null
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 删除通知
     */
    public function delete(Request $request)
    {
        $validated = $request->validate([
            'notification_id' => 'required|integer'
        ]);

        $result = NotificationLog::deleteOne(
            $request->user()->id,
            $validated['notification_id']
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 获取通知详情
     */
    public function getDetail(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|integer|min:1'
        ]);

        $result = NotificationLog::getDetail($request->user()->id, $validated['id']);
        return ResponseHelper::result(...$result);
    }
}
