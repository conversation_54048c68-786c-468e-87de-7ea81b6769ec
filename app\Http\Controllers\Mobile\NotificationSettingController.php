<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\NotificationSetting;
use Illuminate\Http\Request;

class NotificationSettingController extends Controller
{
    /**
     * 获取用户的通知设置
     */
    public function getSettings(Request $request)
    {
        $result = NotificationSetting::getUserSettings($request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 更新用户的通知设置
     */
    public function updateSettings(Request $request)
    {
        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'boolean'
        ]);

        $result = NotificationSetting::updateSettings(
            $request->user()->id,
            $validated['settings']
        );

        return ResponseHelper::result(...$result);
    }
}
