<?php

namespace App\Services\PetCalculator;

class DogEnergyDemand extends EnergyDemand
{
    //计算基础能量需求
    protected function getBaseEnergyDemand(): int
    {
        $diff = $this->getBirthDiff();
        if ($diff->y <= 2) {
            $result = $this->getInfancy();
        } elseif ($diff->y <= 7) {
            $result = $this->getAdulthood();
        } elseif ($diff->y <= 10) {
            $result = $this->getMaturityPeriod();
        } else {
            $result = $this->getOldAge();
        }
        return intval($result);
    }

    //幼年期（0-24 個月）
    protected function getInfancy()
    {
        //公式：ME = 130 × BWa^0.75 × 3.2 × [e^(-0.87p) – 0.1]  p=BWa/BWm
        return 130 * pow($this->pet['weight'], 0.75) * 3.2 * (exp(-0.87 * ($this->pet['weight'] / $this->getWeightMax())) - 0.1);
    }

    //成年期（2-7 歲）
    protected function getAdulthood()
    {
        if ($this->pet['active_status'] == 2) {
            //活躍犬：180 × BWa^0.75
            return 180 * pow($this->pet['weight'], 0.75);
        } else {
            //平均活躍寵物犬：130 × BWa^0.75
            return 130 * pow($this->pet['weight'], 0.75);
        }
    }

    //熟年期（7-10 歲）
    protected function getMaturityPeriod()
    {
        //活躍老犬：105 × Bwa^0.75
        return 105 * pow($this->pet['weight'], 0.75);
    }

    //老年期（10 歲以上）
    protected function getOldAge()
    {
        //不活躍犬：95 × Bwa^0.75
        return 95 * pow($this->pet['weight'], 0.75);
    }
}
