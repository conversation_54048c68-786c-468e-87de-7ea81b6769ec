<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * 分享内容标签关联模型
 * @package App\Models
 */
class AppContentTag extends Model
{
    use HasFactory;

    protected $table = 'app_contents_tags';

    protected $fillable = [
        'app_content_id',
        'app_share_tag_id',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 关联分享内容
     */
    public function appContent()
    {
        return $this->belongsTo(AppContent::class, 'app_content_id');
    }

    /**
     * 关联标签
     */
    public function shareTag()
    {
        return $this->belongsTo(AppShareTag::class, 'app_share_tag_id');
    }

    /**
     * 批量添加内容标签关联
     */
    public static function addContentTags($contentId, $tagIds)
    {
        if (empty($tagIds)) {
            return true;
        }

        $data = [];
        foreach ($tagIds as $tagId) {
            $data[] = [
                'app_content_id' => $contentId,
                'app_share_tag_id' => $tagId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // 先删除旧的关联
        self::where('app_content_id', $contentId)->delete();
        
        // 插入新的关联
        self::insert($data);

        // 更新标签使用次数
        foreach ($tagIds as $tagId) {
            AppShareTag::incrementUseCount($tagId);
        }

        return true;
    }

    /**
     * 删除内容的所有标签关联
     */
    public static function removeContentTags($contentId)
    {
        $tags = self::where('app_content_id', $contentId)->get();
        
        foreach ($tags as $tag) {
            AppShareTag::decrementUseCount($tag->app_share_tag_id);
        }

        return self::where('app_content_id', $contentId)->delete();
    }

    /**
     * 获取内容的标签
     */
    public static function getContentTags($contentId)
    {
        return self::with('shareTag')
            ->where('app_content_id', $contentId)
            ->get()
            ->pluck('shareTag')
            ->filter();
    }

    /**
     * 根据标签获取相关内容
     */
    public static function getContentsByTags($tagIds, $excludeContentId = null, $limit = 10)
    {
        // 获取包含指定标签的内容ID
        $contentIds = self::whereIn('app_share_tag_id', $tagIds)
            ->when($excludeContentId, function($query, $excludeContentId) {
                return $query->where('app_content_id', '!=', $excludeContentId);
            })
            ->groupBy('app_content_id')
            ->pluck('app_content_id');

        // 直接查询分享内容
        return AppContent::whereIn('id', $contentIds)
            ->where('status', 1)
            ->with([
                'user:id,username,avatar',
                'pets:id,name,avatar',
                'tags' => function ($query) {
                    $query->select('app_share_tags.id', 'app_share_tags.name', 'app_share_tags.color');
                }
            ])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
