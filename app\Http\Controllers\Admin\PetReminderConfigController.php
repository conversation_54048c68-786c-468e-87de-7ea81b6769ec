<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\PetReminder;
use App\Models\PetReminderConfig;
use Illuminate\Http\Request;

class PetReminderConfigController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'        => 'sometimes|integer|gt:0',
            'per_page'    => 'sometimes|integer|between:1,200',
            'keyword'     => 'nullable|max:255',
            'sort_name'   => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'     => 'nullable|in:asc,desc',
            'type'        => 'nullable|in:' . implode(',', array_keys(PetReminder::MAPPING)),
            'is_tutorial' => 'nullable|in:0,1',
        ]);
        $formData = $request->all();
        $records = PetReminderConfig::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(PetReminderConfig::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'            => 'sometimes|integer|gte:0',
            'type'          => 'required|in:' . implode(',', array_keys(PetReminder::MAPPING)),
            //messages必须是json数组格式，且每一项元素必须是字符串
            'messages'      => 'required|array',
            'messages.*'    => 'required|string',
            'messages_tc'   => 'required|array',
            'messages_tc.*' => 'required|string',
            'messages_en'   => 'required|array',
            'messages_en.*' => 'required|string',
            'is_tutorial'   => 'required|in:0,1',
            'details'       => 'required_if:is_tutorial,1',
            'details_tc'    => 'required_if:is_tutorial,1',
            'details_en'    => 'required_if:is_tutorial,1',
            'sort_order'    => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        //messages长度必须相同
        $formData['messages'] = $request->input('messages', []);
        $formData['messages_tc'] = $request->input('messages_tc', []);
        $formData['messages_en'] = $request->input('messages_en', []);
        if (count($formData['messages']) != count($formData['messages_tc'])
            || count($formData['messages']) != count($formData['messages_en'])
            || count($formData['messages_tc']) != count($formData['messages_en'])) {
            return ResponseHelper::error('messages数量必须相同');
        }
        $result = PetReminderConfig::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = PetReminderConfig::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
