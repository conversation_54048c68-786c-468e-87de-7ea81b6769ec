<?php

namespace App\Providers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class LogSqlProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        Log::channel('sql')->info('请求地址: ' . \Request::instance()->getUri());
        $GLOBALS['sqlTimes'] = 0;
        DB::listen(function ($query) {
            $sql = array_reduce($query->bindings, function ($sql, $binding) {
                return preg_replace('/\?/', is_numeric($binding) ? $binding : sprintf("'%s'", $binding), $sql, 1);
            }, $query->sql);

            $mtime = $query->time / 1000;
            Log::channel('sql')->info('[' . $mtime . 's] ' . $sql);
            $GLOBALS['sqlTimes'] += $mtime;
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
