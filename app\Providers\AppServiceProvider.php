<?php

namespace App\Providers;

use App\Console\Scheduling\CaptureCommandName;
use App\Helpers\GoEasy;
use Illuminate\Console\Events\CommandStarting;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        if ($this->app->isLocal()) {
            $this->app->register(\Barryvdh\LaravelIdeHelper\IdeHelperServiceProvider::class);
            $this->app->register(LogSqlProvider::class);
        }
        $this->app->register(ValidatorExtendProvider::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        defined('LOG_SPLIT_LINE') or define('LOG_SPLIT_LINE', '--------------------------------------------------------------------------------------------------------------------');

        $this->app->bind('exportPath', function ($app, $params) {
            $user = $app->make('request')->user();
            $guardName = $app['auth']->getDefaultDriver();
            $provider = config('auth.guards.' . $guardName . '.provider');
            list($fileName) = $params;
            return getExportPath($user->id, $fileName, $provider);
        });

        //监听调度任务命令名称，方便异常捕获获取名称
        Event::listen(CommandStarting::class, [CaptureCommandName::class, 'handle']);
    }
}
