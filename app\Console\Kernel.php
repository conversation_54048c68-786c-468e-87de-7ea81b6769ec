<?php

namespace App\Console;

use App\Models\HongKongDistrict;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //清除令牌
        $schedule->command('passport:purge')->hourly();
        //订单失效处理
        $schedule->command('order:invalidation')->everyMinute();

        // 宠物提醒相关计划任务
        $schedule->command('pet:birthday-reminders', ['--days' => 14])->daily();
        $schedule->command('pet:birthday-reminders', ['--days' => 7])->daily();
        $schedule->command('pet:birthday-reminders', ['--days' => 3])->daily();
        $schedule->command('pet:birthday-reminders', ['--days' => 0])->daily();
        $schedule->command('pet:vaccine-reminders', ['--days' => 14])->daily();
        $schedule->command('pet:vaccine-reminders', ['--days' => 7])->daily();
        $schedule->command('pet:vaccine-reminders', ['--days' => 3])->daily();
        $schedule->command('pet:vaccine-reminders', ['--days' => 0])->daily();

        // 咨询提醒相关计划任务
        $schedule->command('consult:book-reminders', ['--days' => 2])->daily();
        $schedule->command('consult:book-reminders', ['--days' => 0])->daily();
        $schedule->command('consult:book-will-start')->everyMinute();
        $schedule->command('consult:book-started')->everyMinute();
        $schedule->command('consult:book-no-enter')->everyMinute();
        $schedule->command('consult:reply-timeout')->everyMinute();
        $schedule->command('consult:auto-end')->everyMinute();

        //医疗档案分析
        $schedule->command('analyze:pet_medical_record')->everyMinute();
    }


    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
