<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAnalysisQuickQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('analysis_quick_questions', function (Blueprint $table) {
            $table->id();
            $table->string('analysis_type', 50)->comment('分析类型：food, stool, health');
            $table->string('question', 255)->comment('问题内容');
            $table->string('icon', 255)->nullable()->comment('图标URL');
            $table->string('pet_type', 20)->default('general')->comment('适用宠物类型：general, dog, cat');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->boolean('status')->default(1)->comment('状态：1启用，0禁用');
            $table->timestamps();
            
            $table->index(['analysis_type', 'pet_type', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('analysis_quick_questions');
    }
}
