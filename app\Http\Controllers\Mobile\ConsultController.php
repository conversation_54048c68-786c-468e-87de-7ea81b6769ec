<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\Consult;
use App\Models\ConsultAccessToken;
use App\Models\ConsultOTP;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class ConsultController extends Controller
{
    public function waitingList(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
        ]);
        $formData = $request->all();
        $formData['_waiting'] = 'user';
        $records = Consult::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'book_status'   => 'nullable|in:0,1,2,3',  //预约状态:0=非预约,1=预约中,2=预约成功,3=预约失败
            'status'        => 'nullable|in:1,2,3,4',  //会话状态:1=待接入,2=已接入,3=已结束,4=已取消
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'book_start'    => 'nullable|required_with:book_end|date_format:"Y-m-d H:i:s"',
            'book_end'      => 'nullable|required_with:book_start|date_format:"Y-m-d H:i:s"|after:book_start',
            'pet_id'        => 'nullable|integer|exists:pets,id',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = Consult::getList($formData, $request->user()->id)->toArray();
        $records['status_list'] = Consult::STATUS_TEXT_LIST;
        $records['book_status_list'] = (object)Consult::BOOK_STATUS_TEXT_LIST;
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $record = Consult::getDetail($request->input('id'), ['user_id' => $request->user()->id]);
        return ResponseHelper::success($record);
    }

    public function submitCheck(Request $request)
    {
        $submit_count = Consult::getSubmitCountThisWeek($request->user()->id);
        $can_submit = $submit_count < Consult::SUBMIT_COUNT_PER_WEEK;
        return ResponseHelper::success(['submit_count' => $submit_count, 'submit_max' => Consult::SUBMIT_COUNT_PER_WEEK, 'can_submit' => $can_submit]);
    }

    public function create(Request $request)
    {
        $rules = [
            'pet_id'                              => 'required|integer|exists:pets,id',
            'questionnaire'                       => 'required|array',
            'questionnaire.appetite'              => 'nullable|string|in:poor,normal,strong',
            'questionnaire.food_type'             => 'nullable|string|in:dry,wet,raw,custom',
            'questionnaire.custom_food'           => 'nullable|string|max:500',
            'questionnaire.meal_frequency'        => 'nullable|string|in:free_feeding,1_meal,2_meals,3_plus_meals',
            'questionnaire.daily_amount'          => 'nullable|string|max:500',
            'questionnaire.environmental_allergy' => 'nullable|string|max:500',
            'questionnaire.food_allergy'          => 'nullable|string|max:500',
            'questionnaire.snacks'                => 'nullable|string|in:yes,no',
            'questionnaire.snack_details'         => 'nullable|string|max:500',
            'questionnaire.water_intake'          => 'nullable|string|max:500',
            'questionnaire.fur_image'             => 'nullable|url|pic|trust_host',
            'questionnaire.fur_moisture'          => 'nullable|string|in:dry,normal,wet',
            'questionnaire.fur_density'           => 'nullable|string|in:thick,normal,thin',
            'questionnaire.fur_color'             => 'nullable|string|in:faded,normal',
            'questionnaire.shedding'              => 'nullable|string|in:no,yes',
            'questionnaire.shedding_type'         => 'nullable|required_if:questionnaire.shedding,yes|string|in:seasonal,excessive,chronic',
            'questionnaire.dandruff'              => 'nullable|string|in:no,yes',
            'questionnaire.sleep'                 => 'nullable|string|in:little,normal,excessive',
            'questionnaire.urine_frequency'       => 'nullable|string|in:little,normal,much',
            'questionnaire.urine_color'           => 'nullable|string|in:light_yellow,yellow,dark_yellow,orange,red,clear',
            'questionnaire.stool_frequency'       => 'nullable|string|in:little,normal,much',
            'questionnaire.stool_color'           => 'nullable|string|in:brown,yellow,green,black,red,white',
            'questionnaire.stool_shape'           => 'nullable|string|in:normal,soft,watery,hard,pellets',
            'questionnaire.supplements'           => 'nullable|string|in:no,yes',
            'questionnaire.supplement_detail'     => 'nullable|string|max:500',
            'questionnaire.food_images'           => 'nullable|array',
            'questionnaire.food_images.*'         => 'nullable|url|pic|trust_host',
            'questionnaire.snack_images'          => 'nullable|array',
            'questionnaire.snack_images.*'        => 'nullable|url|pic|trust_host',
            'questionnaire.urine_images'          => 'nullable|array',
            'questionnaire.urine_images.*'        => 'nullable|url|pic|trust_host',
            'questionnaire.stool_images'          => 'nullable|array',
            'questionnaire.stool_images.*'        => 'nullable|url|pic|trust_host',
            'questionnaire.supplement_images'     => 'nullable|array',
            'questionnaire.supplement_images.*'   => 'nullable|url|pic|trust_host',
            'questionnaire.additional_question'   => 'nullable|string|max:1000',
            'is_book'                             => 'required|integer|in:0,1',
            'book_time'                           => 'required_if:is_book,1|date_format:Y-m-d H:i:s',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['questionnaire'] = $request->input('questionnaire');
        $formData['user_id'] = $request->user()->id;
        $result = Consult::createConsult($formData);
        return ResponseHelper::result(...$result);
    }

    public function lineupCount(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        if (!Consult::isOwner($request->input('id'), $request->user()->id)) {
            return ResponseHelper::error(__('common.no_permission'));
        }
        $count = Consult::getLineupCount($request->input('id'), $request->user()->id);
        return ResponseHelper::success(['count' => $count]);
    }

    public function getOTP(Request $request)
    {
        $rules = [
            'consult_id' => 'nullable|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $formData['role'] = 1;
        $result = ConsultOTP::createOTP($formData);
        return ResponseHelper::result(...$result);
    }

    public function getAccessToken(Request $request)
    {
        $rules = [
            'consult_id' => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $formData['role'] = 1;
        $result = ConsultAccessToken::createAccessToken($formData);
        return ResponseHelper::result(...$result);
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::cancel($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function end(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::end($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::del($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }
}
