<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 宠物证件模型
 */
class PetCertificate extends Model
{
    use HasFactory;

    protected $table = 'pets_certificates';

    protected $fillable = [
        'pet_id',
        'certificate_type',
        'image_url',
        'ai_recognition_result',
        'status',
        'admin_remark',
        'admin_id',
        'audited_at'
    ];

    protected $casts = [
        'image_url'             => 'array',
        'ai_recognition_result' => 'array',
        'audited_at'            => 'datetime'
    ];

    // 证件类型常量
    const TYPE_BIRTH_CERTIFICATE = 1; // 出生证明
    const TYPE_VACCINE_CARD = 2;      // 疫苗针卡
    const TYPE_CHIP_CODE = 3;         // 晶片编码
    const TYPE_MEDICAL_RECORD = 4;    // 医疗档案

    // 审核状态常量
    const STATUS_PENDING = 1;  // 待审核
    const STATUS_APPROVED = 2; // 通过
    const STATUS_REJECTED = 3; // 拒绝

    /**
     * 关联宠物
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * 关联审核管理员
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * 获取证件类型名称
     */
    public function getCertificateTypeNameAttribute()
    {
        $types = [
            self::TYPE_BIRTH_CERTIFICATE => '出生证明',
            self::TYPE_VACCINE_CARD      => '疫苗针卡',
            self::TYPE_CHIP_CODE         => '晶片编码',
            self::TYPE_MEDICAL_RECORD    => '医疗档案'
        ];

        return $types[$this->certificate_type] ?? '未知类型';
    }

    /**
     * 获取审核状态名称
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING  => '待审核',
            self::STATUS_APPROVED => '通过',
            self::STATUS_REJECTED => '拒绝'
        ];

        return $statuses[$this->status] ?? '未知状态';
    }

    /**
     * 获取所有证件类型
     */
    public static function getCertificateTypes()
    {
        return [
            self::TYPE_BIRTH_CERTIFICATE => '出生证明',
            self::TYPE_VACCINE_CARD      => '疫苗针卡',
            self::TYPE_CHIP_CODE         => '晶片编码',
            self::TYPE_MEDICAL_RECORD    => '医疗档案'
        ];
    }

    /**
     * 获取所有审核状态
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_PENDING  => '待审核',
            self::STATUS_APPROVED => '通过',
            self::STATUS_REJECTED => '拒绝'
        ];
    }

    /**
     * 作用域：按证件类型筛选
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('certificate_type', $type);
    }

    /**
     * 作用域：按审核状态筛选
     */
    public function scopeOfStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待审核的证件
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 获取图片URL列表（支持向后兼容）
     */
    public function getImageUrlsAttribute()
    {
        $imageUrl = $this->attributes['image_url'] ?? null;

        if (empty($imageUrl)) {
            return [];
        }

        // 如果是JSON数组格式，直接返回
        if (is_array($imageUrl)) {
            return $imageUrl;
        }

        // 如果是字符串，尝试解析JSON
        if (is_string($imageUrl)) {
            $decoded = json_decode($imageUrl, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded;
            }
            // 如果不是JSON，当作单个URL处理
            return [$imageUrl];
        }

        return [];
    }

    /**
     * 获取第一张图片URL（向后兼容）
     */
    public function getFirstImageUrlAttribute()
    {
        $urls = $this->image_urls;
        return !empty($urls) ? $urls[0] : null;
    }

    /**
     * 设置图片URL（支持字符串和数组）
     */
    public function setImageUrlAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['image_url'] = json_encode($value);
        } elseif (is_string($value)) {
            // 检查是否为JSON字符串
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $this->attributes['image_url'] = $value;
            } else {
                // 单个URL转为数组格式
                $this->attributes['image_url'] = json_encode([$value]);
            }
        } else {
            $this->attributes['image_url'] = json_encode([]);
        }
    }

    /**
     * 作用域：已通过的证件
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * 作用域：已拒绝的证件
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    //获取宠物上传的证书
    public static function getPostedCounts($petIds)
    {
        $list = self::whereIn('pet_id', $petIds)->select('pet_id', 'certificate_type')->get();
        $counts = [];
        foreach ($list as $item) {
            $counts[$item->pet_id][] = $item->certificate_type;
        }
        return $counts;
    }
}
