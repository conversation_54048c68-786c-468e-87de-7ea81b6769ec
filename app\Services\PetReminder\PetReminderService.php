<?php

namespace App\Services\PetReminder;

use App\Models\AppContent;
use App\Models\Consult;
use App\Models\Pet;
use App\Models\PetAlbum;
use App\Models\PetCertificate;
use App\Models\PetLog;
use App\Models\PetMaster;
use App\Models\PetReminder;
use App\Models\PetReminderConfig;
use App\Models\PetReminderMemo;
use Carbon\Carbon;

class PetReminderService
{
    private int $userId;
    private int $petId;
    private array $petIds;
    private array $queryPetIds;
    private array $configs;

    const REPEATABLE_TYPES = [
        PetReminder::TYPE_PET_KNOWLEDGE_STAGE_BEHAVIOR,
        PetReminder::TYPE_PET_KNOWLEDGE_STAGE_NUTRITION,
        PetReminder::TYPE_PET_KNOWLEDGE_OTHER,
        PetReminder::TYPE_IDLE_TALK,
    ];

    public function __construct($userId, $petId)
    {
        $this->userId = $userId;
        $this->petId = $petId;
        $this->petIds = array_keys(PetMaster::getPetIds($this->userId));
        $this->queryPetIds = $this->petId ? [$this->petId] : $this->petIds;
        $this->configs = PetReminderConfig::getAll();
        PetReminderMemo::initList($userId);
    }

    public function getReminder()
    {
        $typesGroup = [];
        foreach ($this->configs as $config) {
            $typesGroup[$config['sort_order']][] = $config['type'];
        }
        $last_records = PetReminder::getLastRecords($this->userId, $this->petId);
        foreach ($typesGroup as $types) {
            //先打乱顺序
            shuffle($types);
            //从工具中获取消息
            foreach ($types as $type) {
                if (!$this->checkAvailable($type)) {
                    continue;
                }
                //未生成提醒则直接生成，如果已生成就需要判断是否已读且判断工具是否在循环周期
                $reminder = $last_records[$type] ?? null;
                if (!$reminder || ($reminder['read_at'] && $this->checkNextPeriod($type, $reminder['read_at']))) {
                    $reminder = $this->buildReminder($type);
                }
                if ($reminder && empty($reminder['read_at']) && $this->checkExpire($type, $reminder['created_at'])) {
                    return $reminder;
                }
            }
        }
        return $this->buildReminder(PetReminder::TYPE_IDLE_TALK_END);
    }

    public function checkAvailable($type): bool
    {
        switch ($type) {
            case PetReminder::TYPE_SELF_INTRODUCTION:
            case PetReminder::TYPE_TUTORIAL_ADD_PET:
                return empty($this->petIds);
            case PetReminder::TYPE_TUTORIAL_PET_FOOD_ANALYSIS:
            case PetReminder::TYPE_TUTORIAL_PET_HEALTH_ANALYSIS:
            case PetReminder::TYPE_TUTORIAL_NUTRITION_CONSULTATION:
            case PetReminder::TYPE_TUTORIAL_FAMILY_CIRCLE:
            case PetReminder::TYPE_TUTORIAL_PET_FRIEND_CIRCLE:
            case PetReminder::TYPE_TUTORIAL_DISCOVERY_PAGE:
            case PetReminder::TYPE_TUTORIAL_REMINDER_MEMO:
                return $this->checkRemindTutorial($type);
        }
        if (in_array($type, self::REPEATABLE_TYPES)) {
            return $this->checkRemindRepeatable($type);
        }
        return $this->checkAvailableWithPets($type);
    }

    public function checkAvailableWithPets($type): bool
    {
        //以下类型必须要有宠物，没有宠物则不触发提醒
        if (empty($this->petIds)) {
            return false;
        }
        switch ($type) {
            case PetReminder::TYPE_TUTORIAL_ADD_PARENT:
                return $this->checkRemindAddPetFamily();
            case PetReminder::TYPE_TUTORIAL_ADD_ALBUM:
                return $this->checkRemindPostAlbum();
            case PetReminder::TYPE_TUTORIAL_ADD_CONTENT:
                return $this->checkRemindPostContent();
            case PetReminder::TYPE_TUTORIAL_ADD_FILE:
                return $this->checkRemindAddFile(false);
            case PetReminder::TYPE_TUTORIAL_ADD_WEIGHT:
                return $this->checkRemindAddWeight();
            case PetReminder::TYPE_ADVICE_ADD_ALBUM:
                return $this->checkRemindPostAlbum(10);
            case PetReminder::TYPE_ADVICE_ADD_CONTENT:
                return $this->checkRemindPostContent(10);
            case PetReminder::TYPE_ADVICE_ADD_FILE:
                return $this->checkRemindAddFile(true);
            case PetReminder::TYPE_ADVICE_ADD_WEIGHT:
                return $this->checkRemindUpdateWeight();
            case PetReminder::TYPE_BIRTHDAY_REMINDER:
                return $this->checkRemindPetBirthday();
            case PetReminder::TYPE_PET_CONSULT_REMINDER:
                return $this->checkRemindPetConsult();
            case PetReminder::TYPE_FAMILY_UPLOAD_ALBUM_REMINDER:
            case PetReminder::TYPE_FAMILY_POST_CONTENT_REMINDER:
            case PetReminder::TYPE_FAMILY_UPLOAD_FILE_REMINDER:
            case PetReminder::TYPE_PET_FILE_VACCINE:
            case PetReminder::TYPE_PET_FILE_REPORT:
                return true;  //默认启用，只要有提前生成提醒，并且未读就会显示
        }
        return false;
    }

    //判断已读消息是否进入下一个周期，如果返回true，则会自动生成提醒
    public function checkNextPeriod($type, $read_at): bool
    {
        if (in_array($type, self::REPEATABLE_TYPES)) {
            return true; // checkRemindRepeatable 已经检查过了，如果当天所有消息都发了，这里即使是true也没用，因为messages已经变成空数组了
        }
        // 返回 true 则会自动生成新的提醒
        switch ($type) {
            case PetReminder::TYPE_SELF_INTRODUCTION:
            case PetReminder::TYPE_TUTORIAL_ADD_PET:
            case PetReminder::TYPE_TUTORIAL_ADD_ALBUM:
            case PetReminder::TYPE_TUTORIAL_ADD_FILE:
            case PetReminder::TYPE_TUTORIAL_ADD_WEIGHT:
            case PetReminder::TYPE_TUTORIAL_PET_FOOD_ANALYSIS:
            case PetReminder::TYPE_TUTORIAL_PET_HEALTH_ANALYSIS:
            case PetReminder::TYPE_TUTORIAL_NUTRITION_CONSULTATION:
            case PetReminder::TYPE_TUTORIAL_FAMILY_CIRCLE:
            case PetReminder::TYPE_TUTORIAL_PET_FRIEND_CIRCLE:
            case PetReminder::TYPE_TUTORIAL_DISCOVERY_PAGE:
            case PetReminder::TYPE_TUTORIAL_REMINDER_MEMO:
            case PetReminder::TYPE_IDLE_TALK_END:
                return !Carbon::parse($read_at)->startOfDay()->isSameDay(Carbon::today());
            case PetReminder::TYPE_TUTORIAL_ADD_PARENT:
                return !Carbon::parse($read_at)->startOfDay()->isSameWeek(Carbon::today());
            case PetReminder::TYPE_ADVICE_ADD_ALBUM:
                return Carbon::parse($read_at)->startOfDay()->diffInDays(Carbon::today()) >= 5;
            case PetReminder::TYPE_ADVICE_ADD_FILE:
            case PetReminder::TYPE_ADVICE_ADD_WEIGHT:
                return Carbon::parse($read_at)->startOfDay()->diffInDays(Carbon::today()) >= 7;
            default:
                // 生日、预约、家人上传、疫苗、医疗档案 相关的（即已经有提前生成的提醒），不需要自动生成
                return false;
        }
    }

    //判断未读消息是否过期，false代表过期，跳过显示该提醒
    public function checkExpire($type, $created_at): bool
    {
        switch ($type) {
            case PetReminder::TYPE_PET_FILE_REPORT: //报告有效期60天
                return Carbon::parse($created_at)->startOfDay()->diffInDays(Carbon::today()) <= 60;
            default:
                return true;  //默认未读的消息，永远都会显示
        }
    }

    public function checkRemindAddPetFamily()
    {
        $masters = PetMaster::whereIn('pet_id', $this->queryPetIds)->select('pet_id', 'user_id')->get();
        $allMasterIds = [];
        foreach ($masters as $item) {
            $allMasterIds[$item->pet_id][] = $item->user_id;
        }
        foreach ($this->queryPetIds as $petId) {
            $masterIds = $allMasterIds[$petId] ?? [];
            if (count($masterIds) == 1) {
                $this->petId = $petId;  //需要针对该宠物进行提醒
                return true;
            }
        }
        return false;
    }

    //todo 是否需要判断关联了宠物的才算
    public function checkRemindPostAlbum($days = 0)
    {
        $postedPetIds = PetAlbum::getPostedPetIds($this->userId, $this->queryPetIds, $days);
        foreach ($this->queryPetIds as $petId) {
            if (!in_array($petId, $postedPetIds)) {
                $this->petId = $petId;  //需要针对该宠物进行提醒
                return true;
            }
        }
        return false;
    }

    //todo 是否需要判断关联了宠物的才算
    public function checkRemindPostContent($days = 0)
    {
        $postedPetIds = AppContent::getPostedPetIds($this->userId, $this->queryPetIds, $days);
        foreach ($this->queryPetIds as $petId) {
            if (!in_array($petId, $postedPetIds)) {
                $this->petId = $petId;  //需要针对该宠物进行提醒
                return true;
            }
        }
        return false;
    }

    public function checkRemindAddFile(bool $checkAll)
    {
        static $counts = null;  //避免重复查询
        if (!isset($counts)) {
            $counts = PetCertificate::getPostedCounts($this->queryPetIds);
        }
        if (false === $checkAll) {
            //只检查是否有上传
            foreach ($this->queryPetIds as $petId) {
                if (empty($counts[$petId])) {
                    $this->petId = $petId;  //需要针对该宠物进行提醒
                    return true;
                }
            }
        } else {
            //读取宠物品种
            $petTypes = Pet::whereIn('id', $this->petIds)->select('id', 'type_id')->pluck('type_id', 'id')->toArray();
            //检查所有证书都要上传
            foreach ($this->queryPetIds as $petId) {
                $checkCount = match ($petTypes[$petId]) {
                    1 => 4,         //狗
                    2 => 3,         //猫
                    default => 0,   //其他
                };
                if (count($counts[$petId] ?? []) < $checkCount) {
                    $this->petId = $petId;  //需要针对该宠物进行提醒
                    return true;
                }
            }
        }
        return false;
    }

    public function checkRemindAddWeight()
    {
        $weights = Pet::whereIn('id', $this->queryPetIds)->pluck('weight', 'id')->toArray();
        foreach ($this->queryPetIds as $petId) {
            if (empty($weights[$petId])) {
                $this->petId = $petId;  //需要针对该宠物进行提醒
                return true;
            }
        }
        return false;
    }

    public function checkRemindTutorial($type)
    {
        $configIds = array_column($this->configs, 'id', 'type');
        $configId = $configIds[$type];
        return PetReminderMemo::where('user_id', $this->userId)
            ->where('config_id', $configId)
            ->whereNull('read_at')
            ->exists();
    }

    public function checkRemindUpdateWeight()
    {
        $times = PetLog::getLastRecords($this->queryPetIds);
        foreach ($this->queryPetIds as $petId) {
            if (empty($times[$petId]) || Carbon::parse($times[$petId])->diffInDays(Carbon::today()) >= 60) {
                $this->petId = $petId;  //需要针对该宠物进行提醒
                return true;
            }
        }
        return false;
    }

    public function checkRemindRepeatable($type)
    {
        static $records = null; //避免重複查詢
        if (!isset($records)) {
            $petId = $this->petId;
            $records = PetReminder::where('user_id', $this->userId)
                ->when($petId, function ($query, $petId) {
                    $query->where('pet_id', $petId);
                })
                ->whereIn('type', self::REPEATABLE_TYPES)
                ->where('created_at', '>=', Carbon::today())
                ->get();
        }
        //可重複的提醒，需要查出今天發的所有內容
        //todo 待完善: 比如寵物常識，應該根據當前寵物，讀取寵物pdf
        $messages = $this->configs[$type]['messages'];
        //統計對應$type消息次數
        $messageCount = [];
        foreach ($records as $record) {
            if ($record->type != $type) {
                continue;
            }
            if (isset($messageCount[$record->content])) {
                $messageCount[$record->content]++;
            } else {
                $messageCount[$record->content] = 1;
            }
        }
        //超过次数的就把$messages里面相同内容的删除
        $repeatTimes = $type == PetReminder::TYPE_IDLE_TALK ? 3 : 1;
        $messages = array_filter($messages, function ($content) use ($messageCount, $repeatTimes) {
            return !isset($messageCount[$content]) || $messageCount[$content] < $repeatTimes;
        });
        $this->configs[$type]['messages'] = $messages;
        return !empty($messages);
    }

    public function checkRemindPetBirthday()
    {
        $birthdays = Pet::whereIn('id', $this->queryPetIds)->pluck('birthday', 'id')->toArray();
        //生日超过今天就不提示
        foreach ($this->queryPetIds as $petId) {
            if (!empty($birthdays[$petId])) {
                $dateString = date('m-d', strtotime($birthdays[$petId]));
                $today = Carbon::today();
                $targetDate = Carbon::createFromFormat('m-d', $dateString)->year($today->year)->startOfDay();
                if ($targetDate->lessThanOrEqualTo($today)) {
                    $this->petId = $petId; //需要针对该宠物进行提醒
                    $this->configs[PetReminder::TYPE_BIRTHDAY_REMINDER]['messages'] = [];   //定时任务已经生成，后面不需要再自动生成提醒
                    return true;
                }
            }
        }
        return false;
    }

    public function checkRemindPetConsult()
    {
        $consultPetIds = Consult::where('is_book', 1)
            ->where('book_status', Consult::CONSULT_BOOK_STATUS_SUCCESS)
            ->where('status', Consult::CONSULT_STATUS_WAITING)
            ->whereDate('book_time', '>=', Carbon::today()->format('Y-m-d'))
            ->where('user_id', $this->userId)
            ->whereIn('pet_id', $this->queryPetIds)
            ->where('created_at', '>=', now()->subDays(30))  //只查询一个月内的
            ->pluck('pet_id')->toArray();
        foreach ($this->queryPetIds as $petId) {
            if (in_array($petId, $consultPetIds)) {
                $this->petId = $petId; //需要针对该宠物进行提醒
                $this->configs[PetReminder::TYPE_PET_CONSULT_REMINDER]['messages'] = []; //定时任务/今日咨询队列 已经生成，后面不需要再自动生成提醒
                return true;
            }
        }
        return false;
    }

    public function buildReminder($type)
    {
        $messages = $this->configs[$type]['messages'] ?? [];
        if (empty($messages)) {
            return null;
        }
        $index = array_rand($messages);
        $content = $this->configs[$type]['messages'][$index];
        if ($this->petId) {
            $pet = Pet::with(['petType', 'petBreed'])->where('id', $this->petId)->first();
            $content = str_replace('{PET_NAME}', $pet->name, $content);
        }
        $data = [
            'user_id'      => $this->userId,
            'pet_id'       => $this->petId,
            'type'         => $type,
            'title'        => PetReminder::MAPPING[$type]['label'],
            'content'      => $content,
            'trigger_date' => Carbon::today()->format('Y-m-d'),
        ];
        // 创建提醒
        return PetReminder::create($data)->toArray();
    }
}
