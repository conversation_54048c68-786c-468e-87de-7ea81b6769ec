<?php

namespace App\Listeners;

use App\Events\ParentAddedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendParentAddedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\ParentAddedEvent  $event
     * @return void
     */
    public function handle(ParentAddedEvent $event)
    {
        try {
            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Parent added notification failed: Pet not found', [
                    'pet_id' => $event->petId,
                    'owner_id' => $event->ownerId,
                    'parent_id' => $event->parentId
                ]);
                return;
            }

            // 获取新家长信息
            $parent = AppUser::find($event->parentId);

            if (!$parent) {
                Log::error('Parent added notification failed: Parent not found', [
                    'pet_id' => $event->petId,
                    'parent_id' => $event->parentId
                ]);
                return;
            }

            // 获取宠物所有者信息
            $owner = AppUser::find($event->ownerId);

            if (!$owner) {
                Log::error('Parent added notification failed: Owner not found', [
                    'pet_id' => $event->petId,
                    'owner_id' => $event->ownerId
                ]);
                return;
            }

            // 向宠物所有者发送通知
            NotificationService::createNotification(
                $event->ownerId, // 接收通知的用户ID（宠物所有者）
                NotificationSetting::TYPE_PARENT_ADDED, // 通知类型
                '添加家长通知',
                "您已成功将 {$parent->username} 添加为宠物 {$pet->name} 的家长",
                null, // 发送者ID（系统通知，无发送者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            // 向新家长发送通知
            NotificationService::createNotification(
                $event->parentId, // 接收通知的用户ID（新家长）
                NotificationSetting::TYPE_PARENT_ADDED, // 通知类型
                '添加家长通知',
                "您已被 {$owner->username} 添加为宠物 {$pet->name} 的家长",
                $event->ownerId, // 发送者ID（宠物所有者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            Log::info('Parent added notification sent', [
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId,
                'parent_id' => $event->parentId
            ]);
        } catch (\Exception $e) {
            Log::error('Parent added notification failed', [
                'error' => $e->getMessage(),
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId,
                'parent_id' => $event->parentId
            ]);
        }
    }
}
