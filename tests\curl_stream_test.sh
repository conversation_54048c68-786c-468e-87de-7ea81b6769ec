#!/bin/bash

echo "===== 测试食物分析流式响应 ====="
curl -X POST "http://localhost:8000/api/mobile/petChat/food?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "可乐能不能喝"}' \
  -v

echo -e "\n\n===== 测试排泄物分析流式响应 ====="
curl -X POST "http://localhost:8000/api/mobile/petChat/stool?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "我家狗狗的便便有点黑色"}' \
  -v

echo -e "\n\n===== 测试健康分析流式响应 ====="
curl -X POST "http://localhost:8000/api/mobile/petChat/health?stream=true" \
  -H "Content-Type: application/json" \
  -d '{"message": "我家狗狗最近没精神"}' \
  -v
