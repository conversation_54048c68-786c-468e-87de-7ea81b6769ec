<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateCategoryBrandsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('goods_categorys_brands', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('category_id')->default(0)->index()->comment('分类ID');
            $table->bigInteger('brand_id')->default(0)->index()->comment('品牌ID');
            $table->unique('id');
        });
        DB::statement("ALTER TABLE `goods_categorys_brands` COMMENT='分类品牌中间表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods_categorys_brands');
    }
}
