<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\ConsultMessage;
use Illuminate\Http\Request;

class ConsultMessageController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'consult_id'    => 'nullable|integer|exists:consults,id',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
        ]);
        $formData = $request->all();
        $records = ConsultMessage::getList($formData);
        return ResponseHelper::success($records);
    }
}
