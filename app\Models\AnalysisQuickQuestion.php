<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AnalysisQuickQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'analysis_type', 'question', 'icon', 'pet_type', 'sort_order', 'status'
    ];
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

}
