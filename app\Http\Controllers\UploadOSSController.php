<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Helpers\ResponseHelper;
use OSS\OssClient;

class UploadOSSController extends Controller
{
    private $accessKeyId;
    private $accessKeySecret;
    private $endpoint;
    private $bucket;
    private $ossClient;
    private $imageMaxSize;
    private $videoMaxSize;
    private $imgMaxWidth;
    private $imgMaxHeight;

    public function __construct()
    {
        $this->accessKeyId = config('services.alioss.key_id');
        $this->accessKeySecret = config('services.alioss.key_secret');
        $this->endpoint = 'https://' . config('services.alioss.server');
        $this->bucket = config('services.alioss.bucket');
        $this->ossClient = new OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint);
        $this->imageMaxSize = config('services.alioss.image_max_size');
        $this->videoMaxSize = config('services.alioss.video_max_size');
        $this->imgMaxWidth = config('services.alioss.img_max_width');
        $this->imgMaxHeight = config('services.alioss.img_max_height');
    }

    /**
     * 同理 应该放到models or helpers类
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function upload(Request $request): \Illuminate\Http\JsonResponse
    {
        $formData = $request->all();
        $type = $formData['type'] ?? "common";
        $file = $request->file('file');
        if (config('app.env') == 'production') {
            $env = 'master';
        } else {
            $env = 'develop';
        }
        if (!$file) {
            return ResponseHelper::error(__('unknown file'));
        }
        //文件后缀限制（必须：否则别人上传钓鱼网站会导致oss被封杀）
        $extension = $file->getClientOriginalExtension();
        if (in_array($extension, ['gif', 'jpg', 'jpeg', 'bmp', 'png', 'webp'])) {
            $maxSize = $this->imageMaxSize;
            $fileDir = 'images';
        } elseif (in_array($extension, ['mp4', 'avi', 'rmvb', 'flv', 'wmv'])) {
            $maxSize = $this->videoMaxSize;
            $fileDir = 'videos';
        } else {
            return ResponseHelper::error(__('Uploaded file format is limited'));
        }
        //文件大小限制
        $fileSize = $file->getSize();
        preg_match('/(\d+)(\w+)/', $maxSize, $matches);
        $sizeType = strtolower($matches[2]);
        $sizeTypeDict = ['b' => 0, 'k' => 1, 'kb' => 1, 'm' => 2, 'mb' => 2, 'gb' => 3, 'g' => 3];
        $maxSize = (int)$maxSize * pow(1024, $sizeTypeDict[$sizeType] ?? 0);
        if ($fileSize > $maxSize) {
            return ResponseHelper::error(__('filesize not match'));
        }
        //图片宽高限制
        $filePath = $file->getPathname();
        if ($fileDir === 'images') {
            $imgInfo = getimagesize($filePath);
            $imagewidth = isset($imgInfo[0]) ? $imgInfo[0] : 0;
            $imageheight = isset($imgInfo[1]) ? $imgInfo[1] : 0;
            if ($imagewidth > $this->imgMaxWidth) {
                return ResponseHelper::error(__('image width not match'));
            }
            if ($imageheight > $this->imgMaxHeight) {
                return ResponseHelper::error(__('image height not match'));
            }
        }
        //上传至oss
        $fileName = $env . '/' . $fileDir . '/' . $type . '/' . time() . '-' . mt_rand(0, 10000) . '.' . $extension;
        $mimeType = $file->getClientMimeType(); // 获取文件的 MIME 类型
        $options = array(
            OssClient::OSS_CONTENT_TYPE => $mimeType, // 设置Content-Type
        );
        $record = $this->ossClient->uploadFile($this->bucket, $fileName, $filePath, $options);
        if (!empty($record['oss-request-url'])) {
            return ResponseHelper::success(['url' => $record['oss-request-url']], __('upload.success'));
        } else {
            return ResponseHelper::error(__('upload.failed'));
        }
    }

    /**
     * 上传APK/WGT包
     */
    public function uploadPackage(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'file' => 'required|file',
            'type' => 'required|in:apk,wgt'
        ]);

        try {
            $file = $request->file('file');
            $type = $request->input('type');

            // 验证文件后缀
            $extension = strtolower($file->getClientOriginalExtension());
            if ($extension !== $type) {
                throw new \Exception("文件后缀必须是{$type}");
            }

            // 验证文件类型
            $extension = strtolower($file->getClientOriginalExtension());
            if ($extension !== $type) {
                throw new \Exception("只允许上传{$type}文件");
            }

            // 验证文件大小(假设100MB)
            $maxSize = 100 * 1024 * 1024;
            if ($file->getSize() > $maxSize) {
                throw new \Exception('文件大小超出限制');
            }

            // 生成存储路径
            $env = config('app.env') == 'production' ? 'master' : 'develop';
            $fileName = $env . '/packages/' . $type . '/' . date('YmdHis') . '-' . mt_rand(1000, 9999) . '.' . $type;

            // 上传文件
            $options = [
                OssClient::OSS_CONTENT_TYPE => $file->getMimeType(),
            ];

            $record = $this->ossClient->uploadFile(
                $this->bucket,
                $fileName,
                $file->getPathname(),
                $options
            );

            if (!empty($record) && !empty($record['oss-request-url'])) {
                return ResponseHelper::success(['url' => $record['oss-request-url']], '上传成功');
            }

            return ResponseHelper::error('上传失败');

        } catch (\Exception $e) {

            return ResponseHelper::error($e->getMessage());
        }
    }



}
