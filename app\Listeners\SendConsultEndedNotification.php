<?php

namespace App\Listeners;

use App\Events\ConsultEndedEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultEndedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\ConsultEndedEvent $event
     * @return void
     */
    public function handle(ConsultEndedEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'        => GoEasyServiceProvider::EVENT_CONSULT_ENDED,
                'consult'      => $consult->toArray(),
                'operate_type' => $event->operateType, // user / dietitian / system
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定用户
            if ($event->operateType == 'dietitian' || $event->operateType == 'system') {
                $channel = GoEasyServiceProvider::getChannelForUser($consult->user_id);
                $notification = ConsultNotificationService::getConsultEndedMessage();
                $res = $goEasy->publish($channel, $json, $notification);
                if (!$res || $res['code'] != 200) {
                    throw new \Exception('GoEasy publish to user failed, reason: ' . ($res['content'] ?? '未知错误'));
                }
            }

            //推送给指定营养师
            if ($event->operateType == 'user' || $event->operateType == 'system') {
                $channel = GoEasyServiceProvider::getChannelForDietitian($consult->dietitian_id);
                $notification = ConsultNotificationService::getConsultEndedMessage();
                $res = $goEasy->publish($channel, $json, $notification);
                if (!$res || $res['code'] != 200) {
                    throw new \Exception('GoEasy publish to dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
                }
            }

            Log::info('Consult ended notification sent', [
                'consult_id'   => $event->consultId,
                'operate_type' => $event->operateType,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult ended notification failed', [
                'error'        => $e->getMessage(),
                'consult_id'   => $event->consultId,
                'operate_type' => $event->operateType,
            ]);
        }
    }
}
