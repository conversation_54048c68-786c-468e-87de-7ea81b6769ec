<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAnalysisBodyPartsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('analysis_body_parts', function (Blueprint $table) {
            $table->id();
            $table->string('code', 50)->comment('部位代码');
            $table->string('name', 100)->comment('部位名称');
            $table->text('description')->nullable()->comment('描述');
            $table->string('icon', 255)->nullable()->comment('图标URL');
            $table->string('pet_type', 20)->default('general')->comment('适用宠物类型：general, dog, cat');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->boolean('status')->default(1)->comment('状态：1启用，0禁用');
            $table->timestamps();
            
            $table->unique(['code', 'pet_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('analysis_body_parts');
    }
}
