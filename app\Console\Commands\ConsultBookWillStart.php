<?php

namespace App\Console\Commands;

use App\Events\ConsultBookWillStartEvent;
use App\Models\Consult;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ConsultBookWillStart extends Command
{
    protected $signature = 'consult:book-will-start';

    protected $description = '咨询预约即将开始提醒';

    public function handle()
    {
        Consult::query()
            ->select('id')
            ->where('is_book', 1)
            ->where('book_status', Consult::CONSULT_BOOK_STATUS_SUCCESS)
            ->where('book_notify', 0)
            ->where('status', Consult::CONSULT_STATUS_WAITING)
            ->where('book_time', '<=', now()->addMinutes(15))
            ->where('book_time', '>=', now())
            ->where('created_at', '>=', now()->subDays(30))  //只查询一个月内的
            ->chunk(100, function ($consults) {
                foreach ($consults as $consult) {
                    try {
                        event(new ConsultBookWillStartEvent($consult->id));
                        Consult::where('id', $consult->id)->update([
                            'book_notify' => 1,
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Consult book will start notification failed', [
                            'error'      => $e->getMessage(),
                            'consult_id' => $consult->id,
                        ]);
                    }
                }
            });
    }
}
