<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Article;
use App\Models\PetReminder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ArticleController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'            => 'sometimes|integer|gt:0',
            'per_page'        => 'sometimes|integer|between:1,200',
            'keyword'         => 'nullable|string',
            'sort_name'       => 'nullable|in:id,created_at,updated_at,view_count',
            'sort_by'         => 'nullable|in:asc,desc',
        ]);
        $params = $request->all();
        $params['status'] = 1; //只查看上架的
        $records = Article::getList($params, $request->user()->id);
        PetReminder::markMemoRead($request->user()->id, PetReminder::TYPE_TUTORIAL_DISCOVERY_PAGE);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $user = Auth::guard('api')->user();
        $result = Article::getDetailForApp($request->input('id'), $user ? $user->id : -1);
        return ResponseHelper::result(...$result);
    }
}
