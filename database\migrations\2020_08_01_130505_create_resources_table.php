<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateResourcesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('resources', function (Blueprint $table) {
            $table->id();
            $table->tinyInteger('type')->default(1)->comment('资源类型1图片2视频');
            $table->string('depict',100)->nullable()->comment('资源描述');
            $table->string('img',255)->comment('资源地址');
            $table->integer('image_id')->default(0);
            $table->string('image_type',200);
            $table->timestamps();
            $table->unique('id');
        });
        DB::statement("ALTER TABLE `resources` COMMENT='资源'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('resources');
    }
}
