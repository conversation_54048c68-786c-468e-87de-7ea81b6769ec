<?php

namespace App\Listeners;

use App\Events\PetAlbumLikedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPetAlbumLikedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\PetAlbumLikedEvent $event
     * @return void
     */
    public function handle(PetAlbumLikedEvent $event)
    {
        try {
            // 获取点赞者信息
            $liker = AppUser::find($event->likerId);

            if (!$liker) {
                Log::error('Post liked notification failed: Liker not found', [
                    'liker_id' => $event->likerId,
                    'album_id' => $event->albumId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->albumOwnerId, // 接收通知的用户ID（相册所有者）
                NotificationSetting::TYPE_ALBUM_LIKED, // 通知类型
                '相册点赞通知',
                "用户 {$liker->username} 点赞了您的相册",
                $event->likerId, // 发送者ID（点赞者）
                $event->albumId, // 相关ID（相册ID）
                NotificationSetting::RELATION_TYPE_ALBUM // 相关类型
            );

            Log::info('Post liked notification sent', [
                'album_id' => $event->albumId,
                'owner_id' => $event->albumOwnerId,
                'liker_id' => $event->likerId
            ]);
        } catch (\Exception $e) {
            Log::error('Post liked notification failed', [
                'error'    => $e->getMessage(),
                'album_id' => $event->albumId,
                'owner_id' => $event->albumOwnerId,
                'liker_id' => $event->likerId
            ]);
        }
    }
}
