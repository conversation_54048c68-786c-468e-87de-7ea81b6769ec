<?php

namespace App\Listeners;

use App\Events\PetChanged;
use App\Models\PetLog as PetLogModel;

//写入操作日志
class PetLogger
{
    private array $listenFields = [
        'neutered',
        'vaccinated',
        'birth_certificate',
        'bloodline_certificate',
        'vaccine_certificate',
        'health_report',
        'is_lost',
        'weight',
        'weight_status',
        'active_status',
        'is_pregnant',
        'is_ill',
        'is_dead',
    ];

    public function handle(PetChanged $event)
    {
        $created_at = date('Y-m-d H:i:s');
        foreach ($this->listenFields as $field) {
            $old_value = $event->oldData[$field] ?? '';
            $new_value = $event->newData[$field] ?? '';
            if ($new_value == $old_value) {
                continue;
            }
            $log = [
                'user_id'    => $event->userId,
                'pet_id'     => $event->newData['id'],
                'log_field'  => $field,
                'old_value'  => $old_value,
                'new_value'  => $new_value,
                'created_at' => $created_at,
            ];
            // 如果是体重记录，检查当天是否已有记录
            if ($log['log_field'] === 'weight') {
                $this->handleWeightLog($log);
            } else {
                // 其他字段直接插入
                PetLogModel::create($log);
            }
        }
    }

    /**
     * 处理体重日志 - 当天覆盖，非当天新增
     */
    private function handleWeightLog($log)
    {
        $today = date('Y-m-d');

        // 查找当天是否已有体重记录
        $existingLog = PetLogModel::where('pet_id', $log['pet_id'])
            ->where('log_field', 'weight')
            ->whereDate('created_at', $today)
            ->first();

        if ($existingLog) {
            // 当天已有记录，更新现有记录
            $existingLog->update([
                'old_value' => $existingLog->new_value, // 将之前的新值作为旧值
                'new_value' => $log['new_value'],
                'updated_at' => now()
            ]);
        } else {
            // 当天没有记录，创建新记录
            PetLogModel::create($log);
        }
    }
}
