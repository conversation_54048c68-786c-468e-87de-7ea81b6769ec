<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ParentRemovedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 宠物 ID
     *
     * @var int
     */
    public $petId;

    /**
     * 宠物所有者 ID
     *
     * @var int
     */
    public $ownerId;

    /**
     * 被移除的家长 ID
     *
     * @var int
     */
    public $parentId;

    /**
     * Create a new event instance.
     *
     * @param int $petId 宠物 ID
     * @param int $ownerId 宠物所有者 ID
     * @param int $parentId 被移除的家长 ID
     * @return void
     */
    public function __construct($petId, $ownerId, $parentId)
    {
        $this->petId = $petId;
        $this->ownerId = $ownerId;
        $this->parentId = $parentId;
    }
}
