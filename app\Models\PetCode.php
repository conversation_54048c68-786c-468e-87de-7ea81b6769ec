<?php

namespace App\Models;

use App\Events\ParentAddedEvent;
use App\Events\PetFamilyCodeEvent;
use App\Events\PetReceivedEvent;
use App\Events\PetTransferCodeEvent;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetCode extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_codes';

    protected $fillable = [
        'pet_id',
        'user_id',
        'code',
        'type',
        'status',
        'images',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function generateCode()
    {
        return strtoupper(substr(md5(microtime(true) . '_' . rand(1000, 9999)), 0, 16));
    }

    public static function saveDetail($data, $userId)
    {
        DB::beginTransaction();
        try {
            $pet = Pet::where('id', $data['pet_id'])->first() or throw_if(true, 'RuntimeException', "寵物：{$data['pet_id']}不存在或已删除");
            Pet::isOwner($pet, $userId) or throw_if(true, 'RuntimeException', "沒有權限");
            $data['user_id'] = $userId;
            $data['status'] = 1;
            $data['code'] = self::generateCode();
            if (!empty($data['images']) && is_array($data['images'])) {
                $data['images'] = implode(',', $data['images']);
            } else {
                $data['images'] = '';  // 如果没有图片就设为空字符串
            }
            $result = self::create($data);
            $id = $result->id;
            if ($id) {
                $success = true;
                $record['code'] = $data['code'];
                $message = '保存成功';

                // 触发相应的事件
                if ($data['type'] == 1) { // 转让宠物
                    event(new PetTransferCodeEvent($data['pet_id'], $userId, $data['code']));
                } else if ($data['type'] == 2) { // 添加家人
                    event(new PetFamilyCodeEvent($data['pet_id'], $userId, $data['code']));
                }
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function getDetail($code)
    {
        $info = PetCode::where('code', $code)->where('created_at', '>=', now()->subDays(3))->first();
        if (!$info) {
            return [false, null, '獲取失敗'];
        }
        if ($info['status'] == 2) {
            return [false, null, $info['type'] == 1 ? '寵物已轉讓' : '已添加家人'];
        }
        if ($info['status'] == 3) {
            return [false, null, '二維碼已失效'];
        }
        if (!$info->pet = Pet::getDetail($info['pet_id'])) {
            return [false, null, '寵物不存在或已刪除'];
        }
        return [true, $info, '獲取成功'];
    }

    public static function cancelCode($id, $user_id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "二維碼：{$id}不存在或已删除");
            $info['user_id'] == $user_id or throw_if(true, 'RuntimeException', "您没有权限取消该二維碼");
            $info['status'] == 2 and throw_if(true, 'RuntimeException', $info['type'] == 1 ? '寵物已轉讓' : '已添加家人');
            $info['status'] == 3 and throw_if(true, 'RuntimeException', '二維碼已失效');
            $success = $info->where('id', $id)->update(['status' => 3]);
            if ($success) {
                $success = true;
                $message = '取消成功';
            } else {
                throw new Exception('取消失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function receivePet($code, $user)
    {
        DB::beginTransaction();
        try {
            $result = self::getDetail($code);
            if (!$result[0]) {
                DB::rollBack();
                return $result;
            }
            $info = $result[1];
            $userId = $user->id;
            $success = self::where('id', $info['id'])->where('status', 1)->update(['status' => 2]);
            $pet = $info->pet;
            Pet::isOwner($pet, $userId) and throw_if(true, 'RuntimeException', "不能接收自己的寵物");
            if ($success && $info['type'] == 1) {  //转让宠物
                $success = Pet::where('id', $pet['id'])->where('owner_id', $pet['owner_id'])->update(['owner_id' => $userId]);
                if ($info['role'] == 2) { //朋友
                    $success && $success = PetMaster::resetMasters($pet['id'], 3);  //宠物转让
                }
                if (!PetMaster::isMaster($pet['id'], $userId)) {
                    $success && $success = PetMaster::addMaster($pet['id'], $userId);
                }
                //生成动态
                if ($success) {
                    $contentData = [
                        'title'           => '迎接新成员',
                        'content'         => '迎接新成员',
                        'status'          => 1,
                        'type'            => 2, //私人记录
                        'permission_type' => 2,
                        'pet_ids'         => [$pet['id']], // 改为数组格式
                        'images'          => $info->images,
                        'longitude'       => $user->longitude,
                        'latitude'        => $user->latitude,
                    ];
                    $res = AppContent::saveDetail($contentData, $userId);
                    $success = $success && $res[0];
                }
            }
            if ($success && $info['type'] == 2) { //添加家人
                if (!PetMaster::isMaster($pet['id'], $userId)) {
                    $success = $success && PetMaster::addMaster($pet['id'], $userId);

                    // 触发家长添加事件
                    event(new ParentAddedEvent($pet['id'], $pet['owner_id'], $userId));
                } else {
                    throw new Exception('您已經是該寵物的家人');
                }
            }
            if ($success) {
                $record = [];
                $message = '接收成功';

                // 触发宠物接收事件
                $receiveType = $info['type'] == 1 ? 'transfer' : 'family';
                event(new PetReceivedEvent($pet['id'], $pet['owner_id'], $userId, $receiveType));
            } else {
                throw new Exception('接收失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
