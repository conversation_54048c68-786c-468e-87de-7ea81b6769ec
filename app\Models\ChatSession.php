<?php

namespace App\Models;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTimeInterface;

/**
 * @mixin Builder
 */
class ChatSession extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'session_id',
        'user_id',
        'pet_ids',
        'title',
        'analysis_type',
        'status',
        'last_message_at'
    ];

    protected $casts = [
        'last_message_at' => 'datetime',
        'pet_ids' => 'array',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取会话的所有消息
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class, 'session_id', 'session_id');
    }

    /**
     * 获取会话关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 获取会话关联的所有宠物（保持历史原始顺序）
     */
    public function pets()
    {
        if (empty($this->pet_ids)) {
            return collect();
        }
        // 历史数据保持创建时的原始顺序，不受用户排序设置影响
        return Pet::getPetsByIdsWithOriginalOrder($this->pet_ids, $this->user_id);
    }

    /**
     * 创建新会话
     * @param int $userId 用户ID
     * @param array|null $petIds 宠物ID数组
     * @param int $analysisType 分析类型
     * @param string|null $title 会话标题
     */
    public static function createSession($userId, $petIds = null, $analysisType = 0, $title = null)
    {
        $sessionId = self::generateSessionId();

        // 处理宠物ID数组
        $finalPetIds = null;
        if (!empty($petIds)) {
            if (is_array($petIds)) {
                $finalPetIds = array_map('intval', $petIds);
            } else {
                $finalPetIds = [(int)$petIds];
            }
        }

        return self::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'pet_ids' => $finalPetIds,
            'title' => $title,
            'analysis_type' => $analysisType,
            'status' => 1
        ]);
    }





    /**
     * 生成唯一会话 ID
     */
    public static function generateSessionId()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
