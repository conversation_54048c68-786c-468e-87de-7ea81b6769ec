<?php

namespace App\Services;

use App\Models\Good;
use App\Models\Pet;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SimpleAIRecommendationService
{
    /**
     * 基于AI分析结果生成商品推荐（商品字段方案）
     *
     * @param string $analysisContent AI分析内容
     * @param string $analysisType 分析类型：food,health,stool
     * @param int|null $petId 宠物ID
     * @param int $limit 推荐商品数量限制
     * @return array 推荐结果
     */
    public function generateRecommendations(string $analysisContent, string $analysisType, ?int $petId = null, int $limit = 5): array
    {
        try {
            // 1. 从分析内容中提取关键词
            $extractedKeywords = $this->extractKeywords($analysisContent);
            
            // 2. 获取宠物信息
            $pet = $petId ? Pet::with(['petType', 'petBreed'])->find($petId) : null;
            
            // 3. 基于关键词直接查询商品
            $recommendations = $this->getRecommendedProducts($extractedKeywords, $analysisType, $pet, $limit);
            
            return [
                'status' => 'success',
                'recommendations' => $recommendations,
                'matched_keywords' => $extractedKeywords,
                'total_count' => count($recommendations)
            ];
            
        } catch (\Exception $e) {
            Log::error('简化AI商品推荐生成失败: ' . $e->getMessage());
            return [
                'status' => 'error',
                'message' => '推荐生成失败',
                'recommendations' => []
            ];
        }
    }

    /**
     * 从AI分析内容中提取关键词
     *
     * @param string $content
     * @return array
     */
    private function extractKeywords(string $content): array
    {
        $keywords = [];

        // 获取所有启用AI推荐的商品的关键词（缓存1小时）
        $cacheKey = 'ai_goods_keywords';
        $allKeywords = Cache::remember($cacheKey, 3600, function () {
            return Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
                ->whereNotNull('ai_trigger_keywords')
                ->where('ai_trigger_keywords', '!=', '')
                ->pluck('ai_trigger_keywords')
                ->flatMap(function ($keywordString) {
                    return explode(',', $keywordString);
                })
                ->map(function ($keyword) {
                    return trim($keyword);
                })
                ->filter(function ($keyword) {
                    return !empty($keyword);
                })
                ->unique()
                ->values()
                ->toArray();
        });

        // 在分析内容中查找匹配的关键词
        foreach ($allKeywords as $keyword) {
            if (!empty($keyword) && mb_strpos($content, $keyword) !== false) {
                $keywords[] = $keyword;
            }
        }

        return array_unique($keywords);
    }

    /**
     * 获取推荐商品
     *
     * @param array $extractedKeywords
     * @param string $analysisType
     * @param Pet|null $pet
     * @param int $limit
     * @return array
     */
    private function getRecommendedProducts(array $extractedKeywords, string $analysisType, ?Pet $pet, int $limit): array
    {
        if (empty($extractedKeywords)) {
            return [];
        }

        // 构建基础查询（ai_trigger_keywords不为空表示启用AI推荐）
        $query = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->whereNotNull('ai_trigger_keywords')
            ->where('ai_trigger_keywords', '!=', '')
            ->where(function ($q) use ($analysisType) {
                $q->whereNull('ai_analysis_types')
                  ->orWhere('ai_analysis_types', 'like', "%{$analysisType}%");
            });

        // 关键词匹配条件
        $query->where(function ($q) use ($extractedKeywords) {
            foreach ($extractedKeywords as $keyword) {
                $q->orWhere('ai_trigger_keywords', 'like', "%{$keyword}%");
            }
        });

        // 如果有宠物信息，优先匹配宠物类型
        if ($pet) {
            $query->where(function ($q) use ($pet) {
                $q->whereHas('recommendedPetTypes', function ($query) use ($pet) {
                    $query->where('pet_type_id', $pet->petType->id);
                })
                ->orWhereDoesntHave('recommendedPetTypes'); // 或者没有限制宠物类型的商品
            });
        }

        // 获取商品并计算匹配度
        $products = $query->get();
        
        $recommendations = [];
        foreach ($products as $product) {
            // 计算关键词匹配数量
            $productKeywords = explode(',', $product->ai_trigger_keywords);
            $productKeywords = array_map('trim', $productKeywords);
            
            $matchedKeywords = array_intersect($extractedKeywords, $productKeywords);
            $matchScore = count($matchedKeywords);
            
            if ($matchScore > 0) {
                $recommendations[] = [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_name_tc' => $product->name_tc ?? $product->name,
                    'product_name_en' => $product->name_en ?? '',
                    'product_image' => $product->image,
                    'price' => $product->price,
                    'market_price' => $product->market_price ?? $product->price,
                    'matched_keywords' => array_values($matchedKeywords),
                    'recommendation_reason' => '根据AI分析结果推荐',
                    'recommendation_reason_tc' => '根據AI分析結果推薦',
                    'recommendation_score' => $matchScore,
                    'match_count' => $matchScore
                ];
            }
        }

        // 按匹配度排序
        usort($recommendations, function ($a, $b) {
            if ($a['recommendation_score'] == $b['recommendation_score']) {
                return $b['product_id'] - $a['product_id']; // ID倒序作为次要排序
            }
            return $b['recommendation_score'] - $a['recommendation_score'];
        });

        return array_slice($recommendations, 0, $limit);
    }


}
