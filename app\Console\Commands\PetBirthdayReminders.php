<?php

namespace App\Console\Commands;

use App\Models\Pet;
use App\Models\PetMaster;
use App\Models\PetReminder;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PetBirthdayReminders extends Command
{
    protected $signature = 'pet:birthday-reminders {days=7 : 提前几天生成生日提醒}';


    protected $description = '生成宠物生日提醒';

    public function handle()
    {
        $days = (int)$this->argument('days');
        $this->info("开始生成宠物生日提醒，提前{$days}天通知");

        $targetDate = now()->addDays($days);
        $targetMonth = $targetDate->month;
        $targetDay = $targetDate->day;

        $totalCount = 0;
        Pet::whereNotNull('birthday')
            ->whereRaw('MONTH(birthday) = ?', [$targetMonth])
            ->whereRaw('DAY(birthday) = ?', [$targetDay])
            ->select('id', 'user_id', 'name', 'birthday')
            ->chunkById(100, function ($pets) use ($targetDate, $days, &$totalCount) {
                $batchSizeInsert = 1000;
                $remindersToCreate = [];
                $petIds = $pets->pluck('id')->toArray();
                $masters = PetMaster::whereIn('pet_id', $petIds)->select('pet_id', 'user_id')->get();
                $allMasterIds = [];
                foreach ($masters as $item) {
                    $allMasterIds[$item->pet_id][] = $item->user_id;
                }
                foreach ($pets as $pet) {
                    $masterIds = $allMasterIds[$pet->id];
                    foreach ($masterIds as $userId) {
                        // 处理提醒逻辑
                        $birthDate = Carbon::createFromFormat('Y-m-d', $pet->birthday)->startOfDay();
                        $age = $birthDate->diffInYears($targetDate);
                        if ($days > 0) {
                            $content = "您的寵物 {$pet->name} 還有 {$days} 天就要過{$age}嵗生日啦！";
                        } else {
                            $content = "生日快樂！今日是您的寵物 {$pet->name} {$age}岁生日啦！";
                        }

                        // 创建提醒
                        $remindersToCreate[] = [
                            'user_id'      => $userId,
                            'pet_id'       => $pet->id,
                            'type'         => PetReminder::TYPE_BIRTHDAY_REMINDER,
                            'title'        => PetReminder::MAPPING[PetReminder::TYPE_BIRTHDAY_REMINDER]['label'],
                            'content'      => $content,
                            'trigger_date' => now()->format('Y-m-d'),
                            'status'       => 1,
                            'created_at'   => now(),
                            'updated_at'   => now(),
                        ];

                        // 批量插入
                        if (count($remindersToCreate) >= $batchSizeInsert) {
                            PetReminder::insert($remindersToCreate);
                            $totalCount += count($remindersToCreate);
                            $remindersToCreate = [];
                        }
                    }
                }

                // 插入剩余数据
                if (!empty($remindersToCreate)) {
                    PetReminder::insert($remindersToCreate);
                    $totalCount += count($remindersToCreate);
                }
            });

        $this->info("共生成 {$totalCount} 条生日提醒");
        Log::info("Generated {$totalCount} pet birthday reminders");

        return self::SUCCESS;
    }
}
