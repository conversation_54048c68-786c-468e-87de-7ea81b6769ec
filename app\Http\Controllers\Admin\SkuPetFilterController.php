<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\GoodSku;

use App\Services\PetMatchingService;
use Illuminate\Http\Request;

/**
 * SKU宠物筛选标签控制器
 */
class SkuPetFilterController extends Controller
{
    protected $matchingService;

    public function __construct(PetMatchingService $matchingService)
    {
        $this->matchingService = $matchingService;
    }

    /**
     * 获取参数说明
     * 告诉前端接受的参数格式和含义
     */
    public function getParamInfo()
    {
        return ResponseHelper::success([
            'supported_params' => [
                'pet_type' => [
                    'format' => 'pet_type:{id}',
                    'description' => '宠物类型ID，支持单选或多选（逗号分隔），如：pet_type:1（狗）、pet_type:1,2（狗和猫）',
                    'examples' => ['pet_type:1', 'pet_type:1,2']
                ],
                'pet_breed' => [
                    'format' => 'pet_breed:{id}',
                    'description' => '宠物品种ID，支持单选或多选（逗号分隔），如：pet_breed:15（金毛）、pet_breed:15,16,20（多个品种）',
                    'examples' => ['pet_breed:15', 'pet_breed:15,16,20']
                ],
                'size' => [
                    'format' => 'size:{category}',
                    'description' => '体型分类，支持单选或多选（逗号分隔），如：size:large（大型）、size:large,medium（大型和中型）',
                    'examples' => ['size:large', 'size:large,medium']
                ],
                'hair' => [
                    'format' => 'hair:{type}',
                    'description' => '毛发类型，支持单选或多选（逗号分隔），如：hair:long（长毛）、hair:long,short（长毛和短毛）',
                    'examples' => ['hair:long', 'hair:long,short']
                ],
                'age' => [
                    'format' => 'age:{range}',
                    'description' => '年龄范围，支持单个或多个范围（逗号分隔），如：age:1-7（1-7岁）、age:1-3,6-10（1-3岁或6-10岁），自动限制在0-20岁范围内',
                    'examples' => ['age:1-7', 'age:1-3,6-10', 'age:2']
                ],
                'life' => [
                    'format' => 'life:{cycle}',
                    'description' => '生命周期，支持单选或多选（逗号分隔），如：life:adult（成年期）、life:adult,senior（成年期和熟年期），兼容puppy/kitten',
                    'examples' => ['life:adult', 'life:adult,senior', 'life:puppy']
                ],
                'bcs' => [
                    'format' => 'bcs:{status}',
                    'description' => '体况评分(Body Condition Score)，支持单选或多选（逗号分隔），如：bcs:5（正常）、bcs:2,4（非常瘦和瘦）',
                    'examples' => ['bcs:5', 'bcs:2,4', 'bcs:7,9'],
                    'values' => [
                        '2' => '非常瘦',
                        '4' => '瘦',
                        '5' => '正常',
                        '7' => '胖',
                        '9' => '非常胖'
                    ],
                    'stage_mapping' => [
                        '1-2分' => '非常瘦',
                        '3-4分' => '瘦',
                        '5分' => '正常',
                        '6-7分' => '胖',
                        '8-9分' => '非常胖'
                    ],
                    'note' => '基于pets表的weight_status字段：1-9分制BCS评分，前端使用阶段代表值：2,4,5,7,9'
                ],
                'weight' => [
                    'format' => 'weight:{range}',
                    'description' => '体重范围筛选，支持单个范围或多个范围（逗号分隔），如：weight:5-15（5-15kg）、weight:2-8,15-25（2-8kg或15-25kg）',
                    'examples' => ['weight:5-15', 'weight:2-8,15-25', 'weight:0.5-3'],
                    'validation' => [
                        '格式' => '数字-数字（如：5-15）',
                        '体重值' => '必须大于0，支持小数',
                        '范围' => '最小值不能大于等于最大值',
                        '合理范围' => '0.1-100kg'
                    ],
                    'note' => '基于pets表的weight字段(decimal 5,2)，单位：kg'
                ]
            ],
            'usage_rules' => [
                '标签格式为 "类型:值"',
                '所有标签类型都支持多选（逗号分隔值）',
                '多个标签之间是AND关系（都必须满足）',
                '单个标签内的多个值是OR关系（满足任一即可）',
                'age类型支持范围值（如：1-7）或单值（如：2）',
                'weight类型只支持范围值（如：5-15），支持多范围组合',
                '生命周期基于宠物生日和品种数据自动计算',
                '年龄自动限制在0-20岁范围内',
                'bcs基于宠物的实际体况评分进行匹配，使用9分制BCS系统，前端约定阶段值：2=非常瘦,4=瘦,5=正常,7=胖,9=非常胖',
                'weight基于宠物的实际体重进行范围匹配',
                'weight用于根据宠物体重推荐合适规格的产品'
            ]
        ]);
    }

    /**
     * 保存SKU的宠物筛选标签
     */
    public function saveSkuFilterTags(Request $request)
    {
        $request->validate([
            'sku_id' => 'required|integer|exists:goods_skus,id',
            'pet_filter_tags' => 'required|array|min:1',
            'pet_filter_tags.*' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // 验证标签格式：type:value 或 type:value1,value2
                    if (!preg_match('/^[a-z_]+:[a-zA-Z0-9\.\-,]+$/', $value)) {
                        $fail('标签格式无效，应为 "类型:值" 或 "类型:值1,值2"');
                        return;
                    }

                    // 验证标签类型
                    [$type, $values] = explode(':', $value, 2);
                    $validTypes = ['pet_type', 'pet_breed', 'size', 'hair', 'age', 'life', 'bcs', 'weight'];

                    if (!in_array($type, $validTypes)) {
                        $fail("不支持的标签类型: {$type}，支持的类型: " . implode(', ', $validTypes));
                        return;
                    }

                    // 验证值格式 - 修复：不能使用empty()因为它会将"0"视为空值
                    if ($values === '' || $values === null) {
                        $fail("标签值不能为空");
                        return;
                    }

                    // 对于age类型，验证年龄范围格式
                    if ($type === 'age') {
                        $ageValues = explode(',', $values);
                        foreach ($ageValues as $ageValue) {
                            $ageValue = trim($ageValue);
                            if (!preg_match('/^\d+(\.\d+)?(-\d+(\.\d+)?)?$/', $ageValue)) {
                                $fail("年龄格式无效: {$ageValue}，应为数字或范围（如：2 或 1-7）");
                                return;
                            }
                        }
                    }

                    // 对于weight类型，验证体重范围格式
                    if ($type === 'weight') {
                        $weightValues = explode(',', $values);
                        foreach ($weightValues as $weightValue) {
                            $weightValue = trim($weightValue);
                            if (!preg_match('/^\d+(\.\d+)?-\d+(\.\d+)?$/', $weightValue)) {
                                $fail("体重格式无效: {$weightValue}，应为范围格式（如：5-15 或 2.5-10.5）");
                                return;
                            }

                            // 验证范围的合理性
                            [$minWeight, $maxWeight] = explode('-', $weightValue, 2);
                            $minWeight = (float) $minWeight;
                            $maxWeight = (float) $maxWeight;

                            if ($minWeight <= 0 || $maxWeight <= 0) {
                                $fail("体重值必须大于0: {$weightValue}");
                                return;
                            }

                            if ($minWeight >= $maxWeight) {
                                $fail("体重范围最小值不能大于等于最大值: {$weightValue}");
                                return;
                            }

                            if ($minWeight < 0.1 || $maxWeight > 100) {
                                $fail("体重范围超出合理范围(0.1-100kg): {$weightValue}");
                                return;
                            }
                        }
                    }

                    // 对于bcs类型，验证体况评分数字格式（9分制）
                    if ($type === 'bcs') {
                        $bcsValues = explode(',', $values);
                        foreach ($bcsValues as $bcsValue) {
                            $bcsValue = trim($bcsValue);
                            if (!preg_match('/^[1-9]$/', $bcsValue)) {
                                $fail("体况评分格式无效: {$bcsValue}，应为数字1-9（9分制BCS评分）");
                                return;
                            }
                        }
                    }
                }
            ]
        ]);

        $skuId = $request->input('sku_id');
        $filterTags = $request->input('pet_filter_tags');

        try {
            // 获取SKU信息
            $sku = GoodSku::with('good')->find($skuId);
            if (!$sku) {
                return ResponseHelper::error('SKU不存在');
            }

            // 计算匹配的宠物
            $matchingResult = $this->matchingService->calculateMatchingPets($filterTags);

            // 更新SKU的筛选标签和匹配数量
            $sku->update([
                'pet_filter_tags' => $filterTags,
                'matching_pets_count' => $matchingResult['count']
            ]);

            return ResponseHelper::success([
                'sku_info' => [
                    'id' => $sku->id,
                    'good_name' => $sku->good->name ?? '',
                    'product_sku' => $sku->product_sku,
                    'price' => $sku->price,
                ],
                'filter_tags' => $filterTags,
                'matching_result' => [
                    'total_count' => $matchingResult['count'],
                    'filter_summary' => $matchingResult['filter_summary'],
                    'matching_pets' => $matchingResult['pets']
                ]
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('保存筛选标签失败：' . $e->getMessage());
        }
    }

    /**
     * 验证SKU筛选标签的匹配逻辑
     * 基于已保存的筛选标签验证匹配结果，用于后台测试
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateFilterTags(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'sku_id' => 'required|integer|exists:goods_skus,id'
        ]);

        $skuId = $request->input('sku_id');

        try {
            // 获取SKU信息和已保存的筛选标签
            $sku = GoodSku::with('good')->find($skuId);
            if (!$sku) {
                return ResponseHelper::error('SKU不存在');
            }

            // 检查是否有保存的筛选标签
            $filterTags = $sku->pet_filter_tags ?? [];
            if (empty($filterTags)) {
                return ResponseHelper::error('该SKU尚未设置筛选标签');
            }

            // 计算匹配的宠物
            $matchingResult = $this->matchingService->calculateMatchingPets($filterTags);

            return ResponseHelper::success([
                'sku_info' => [
                    'id' => $sku->id,
                    'good_name' => $sku->good->name ?? '',
                    'product_sku' => $sku->product_sku,
                    'price' => $sku->price,
                ],
                'filter_tags' => $filterTags,
                'matching_result' => [
                    'total_count' => $matchingResult['count'],
                    'filter_summary' => $matchingResult['filter_summary'],
                    'matching_pets' => $matchingResult['pets']
                ]
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('验证筛选标签失败：' . $e->getMessage());
        }
    }


}
