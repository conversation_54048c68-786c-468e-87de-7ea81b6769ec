<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

/**
 * 实验室报告错误实时监控
 * 监控识别失败率，当超过阈值时发出告警
 */
class MonitorLabReportErrors extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'lab-report:monitor
                            {--interval=300 : 监控间隔(秒)}
                            {--failure-threshold=70 : 失败率告警阈值(%)}
                            {--min-samples=5 : 最小样本数}';

    /**
     * 命令描述
     */
    protected $description = '实时监控实验室报告识别错误率';

    /**
     * 执行命令
     */
    public function handle()
    {
        $interval = (int)$this->option('interval');
        $failureThreshold = (int)$this->option('failure-threshold');
        $minSamples = (int)$this->option('min-samples');

        $this->info("🔍 开始监控实验室报告识别错误率");
        $this->info("📊 监控间隔: {$interval}秒");
        $this->info("⚠️  失败率告警阈值: {$failureThreshold}%");
        $this->info("📈 最小样本数: {$minSamples}");
        $this->info("按 Ctrl+C 停止监控\n");

        while (true) {
            $this->checkErrorRate($failureThreshold, $minSamples);
            sleep($interval);
        }
    }

    /**
     * 检查错误率
     */
    protected function checkErrorRate(int $failureThreshold, int $minSamples): void
    {
        $now = now();
        $this->info("[{$now->format('Y-m-d H:i:s')}] 检查中...");

        // 获取最近1小时的统计数据
        $stats = $this->getRecentStats(60);

        if ($stats['total'] < $minSamples) {
            $this->comment("📊 样本数不足 ({$stats['total']}/{$minSamples})，跳过检查");
            return;
        }

        $failureRate = round(($stats['failures'] / $stats['total']) * 100, 1);
        $successRate = round(($stats['successes'] / $stats['total']) * 100, 1);

        // 显示当前状态
        $statusColor = $failureRate > $failureThreshold ? 'error' : 'info';
        $this->line("📈 最近1小时统计:", null, $statusColor);
        $this->line("   总分析次数: {$stats['total']}", null, $statusColor);
        $this->line("   成功次数: {$stats['successes']} ({$successRate}%)", null, $statusColor);
        $this->line("   失败次数: {$stats['failures']} ({$failureRate}%)", null, $statusColor);

        // 显示失败原因分布
        if (!empty($stats['failure_reasons'])) {
            $this->line("   失败原因分布:", null, $statusColor);
            foreach ($stats['failure_reasons'] as $reason => $count) {
                $percentage = round(($count / $stats['failures']) * 100, 1);
                $this->line("     - {$reason}: {$count} ({$percentage}%)", null, $statusColor);
            }
        }

        // 检查是否需要告警
        if ($failureRate > $failureThreshold) {
            $this->sendAlert($stats, $failureRate);
        }

        // 更新监控缓存
        $this->updateMonitoringCache($stats);

        $this->line("");
    }

    /**
     * 獲取最近的統計數據
     */
    protected function getRecentStats(int $minutes): array
    {
        $stats = [
            'total' => 0,
            'successes' => 0,
            'failures' => 0,
            'failure_reasons' => [],
            'avg_processing_time' => 0,
            'avg_quality_score' => 0
        ];

        // 讀取最近的日誌文件
        $logFile = storage_path('logs/laravel-' . now()->format('Y-m-d') . '.log');
        if (!file_exists($logFile)) {
            return $stats;
        }

        $content = file_get_contents($logFile);
        $lines = explode("\n", $content);
        $cutoffTime = now()->subMinutes($minutes);

        $processingTimes = [];
        $qualityScores = [];

        foreach ($lines as $line) {
            if (empty(trim($line))) continue;

            // 解析日誌時間
            if (!preg_match('/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/', $line, $timeMatches)) {
                continue;
            }

            $logTime = Carbon::createFromFormat('Y-m-d H:i:s', $timeMatches[1]);
            if ($logTime < $cutoffTime) {
                continue;
            }

            // 統計成功分析
            if (strpos($line, '✅ 實驗室報告分析成功完成') !== false) {
                $stats['total']++;
                $stats['successes']++;

                // 提取處理時間和質量分數
                if (preg_match('/"processing_time":([0-9.]+)/', $line, $timeMatch)) {
                    $processingTimes[] = (float)$timeMatch[1];
                }
                if (preg_match('/"quality_score":(\d+)/', $line, $qualityMatch)) {
                    $qualityScores[] = (int)$qualityMatch[1];
                }
            }

            // 統計失敗分析
            if (strpos($line, '❌ 圖片識別失敗 - 數據不完整') !== false) {
                $stats['total']++;
                $stats['failures']++;

                // 提取失敗原因
                if (preg_match('/"failure_reasons":\[([^\]]+)\]/', $line, $reasonMatch)) {
                    $reasons = json_decode('[' . $reasonMatch[1] . ']', true);
                    if (is_array($reasons)) {
                        foreach ($reasons as $reason) {
                            $reason = trim($reason, '"');
                            $stats['failure_reasons'][$reason] = ($stats['failure_reasons'][$reason] ?? 0) + 1;
                        }
                    }
                }
            }

            // 統計JSON解析失敗
            if (strpos($line, '❌ JSON解析完全失敗') !== false) {
                $stats['total']++;
                $stats['failures']++;
                $stats['failure_reasons']['json_parse_failure'] = ($stats['failure_reasons']['json_parse_failure'] ?? 0) + 1;
            }
        }

        // 计算平均值
        if (!empty($processingTimes)) {
            $stats['avg_processing_time'] = round(array_sum($processingTimes) / count($processingTimes), 2);
        }
        if (!empty($qualityScores)) {
            $stats['avg_quality_score'] = round(array_sum($qualityScores) / count($qualityScores), 1);
        }

        return $stats;
    }

    /**
     * 發送告警
     */
    protected function sendAlert(array $stats, float $failureRate): void
    {
        $alertKey = 'lab_report_alert_' . now()->format('Y-m-d-H');

        // 避免重複告警（每小時最多一次）
        if (Cache::has($alertKey)) {
            return;
        }

        $this->error("🚨 告警: 實驗室報告識別失敗率過高!");
        $this->error("   當前失敗率: {$failureRate}%");
        $this->error("   樣本數量: {$stats['total']}");

        if (!empty($stats['failure_reasons'])) {
            $this->error("   主要失敗原因:");
            foreach ($stats['failure_reasons'] as $reason => $count) {
                $this->error("     - {$reason}: {$count}次");
            }
        }

        // 提供建议
        $this->warn("\n💡 建议检查:");
        $this->warn("   1. AI服务状态是否正常");
        $this->warn("   2. 最近是否有图片格式变化");
        $this->warn("   3. 运行详细日志分析: php artisan lab-report:analyze-logs --failures-only --hours=1");

        // 设置告警缓存，1小时内不重复告警
        Cache::put($alertKey, true, 3600);

        // 这里可以集成邮件、短信、钉钉等告警方式
        // $this->sendEmailAlert($stats, $failureRate);
        // $this->sendDingTalkAlert($stats, $failureRate);
    }

    /**
     * 更新监控缓存
     */
    protected function updateMonitoringCache(array $stats): void
    {
        $cacheKey = 'lab_report_monitoring_' . now()->format('Y-m-d-H');
        Cache::put($cacheKey, $stats, 7200); // 缓存2小时
    }

    /**
     * 发送邮件告警（示例）
     */
    protected function sendEmailAlert(array $stats, float $failureRate): void
    {
        // 实现邮件告警逻辑
        // Mail::to(config('monitoring.alert_emails'))
        //     ->send(new LabReportErrorAlert($stats, $failureRate));
    }

    /**
     * 发送钉钉告警（示例）
     */
    protected function sendDingTalkAlert(array $stats, float $failureRate): void
    {
        // 实现钉钉告警逻辑
        // $webhook = config('monitoring.dingtalk_webhook');
        // Http::post($webhook, [
        //     'msgtype' => 'text',
        //     'text' => [
        //         'content' => "实验室报告识别失败率告警: {$failureRate}%"
        //     ]
        // ]);
    }
}
