<?php

namespace App\Exports;

use App\Models\GoodIndent;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class GoodIndentExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder, WithColumnWidths
{
    private $formData;
    private static $widthMap = [
        '訂單ID'   => 10,
        '訂單標識'   => 20,
        '交易單號'   => 30,
        '用戶ID'   => 10,
        '用戶昵稱'   => 10,
        '子訂單ID'  => 10,
        '商品ID'   => 10,
        '規格ID'   => 10,
        '運費模板ID' => 15,
        '商品名稱'   => 30,
        '單價'     => 10,
        '購買數量'   => 10,
        '重量'     => 10,
        '用戶備注'   => 20,
        '收貨人姓名'  => 10,
        '收貨人電話'  => 15,
        '收貨人地址'  => 35,
        '商品總額'   => 12,
        '運費'     => 10,
        '優惠券代碼'  => 15,
        '優惠券金額'  => 12,
        '支付金額'   => 12,
        '狀態'     => 15,
        '退款數量'   => 10,
        '退款金額'   => 12,
        '下單時間'   => 20,
        '付款時間'   => 20,
        '發貨時間'   => 20,
        '收貨時間'   => 20,
        '退款時間'   => 20,
    ];

    public function __construct(array $formData)
    {
        $this->formData = $formData;
    }

    public function collection()
    {
        $records = GoodIndent::getList($this->formData); //后台导出
        $cellData = [];
        foreach ($records as $record) {
            foreach ($record['commodities'] as $k => $commodity) {
                $cellData[] = [
                    $record['id'],
                    $record['identification'],
                    isset($record['payment_log']) ? $record['payment_log']['transaction_id'] : '',
                    $record['user_id'],
                    $record['user']['username'],
                    $commodity['id'],
                    $commodity['good_id'],
                    $commodity['good_sku_id'],
                    $commodity['freight_id'],
                    $commodity['name'],
                    $commodity['price'],
                    $commodity['number'],
                    $commodity['weight'],
                    $commodity['remark'],
                    isset($record['location']) ? $record['location']['name'] : '',
                    isset($record['location']) ? $record['location']['cellphone'] : '',
                    isset($record['location']) ? "{$record['location']['location']}{$record['location']['address']}{$record['location']['house']}" : '',
                    $commodity['good_total'],
                    $commodity['carriage_total'],
                    $record['user_coupon_id'],
                    $commodity['coupon_total'],
                    $commodity['pay_total'],
                    GoodIndent::STATE_TEXT_LIST[$record['state']],
                    $commodity['refund_number'],
                    $commodity['refund_total'],
                    $record['created_at'],
                    $record['pay_time'],
                    $record['shipping_time'],
                    $record['receiving_time'],
                    $record['refund_time'],
                ];
            }
        }
        return Collection::make($cellData);
    }

    public function bindValue(Cell $cell, $value)
    {
        $columns = array_keys(self::$widthMap);
        $colIndex = Coordinate::columnIndexFromString($cell->getColumn());

        if (in_array($columns[$colIndex - 1], ['訂單標識', '收貨人電話'])) {
            $cell->setValueExplicit((string)$value);
            return true; // 表示已处理该单元格
        }

        return parent::bindValue($cell, $value);
    }

    public function headings(): array
    {
        return array_keys(self::$widthMap);
    }

    public function columnWidths(): array
    {
        $result = [];
        $columns = array_keys(self::$widthMap);
        foreach ($columns as $index => $header) {
            $colLetter = Coordinate::stringFromColumnIndex($index + 1); // A=1, B=2...
            $result[$colLetter] = self::$widthMap[$header];
        }

        return $result;
    }
}
