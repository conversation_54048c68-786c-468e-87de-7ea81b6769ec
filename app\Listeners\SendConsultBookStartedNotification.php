<?php

namespace App\Listeners;

use App\Events\ConsultBookStartedEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultBookStartedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultBookStartedEvent $event
     * @return void
     */
    public function handle(ConsultBookStartedEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_CONSULT_BOOK_STARTED,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定用户
            $channel = GoEasyServiceProvider::getChannelForUser($consult->user_id);
            $notification = ConsultNotificationService::getConsultBookStartedMessage();
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to user failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            //推送给指定营养师
            $channel = GoEasyServiceProvider::getChannelForDietitian($consult->dietitian_id);
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            Log::info('Consult book started notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult book started notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
