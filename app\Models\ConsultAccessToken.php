<?php

namespace App\Models;

use App\Helpers\GoEasy;
use App\Helpers\RedisLock;
use App\Providers\GoEasyServiceProvider;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class ConsultAccessToken extends Model
{
    use HasFactory;

    protected $table = 'consults_access_tokens';

    protected $fillable = [
        'consult_id',
        'user_id',
        'role',
        'access_token',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取消息所属的咨询
     */
    public function consult()
    {
        return $this->belongsTo(Consult::class, 'consult_id');
    }

    /**
     * 获取消息关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public static function createAccessToken(array $data)
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $consultId = $data['id'] ?? 0;
            if ($data['role'] == 1) {
                if (!$consult = Consult::isOwner($consultId, $userId)) {
                    throw new Exception('用户无权限操作');
                }
                if ($consult->status == Consult::CONSULT_STATUS_WAITING) {
                    throw new Exception('未有營養師接入');
                }
                if ($consult->status != Consult::CONSULT_STATUS_ACCEPTED) {
                    throw new Exception('咨詢已結束');
                }
                $baseTime = $consult->first_ask_time ?: date('Y-m-d H:i:s');
                $timeLimit = Consult::CONSULT_CONVERSATION_TIME_LIMIT;
            } else {
                if (!$consult = Consult::getDetail($consultId)) {
                    throw new Exception('咨询不存在');
                }
                if ($data['role'] == 2 && $consult->dietitian_id > 0 && $consult->dietitian_id != $userId) {
                    throw new Exception('營養師无权限操作');
                }
                if ($consult->status != Consult::CONSULT_STATUS_WAITING && $consult->status != Consult::CONSULT_STATUS_ACCEPTED) {
                    throw new Exception('咨詢已結束');
                }
                $baseTime = $consult->accept_time;
                $timeLimit = Consult::CONSULT_AUTO_CLOSE_TIME_LIMIT + Consult::CONSULT_CONVERSATION_TIME_LIMIT;
            }
            $lock = RedisLock::lock($redis, 'CreateConsultAccessToken_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            $expire = strtotime('+' . $timeLimit . ' minutes', strtotime($baseTime));
            if ($expire < time()) {
                throw new Exception('咨詢時間已到期');
            }
            /**
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $connectId = GoEasyServiceProvider::getConnectForUser($userId);
            $groupId = GoEasyServiceProvider::getGroupForConsult($consultId);
            $data['access_token'] = $goEasy->pubSubAccessToken($connectId, $groupId, $expire);
            $result = self::create($data);
            $id = $result->id;
            $success = true;
            if ($success) {
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($data['user_id'] . '创建AccessToken失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'CreateConsultAccessToken_' . $userId);
        }
        return [$success, $record, $message];
    }

}
