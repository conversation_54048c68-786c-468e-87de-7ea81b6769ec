<?php

namespace App\Models;

use App\Events\FriendHandledEvent;
use App\Events\FriendRequestEvent;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;


/**
 * @mixin Builder
 */
class FriendRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_friends_requests';

    protected $fillable = [
        'from_user_id',
        'to_user_id',
        'remark',
        'status'
    ];


    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }


    /**
     * 发送好友申请
     */
    public static function sendRequest($fromUserId, $toUserId, $remark = null)
    {
        if ($fromUserId == $toUserId) {
            return [false, [], '不能添加自己为好友'];
        }

        DB::beginTransaction();

        try {
            // 检查是否已经是好友
            $exists = Friend::where('user_id', $fromUserId)
                ->where('friend_id', $toUserId)
                ->exists();

            if ($exists) {
                DB::rollBack();
                return [false, [], '你们已经是好友了'];
            }

            // 检查是否有待处理的申请
            $pendingRequest = self::where('from_user_id', $fromUserId)
                ->where('to_user_id', $toUserId)
                ->where('status', 0)
                ->exists();

            if ($pendingRequest) {
                DB::rollBack();
                return [false, [], '已发送过好友申请,请等待对方处理'];
            }

            // 创建好友申请
            $friendRequest = self::create([
                'from_user_id' => $fromUserId,
                'to_user_id' => $toUserId,
                'remark' => $remark,
                'status' => 0
            ]);
            $id = $friendRequest->id;

            if ($id > 0) {
                $success = true;
                $record = [];
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new \Exception('保存失败');
            }

            event(new FriendRequestEvent($fromUserId, $toUserId));

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    /**
     * 处理好友申请
     */
    public static function handleRequest($userId, $requestId, $action)
    {
        DB::beginTransaction();

        try {
            $friendRequest = self::where('id', $requestId)
                ->where('to_user_id', $userId)
                ->where('status', 0)
                ->first();
            if (!$friendRequest) {
                DB::rollBack();
                return [false, [], '申请不存在或已处理'];
            }

            if ($action == 'accept') {
                $fromUserId = $friendRequest->from_user_id;

                // 创建双向好友关系
                $friends = [
                    [
                        'user_id' => $userId,
                        'friend_id' => $fromUserId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ],
                    [
                        'user_id' => $fromUserId,
                        'friend_id' => $userId,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]
                ];
                Friend::insert($friends);

                $friendRequest->update(['status' => 1]);
                $message = '已添加好友';

                event(new FriendHandledEvent($friendRequest->from_user_id, $friendRequest->to_user_id, $action));
            } else {
                $friendRequest->update(['status' => 2]);
                $message = '已拒绝申请';
            }

            $success = true;
            $record = array();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

}
