<?php

namespace App\Models;

use App\Helpers\RedisLock;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Browse extends Model
{
    protected $fillable = ['user_id', 'good_id'];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    // 商品
    public function Good()
    {
        return $this->hasOne(Good::class, 'id', 'good_id');
    }

    public static function getList($search_data = array())
    {
        // 遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "updated_at";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";

        return self::when($keyword, function (Builder $query) use ($keyword) {
            $query->whereIn('good_id', function ($subQuery) use ($keyword) {
                $subQuery->select('id')->from('goods')
                    ->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->with([
                'Good' => function ($q) {
                    $q->select('id', 'order_price', 'name', 'image');
                }
            ])
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $lock = RedisLock::lock($redis, 'browse_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            // 浏览记录方式一：没有浏览记录的新增，有的更新
            $Browse = Browse::where('user_id', $userId)->where('good_id', $data['good_id'])->first();
            if (!$Browse) {
                $Browse = new Browse();
            } else {
                $Browse->updated_at = now()->toDateTimeString();
            }
            $Browse->user_id = $userId;
            $Browse->good_id = $data['good_id'];
            $success = $Browse->save();
            // 浏览记录方式二：所有的浏览记录都添加，用于后期的用户行为分析
            //            $Browse = new Browse();
            //            $Browse->user_id = $user_id;
            //            $Browse->good_id = $request->id;
            //            $Browse->save();
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $Browse->id;
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'browse_' . $userId);
        }
        return [$success, $record, $message];
    }
}
