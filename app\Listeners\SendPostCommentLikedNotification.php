<?php

namespace App\Listeners;

use App\Events\PostCommentLikedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPostCommentLikedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PostCommentLikedEvent $event
     * @return void
     */
    public function handle(PostCommentLikedEvent $event)
    {
        try {
            // 如果评论者和点赞者是同一人，不发送通知
            if ($event->commentOwnerId == $event->likerId) {
                return;
            }

            // 获取点赞者信息
            $liker = AppUser::find($event->likerId);

            if (!$liker) {
                Log::error('Comment liked notification failed: Liker not found', [
                    'liker_id' => $event->likerId,
                    'comment_id' => $event->commentId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->commentOwnerId, // 接收通知的用户ID（评论所有者）
                NotificationSetting::TYPE_COMMENT_LIKED, // 通知类型
                '评论点赞通知',
                "用户 {$liker->username} 点赞了您的评论",
                $event->likerId, // 发送者ID（点赞者）
                $event->commentId, // 相关ID（评论ID）
                NotificationSetting::RELATION_TYPE_COMMENT // 相关类型
            );

            Log::info('Comment liked notification sent', [
                'comment_id' => $event->commentId,
                'content_id' => $event->contentId,
                'owner_id' => $event->commentOwnerId,
                'liker_id' => $event->likerId
            ]);
        } catch (\Exception $e) {
            Log::error('Comment liked notification failed', [
                'error' => $e->getMessage(),
                'comment_id' => $event->commentId,
                'owner_id' => $event->commentOwnerId,
                'liker_id' => $event->likerId
            ]);
        }
    }
}
