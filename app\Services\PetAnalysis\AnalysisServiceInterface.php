<?php

namespace App\Services\PetAnalysis;

interface AnalysisServiceInterface
{
    /**
     * 执行分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位（用于健康分析）
     * @param array|null $symptoms 症状列表（用于健康分析）
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果或流式响应
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed;

    /**
     * 获取分析类型标识符
     *
     * @return string
     */
    public function getType(): string;

    /**
     * 获取分析服务名称
     *
     * @return string
     */
    public function getName(): string;

    /**
     * 获取分析服务描述
     *
     * @return string
     */
    public function getDescription(): string;
}
