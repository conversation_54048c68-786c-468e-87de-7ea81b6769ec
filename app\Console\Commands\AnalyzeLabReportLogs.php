<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;

/**
 * 实验室报告分析日志分析工具
 * 帮助开发者快速定位"图片未识别到完整的参数与数值"错误的具体原因
 */
class AnalyzeLabReportLogs extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'lab-report:analyze-logs
                            {--date= : 分析指定日期的日志 (Y-m-d 格式)}
                            {--hours=24 : 分析最近N小时的日志}
                            {--pet-id= : 分析特定宠物ID的日志}
                            {--failures-only : 只显示失败的分析}
                            {--summary : 显示统计摘要}';

    /**
     * 命令描述
     */
    protected $description = '分析实验室报告识别日志，帮助定位识别失败的具体原因';

    /**
     * 執行命令
     */
    public function handle()
    {
        $this->info('🔍 開始分析實驗室報告識別日誌...');

        $date = $this->option('date');
        $hours = $this->option('hours');
        $petId = $this->option('pet-id');
        $failuresOnly = $this->option('failures-only');
        $summary = $this->option('summary');

        // 獲取日誌文件路徑
        $logFiles = $this->getLogFiles($date, $hours);

        if (empty($logFiles)) {
            $this->error('❌ 未找到相關日誌文件');
            return 1;
        }

        $this->info("📁 找到 " . count($logFiles) . " 個日誌文件");

        // 分析日誌
        $analysisResults = $this->analyzeLogs($logFiles, $petId, $failuresOnly);

        // 顯示結果
        if ($summary) {
            $this->displaySummary($analysisResults);
        } else {
            $this->displayDetailedResults($analysisResults);
        }

        return 0;
    }

    /**
     * 获取日志文件列表
     */
    protected function getLogFiles(?string $date, int $hours): array
    {
        $logFiles = [];

        if ($date) {
            // 分析指定日期
            $targetDate = Carbon::createFromFormat('Y-m-d', $date);
            $logFile = storage_path("logs/laravel-{$targetDate->format('Y-m-d')}.log");
            if (file_exists($logFile)) {
                $logFiles[] = $logFile;
            }
        } else {
            // 分析最近N小时
            $endTime = now();
            $startTime = $endTime->copy()->subHours($hours);

            while ($startTime <= $endTime) {
                $logFile = storage_path("logs/laravel-{$startTime->format('Y-m-d')}.log");
                if (file_exists($logFile) && !in_array($logFile, $logFiles)) {
                    $logFiles[] = $logFile;
                }
                $startTime->addDay();
            }
        }

        return $logFiles;
    }

    /**
     * 分析日志内容
     */
    protected function analyzeLogs(array $logFiles, ?string $petId, bool $failuresOnly): array
    {
        $results = [
            'total_analyses' => 0,
            'successful_analyses' => 0,
            'failed_analyses' => 0,
            'failure_reasons' => [],
            'detailed_failures' => [],
            'performance_stats' => [],
            'cache_stats' => ['hits' => 0, 'misses' => 0]
        ];

        foreach ($logFiles as $logFile) {
            $this->info("📖 分析文件: " . basename($logFile));

            $content = file_get_contents($logFile);
            $lines = explode("\n", $content);

            foreach ($lines as $line) {
                if (empty(trim($line))) continue;

                // 解析日志行
                $logEntry = $this->parseLogLine($line);
                if (!$logEntry) continue;

                // 过滤宠物ID
                if ($petId && isset($logEntry['context']['pet_id']) && $logEntry['context']['pet_id'] != $petId) {
                    continue;
                }

                // 分析不同类型的日志
                $this->analyzeLogEntry($logEntry, $results, $failuresOnly);
            }
        }

        return $results;
    }

    /**
     * 解析單行日誌
     */
    protected function parseLogLine(string $line): ?array
    {
        // 匹配Laravel日誌格式
        $pattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \w+\.(\w+): (.+?) (\{.*\})?$/';

        if (preg_match($pattern, $line, $matches)) {
            $context = [];
            if (isset($matches[4]) && !empty($matches[4])) {
                $contextJson = json_decode($matches[4], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $context = $contextJson;
                }
            }

            return [
                'timestamp' => $matches[1],
                'level' => $matches[2],
                'message' => $matches[3],
                'context' => $context
            ];
        }

        return null;
    }

    /**
     * 分析單個日誌條目
     */
    protected function analyzeLogEntry(array $logEntry, array &$results, bool $failuresOnly): void
    {
        $message = $logEntry['message'];
        $context = $logEntry['context'];

        // 統計分析次數
        if (strpos($message, '✅ 實驗室報告分析成功完成') !== false) {
            $results['total_analyses']++;
            $results['successful_analyses']++;

            if (!$failuresOnly) {
                $results['performance_stats'][] = [
                    'timestamp' => $logEntry['timestamp'],
                    'pet_id' => $context['pet_id'] ?? null,
                    'processing_time' => $context['processing_time'] ?? null,
                    'quality_score' => $context['quality_score'] ?? null,
                    'total_indicators' => $context['total_indicators'] ?? null,
                    'organs_count' => $context['total_organs'] ?? null
                ];
            }
        }

        // 統計失敗分析
        if (strpos($message, '❌ 圖片識別失敗 - 數據不完整') !== false) {
            $results['total_analyses']++;
            $results['failed_analyses']++;

            $failureReasons = $context['failure_reasons'] ?? [];
            foreach ($failureReasons as $reason) {
                $results['failure_reasons'][$reason] = ($results['failure_reasons'][$reason] ?? 0) + 1;
            }

            $results['detailed_failures'][] = [
                'timestamp' => $logEntry['timestamp'],
                'pet_id' => $context['pet_id'] ?? null,
                'failure_reasons' => $failureReasons,
                'debug_info' => $context['debug_info'] ?? null
            ];
        }

        // 統計JSON解析失敗
        if (strpos($message, '❌ JSON解析完全失敗') !== false) {
            $results['total_analyses']++;
            $results['failed_analyses']++;

            $results['failure_reasons']['json_parse_failure'] = ($results['failure_reasons']['json_parse_failure'] ?? 0) + 1;

            $results['detailed_failures'][] = [
                'timestamp' => $logEntry['timestamp'],
                'pet_id' => $context['pet_id'] ?? null,
                'failure_reasons' => ['json_parse_failure'],
                'debug_info' => [
                    'content_length' => $context['content_length'] ?? null,
                    'has_json_structure' => $context['has_json_structure'] ?? null,
                    'reason' => $context['reason'] ?? null
                ]
            ];
        }

        // 統計緩存命中率
        if (strpos($message, '🚀 緩存命中') !== false) {
            $results['cache_stats']['hits']++;
        }
        if (strpos($message, '分析完成並緩存') !== false) {
            $results['cache_stats']['misses']++;
        }
    }

    /**
     * 显示统计摘要
     */
    protected function displaySummary(array $results): void
    {
        $this->info("\n📊 实验室报告分析统计摘要");
        $this->info("=" . str_repeat("=", 50));

        $this->table([
            '指标', '数值'
        ], [
            ['总分析次数', $results['total_analyses']],
            ['成功分析', $results['successful_analyses']],
            ['失败分析', $results['failed_analyses']],
            ['成功率', $results['total_analyses'] > 0 ? round($results['successful_analyses'] / $results['total_analyses'] * 100, 2) . '%' : '0%'],
            ['缓存命中', $results['cache_stats']['hits']],
            ['缓存未命中', $results['cache_stats']['misses']],
            ['缓存命中率', ($results['cache_stats']['hits'] + $results['cache_stats']['misses']) > 0 ?
                round($results['cache_stats']['hits'] / ($results['cache_stats']['hits'] + $results['cache_stats']['misses']) * 100, 2) . '%' : '0%']
        ]);

        if (!empty($results['failure_reasons'])) {
            $this->info("\n🚨 失败原因统计:");
            $failureTable = [];
            foreach ($results['failure_reasons'] as $reason => $count) {
                $failureTable[] = [$reason, $count];
            }
            $this->table(['失败原因', '次数'], $failureTable);
        }
    }

    /**
     * 显示详细结果
     */
    protected function displayDetailedResults(array $results): void
    {
        $this->displaySummary($results);

        if (!empty($results['detailed_failures'])) {
            $this->info("\n🔍 详细失败分析:");
            $this->info("-" . str_repeat("-", 80));

            foreach ($results['detailed_failures'] as $failure) {
                $this->warn("\n⏰ 时间: " . $failure['timestamp']);
                $this->warn("🐾 宠物ID: " . ($failure['pet_id'] ?? '未知'));
                $this->warn("❌ 失败原因: " . implode(', ', $failure['failure_reasons']));

                if (!empty($failure['debug_info'])) {
                    $this->info("🔧 调试信息:");
                    foreach ($failure['debug_info'] as $key => $value) {
                        if (is_array($value)) {
                            $this->info("  - {$key}: " . json_encode($value, JSON_UNESCAPED_UNICODE));
                        } else {
                            $this->info("  - {$key}: {$value}");
                        }
                    }
                }
            }
        }

        if (!empty($results['performance_stats'])) {
            $this->info("\n⚡ 性能统计 (最近10次成功分析):");
            $recentStats = array_slice($results['performance_stats'], -10);

            $perfTable = [];
            foreach ($recentStats as $stat) {
                $perfTable[] = [
                    $stat['timestamp'],
                    $stat['pet_id'] ?? 'N/A',
                    $stat['processing_time'] ?? 'N/A',
                    $stat['quality_score'] ?? 'N/A',
                    $stat['total_indicators'] ?? 'N/A',
                    $stat['organs_count'] ?? 'N/A'
                ];
            }

            $this->table([
                '时间', '宠物ID', '处理时间(s)', '质量分数', '指标数', '器官数'
            ], $perfTable);
        }
    }
}
