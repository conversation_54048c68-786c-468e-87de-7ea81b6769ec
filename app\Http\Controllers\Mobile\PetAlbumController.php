<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\PetAlbum;
use App\Models\Pet;
use App\Models\PetAlbumLike;
use App\Models\PetMaster;

use Illuminate\Http\Request;

/**
 * 宠物相册管理
 */
class PetAlbumController extends Controller
{
    /**
     * 保存相册记录（前端先通过共用上传接口上传图片，再调用此接口保存相册）
     */
    public function save(Request $request)
    {
        // 验证规则 - 只验证相册保存相关参数
        $rules = [
            'description' => 'nullable|string|max:255',
            'pet_ids'     => 'nullable|string|max:255|regex:/^[0-9,]+$/', // 字符串格式，如"4,5,6"
            'images'      => 'required|array',
            'images.*'    => 'required|url|pic|trust_host',
        ];
        $request->validate($rules);
        $userId = $request->user()->id;
        $description = $request->input('description');
        $petIds = $request->input('pet_ids'); // 字符串格式，如"4,5,6"
        $images = $request->input('images'); // 已上传的图片URL数组
        // 验证用户对所有宠物的权限（如果指定了宠物）
        if (!empty($petIds)) {
            $petIdsArray = explode(',', $petIds);
            foreach ($petIdsArray as $petId) {
                if (!is_numeric($petId) || $petId <= 0) {
                    return ResponseHelper::error("无效的宠物ID: {$petId}");
                }
                if (!PetMaster::isMaster($petId, $userId)) {
                    return ResponseHelper::error("无权限为宠物ID {$petId} 上传照片");
                }
            }
        }
        // 计算宠物数量
        $petCount = !empty($petIds) ? count(explode(',', $petIds)) : 0;
        // 创建一条相册记录包含所有信息
        $albumData = [
            'user_id'     => $userId,
            'pet_ids'     => $petIds, // 直接使用字符串格式
            'images'      => $images,
            'description' => $description,
            'status'      => 1,
        ];
        [$success, $album, $message] = PetAlbum::saveMultiAlbum($albumData);
        if (!$success) {
            return ResponseHelper::error("相册记录保存失败：" . $message);
        }
        return ResponseHelper::success([
            'album'      => $album,
            'statistics' => [
                'total_images'  => count($images),
                'total_pets'    => $petCount,
                'album_records' => 1,
            ],
            'pet_ids'    => $petIds,
            'images'     => $images,
        ], "保存成功，" . count($images) . "张图片关联到" . $petCount . "只宠物");
    }


    /**
     * 获取相册列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'           => 'sometimes|integer|gt:0',
            'per_page'       => 'sometimes|integer|between:1,100',
            'pet_id'         => 'nullable|string|max:255|regex:/^[0-9,]+$/', // 支持多个宠物ID，逗号分隔格式
            'is_no_pet_ids'  => 'nullable|integer|in:0,1', // 1=筛选没有宠物ID的相册
            'is_post_source' => 'nullable|integer|in:0,1', // 0=非帖子来源, 1=帖子来源
            'userid'         => 'nullable|integer|gt:0', // 按用户ID筛选相册
        ]);

        $formData = $request->all();
        $userId = $request->user()->id;
        //处理冲突11
        // 处理pet_id参数，支持多个宠物ID筛选
        if (!empty($formData['pet_id'])) {
            // 解析逗号分隔的宠物ID字符串
            $petIdsString = trim($formData['pet_id']);
            $petIdsArray = array_filter(array_map('trim', explode(',', $petIdsString)));

            // 验证所有宠物ID的权限
            foreach ($petIdsArray as $petId) {
                if (!is_numeric($petId) || $petId <= 0) {
                    return ResponseHelper::error("无效的宠物ID: {$petId}");
                }
                if (!PetMaster::isMaster($petId, $userId)) {
                    return ResponseHelper::error("无权限查看宠物ID {$petId} 的相册");
                }
            }

            // 将处理后的宠物ID数组传递给模型
            $formData['pet_id'] = $petIdsArray;
            $formData['strict_filter'] = true; // 启用严格筛选模式，不包含通用相册
        } else {
            // 无论是否指定userid，都返回当前用户和其家人的相册
            $familyUserIds = PetMaster::getMyFamilyUserIds($userId, true); // 包含自己

            // 如果指定了userid，检查该用户是否是家人
            if (!empty($formData['userid'])) {
                $targetUserId = $formData['userid'];
                if (!in_array($targetUserId, $familyUserIds)) {
                    return ResponseHelper::error("无权限查看该用户的相册");
                }
            }

            $formData['family_user_ids'] = $familyUserIds;
        }

        $records = PetAlbum::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    /**
     * 批量编辑相册记录（对象数组格式）
     */
    public function edit(Request $request)
    {
        $request->validate([
            'albums'               => 'required|array|min:1|max:50', // 相册对象数组，最多50条
            'albums.*.id'          => 'required|integer|gt:0',
            'albums.*.images'      => 'required|array|min:1|max:30',
            'albums.*.images.*'    => 'required|string|max:500|url|pic|trust_host',
            'albums.*.pet_ids'     => 'required|array|max:20',
            'albums.*.pet_ids.*'   => 'integer|gt:0',
            'albums.*.description' => 'nullable|string|max:255',
            'albums.*.category'    => 'nullable|string|max:50',
        ]);

        $userId = $request->user()->id;
        $albumsData = $request->input('albums');

        // 验证所有宠物权限
        foreach ($albumsData as $albumData) {
            if (!empty($albumData['pet_ids'])) {
                foreach ($albumData['pet_ids'] as $petId) {
                    if (!is_numeric($petId) || $petId <= 0) {
                        return ResponseHelper::error("无效的宠物ID: {$petId}");
                    }
                    if (!PetMaster::isMaster($petId, $userId)) {
                        return ResponseHelper::error("无权限为宠物ID {$petId} 编辑相册");
                    }
                }
            }
        }
        [$success, $data, $message] = PetAlbum::batchEditAlbums($albumsData, $userId);
        return ResponseHelper::result($success, $data, $message);
    }

    /**
     * 删除相册图片
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $userId = $request->user()->id;
        $id = $request->input('id');

        [$success, $data, $message] = PetAlbum::deleteAlbum($id, $userId);
        return ResponseHelper::result($success, $data, $message);
    }

    public function likeAlbum(Request $request)
    {
        $request->validate([
            'album_id' => 'required|integer|gt:0',
        ]);
        $result = PetAlbumLike::likeAlbum($request->user()->id, $request->album_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelLikeAlbum(Request $request)
    {
        $request->validate([
            'album_id' => 'required|integer|gt:0',
        ]);
        $result = PetAlbumLike::cancelLikeAlbum($request->user()->id, $request->album_id);
        return ResponseHelper::result(...$result);
    }
}
