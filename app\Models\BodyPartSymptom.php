<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BodyPartSymptom extends Model
{
    use HasFactory;

    protected $fillable = [
        'body_part_id', 'code', 'name', 'description', 'icon', 'pet_type', 'sort_order', 'status'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取关联的身体部位
     */
    public function bodyPart()
    {
        return $this->belongsTo(AnalysisBodyPart::class, 'body_part_id');
    }
}
