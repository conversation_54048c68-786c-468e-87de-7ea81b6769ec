<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class OnlineRefresh
{
    public function handle(Request $request, Closure $next, string $guard)
    {
        if ($user = $request->user()) {
            self::addMember($user->id, $guard);
        }
        return $next($request);
    }

    public static function addMember($user_id, string $guard)
    {
        return redis()->zAdd(self::getRedisKey($guard), time(), $user_id);
    }

    public static function removeMember($user_id, string $guard)
    {
        return redis()->zRem(self::getRedisKey($guard), $user_id);
    }

    public static function getOnlineMembers(string $guard, $valid_minutes = 15)
    {
        $end_time = time();
        $start_time = $end_time - $valid_minutes * 60;
        return redis()->zRangeByScore(self::getRedisKey($guard), $start_time, $end_time);
    }

    public static function getRedisKey(string $guard)
    {
        return "online_{$guard}";
    }

    public static function hangUpMember($user_id, string $guard)
    {
        return redis()->zAdd(self::getRedisKey($guard), time() * -1, $user_id);
    }

    public static function getHangupMembers(string $guard, $valid_minutes = 15)
    {
        $end_time = time() * -1;
        $start_time = $end_time + $valid_minutes * 60;
        return redis()->zRangeByScore(self::getRedisKey($guard), $end_time, $start_time);
    }
}
