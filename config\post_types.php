<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 帖子类型配置
    |--------------------------------------------------------------------------
    |
    | 定义系统支持的帖子类型及其特性
    |
    */

    'types' => [
        1 => [
            'name' => '正常帖子',
            'description' => '一般性分享内容，必须关联宠物，title和content必须填写',
            'require_pets' => true,           // 必须关联宠物
            'auto_save_to_album' => true,     // 自动保存图片到相册
            'default_visibility' => 'public',
            'allowed_visibility' => ['public', 'friends', 'family', 'private'],
            'validation_rules' => [
                'pet_ids' => 'required|number_combine',
                'title' => 'required|string',      // 必须填写
                'content' => 'required|string',    // 必须填写
                'images' => 'nullable|array',
            ],
            'min_content_requirement' => true, // title和content都必须填写
        ],

        2 => [
            'name' => '日常贴文',
            'description' => '记录日常生活的帖子，无须关联宠物，title和content可以为空',
            'require_pets' => false,          // 无须关联宠物
            'auto_save_to_album' => 'conditional', // 条件保存：关联宠物时才保存
            'default_visibility' => 'public', // 默认可见性
            'allowed_visibility' => ['public', 'friends', 'family', 'private'], // 允许的可见性
            'validation_rules' => [
                'pet_ids' => 'nullable|number_combine',
                'title' => 'nullable|string',      // 可以为空
                'content' => 'nullable|string',    // 可以为空
                'images' => 'nullable|array',
            ],
            'min_content_requirement' => false, // title和content都可以为空
        ],

        3 => [
            'name' => '预留类型',
            'description' => '预留的帖子类型，暂未使用',
            'require_pets' => false,
            'auto_save_to_album' => false,
            'default_visibility' => 'public',
            'allowed_visibility' => ['public', 'friends', 'family', 'private'],
            'validation_rules' => [
                'pet_ids' => 'nullable|number_combine',
                'title' => 'nullable|string',
                'content' => 'nullable|string',
                'images' => 'nullable|array',
            ],
            'min_content_requirement' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 首页展示模式配置
    |--------------------------------------------------------------------------
    |
    | 定义统一首页接口支持的展示模式
    |
    */

    'feed_modes' => [
        'public' => [
            'name' => '公共社群',
            'description' => '展示所有公开帖子',
            'visibility_filter' => ['public'],
            'exclude_self' => true,
            'include_family' => false,
            'include_following' => false,
        ],

        'similar' => [
            'name' => '同种社群',
            'description' => '基于宠物种类推荐相似宠物的帖子',
            'visibility_filter' => ['public'],
            'exclude_self' => true,
            'pet_type_filter' => true,
            'include_family' => false,
            'include_following' => false,
        ],

        'family' => [
            'name' => '家人动态',
            'description' => '仅展示家人的帖子（共同宠物主人）',
            'visibility_filter' => ['public', 'family'],
            'exclude_self' => false,
            'include_family' => true,
            'include_following' => false,
        ],

        'friends' => [
            'name' => '朋友动态',
            'description' => '仅展示关注用户的帖子',
            'visibility_filter' => ['public', 'friends'],
            'exclude_self' => false,
            'include_family' => false,
            'include_following' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 相册保存策略配置
    |--------------------------------------------------------------------------
    |
    | 定义不同帖子类型的相册保存策略
    |
    */

    'album_save_strategy' => [
        1 => [
            'enabled' => true,
            'condition' => 'always', // 总是保存
            'description_template' => '{title}', // 使用帖子标题作为相册描述
            'fallback_description' => '来自正常帖子的图片',
        ],

        2 => [
            'enabled' => true,
            'condition' => 'has_pets', // 只有关联宠物时才保存
            'description_template' => '{title}',
            'fallback_description' => '来自日常贴文的图片',
        ],

        3 => [
            'enabled' => false,
            'condition' => 'never',
            'description_template' => '{title}',
            'fallback_description' => '预留类型图片',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 验证规则配置
    |--------------------------------------------------------------------------
    |
    | 定义不同场景下的验证规则
    |
    */

    'validation' => [
        1 => [
            'pet_ids_required' => true,      // 必须关联宠物
            'title_required' => true,        // title必须填写
            'content_required' => true,      // content必须填写
            'follow_standard_rules' => true, // 遵循标准验证规则
        ],

        2 => [
            'pet_ids_required' => false,     // 无须关联宠物
            'title_required' => false,       // title可以为空
            'content_required' => false,     // content可以为空
            'min_content_fields' => 0,       // 不强制要求内容
        ],

        3 => [
            'pet_ids_required' => false,
            'follow_standard_rules' => true,
        ],

        'private_record' => [
            'min_content_fields' => 1,
            'content_fields' => ['title', 'content', 'images'],
            'error_message' => '私人记录至少需要填写标题、内容或上传图片中的一项',
        ],

        'draft_content' => [
            'min_content_fields' => 1,
            'content_fields' => ['title', 'content', 'images'],
            'error_message' => '草稿至少需要填写标题、内容或上传图片中的一项',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 性能优化配置
    |--------------------------------------------------------------------------
    |
    | 定义查询优化和缓存策略
    |
    */

    'performance' => [
        'cache' => [
            'enabled' => true,
            'ttl' => 300, // 5分钟缓存
            'keys' => [
                'pet_type_filter' => 'pet_type_filter:{user_id}:{pet_ids}',
                'family_users' => 'family_users:{user_id}',
                'following_users' => 'following_users:{user_id}',
            ],
        ],

        'pagination' => [
            'default_per_page' => 15,
            'max_per_page' => 200,
        ],

        'query_optimization' => [
            'use_indexes' => true,
            'eager_load_relations' => ['user', 'pets', 'tags'],
            'select_fields' => [
                'app_contents.id',
                'app_contents.user_id',
                'app_contents.title',
                'app_contents.content',
                'app_contents.images',
                'app_contents.visibility',
                'app_contents.post_type',
                'app_contents.status',
                'app_contents.created_at',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | 错误消息配置
    |--------------------------------------------------------------------------
    |
    | 定义各种验证错误的消息
    |
    */

    'error_messages' => [
        'daily_post_requires_pets' => '日常貼文必須關聯至少一隻寵物',
        'daily_post_requires_content' => '日常貼文至少需要填寫標題、內容或上傳圖片中的一項',
        'invalid_post_type' => '無效的帖子類型',
        'invalid_feed_type' => '無效的展示模式',
        'pet_not_owned' => '寵物不屬於您，無法關聯',
        'private_record_content_required' => '私人記錄至少需要填寫標題、內容或上傳圖片中的一項',
        'draft_content_required' => '草稿至少需要填寫標題、內容或上傳圖片中的一項',
    ],

    /*
    |--------------------------------------------------------------------------
    | 功能开关配置
    |--------------------------------------------------------------------------
    |
    | 控制各种功能的开启和关闭
    |
    */

    'features' => [
        'unified_feed' => true,           // 统一首页接口
        'post_type_selection' => true,   // 帖子类型选择
        'auto_album_save' => true,       // 自动相册保存
        'pet_type_recommendation' => true, // 宠物种类推荐
        'mention_notification' => true,   // @提及通知
        'backward_compatibility' => true, // 向后兼容性
    ],
];
