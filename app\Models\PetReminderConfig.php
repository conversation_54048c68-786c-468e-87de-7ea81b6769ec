<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetReminderConfig extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_reminders_configs';

    protected $fillable = [
        'type',
        'messages',
        'messages_tc',
        'messages_en',
        'is_tutorial',
        'details',
        'details_tc',
        'details_en',
        'sort_order',
    ];

    protected $casts = [
        'messages'    => 'array',
        'messages_tc' => 'array',
        'messages_en' => 'array',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getList($search_data = array())
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $type = $search_data['type'] ?? "";
        $isTutorial = $search_data['is_tutorial'] ?? "";

        return self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {

            });
        })
            ->when($type, function (Builder $query) use ($type) {
                $query->where('type', $type);
            })
            ->when($isTutorial, function (Builder $query) use ($isTutorial) {
                $query->where('is_tutorial', $isTutorial);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)->first();
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('type', $data['type'])->where('id', '!=', $id)->exists()) {
                throw new Exception('寵物提醒配置已存在');
            }
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "寵物提醒：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "寵物提醒：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function getAll()
    {
        //todo 缓存
        return self::all()->keyBy('type')->toArray();
    }
}
