<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppContent;
use App\Models\AppContentLike;
use App\Models\AppContentTag;
use App\Models\AppShareTag;
use App\Models\AppUser;
use App\Models\AppUserFollow;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Models\PetMaster;
use App\Models\PetReminder;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AppContentController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
        ]);

        $params = $request->all();
        $params['status'] = 1;
        $params['_index'] = 1;

        $user = Auth::guard('api')->user();
        $records = AppContent::getList($params, $user ? $user->id : -1);
        return ResponseHelper::success($records);
    }

    public function stranger(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
            'pet_id'   => 'sometimes|string', // 支持字符串格式：单个数字或逗号分隔
        ]);

        $params = $request->all();
        $params['status'] = 1;
        $params['_stranger'] = 1;

        // 如果传入了宠物ID，获取这些宠物的种类，然后筛选相同种类的帖子
        if (!empty($params['pet_id'])) {
            // 处理字符串格式的pet_id：支持单个ID或逗号分隔的多个ID
            if (strpos($params['pet_id'], ',') !== false) {
                // 逗号分隔格式：'1,2,3'
                $petIds = explode(',', $params['pet_id']);
                $petIds = array_map('trim', $petIds); // 去除空格
                $petIds = array_filter($petIds, 'is_numeric'); // 只保留数字
                $petIds = array_map('intval', $petIds); // 转换为整数
            } else {
                // 单个数字格式：'1'
                $petIds = [intval($params['pet_id'])];
            }

            // 过滤掉无效的ID（小于等于0的）
            $petIds = array_filter($petIds, function($id) {
                return $id > 0;
            });

            if (!empty($petIds)) {
                // 获取所有指定宠物的种类ID
                $pets = Pet::with('petType')->whereIn('id', $petIds)->get();
                $petTypeIds = [];
                $petTypeNames = [];

                foreach ($pets as $pet) {
                    if ($pet->petType) {
                        $petTypeIds[] = $pet->petType->id;
                        $petTypeNames[] = $pet->petType->name;
                    }
                }

                if (!empty($petTypeIds)) {
                    // 去重并传递给getList方法
                    $params['_pet_type_filter'] = array_unique($petTypeIds);
                    $params['_pet_type_names'] = array_unique($petTypeNames);
                }
            }

            // 移除pet_id参数，避免影响原有的宠物筛选逻辑
            unset($params['pet_id']);
        }

        $records = AppContent::getList($params, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function nearby(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'longitude' => 'required|numeric',
            'latitude'  => 'required|numeric',
        ]);

        $params = $request->all();
        $params['status'] = 1;
        $params['_nearby'] = 1;
        $nearbyContents = AppContent::getList($params, $request->user()->id);

        return response()->json($nearbyContents);
    }

    /**
     * 统一首页接口 - 支持四种展示模式
     */
    public function unifiedFeed(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'feed_type' => 'required|in:public,similar,family,friends,', // 展示模式：public=公共社群,similar=同种社群,family=家人动态,friends=朋友动态
            'pet_ids'   => 'sometimes|string', // 支持多只宠物ID，逗号分隔或单个ID，所有模式下均可使用
        ]);

        $params = $request->all();
        $params['status'] = 1;
        $user = Auth::guard('api')->user();
        $userId = $user ? $user->id : 0; // 未登录用户ID为0

        // 处理宠物ID筛选（所有模式通用）
        if (!empty($params['pet_ids'])) {
            $this->processPetIdsFilter($params);
        }

        switch ($params['feed_type']) {
            case 'public':
                // 公共社群：展示所有公开帖子（排除自己的）
                $params['_public_feed'] = 1;
                break;

            case 'similar':
                // 同种社群：基于宠物种类推荐相似宠物的帖子
                $params['_similar_feed'] = 1;
                $this->processSimilarFeedParams($params, $user);
                PetReminder::markMemoRead($userId, PetReminder::TYPE_TUTORIAL_PET_FRIEND_CIRCLE);
                break;

            case 'family':
                // 家人动态：仅展示家人的帖子（共同宠物主人）
                if (!$user) {
                    return ResponseHelper::error('查看家人动态需要登录', 401);
                }
                $params['_family_feed'] = 1;
                PetReminder::markMemoRead($userId, PetReminder::TYPE_TUTORIAL_FAMILY_CIRCLE);
                break;

            case 'friends':
                // 朋友动态：仅展示关注用户的帖子（排除家人）
                if (!$user) {
                    return ResponseHelper::error('查看朋友动态需要登录', 401);
                }
                $params['_friends_feed'] = 1;
                break;
        }

        $records = AppContent::getList($params, $userId);
        return ResponseHelper::success($records);
    }

    public function getList(Request $request)
    {
        $request->validate([
            'page'            => 'sometimes|integer|gt:0',
            'per_page'        => 'sometimes|integer|between:1,200',
            'keyword'         => 'nullable|string',
            'sort_name'       => 'nullable|in:id,created_at',
            'sort_by'         => 'nullable|in:asc,desc',
            'status'          => 'required|in:0,1',
            'visibility'      => 'nullable|in:public,friends,family,private',
            'pet_id'          => 'nullable|integer',  //查看某个宠物动态，或者转让宠物时查到发过的帖子-用于选择初始图片
            'user_id'         => 'nullable|integer',
            'good_id'         => 'nullable|integer',
        ]);

        $params = $request->all();
        if ($params['status'] == 0) {
            $params['user_id'] = $request->user()->id; //只能查看自己写的草稿
        }

        $records = AppContent::getList($params, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function save(Request $request)
    {
        // 基础验证规则
        $rules = [
            'id'                        => 'nullable|integer',
            'status'                    => 'required|in:0,1',  //状态:0=草稿,1=已发布
            'visibility'                => 'required|in:public,friends,family,private', //可见性权限
            'pet_ids'                   => 'nullable|number_combine',
            'good_indent_commodity_ids' => 'nullable|number_combine',
            'tag_ids'                   => 'nullable|number_combine',
            'mention_user_ids'          => 'nullable|array', // @提及的用户ID数组
            'mention_user_ids.*'        => 'integer|gt:0',
            'images'                    => 'nullable|array',
            'images.*'                  => 'nullable|url|pic|trust_host',
            'longitude'                 => 'nullable|numeric',
            'latitude'                  => 'nullable|numeric',
        ];

        // 根据状态和可见性动态调整验证规则
        $status = $request->input('status');
        $visibility = $request->input('visibility');

        if ($status == 1) {
            // 正式发布时的验证规则
            if ($visibility === 'private') {
                // 私人记录：至少需要标题、内容或图片中的一项
                $rules['title'] = 'nullable|string';
                $rules['content'] = 'nullable|string';
            } else {
                // 普通帖子：title和content都不能为空
                $rules['title'] = 'required|string';
                $rules['content'] = 'required|string';
            }
        } else {
            // 草稿时，title和content都可以为空
            $rules['title'] = 'nullable|string';
            $rules['content'] = 'nullable|string';
        }

        // 执行基础验证
        $request->validate($rules);

        // 执行特殊场景的自定义验证
        $this->validateSpecialScenarios($request, $status, $visibility);
        $data = $request->all(); // 获取所有请求数据
        $user = $request->user();
        if (empty($data['longitude']) && $user && $user->longitude > 0) {
            $data['longitude'] = $user->longitude;
        }
        if (empty($data['latitude']) && $user && $user->latitude > 0) {
            $data['latitude'] = $user->latitude;
        }

        // 保存分享逻辑
        list($success, $record, $message) = AppContent::saveDetail($data, $user->id);
        if (!$success) {
            return ResponseHelper::error($message);
        }

        // 处理@提及功能（仅在正式发布时）
        if ($status == 1 && !empty($data['mention_user_ids'])) {
            $contentId = $record['id'];
            $userId = $user->id;
            $mentionUserIds = $data['mention_user_ids'];

            // 验证被@的用户是否为家人或关注用户
            $validMentionUserIds = $this->validateMentionUsers($userId, $mentionUserIds);

            if (!empty($validMentionUserIds)) {
                // 发送@提及通知
                $this->sendContentMentionNotifications($validMentionUserIds, $userId, $contentId);
            }
        }

        // 图片自动存储到相册（仅在正式发布且有图片时）
        if ($status == 1 && !empty($data['images'])) {
            AppContent::saveImagesToAlbum($data, $record['id'], $user->id);
        }

        // 返回分享和标记的 ID
        return ResponseHelper::result(true, [
            'content_id' => $record['id'] ?? null,
        ], '保存成功');
    }

    /**
     * 新版发帖接口 - 支持帖子类型选择
     */
    public function saveWithType(Request $request)
    {
        // 基础验证规则
        $rules = [
            'id'                        => 'nullable|integer',
            'status'                    => 'required|in:0,1',  //状态:0=草稿,1=已发布
            'visibility'                => 'required|in:public,friends,family,private', //可见性权限
            'post_type'                 => 'nullable|in:1,2', //帖子类型:1=动态,2=日常
            'pet_ids'                   => 'nullable|number_combine',
            'good_indent_commodity_ids' => 'nullable|number_combine',
            'tag_ids'                   => 'nullable|number_combine',
            'mention_user_ids'          => 'nullable|array', // @提及的用户ID数组
            'mention_user_ids.*'        => 'integer|gt:0',
            'images'                    => 'nullable|array',
            'images.*'                  => 'nullable|url|pic|trust_host',
            'longitude'                 => 'nullable|numeric',
            'latitude'                  => 'nullable|numeric',
        ];

        // 根据帖子类型添加特殊验证规则
        $postType = $request->input('post_type', 2); // 默认为正常帖子


        // 根据状态和可见性动态调整验证规则
        $status = $request->input('status');
        $visibility = $request->input('visibility');

        if ($status == 1) {
            // 正式发布时的验证规则
            if ($postType == 1) {
                // 动态：title和content可以为空
                $rules['title'] = 'required|string';
                $rules['content'] = 'nullable|string';
            } else {
                // 日常：title和content必须填写
                $rules['title'] = 'nullable|string';
                $rules['content'] = 'nullable|string';
            }
        } else {
            // 草稿时，title和content都可以为空
            $rules['title'] = 'nullable|string';
            $rules['content'] = 'nullable|string';
        }

        // 执行基础验证
        $request->validate($rules);

        // 执行特殊场景的自定义验证
        $this->validateSpecialScenariosWithType($request, $status, $visibility, $postType);
        $data = $request->all(); // 获取所有请求数据

        // 确保post_type有默认值
        if (empty($data['post_type'])) {
            $data['post_type'] = 1; // 默认为正常帖子
        }
        $user = $request->user();
        if (empty($data['longitude']) && $user && $user->longitude > 0) {
            $data['longitude'] = $user->longitude;
        }
        if (empty($data['latitude']) && $user && $user->latitude > 0) {
            $data['latitude'] = $user->latitude;
        }

        // 保存分享逻辑
        list($success, $record, $message) = AppContent::saveDetail($data, $user->id);
        if (!$success) {
            return ResponseHelper::error($message);
        }

        // 处理@提及功能（仅在正式发布时）
        if ($status == 1 && !empty($data['mention_user_ids'])) {
            $contentId = $record['id'];
            $userId = $user->id;
            $mentionUserIds = $data['mention_user_ids'];

            // 验证被@的用户是否为家人或关注用户
            $validMentionUserIds = $this->validateMentionUsers($userId, $mentionUserIds);

            if (!empty($validMentionUserIds)) {
                // 发送@提及通知
                $this->sendContentMentionNotifications($validMentionUserIds, $userId, $contentId);
            }
        }

        // 根据帖子类型决定是否保存图片到相册
        if ($status == 1 && !empty($data['images'])) {
            $this->handleImageSaveToAlbum($data, $record['id'], $user->id, $postType);
        }

        // 返回分享和标记的 ID
        return ResponseHelper::result(true, [
            'content_id' => $record['id'] ?? null,
        ], '保存成功');
    }

    public function detail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $user = Auth::guard('api')->user();
        $result = AppContent::getDetailForApp($request->input('id'), $user ? $user->id : -1);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = AppContent::delForApp($request->input('id'), $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    public function likeContent(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentLike::likeContent($request->user()->id, $request->content_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelLikeContent(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentLike::cancelLikeContent($request->user()->id, $request->content_id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 获取标签列表
     */
    public function getTags(Request $request)
    {
        $request->validate([
            'limit' => 'sometimes|integer|between:1,50',
        ]);

        $limit = $request->input('limit', 20);
        $tags = AppShareTag::getPopularTags($limit);

        return ResponseHelper::success($tags);
    }



    /**
     * 根据标签获取相关内容推荐
     */
    public function getRelatedContentsByTags(Request $request)
    {
        $request->validate([
            'tag_ids' => 'required|string',  // 标签ID，逗号分隔，如："1,2,3"
            'limit'   => 'sometimes|integer|between:1,20',
        ]);

        $tagIdsString = $request->input('tag_ids');
        $limit = $request->input('limit', 10);
        $user = Auth::guard('api')->user();

        // 解析标签ID
        $tagIds = array_filter(explode(',', $tagIdsString));
        if (empty($tagIds)) {
            return ResponseHelper::success([]);
        }

        // 验证标签是否存在
        $validTagIds = AppShareTag::whereIn('id', $tagIds)
            ->where('status', 1)
            ->pluck('id')
            ->toArray();

        if (empty($validTagIds)) {
            return ResponseHelper::success([]);
        }

        // 根据标签获取相关分享内容
        $relatedContents = AppContentTag::getContentsByTags($validTagIds, null, $limit);

        // 处理返回数据
        $result = $relatedContents->map(function ($content) use ($user) {
            $item = $content->toArray();

            // 处理图片数组
            $item['images'] = $content->images ? array_filter(explode(',', $content->images)) : [];

            // 添加标签信息
            $item['tags'] = $content->tags->map(function($tag) {
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                    'color' => $tag->color
                ];
            });

            if ($user) {
                AppContent::renderTimeAgo($item);
                AppContent::renderRealtimeDetail($item, $user->id);
            }

            return $item;
        });

        return ResponseHelper::success($result);
    }

    /**
     * 获取可@提及的用户列表（家人+关注用户）
     */
    public function getMentionableUsers(Request $request)
    {
        $userId = $request->user()->id;
        $mentionableUsers = [];

        // 获取家人列表
        $pets = Pet::where('owner_id', $userId)->get(['id', 'name']);
        foreach ($pets as $pet) {
            $masters = PetMaster::with(['user:id,username,avatar'])
                ->where('pet_id', $pet->id)
                ->whereNull('deleted_at')
                ->where('user_id', '!=', $userId)
                ->get();

            foreach ($masters as $master) {
                if ($master->user) {
                    $mentionableUsers[$master->user->id] = [
                        'id' => $master->user->id,
                        'username' => $master->user->username,
                        'avatar' => $master->user->avatar,
                        'type' => 'family',
                        'relation' => '家人(' . $pet->name . ')',
                    ];
                }
            }
        }

        // 获取关注用户列表
        AppUserFollow::ensureInitRedis($userId);
        $cacheFollowingOfMine = "app_user:{$userId}:following";
        $followingUserIds = redis()->zRevRange($cacheFollowingOfMine, 0, 99); // 最多100个

        if (!empty($followingUserIds)) {
            $followingUsers = AppUser::select(['id', 'username', 'avatar'])
                ->whereIn('id', $followingUserIds)
                ->get();

            foreach ($followingUsers as $user) {
                if (!isset($mentionableUsers[$user->id])) {
                    $mentionableUsers[$user->id] = [
                        'id' => $user->id,
                        'username' => $user->username,
                        'avatar' => $user->avatar,
                        'type' => 'following',
                        'relation' => '关注用户',
                    ];
                }
            }
        }

        return ResponseHelper::success(array_values($mentionableUsers));
    }

    /**
     * 验证特殊场景的业务规则
     *
     * @param Request $request
     * @param int $status 状态：0=草稿，1=已发布
     * @param string $visibility 可见性：public/friends/family/private
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateSpecialScenarios(Request $request, $status, $visibility)
    {
        $title = $request->input('title');
        $content = $request->input('content');
        $images = $request->input('images', []);

        // 场景1：私人记录场景（visibility=private）
        if ($visibility === 'private') {
            $this->validatePrivateRecord($title, $content, $images);
        }

        // 场景2：草稿状态场景（status=0）
        if ($status == 0) {
            $this->validateDraftContent($title, $content, $images);
        }
    }

    /**
     * 验证私人记录场景
     * 当 visibility=private 时，允许用户只上传图片而不填写标题和内容
     * 但至少需要标题、内容或图片中的一项
     *
     * @param string|null $title
     * @param string|null $content
     * @param array $images
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validatePrivateRecord($title, $content, $images)
    {
        // 检查是否所有内容都为空
        $titleEmpty = empty($title);
        $contentEmpty = empty($content);
        $imagesEmpty = empty($images) || count($images) == 0;

        // 如果标题、内容、图片都为空，则抛出错误
        if ($titleEmpty && $contentEmpty && $imagesEmpty) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                'content' => ['私人记录至少需要填写标题、内容或上传图片中的一项']
            ]);
        }
    }

    /**
     * 验证草稿内容场景
     * 当 status=0 时，title、content、images 三个字段中至少需要填写/上传其中一个
     *
     * @param string|null $title
     * @param string|null $content
     * @param array $images
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateDraftContent($title, $content, $images)
    {
        // 检查是否所有内容都为空
        $titleEmpty = empty($title);
        $contentEmpty = empty($content);
        $imagesEmpty = empty($images) || count($images) == 0;

        // 如果三个字段都为空，返回错误
        if ($titleEmpty && $contentEmpty && $imagesEmpty) {
            throw \Illuminate\Validation\ValidationException::withMessages([
                'content' => ['草稿至少需要填写标题、内容或上传图片中的一项']
            ]);
        }
    }

    /**
     * 验证被@的用户是否为家人或关注用户
     */
    private function validateMentionUsers($userId, $mentionUserIds)
    {
        $validMentionUserIds = [];

        foreach ($mentionUserIds as $mentionUserId) {
            // 检查是否为家人关系或关注关系
            if (PetMaster::isFamily($userId, $mentionUserId) ||
                AppUserFollow::isFollowing($userId, $mentionUserId)) {
                $validMentionUserIds[] = $mentionUserId;
            }
        }

        return $validMentionUserIds;
    }

    /**
     * 处理通用的宠物ID筛选参数
     */
    private function processPetIdsFilter(&$params)
    {
        $petIdsString = $params['pet_ids'];

        // 处理字符串格式的pet_ids：支持单个ID或逗号分隔的多个ID
        if (strpos($petIdsString, ',') !== false) {
            // 多个宠物ID：'1,2,3'
            $petIds = explode(',', $petIdsString);
            $petIds = array_map('trim', $petIds);
            $petIds = array_filter($petIds, 'is_numeric');
            $petIds = array_map('intval', $petIds);
        } else {
            // 单个宠物ID：'1'
            $petIds = [intval($petIdsString)];
        }

        // 过滤掉无效的ID（小于等于0的）
        $petIds = array_filter($petIds, function($id) {
            return $id > 0;
        });

        if (!empty($petIds)) {
            $params['_filtered_pet_ids'] = $petIds;
        }
    }

    /**
     * 处理同种社群推荐的参数 - 支持多只宠物筛选
     */
    private function processSimilarFeedParams(&$params, $user)
    {
        // 如果传入了宠物ID，使用指定的宠物
        if (!empty($params['_filtered_pet_ids'])) {
            $this->processPetTypeFilter($params);
        } else if ($user && $user->id) {
            // 否则使用用户所有宠物的种类（需要用户登录）
            $userPets = Pet::where('owner_id', $user->id)->with('petType')->get();
            if ($userPets->isNotEmpty()) {
                $petTypeIds = [];
                foreach ($userPets as $pet) {
                    if ($pet->petType) {
                        $petTypeIds[] = $pet->petType->id;
                    }
                }
                if (!empty($petTypeIds)) {
                    $params['_pet_type_filter'] = array_unique($petTypeIds);
                }
            }
        }
    }

    /**
     * 处理宠物种类筛选参数 - 支持多只宠物
     */
    private function processPetTypeFilter(&$params)
    {
        $petIds = $params['_filtered_pet_ids'];

        if (!empty($petIds)) {
            // 获取所有指定宠物的种类ID
            $pets = Pet::with('petType')->whereIn('id', $petIds)->get();
            $petTypeIds = [];
            $petTypeNames = [];

            foreach ($pets as $pet) {
                if ($pet->petType) {
                    $petTypeIds[] = $pet->petType->id;
                    $petTypeNames[] = $pet->petType->name;
                }
            }

            if (!empty($petTypeIds)) {
                // 去重并传递给getList方法
                $params['_pet_type_filter'] = array_unique($petTypeIds);
                $params['_pet_type_names'] = array_unique($petTypeNames);
                $params['_selected_pet_count'] = count($petIds); // 记录选择的宠物数量
            }
        }
    }

    /**
     * 验证特殊场景的业务规则（支持帖子类型）
     */
    protected function validateSpecialScenariosWithType(Request $request, $status, $visibility, $postType)
    {
        $title = $request->input('title');
        $content = $request->input('content');
        $images = $request->input('images', []);
        $petIds = $request->input('pet_ids');

        // 确保postType有默认值
        $postType = $postType ?: 1;



        // 场景1：私人记录场景（visibility=private）
        if ($visibility === 'private') {
            $this->validatePrivateRecord($title, $content, $images);
        }

        // 场景2：草稿状态场景（status=0）
        if ($status == 0) {
            $this->validateDraftContent($title, $content, $images);
        }
    }

    /**
     * 验证正常帖子
     */


    /**
     * 根据帖子类型处理图片保存到相册
     */
    private function handleImageSaveToAlbum($data, $contentId, $userId, $postType)
    {
        // 确保postType有默认值
        $postType = $postType ?: 1;

        switch ($postType) {
            case 1:
                // 正常帖子：必须关联宠物，图片自动保存到宠物相册
                AppContent::saveImagesToAlbum($data, $contentId, $userId);
                break;

            case 2:
            default:
                // 日常贴文：只有关联宠物时才保存到相册
                if (!empty($data['pet_ids'])) {
                    AppContent::saveImagesToAlbum($data, $contentId, $userId);
                }
                break;
        }
    }

    /**
     * 发送分享内容@提及通知
     */
    private function sendContentMentionNotifications($mentionUserIds, $fromUserId, $contentId)
    {
        $fromUser = AppUser::find($fromUserId);
        $content = AppContent::find($contentId);

        foreach ($mentionUserIds as $mentionUserId) {
            // 构建通知内容，包含更多上下文信息
            $notificationTitle = '分享@提及';
            $notificationContent = sprintf(
                '%s 在分享《%s》中@了您',
                $fromUser->username,
                $content->title ?: '分享内容'
            );

            // 使用统一通知系统创建@提及通知
            NotificationService::createNotification(
                $mentionUserId,
                NotificationSetting::TYPE_POST_MENTION, // 通知类型
                $notificationTitle,
                $notificationContent,
                $fromUserId,
                $contentId, // 使用内容ID作为相关ID，便于跳转
                NotificationSetting::RELATION_TYPE_CONTENT   // 相关类型为内容
            );
        }
    }
}
