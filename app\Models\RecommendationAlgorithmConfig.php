<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTimeInterface;

/**
 * 推荐算法配置模型
 */
class RecommendationAlgorithmConfig extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'enable_is_new',
        'enable_is_hot',
        'enable_sales_volume',
        'enable_user_purchase',
        'enable_same_pet_preference',
        'weight_is_new',
        'weight_is_hot',
        'weight_sales_volume',
        'weight_user_purchase',
        'weight_same_pet_preference',
        'random_factor_min',
        'random_factor_max',
        'hot_sales_threshold',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'enable_is_new' => 'boolean',
        'enable_is_hot' => 'boolean',
        'enable_sales_volume' => 'boolean',
        'enable_user_purchase' => 'boolean',
        'enable_same_pet_preference' => 'boolean',
        'weight_is_new' => 'decimal:2',
        'weight_is_hot' => 'decimal:2',
        'weight_sales_volume' => 'decimal:2',
        'weight_user_purchase' => 'decimal:2',
        'weight_same_pet_preference' => 'decimal:2',
        'random_factor_min' => 'decimal:2',
        'random_factor_max' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取当前生效的配置
     */
    public static function getActiveConfig(): ?self
    {
        return self::where('is_active', 1)
            ->orderBy('sort_order')
            ->first();
    }

    /**
     * 获取算法公式描述
     */
    public function getFormulaDescription(): string
    {
        $components = [];

        if ($this->enable_is_new) {
            $components[] = "{$this->weight_is_new}×新品";
        }
        if ($this->enable_is_hot) {
            $components[] = "{$this->weight_is_hot}×热销";
        }
        if ($this->enable_sales_volume) {
            $components[] = "{$this->weight_sales_volume}×销量";
        }
        if ($this->enable_user_purchase) {
            $components[] = "{$this->weight_user_purchase}×用户偏好";
        }
        if ($this->enable_same_pet_preference) {
            $components[] = "{$this->weight_same_pet_preference}×同类宠物偏好";
        }

        $formula = implode(' + ', $components);
        return "随机因子({$this->random_factor_min}-{$this->random_factor_max}) × ({$formula})";
    }

    /**
     * 验证配置的有效性
     */
    public function validateConfig(): array
    {
        $errors = [];

        // 至少启用一个算法因子
        $enabledFactors = [
            $this->enable_is_new,
            $this->enable_is_hot,
            $this->enable_sales_volume,
            $this->enable_user_purchase,
            $this->enable_same_pet_preference
        ];

        if (!in_array(true, $enabledFactors)) {
            $errors[] = '至少需要启用一个算法因子';
        }

        // 权重验证
        if ($this->weight_is_new < 0 || $this->weight_is_new > 10) {
            $errors[] = '新品权重必须在0-10之间';
        }

        if ($this->weight_is_hot < 0 || $this->weight_is_hot > 10) {
            $errors[] = '热销权重必须在0-10之间';
        }

        if ($this->weight_sales_volume < 0 || $this->weight_sales_volume > 10) {
            $errors[] = '销量权重必须在0-10之间';
        }

        if ($this->weight_user_purchase < 0 || $this->weight_user_purchase > 10) {
            $errors[] = '用户购买历史权重必须在0-10之间';
        }

        if ($this->weight_same_pet_preference < 0 || $this->weight_same_pet_preference > 10) {
            $errors[] = '同类宠物偏好权重必须在0-10之间';
        }

        // 随机因子验证
        if ($this->random_factor_min >= $this->random_factor_max) {
            $errors[] = '随机因子最小值必须小于最大值';
        }

        if ($this->random_factor_min < 0.1 || $this->random_factor_max > 2.0) {
            $errors[] = '随机因子范围必须在0.1-2.0之间';
        }

        // 阈值验证
        if ($this->hot_sales_threshold < 1) {
            $errors[] = '热销商品销量阈值必须大于0';
        }

        return $errors;
    }

    /**
     * 保存配置详情
     */
    public static function saveDetail($data = [])
    {
        try {
            $id = $data['id'] ?? 0;

            if ($id) {
                $config = self::find($id);
                if (!$config) {
                    return [false, [], '配置不存在'];
                }
            } else {
                $config = new self();
            }

            $config->fill($data);

            // 验证配置
            $errors = $config->validateConfig();
            if (!empty($errors)) {
                return [false, [], implode('；', $errors)];
            }

            // 如果设置为启用，先禁用其他配置
            if ($config->is_active) {
                self::where('id', '!=', $id)->update(['is_active' => 0]);
            }

            $config->save();

            // 清除推荐缓存（当配置变更时）
            \App\Models\GoodRecommendation::clearCache();

            // 根据操作类型返回不同消息
            $message = isset($data['is_active'])
                ? ($config->is_active ? '配置已保存并启用' : '配置已保存并禁用')
                : '保存成功';

            return [true, $config->toArray(), $message];

        } catch (\Exception $e) {
            return [false, [], '保存失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取配置列表
     */
    public static function getList($data = [])
    {
        $page = $data['page'] ?? 1;
        $perPage = $data['per_page'] ?? 20;
        $keyword = $data['keyword'] ?? '';
        $sortName = $data['sort_name'] ?? 'sort_order';
        $sortBy = $data['sort_by'] ?? 'asc';
        $isActive = $data['is_active'] ?? null;

        $query = self::query();

        if ($keyword) {
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }

        if ($isActive !== null) {
            $query->where('is_active', $isActive);
        }

        // 排序
        if (in_array($sortName, ['id', 'created_at', 'updated_at'])) {
            $query->orderBy($sortName, $sortBy);
        } else {
            $query->orderBy('sort_order', 'asc');
        }
        $query->orderBy('created_at', 'desc');

        $configs = $query->paginate($perPage, ['*'], 'page', $page);

        // 为每个配置添加公式描述
        $configs->getCollection()->transform(function ($config) {
            $config->formula_description = $config->getFormulaDescription();
            return $config;
        });

        return $configs;
    }

    /**
     * 获取配置详情
     */
    public static function getDetail(int $id)
    {
        $config = self::find($id);
        if ($config) {
            $config->formula_description = $config->getFormulaDescription();
        }
        return $config;
    }



    /**
     * 删除配置
     */
    public static function del($id)
    {
        try {
            $config = self::find($id);
            if (!$config) {
                return [false, [], '配置不存在'];
            }

            if ($config->is_active) {
                return [false, [], '不能删除正在使用的配置'];
            }

            $config->delete();

            // 清除推荐缓存
            \App\Models\GoodRecommendation::clearCache();

            return [true, [], '删除成功'];

        } catch (\Exception $e) {
            return [false, [], '删除失败：' . $e->getMessage()];
        }
    }
}
