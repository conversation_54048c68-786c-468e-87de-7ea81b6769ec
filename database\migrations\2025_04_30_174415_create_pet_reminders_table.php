<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pet_reminders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('pet_id');
            $table->tinyInteger('type')->comment('1=生日提醒, 2=环境提示');
            $table->string('title');
            $table->text('content');
            $table->date('trigger_date');
            $table->timestamp('read_at');
            $table->tinyInteger('status')->default(1)->comment('1=活动, 0=非活动');
            $table->timestamps();

            // 不使用外键约束，因为可能会有类型不匹配问题
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pet_reminders');
    }
};
