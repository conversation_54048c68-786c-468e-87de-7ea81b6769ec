<?php

namespace App\Services;

use App\Models\Pet;
use App\Models\PetBreed;
use Illuminate\Support\Facades\DB;

/**
 * 宠物匹配服务
 * 根据筛选标签匹配宠物
 */
class PetMatchingService
{
    /**
     * 根据筛选标签计算匹配的宠物数量和信息
     *
     * @param array $filterTags 筛选标签数组
     * @return array 匹配结果
     */
    public function calculateMatchingPets(array $filterTags): array
    {
        if (empty($filterTags)) {
            return [
                'count' => 0,
                'pets' => [],
                'filter_summary' => ['无筛选条件'],
            ];
        }

        // 构建查询 - 使用原来的数据库查询逻辑
        $query = Pet::with(['petType', 'petBreed'])
            ->where('deleted_at', null);

        $filterSummary = [];

        foreach ($filterTags as $tag) {
            $this->applyFilterTag($query, $tag, $filterSummary);
        }

        // 获取匹配的宠物
        $matchingPets = $query->get();

        // 格式化宠物信息
        $petsInfo = $matchingPets->map(function($pet) {
            return [
                'id' => $pet->id,
                'name' => $pet->name,
                'type' => $pet->petType->name ?? '',
                'breed' => $pet->petBreed->name ?? '',
                'age' => $this->calculateAge($pet->birthday),
                'life_cycle' => $this->getLifeCycle($pet),
                'size' => $this->getPetSize($pet),
                'hair_type' => $pet->petBreed->hair_type ?? '',
                'bcs' => $this->getBcsDisplayName($pet->weight_status),
                'weight' => $pet->weight ? $pet->weight . 'kg' : 'unknown',
            ];
        })->toArray();

        return [
            'count' => $matchingPets->count(),
            'pets' => $petsInfo,
            'filter_summary' => $filterSummary,
        ];
    }

    /**
     * 根据SKU筛选标签匹配用户的宠物
     * 直接使用calculateMatchingPets方法，确保与validateFilterTags结果完全一致
     *
     * @param array $filterTags SKU的筛选标签
     * @param int $userId 用户ID
     * @return array 匹配的用户宠物信息
     */
    public function matchUserPetsForSku(array $filterTags, int $userId): array
    {
        if (empty($filterTags)) {
            return [];
        }

        // 直接使用calculateMatchingPets方法
        $matchingResult = $this->calculateMatchingPets($filterTags);

        // 从所有匹配的宠物中筛选出属于当前用户的宠物
        $userMatches = [];
        foreach ($matchingResult['pets'] as $pet) {
            // 检查宠物是否属于当前用户
            $petModel = Pet::where('id', $pet['id'])
                ->where('owner_id', $userId)
                ->where('deleted_at', null)
                ->first();

            if ($petModel) {
                $userMatches[] = [
                    'id' => $pet['id'],
                    'name' => $pet['name'],
                    'avatar' => $petModel->avatar,
                    'type_name' => $pet['type'],
                    'breed_name' => $pet['breed'],
                ];
            }
        }

        return $userMatches;
    }

    /**
     * 批量处理多个SKU的用户宠物匹配
     * 直接使用calculateMatchingPets方法，确保与validateFilterTags结果完全一致
     *
     * @param array $skus SKU数组，每个元素包含id和pet_filter_tags
     * @param int $userId 用户ID
     * @return array 以SKU ID为键的匹配结果数组
     */
    public function batchMatchUserPetsForSkus(array $skus, int $userId): array
    {
        // 调试日志
        \Log::info('PetMatching: batchMatchUserPetsForSkus called', [
            'user_id' => $userId,
            'sku_count' => count($skus),
            'skus' => array_map(function($sku) {
                return [
                    'id' => $sku['id'],
                    'has_tags' => !empty($sku['pet_filter_tags']),
                    'tags_count' => is_array($sku['pet_filter_tags']) ? count($sku['pet_filter_tags']) : 0
                ];
            }, $skus)
        ]);

        $results = [];

        foreach ($skus as $sku) {
            $skuId = $sku['id'];
            $filterTags = $sku['pet_filter_tags'] ?? [];

            // 如果没有筛选标签，表示适合所有宠物，返回用户的所有宠物
            if (empty($filterTags)) {
                $userPets = Pet::where('owner_id', $userId)
                    ->where('deleted_at', null)
                    ->with(['petType:id,name', 'petBreed:id,name'])
                    ->get()
                    ->map(function($pet) {
                        return [
                            'id' => $pet->id,
                            'name' => $pet->name,
                            'avatar' => $pet->avatar,
                            'type_name' => $pet->petType->name ?? '',
                            'breed_name' => $pet->petBreed->name ?? '',
                        ];
                    })
                    ->toArray();

                $results[$skuId] = $userPets;

                \Log::info('PetMatching: Universal SKU processed', [
                    'sku_id' => $skuId,
                    'filter_tags' => 'empty (universal)',
                    'user_pets_count' => count($userPets),
                    'matched_pets' => array_column($userPets, 'name')
                ]);

                continue;
            }

            // 直接使用calculateMatchingPets方法
            $matchingResult = $this->calculateMatchingPets($filterTags);

            // 从所有匹配的宠物中筛选出属于当前用户的宠物
            $userMatches = [];
            foreach ($matchingResult['pets'] as $pet) {
                // 检查宠物是否属于当前用户
                $petModel = Pet::where('id', $pet['id'])
                    ->where('owner_id', $userId)
                    ->where('deleted_at', null)
                    ->first();

                if ($petModel) {
                    $userMatches[] = [
                        'id' => $pet['id'],
                        'name' => $pet['name'],
                        'avatar' => $petModel->avatar,
                        'type_name' => $pet['type'],
                        'breed_name' => $pet['breed'],
                    ];
                }
            }
            $results[$skuId] = $userMatches;

            \Log::info('PetMatching: SKU processed', [
                'sku_id' => $skuId,
                'filter_tags' => $filterTags,
                'total_matched_count' => $matchingResult['count'],
                'user_matched_count' => count($userMatches),
                'matched_pets' => array_column($userMatches, 'name')
            ]);
        }

        return $results;
    }





    /**
     * 检查宠物是否匹配体型标签
     */
    private function isPetMatchingSize($pet, string $value): bool
    {
        if (!$pet->petBreed || !$pet->petBreed->size) {
            return false;
        }

        $sizes = str_contains($value, ',') ?
            array_map('trim', explode(',', $value)) :
            [trim($value)];

        // 直接检查品种的固定体型
        return in_array($pet->petBreed->size, $sizes);
    }

    /**
     * 检查宠物是否匹配毛发类型标签
     */
    private function isPetMatchingHair($pet, string $value): bool
    {
        if (!$pet->petBreed || !$pet->petBreed->hair_type) {
            return false;
        }

        $hairTypes = str_contains($value, ',') ?
            array_map('trim', explode(',', $value)) :
            [trim($value)];

        return in_array($pet->petBreed->hair_type, $hairTypes);
    }

    /**
     * 检查宠物是否匹配年龄标签
     */
    private function isPetMatchingAge($pet, string $value): bool
    {
        if (!$pet->birthday) {
            return false;
        }

        // 使用与数据库查询相同的逻辑
        $birthDate = \Carbon\Carbon::parse($pet->birthday);
        $now = \Carbon\Carbon::now();

        $ageRanges = str_contains($value, ',') ?
            array_map('trim', explode(',', $value)) :
            [trim($value)];

        foreach ($ageRanges as $ageRange) {
            if (str_contains($ageRange, '-')) {
                [$minAge, $maxAge] = explode('-', $ageRange, 2);
                $minAge = (float) $minAge;
                $maxAge = (float) $maxAge;

                // 设置合理的年龄上限（宠物一般不超过20岁）
                $maxAge = min($maxAge, 20);
                $minAge = max($minAge, 0);

                // 计算生日范围 - 与applyAgeFilter保持一致
                $maxBirthday = $now->copy()->subYears($minAge);
                $minBirthday = $now->copy()->subYears($maxAge + 1);

                if ($birthDate >= $minBirthday && $birthDate <= $maxBirthday) {
                    return true;
                }
            } else {
                // 单个年龄值
                $age = (float) $ageRange;
                $age = min($age, 20);
                $age = max($age, 0);

                // 单值年龄允许±0.5岁误差 - 与applyAgeFilter保持一致
                $maxBirthday = $now->copy()->subYears($age - 0.5);
                $minBirthday = $now->copy()->subYears($age + 0.5);

                if ($birthDate >= $minBirthday && $birthDate <= $maxBirthday) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查宠物是否匹配生命周期标签
     */
    private function isPetMatchingLifeCycle($pet, string $value): bool
    {
        if (!$pet->birthday) {
            return false;
        }

        $lifeCycles = str_contains($value, ',') ?
            array_map('trim', explode(',', $value)) :
            [trim($value)];

        // 计算宠物月龄
        $ageInMonths = $this->calculateAgeInMonths($pet->birthday);

        foreach ($lifeCycles as $targetCycle) {
            // 兼容旧的命名方式
            $stageMapping = [
                'puppy' => 'juvenile',
                'kitten' => 'juvenile',
                'adult' => 'adult',
                'senior' => 'senior',
                'elderly' => 'elderly'
            ];

            $normalizedTarget = $stageMapping[$targetCycle] ?? $targetCycle;

            // 获取月份范围配置
            $monthRanges = null;
            if ($pet->petBreed && $pet->petBreed->month_ranges) {
                $monthRanges = $pet->petBreed->month_ranges;
            } else {
                // 使用默认范围
                $monthRanges = \App\Models\PetBreed::getStageMonthRanges();
            }

            if (!isset($monthRanges[$normalizedTarget])) {
                continue;
            }

            $range = $monthRanges[$normalizedTarget];

            // 兼容新旧格式
            $minMonths = $range['min'] ?? $range['start'] ?? 0;
            $maxMonths = $range['max'] ?? $range['end'] ?? null;

            // 检查宠物是否在这个生命周期范围内
            if ($ageInMonths >= $minMonths && ($maxMonths === null || $ageInMonths <= $maxMonths)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查宠物是否匹配体重标签
     */
    private function isPetMatchingWeight($pet, string $value): bool
    {
        if (!$pet->weight || $pet->weight <= 0) {
            return false;
        }

        $weightRanges = str_contains($value, ',') ?
            array_map('trim', explode(',', $value)) :
            [trim($value)];

        foreach ($weightRanges as $weightRange) {
            if (str_contains($weightRange, '-')) {
                [$minWeight, $maxWeight] = explode('-', $weightRange, 2);
                $minWeight = (float) $minWeight;
                $maxWeight = (float) $maxWeight;

                if ($pet->weight >= $minWeight && $pet->weight <= $maxWeight) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 生成筛选摘要
     * 用于统一的内存匹配算法
     */
    private function generateFilterSummary(array $filterTags): array
    {
        $summary = [];

        foreach ($filterTags as $tag) {
            if (!str_contains($tag, ':')) {
                continue;
            }

            [$type, $value] = explode(':', $tag, 2);

            switch ($type) {
                case 'pet_type':
                    $typeIds = str_contains($value, ',') ? explode(',', $value) : [$value];
                    $typeNames = \App\Models\PetType::whereIn('id', $typeIds)->pluck('name')->toArray();
                    $summary[] = "宠物类型：" . implode('、', $typeNames);
                    break;

                case 'pet_breed':
                    $breedIds = str_contains($value, ',') ? explode(',', $value) : [$value];
                    $breedNames = \App\Models\PetBreed::whereIn('id', $breedIds)->pluck('name')->toArray();
                    $summary[] = "宠物品种：" . implode('、', $breedNames);
                    break;

                case 'size':
                    $sizes = str_contains($value, ',') ? explode(',', $value) : [$value];
                    $sizeMap = ['small' => '小型', 'medium' => '中型', 'large' => '大型'];
                    $sizeNames = array_map(fn($s) => $sizeMap[trim($s)] ?? trim($s), $sizes);
                    $summary[] = "体型分类：" . implode('、', $sizeNames);
                    break;

                case 'hair':
                    $hairTypes = str_contains($value, ',') ? explode(',', $value) : [$value];
                    $hairMap = ['short' => '短毛', 'medium' => '中等毛', 'long' => '长毛'];
                    $hairNames = array_map(fn($h) => $hairMap[trim($h)] ?? trim($h), $hairTypes);
                    $summary[] = "毛发类型：" . implode('、', $hairNames);
                    break;

                case 'age':
                    $summary[] = "年龄范围：{$value}岁";
                    break;

                case 'life':
                    $lifeCycles = str_contains($value, ',') ? explode(',', $value) : [$value];
                    $lifeCycleMap = [
                        'juvenile' => '幼年期',
                        'puppy' => '幼犬期',
                        'kitten' => '幼猫期',
                        'adult' => '成年期',
                        'senior' => '熟年期',
                        'elderly' => '老年期'
                    ];
                    $cycleNames = array_map(fn($c) => $lifeCycleMap[trim($c)] ?? trim($c), $lifeCycles);
                    $summary[] = "生命周期：" . implode('、', $cycleNames);
                    break;

                case 'bcs':
                    $bcsValues = str_contains($value, ',') ? explode(',', $value) : [$value];

                    // 简化：直接使用1-9数值，前端传什么后端就用什么
                    $bcsNames = [];
                    foreach ($bcsValues as $bcsValue) {
                        $score = (int) trim($bcsValue);
                        if ($score >= 1 && $score <= 9) {
                            $scoreMap = [
                                1 => '1分-极度消瘦', 2 => '2分-非常消瘦', 3 => '3分-消瘦',
                                4 => '4分-体重不足', 5 => '5分-理想体重', 6 => '6分-略微超重',
                                7 => '7分-超重', 8 => '8分-肥胖', 9 => '9分-极度肥胖'
                            ];
                            $bcsNames[] = $scoreMap[$score];
                        } else {
                            $bcsNames[] = $bcsValue;
                        }
                    }

                    $summary[] = "体况评分：" . implode('、', $bcsNames);
                    break;

                case 'weight':
                    $summary[] = "体重范围：{$value}kg";
                    break;

                default:
                    $summary[] = "未知筛选：{$tag}";
            }
        }

        return $summary;
    }

    /**
     * 应用单个筛选标签到查询
     */
    private function applyFilterTag($query, string $tag, array &$filterSummary)
    {
        if (!str_contains($tag, ':')) {
            return;
        }

        [$type, $value] = explode(':', $tag, 2);

        switch ($type) {
            case 'pet_type':
                // 支持多个类型：pet_type:1 或 pet_type:1,2
                if (str_contains($value, ',')) {
                    $typeIds = array_map('intval', explode(',', $value));
                    $query->whereIn('type_id', $typeIds);

                    $typeNames = \App\Models\PetType::whereIn('id', $typeIds)->pluck('name')->toArray();
                    $filterSummary[] = "宠物类型：" . implode('、', $typeNames);
                } else {
                    $query->where('type_id', (int) $value);
                    $typeName = \App\Models\PetType::find((int) $value)->name ?? "未知类型({$value})";
                    $filterSummary[] = "宠物类型：{$typeName}";
                }
                break;

            case 'pet_breed':
                // 支持多个品种：pet_breed:15 或 pet_breed:15,16,20
                if (str_contains($value, ',')) {
                    // 多个品种（OR关系）
                    $breedIds = array_map('intval', explode(',', $value));
                    $query->whereIn('breed_id', $breedIds);

                    // 获取品种名称
                    $breedNames = PetBreed::whereIn('id', $breedIds)->pluck('name')->toArray();
                    $filterSummary[] = "宠物品种：" . implode('、', $breedNames);
                } else {
                    // 单个品种
                    $query->where('breed_id', (int) $value);
                    $breedName = PetBreed::find((int) $value)->name ?? "未知品种({$value})";
                    $filterSummary[] = "宠物品种：{$breedName}";
                }
                break;

            case 'size':
                // 支持多个体型：size:large 或 size:large,medium
                if (str_contains($value, ',')) {
                    $sizes = array_map('trim', explode(',', $value));
                    $query->whereHas('petBreed', function($q) use ($sizes) {
                        $q->whereIn('size', $sizes);
                    });

                    $sizeMap = ['small' => '小型', 'medium' => '中型', 'large' => '大型'];
                    $sizeNames = array_map(fn($s) => $sizeMap[$s] ?? $s, $sizes);
                    $filterSummary[] = "体型分类：" . implode('、', $sizeNames);
                } else {
                    $query->whereHas('petBreed', function($q) use ($value) {
                        $q->where('size', $value);
                    });
                    $sizeMap = ['small' => '小型', 'medium' => '中型', 'large' => '大型'];
                    $filterSummary[] = "体型分类：" . ($sizeMap[$value] ?? $value);
                }
                break;

            case 'hair':
                // 支持多个毛发类型：hair:long 或 hair:long,short
                if (str_contains($value, ',')) {
                    $hairTypes = array_map('trim', explode(',', $value));
                    $query->whereHas('petBreed', function($q) use ($hairTypes) {
                        $q->whereIn('hair_type', $hairTypes);
                    });

                    $hairMap = ['short' => '短毛', 'medium' => '中等毛', 'long' => '长毛'];
                    $hairNames = array_map(fn($h) => $hairMap[$h] ?? $h, $hairTypes);
                    $filterSummary[] = "毛发类型：" . implode('、', $hairNames);
                } else {
                    $query->whereHas('petBreed', function($q) use ($value) {
                        $q->where('hair_type', $value);
                    });
                    $hairMap = ['short' => '短毛', 'medium' => '中等毛', 'long' => '长毛'];
                    $filterSummary[] = "毛发类型：" . ($hairMap[$value] ?? $value);
                }
                break;

            case 'age':
                // 支持多个年龄范围：age:1-3 或 age:1-3,6-10
                if (str_contains($value, ',')) {
                    $ageRanges = array_map('trim', explode(',', $value));
                    $query->where(function($q) use ($ageRanges) {
                        foreach ($ageRanges as $ageRange) {
                            $q->orWhere(function($subQ) use ($ageRange) {
                                $this->applyAgeFilter($subQ, $ageRange);
                            });
                        }
                    });

                    $rangeSummary = array_map(function($range) {
                        return $this->getAgeRangeSummary($range);
                    }, $ageRanges);
                    $filterSummary[] = "年龄范围：" . implode('、', $rangeSummary);
                } else {
                    $this->applyAgeFilter($query, $value);
                    $filterSummary[] = "年龄范围：" . $this->getAgeRangeSummary($value);
                }
                break;

            case 'life':
                // 支持多个生命周期：life:adult 或 life:adult,senior
                if (str_contains($value, ',')) {
                    $lifeCycles = array_map('trim', explode(',', $value));
                    $query->where(function($q) use ($lifeCycles) {
                        foreach ($lifeCycles as $lifeCycle) {
                            $q->orWhere(function($subQ) use ($lifeCycle) {
                                $this->applyLifeCycleFilter($subQ, $lifeCycle);
                            });
                        }
                    });

                    $lifeCycleMap = [
                        'juvenile' => '幼年期',
                        'puppy' => '幼犬期',
                        'kitten' => '幼猫期',
                        'adult' => '成年期',
                        'senior' => '熟年期',
                        'elderly' => '老年期'
                    ];
                    $cycleNames = array_map(fn($c) => $lifeCycleMap[$c] ?? $c, $lifeCycles);
                    $filterSummary[] = "生命周期：" . implode('、', $cycleNames);
                } else {
                    $this->applyLifeCycleFilter($query, $value);
                    $lifeCycleMap = [
                        'juvenile' => '幼年期',
                        'puppy' => '幼犬期',
                        'kitten' => '幼猫期',
                        'adult' => '成年期',
                        'senior' => '熟年期',
                        'elderly' => '老年期'
                    ];
                    $filterSummary[] = "生命周期：" . ($lifeCycleMap[$value] ?? $value);
                }
                break;

            case 'bcs':
                // 简化BCS筛选：前端传什么值后端就直接使用
                // 支持单个值或多个值（逗号分隔）

                if (str_contains($value, ',')) {
                    $bcsValues = array_map('intval', array_map('trim', explode(',', $value)));
                    $query->whereIn('weight_status', $bcsValues);

                    $bcsNames = array_map(function($score) {
                        return $this->getBcsDisplayName($score);
                    }, $bcsValues);
                    $filterSummary[] = "体况评分：" . implode('、', $bcsNames);

                } else {
                    // 单个值 - 直接使用
                    $bcsValue = (int) $value;
                    $query->where('weight_status', $bcsValue);
                    $filterSummary[] = "体况评分：" . $this->getBcsDisplayName($bcsValue);
                }
                break;

            case 'weight':
                // 支持多个体重范围：weight:5-15 或 weight:2-8,15-25
                if (str_contains($value, ',')) {
                    $weightRanges = array_map('trim', explode(',', $value));
                    $query->where(function($rangeQuery) use ($weightRanges) {
                        foreach ($weightRanges as $range) {
                            if (str_contains($range, '-')) {
                                [$minWeight, $maxWeight] = explode('-', $range, 2);
                                $rangeQuery->orWhereBetween('weight', [(float) $minWeight, (float) $maxWeight]);
                            }
                        }
                    });

                    $rangeDescriptions = array_map(function($range) {
                        return str_replace('-', '-', $range) . 'kg';
                    }, $weightRanges);
                    $filterSummary[] = "体重范围：" . implode('、', $rangeDescriptions);
                } else {
                    // 单个体重范围
                    if (str_contains($value, '-')) {
                        [$minWeight, $maxWeight] = explode('-', $value, 2);
                        $query->whereBetween('weight', [(float) $minWeight, (float) $maxWeight]);
                        $filterSummary[] = "体重范围：{$minWeight}-{$maxWeight}kg";
                    }
                }
                break;
        }
    }

    /**
     * 应用年龄筛选
     */
    private function applyAgeFilter($query, string $ageValue)
    {
        if (str_contains($ageValue, '-')) {
            [$minAge, $maxAge] = explode('-', $ageValue, 2);
            $minAge = (float) $minAge;
            $maxAge = (float) $maxAge;

            // 设置合理的年龄上限（宠物一般不超过20岁）
            $maxAge = min($maxAge, 20);
            $minAge = max($minAge, 0);

            $now = now();
            // 计算生日范围
            $maxBirthday = $now->copy()->subYears($minAge)->format('Y-m-d');
            $minBirthday = $now->copy()->subYears($maxAge + 1)->format('Y-m-d');

            $query->whereBetween('birthday', [$minBirthday, $maxBirthday]);
        } else {
            $age = (float) $ageValue;

            // 设置合理的年龄上限
            $age = min($age, 20);
            $age = max($age, 0);

            $now = now();
            // 单值年龄允许±0.5岁误差
            $maxBirthday = $now->copy()->subYears($age - 0.5)->format('Y-m-d');
            $minBirthday = $now->copy()->subYears($age + 0.5)->format('Y-m-d');

            $query->whereBetween('birthday', [$minBirthday, $maxBirthday]);
        }
    }

    /**
     * 获取年龄范围摘要
     */
    private function getAgeRangeSummary(string $ageValue): string
    {
        if (str_contains($ageValue, '-')) {
            [$minAge, $maxAge] = explode('-', $ageValue, 2);
            $minAge = max((float) $minAge, 0);
            $maxAge = min((float) $maxAge, 20);
            return "{$minAge}-{$maxAge}岁";
        } else {
            $age = min(max((float) $ageValue, 0), 20);
            return "{$age}岁";
        }
    }

    /**
     * 应用生命周期筛选
     * 根据品种的month_ranges数据进行筛选，如果没有则使用默认范围
     * 遵循"品种自定义优先，系统默认兜底"的原则，与getLifeCycle方法保持一致
     */
    private function applyLifeCycleFilter($query, string $lifeCycle)
    {
        // 兼容旧的命名方式
        $stageMapping = [
            'puppy' => 'juvenile',
            'kitten' => 'juvenile',
            'adult' => 'adult',
            'senior' => 'senior',
            'elderly' => 'elderly'
        ];

        $targetStage = $stageMapping[$lifeCycle] ?? $lifeCycle;

        // 获取默认月份范围
        $defaultRanges = PetBreed::getStageMonthRanges();
        if (!isset($defaultRanges[$targetStage])) {
            return; // 无效的生命周期
        }

        $defaultRange = $defaultRanges[$targetStage];

        $query->where(function($q) use ($targetStage, $defaultRange) {
            // 1. 处理有自定义month_ranges的品种
            $q->whereHas('petBreed', function($breedQuery) use ($targetStage) {
                $breedQuery->whereNotNull('month_ranges')
                          ->where(function($rangeQuery) use ($targetStage) {
                              // 使用数据库函数计算宠物月龄
                              $ageInMonthsSQL = 'TIMESTAMPDIFF(MONTH, pets.birthday, NOW())';

                              // 兼容新格式 (min/max) 和旧格式 (start/end)
                              $rangeQuery->where(function($newFormatQuery) use ($targetStage, $ageInMonthsSQL) {
                                  // 新格式: min/max
                                  $newFormatQuery->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.min') IS NOT NULL")
                                                ->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.min') <= {$ageInMonthsSQL}")
                                                ->where(function($maxQuery) use ($targetStage, $ageInMonthsSQL) {
                                                    $maxQuery->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.max') IS NULL")
                                                            ->orWhereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.max') >= {$ageInMonthsSQL}");
                                                });
                              })->orWhere(function($oldFormatQuery) use ($targetStage, $ageInMonthsSQL) {
                                  // 旧格式: start/end
                                  $oldFormatQuery->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.start') IS NOT NULL")
                                                ->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.start') <= {$ageInMonthsSQL}")
                                                ->where(function($endQuery) use ($targetStage, $ageInMonthsSQL) {
                                                    $endQuery->whereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.end') IS NULL")
                                                            ->orWhereRaw("JSON_EXTRACT(month_ranges, '$.{$targetStage}.end') >= {$ageInMonthsSQL}");
                                                });
                              });
                          });
            })
            // 2. 或者处理没有自定义month_ranges的品种，使用系统默认范围
            ->orWhere(function($defaultQuery) use ($defaultRange) {
                $defaultQuery->whereHas('petBreed', function($breedQuery) {
                    $breedQuery->whereNull('month_ranges');
                });

                // 应用默认范围的生日筛选
                $this->applyDefaultLifeCycleRange($defaultQuery, $defaultRange);
            });
        });
    }

    /**
     * 应用默认生命周期范围筛选
     */
    private function applyDefaultLifeCycleRange($query, array $defaultRange)
    {
        $now = now();
        $minMonths = $defaultRange['min'];
        $maxMonths = $defaultRange['max'];

        // 计算生日范围
        $maxBirthday = $now->copy()->subMonths($minMonths)->format('Y-m-d');

        if ($maxMonths === null) {
            // 无上限（如elderly阶段）
            $query->where('birthday', '<=', $maxBirthday);
        } else {
            $minBirthday = $now->copy()->subMonths($maxMonths)->format('Y-m-d');
            $query->whereBetween('birthday', [$minBirthday, $maxBirthday]);
        }
    }

    /**
     * 计算宠物年龄
     */
    private function calculateAge($birthday): float
    {
        if (!$birthday) {
            return 0;
        }

        $birthDate = \Carbon\Carbon::parse($birthday);
        $now = \Carbon\Carbon::now();

        // 使用更精确的年龄计算方法
        $years = $now->diffInYears($birthDate);
        $months = $now->diffInMonths($birthDate) % 12;

        return round($years + ($months / 12), 1);
    }

    /**
     * 获取宠物生命周期
     * 根据品种的month_ranges数据判断，如果没有则使用默认范围
     */
    private function getLifeCycle($pet): string
    {
        if (!$pet->birthday) {
            return 'unknown';
        }

        $ageInMonths = $this->calculateAgeInMonths($pet->birthday);

        // 获取品种的自定义月份范围或使用默认范围
        $monthRanges = $pet->petBreed->month_ranges ?? PetBreed::getStageMonthRanges();

        // 按顺序检查生命周期阶段
        $stages = ['juvenile', 'adult', 'senior', 'elderly'];

        foreach ($stages as $stage) {
            if (!isset($monthRanges[$stage])) {
                continue;
            }

            $range = $monthRanges[$stage];
            $minMonths = $range['min'] ?? 0;
            $maxMonths = $range['max'] ?? null;

            if ($ageInMonths >= $minMonths && ($maxMonths === null || $ageInMonths <= $maxMonths)) {
                // 兼容旧的命名方式
                if ($stage === 'juvenile') {
                    return $pet->type_id == 1 ? 'puppy' : 'kitten';
                }
                return $stage;
            }
        }

        return 'unknown';
    }

    /**
     * 计算宠物年龄（月份）
     */
    private function calculateAgeInMonths($birthday): int
    {
        if (!$birthday) {
            return 0;
        }

        $birthDate = \Carbon\Carbon::parse($birthday);
        $now = \Carbon\Carbon::now();

        return $now->diffInMonths($birthDate);
    }

    /**
     * 获取宠物体型（直接使用品种的固定体型）
     */
    private function getPetSize($pet): string
    {
        if (!$pet->petBreed || !$pet->petBreed->size) {
            return 'unknown';
        }

        return $pet->petBreed->size;
    }

    /**
     * 获取体况评分显示名称（简化9分制BCS系统 - 5个阶段）
     * 根据新的数据库字段定义：1-9分制BCS评分，但只显示5个阶段
     */
    private function getBcsDisplayName($weightStatus): string
    {
        // 简化为5个阶段
        if ($weightStatus >= 1 && $weightStatus <= 2) return '非常瘦';
        if ($weightStatus >= 3 && $weightStatus <= 4) return '瘦';
        if ($weightStatus == 5) return '正常';
        if ($weightStatus >= 6 && $weightStatus <= 7) return '胖';
        if ($weightStatus >= 8 && $weightStatus <= 9) return '非常胖';

        return '未知';
    }

    /**
     * 获取BCS阶段代表值（前端选择器使用）
     * 每个阶段使用中间值作为代表
     */
    public static function getBcsStageValues(): array
    {
        return [
            2 => '非常瘦',  // 代表1-2分
            4 => '瘦',      // 代表3-4分
            5 => '正常',    // 代表5分
            7 => '胖',      // 代表6-7分
            9 => '非常胖'   // 代表8-9分
        ];
    }

    /**
     * 根据阶段代表值获取对应的分数范围
     */
    public static function getBcsScoreRange($stageValue): array
    {
        $ranges = [
            2 => [1, 2],  // 非常瘦
            4 => [3, 4],  // 瘦
            5 => [5],     // 正常
            7 => [6, 7],  // 胖
            9 => [8, 9]   // 非常胖
        ];

        return $ranges[$stageValue] ?? [];
    }




}
