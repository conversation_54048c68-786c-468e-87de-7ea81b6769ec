# 宠物相册API文档

## 概述
宠物相册功能允许用户为自己的宠物上传和管理照片，支持不同类型的照片分类，包括日常照片、医疗记录、成长记录等。

## 权限说明
- 用户只能为自己拥有的宠物或作为家长的宠物上传照片
- 用户只能查看和删除自己上传的照片
- 支持宠物拥有者和家长权限验证

## API接口

### 1. 上传相册图片（支持单张或多张，支持单个或多个宠物）
**接口地址：** `POST /api/mobile/petAlbum/upload`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数（单张单宠物）：**
```json
{
    "pet_id": 1,                    // 必填，单个宠物ID
    "image_type": "daily",          // 可选，图片类型：前端自定义字符串，最长50字符，默认daily
    "description": "宠物日常照片",   // 可选，图片描述
    "file": "图片文件"              // 必填，单个图片文件
}
```

**请求参数（多张单宠物）：**
```json
{
    "pet_id": 1,                    // 必填，单个宠物ID
    "image_type": "daily",          // 可选，图片类型：前端自定义字符串
    "description": "批量上传照片",   // 可选，图片描述
    "files": ["文件1", "文件2"]     // 必填，图片文件数组，最多10张
}
```

**请求参数（单张多宠物）：**
```json
{
    "pet_ids": [1, 2, 3],           // 必填，宠物ID数组，最多10个
    "image_type": "daily",          // 可选，图片类型：前端自定义字符串
    "description": "多宠物照片",     // 可选，图片描述
    "file": "图片文件"              // 必填，单个图片文件
}
```

**请求参数（多张多宠物）：**
```json
{
    "pet_ids": [1, 2, 3],           // 必填，宠物ID数组，最多10个
    "image_type": "daily",          // 可选，图片类型：前端自定义字符串
    "description": "批量多宠物照片", // 可选，图片描述
    "files": ["文件1", "文件2"]     // 必填，图片文件数组，最多10张
}
```

**响应示例（单张单宠物）：**
```json
{
    "status": 200,
    "message": "上传成功",
    "data": {
        "id": 1,
        "user_id": 1,
        "pet_id": 1,
        "image_url": "https://oss.example.com/images/pet_album/daily/1642123456-1234.jpg",
        "image_type": "daily",
        "description": "宠物日常照片",
        "image_info": {
            "width": 1920,
            "height": 1080,
            "size": 256000,
            "extension": "jpg"
        },
        "status": 1,
        "created_at": "2025-01-15 10:30:00",
        "updated_at": "2025-01-15 10:30:00"
    }
}
```

**响应示例（多张或多宠物）：**
```json
{
    "status": 200,
    "message": "批量上传成功，2个文件已为3只宠物创建相册",
    "data": {
        "albums": [
            {
                "id": 1,
                "user_id": 1,
                "pet_id": 1,
                "image_url": "https://oss.example.com/images/pet_album/daily/1642123456-1234-0.jpg",
                "image_type": "daily",
                "description": "批量多宠物照片",
                "created_at": "2025-01-15 10:30:00"
            },
            {
                "id": 2,
                "user_id": 1,
                "pet_id": 2,
                "image_url": "https://oss.example.com/images/pet_album/daily/1642123456-1234-0.jpg",
                "image_type": "daily",
                "description": "批量多宠物照片",
                "created_at": "2025-01-15 10:30:00"
            }
        ],
        "uploaded_count": 6,
        "file_count": 2,
        "pet_count": 3,
        "pet_ids": [1, 2, 3]
    }
}
```

### 2. 获取相册列表
**接口地址：** `GET /api/mobile/petAlbum/list`

**请求头：**
```
Authorization: Bearer {token}
```

**请求参数：**
```json
{
    "page": 1,                      // 可选，页码，默认1
    "per_page": 20,                 // 可选，每页数量，默认20，最大100
    "pet_id": 1,                    // 可选，宠物ID，不传则返回用户所有有权限的宠物相册
    "image_type": "daily"           // 可选，图片类型筛选
}
```

**响应示例：**
```json
{
    "status": 200,
    "message": "获取成功",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "user_id": 1,
                "pet_id": 1,
                "image_url": "https://oss.example.com/images/pet_album/daily/1642123456-1234.jpg",
                "image_type": "daily",
                "description": "宠物日常照片",
                "image_info": {
                    "width": 1920,
                    "height": 1080,
                    "size": 256000,
                    "extension": "jpg"
                },
                "created_at": "2025-01-15 10:30:00",
                "user": {
                    "id": 1,
                    "username": "user123",
                    "chinese_name": "张三",
                    "english_name": "Zhang San",
                    "avatar": "https://oss.example.com/avatars/user1.jpg"
                },
                "pet": {
                    "id": 1,
                    "name": "小白",
                    "avatar": "https://oss.example.com/pets/pet1.jpg"
                }
            }
        ],
        "first_page_url": "http://api.example.com/api/mobile/petAlbum/list?page=1",
        "from": 1,
        "last_page": 5,
        "last_page_url": "http://api.example.com/api/mobile/petAlbum/list?page=5",
        "next_page_url": "http://api.example.com/api/mobile/petAlbum/list?page=2",
        "path": "http://api.example.com/api/mobile/petAlbum/list",
        "per_page": 20,
        "prev_page_url": null,
        "to": 20,
        "total": 100
    }
}
```

### 3. 删除相册图片
**接口地址：** `POST /api/mobile/petAlbum/delete`

**请求头：**
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求参数：**
```json
{
    "id": 1                         // 必填，相册图片ID
}
```

**响应示例：**
```json
{
    "status": 200,
    "message": "删除成功",
    "data": []
}
```



## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 201 | 请指定宠物ID（pet_id）或宠物ID数组（pet_ids） | 缺少宠物ID参数 |
| 201 | 无权限为宠物ID {id} 上传照片 | 用户不是指定宠物的拥有者或家长 |
| 201 | 只支持图片格式：gif, jpg, jpeg, bmp, png, webp | 文件格式不支持 |
| 201 | 文件大小超出限制 | 文件大小超过配置限制 |
| 201 | 图片宽度/高度超出限制 | 图片尺寸超过配置限制 |
| 201 | 上传失败 | OSS上传失败 |
| 201 | 图片不存在或无权限删除 | 删除权限验证失败 |
| 201 | 无权限查看此宠物相册 | 查看权限验证失败 |

## 使用示例

### 前端上传示例（JavaScript）
```javascript
// 单张单宠物上传
const singleFormData = new FormData();
singleFormData.append('pet_id', 1);
singleFormData.append('image_type', 'daily');
singleFormData.append('description', '宠物日常照片');
singleFormData.append('file', fileInput.files[0]);

fetch('/api/mobile/petAlbum/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: singleFormData
})
.then(response => response.json())
.then(data => {
    if (data.status === 200) {
        console.log('单张上传成功:', data.data);
    }
});

// 多张单宠物上传
const multiFormData = new FormData();
multiFormData.append('pet_id', 1);
multiFormData.append('image_type', 'daily');
multiFormData.append('description', '批量上传照片');
for (let i = 0; i < fileInput.files.length; i++) {
    multiFormData.append('files[]', fileInput.files[i]);
}

fetch('/api/mobile/petAlbum/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: multiFormData
})
.then(response => response.json())
.then(data => {
    if (data.status === 200) {
        console.log('批量上传成功:', data.data.albums);
        console.log('上传数量:', data.data.uploaded_count);
    }
});

// 单张多宠物上传
const multiPetFormData = new FormData();
multiPetFormData.append('pet_ids[]', 1);
multiPetFormData.append('pet_ids[]', 2);
multiPetFormData.append('pet_ids[]', 3);
multiPetFormData.append('image_type', 'daily');
multiPetFormData.append('description', '多宠物照片');
multiPetFormData.append('file', fileInput.files[0]);

fetch('/api/mobile/petAlbum/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: multiPetFormData
})
.then(response => response.json())
.then(data => {
    if (data.status === 200) {
        console.log('多宠物上传成功:', data.data.albums);
        console.log('为', data.data.pet_count, '只宠物创建了相册');
    }
});

// 多张多宠物上传
const multiAllFormData = new FormData();
multiAllFormData.append('pet_ids[]', 1);
multiAllFormData.append('pet_ids[]', 2);
multiAllFormData.append('image_type', 'daily');
multiAllFormData.append('description', '批量多宠物照片');
for (let i = 0; i < fileInput.files.length; i++) {
    multiAllFormData.append('files[]', fileInput.files[i]);
}

fetch('/api/mobile/petAlbum/upload', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: multiAllFormData
})
.then(response => response.json())
.then(data => {
    if (data.status === 200) {
        console.log('批量多宠物上传成功:', data.data.albums);
        console.log('总共创建了', data.data.uploaded_count, '条相册记录');
        console.log('文件数:', data.data.file_count, '宠物数:', data.data.pet_count);
    }
});
```

## 注意事项

1. **文件限制：**
   - 支持格式：gif, jpg, jpeg, bmp, png, webp
   - 文件大小：根据系统配置（默认5MB）
   - 图片尺寸：根据系统配置（默认5000x5000）

2. **图片类型自定义：**
   - 支持前端完全自定义图片类型，最长50字符
   - 系统提供推荐类型作为参考，但不强制使用
   - 用户可以创建任意类型，如"生日派对"、"看病记录"、"训练成果"等
   - 系统会记录用户使用过的类型，便于下次选择

3. **权限验证：**
   - 用户必须是宠物拥有者或家长才能上传照片
   - 支持为多个宠物同时上传（最多10个宠物）
   - 系统会验证用户对所有指定宠物的权限
   - 用户只能删除自己上传的照片
   - 查看相册时会自动过滤用户有权限的宠物

4. **批量上传：**
   - 最多支持10张图片同时上传
   - 最多支持为10个宠物同时上传
   - 如果其中一张失败，整个上传会失败
   - 建议前端做好文件预检查和权限验证

5. **多宠物上传：**
   - 一张图片可以同时为多个宠物创建相册记录
   - 每个宠物都会有独立的相册记录
   - 图片只上传一次到OSS，但数据库会为每个宠物创建记录
   - 适用场景：家庭合照、多宠物一起的照片等

6. **存储路径：**
   - 图片存储在OSS的 `{env}/images/pet_album/{image_type}/` 目录下
   - 文件名格式：`{timestamp}-{random}-{index}.{extension}`
