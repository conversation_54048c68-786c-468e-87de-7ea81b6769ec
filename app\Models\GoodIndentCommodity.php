<?php

namespace App\Models;

use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin Builder
 */
class GoodIndentCommodity extends Model
{
    use SoftDeletes;

    protected $table = 'goods_indents_commoditys';

    const REFUND_MARK_NONE = 1; //状态：未申请
    const REFUND_MARK_REFUNDING = 2; //状态：退款中
    const REFUND_MARK_REFUNDED = 3; //状态：已退款

    protected $fillable = [
        'good_id',
        'good_sku_id',
        'good_indent_id',
        'freight_id',
        'image',
        'name',
        'name_tc',
        'name_en',
        'product_code',
        'details',
        'product_sku',
        'remark',
        'price',
        'number',
        'weight',
        'is_inventory',
        'good_total',
        'carriage_total',
        'coupon_total',
        'pay_total',
        'refund_number',
        'refund_total',
        'is_refund',
        'refund_day',
        'refund_mark',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取商品
     */
    public function good()
    {
        return $this->hasOne(Good::class, 'id', 'good_id');
    }

    /**
     * 获取商品SKU
     */
    public function goodSku()
    {
        return $this->hasOne(GoodSku::class, 'id', 'good_sku_id');
    }

    /**
     * 获取订单
     */
    public function goodIndent()
    {
        return $this->hasOne(GoodIndent::class, 'id', 'good_indent_id');
    }

    /**
     * 关联宠物
     */
    public function pets()
    {
        return $this->belongsToMany(
            Pet::class,
            'goods_indents_commoditys_pets',
            'good_indent_commodity_id',
            'pet_id'
        );
    }

    public static function getCommodities($carts, $userId)
    {
        $Commodities = [];
        foreach ($carts as $cart) {
            $Good = Good::select([
                'id',
                'name',
                'name_tc',
                'name_en',
                'image',
                'product_code',
                'details',
                'price',
                'is_inventory',
                'inventory',
                'freight_id',
                'is_show',
                'is_refund',
                'refund_day',
            ])
                ->find($cart['good_id']);
            if (!$Good || $Good->is_show != Good::GOOD_SHOW_PUTAWAY) {
                throw new Exception('存在已失效商品，请重新获取购物车');
            }
            $checkInventory = $Good->is_inventory == Good::GOOD_IS_INVENTORY_NO; //拍下减库存
            if ($cart['good_sku_id'] > 0) { //SKU商品
                $GoodSku = GoodSku::where('good_id', $cart['good_id'])->where('id', $cart['good_sku_id'])->first();
                if (!$GoodSku) {
                    throw new Exception('存在已失效规格，请重新获取购物车');
                }
                if ($checkInventory && $GoodSku->inventory - $cart['number'] < 0) {
                    throw new Exception('存在库存不足的SKU商品，请重新获取购物车');
                }
            } else {
                if ($checkInventory && $Good->inventory - $cart['number'] < 0) {
                    throw new Exception('存在库存不足的商品，请重新获取购物车');
                }
            }
            //子订单
            $indentCommodity = new self();
            $indentCommodity->good_id = $cart['good_id'];
            $indentCommodity->good_sku_id = $cart['good_sku_id'];
            $indentCommodity->remark = $cart['remark'] ?? '';
            $indentCommodity->number = +$cart['number'];
            $indentCommodity->image = $Good->image;
            $indentCommodity->name = $Good->name;
            $indentCommodity->name_tc = $Good->name_tc;
            $indentCommodity->name_en = $Good->name_en;
            $indentCommodity->product_code = $Good->product_code;
            $indentCommodity->details = $Good->details;
            $indentCommodity->price = $Good->price;
            $indentCommodity->is_inventory = $Good->is_inventory;
            $indentCommodity->inventory = $Good->inventory;
            $indentCommodity->freight_id = $Good->freight_id;
            $indentCommodity->weight = +bcmul($indentCommodity->number, $Good->weight, 6);
            $indentCommodity->is_refund = $Good->is_refund;
            $indentCommodity->refund_day = $Good->refund_day;
            if (isset($GoodSku)) {
                $indentCommodity->image = $GoodSku->image ?: $indentCommodity->image;
                $indentCommodity->product_sku = $GoodSku->product_sku;
                $indentCommodity->price = $GoodSku->price;
                $indentCommodity->inventory = $GoodSku->inventory;
                $indentCommodity->weight = +bcmul($indentCommodity->number, $GoodSku->weight, 6);
            }
            $indentCommodity->good_total = +bcmul($indentCommodity->price, $indentCommodity->number, 2);
            $petIds = array_filter(explode(',', $cart['pet_ids'] ?? ''));
            $indentCommodity->pets = Pet::getPetsByIdsWithOriginalOrder($petIds, $userId);
            $Commodities[] = $indentCommodity;
        }
        return $Commodities;
    }

    public static function setCarriageTotal($indentCommodity, $carriageList)
    {
        $carriageInfo = $carriageList[$indentCommodity->freight_id] ?? [];
        $carriageTotal = $carriageInfo['carriage'] ?? 0;
        if ($carriageTotal > 0) {
            $numVal = $carriageInfo['freight']['valuation'] == 1 ? $indentCommodity->number : $indentCommodity->weight;
            $scale = $numVal / ($carriageInfo['freight']['valuation'] == 1 ? $carriageInfo['number'] : $carriageInfo['weight']);
            $indentCommodity->carriage_total = round(bcmul($carriageTotal, $scale, 3), 2);
        } else {
            $indentCommodity->carriage_total = 0;
        }
    }

    public static function saveByGoodIndent($GoodIndent)
    {
        $Commodities = $GoodIndent->commodities;
        foreach ($Commodities as $indentCommodity) {
            $indentCommodity->good_indent_id = $GoodIndent->id;
            if ($indentCommodity->is_inventory == Good::GOOD_IS_INVENTORY_NO) {
                $res = self::decrementInventory($indentCommodity);
                if (!$res) {
                    throw new Exception($indentCommodity->name . '库存不足');
                }
            }
            $goodIndentCommodityId = self::create($indentCommodity->toArray())->id;
            $res = $goodIndentCommodityId > 0;
            if (!$res) {
                throw new Exception('子订单保存失败');
            }
            $petIds = array_column($indentCommodity->pets->toArray(), 'id');
            if (!empty($petIds)) {
                $dataList = [];
                foreach ($petIds as $petId) {
                    $dataList[] = [
                        'good_indent_id'           => $indentCommodity->good_indent_id,
                        'good_indent_commodity_id' => $goodIndentCommodityId,
                        'pet_id'                   => $petId,
                    ];
                }
                $res = GoodIndentCommodityPet::insert($dataList);
                if (!$res) {
                    throw new Exception('子订单关联宠物保存失败');
                }
            }
        }
    }

    public static function saveByPaid($GoodIndent)
    {
        $Commodities = $GoodIndent->commodities;
        foreach ($Commodities as $indentCommodity) {
            if ($indentCommodity->is_inventory == Good::GOOD_IS_INVENTORY_FILM) {
                $res = self::decrementInventory($indentCommodity);
                if (!$res) {
                    throw new Exception($indentCommodity->name . '库存不足');  //应该要自动发起退款
                }
                $res = self::incrementSalesActual($indentCommodity);
                if (!$res) {
                    throw new Exception($indentCommodity->name . '销量增加失败');
                }
            }
        }
    }

    public static function saveByCancel($GoodIndent)
    {
        $Commodities = $GoodIndent->commodities;
        foreach ($Commodities as $indentCommodity) {
            if ($indentCommodity->is_inventory == Good::GOOD_IS_INVENTORY_NO) { //拍下减库存
                $res = self::incrementInventory($indentCommodity);
                if (!$res) {
                    throw new Exception('恢复库存失败');
                }
            }
            if ($GoodIndent->is_cart == 1) {
                self::recoveryCart($indentCommodity);
            }
        }
    }

    //恢复购物车
    public static function recoveryCart($indentCommodity)
    {
        $petIds = GoodIndentCommodityPet::where('good_indent_commodity_id', $indentCommodity->id)->pluck('pet_id')->toArray();
        $res = GoodCart::addShoppingCart([
            'good_id'     => $indentCommodity->good_id,
            'good_sku_id' => $indentCommodity->good_sku_id,
            'number'      => $indentCommodity->number,
            'name'        => $indentCommodity->name,
            'name_tc'     => $indentCommodity->name_tc,
            'name_en'     => $indentCommodity->name_en,
            'image'       => $indentCommodity->image,
            'price'       => $indentCommodity->price,
            'pet_ids'     => implode(',', $petIds),
        ]);
        throw_if(!$res[0], 'RuntimeException', '恢复购物车失败：' . $res[2]);
    }

    public static function incrementSalesActual($indentCommodity)
    {
        return Good::withTrashed()->where('id', $indentCommodity->good_id)->increment('sales_actual', $indentCommodity->number);
    }

    public static function decrementInventory($indentCommodity)
    {
        if ($indentCommodity->good_sku_id > 0) {
            return GoodSku::where('id', $indentCommodity->good_sku_id)
                ->where('inventory', '>=', $indentCommodity->number)
                ->decrement('inventory', $indentCommodity->number);
        } else {
            return Good::where('id', $indentCommodity->good_id)
                ->where('inventory', '>=', $indentCommodity->number)
                ->decrement('inventory', $indentCommodity->number);
        }
    }

    public static function incrementInventory($indentCommodity)
    {
        if ($indentCommodity->good_sku_id > 0) {
            return GoodSku::withTrashed()->where('id', $indentCommodity->good_sku_id)->increment('inventory', $indentCommodity->number);
        } else {
            return Good::withTrashed()->where('id', $indentCommodity->good_id)->increment('inventory', $indentCommodity->number);
        }
    }

    public static function saveByRefund($GoodIndentRefund)
    {
        $res = true;
        if ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_AUDIT) {
            //发起退款
            $res = self::withTrashed()
                ->where('id', $GoodIndentRefund->good_indent_commodity_id)
                ->incrementEach([
                    'refund_number' => $GoodIndentRefund->number,
                    'refund_total'  => $GoodIndentRefund->total,
                ]);
            $res = $res && self::withTrashed()
                    ->where('id', $GoodIndentRefund->good_indent_commodity_id)
                    ->update([
                        'refund_mark' => self::REFUND_MARK_REFUNDING,
                    ]);
        }
        if ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_PASS) {
            //同意退款
            $res = $res && self::withTrashed()
                    ->where('id', $GoodIndentRefund->good_indent_commodity_id)
                    ->update([
                        'refund_mark' => self::REFUND_MARK_REFUNDED,
                    ]);
        }
        if ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_CANCEL || $GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_REJECT) {
            //取消退款 / 拒绝退款
            $res = self::withTrashed()
                ->where('id', $GoodIndentRefund->good_indent_commodity_id)
                ->decrementEach([
                    'refund_number' => $GoodIndentRefund->number,
                    'refund_total'  => $GoodIndentRefund->total,
                ]);
            $data = [
                'refund_mark' => self::REFUND_MARK_NONE,
            ];
            if ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_REJECT) {
                $data['is_refund'] = 0; //不能再发起退款
            }
            $res = $res && self::withTrashed()
                    ->where('id', $GoodIndentRefund->good_indent_commodity_id)
                    ->update($data);
        }
        if (!$res) {
            throw new Exception('子订单更新失败');
        }
    }

    public static function getListForRefund($refundCommodities)
    {
        return self::withTrashed()
            ->from('goods_indents_commoditys as gic')
            ->join('goods_indents as gi', 'gic.good_indent_id', '=', 'gi.id')
            ->whereIn('gic.id', array_column($refundCommodities, 'good_indent_commodity_id'))
            ->select([
                'gic.id',
                'gic.good_indent_id',
                'gic.name',
                'gic.name_tc',
                'gic.name_en',
                'gic.product_sku',
                'gic.price',
                'gic.number',
                'gic.refund_number',
                'gic.is_refund',
                'gic.refund_day',
                'gic.refund_mark',
                'gi.pay_time',
                'gi.user_id',
            ])
            ->get()->toArray();
    }
}
