<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\GoodIndentRefund;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * indentRefund
 * 订单退款管理
 * Class IndentController
 * @package App\Http\Controllers\Admin
 */
class GoodIndentRefundController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'              => 'sometimes|integer|gt:0',
            'per_page'          => 'sometimes|integer|between:1,200',
            'keyword'           => 'nullable|max:255',
            'state'             => 'nullable|between:' . GoodIndentRefund::GOOD_INDENT_REFUND_STATE_AUDIT . ',' . GoodIndentRefund::GOOD_INDENT_REFUND_STATE_CANCEL,
            'created_start'     => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'       => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'audit_time_start'  => 'nullable|required_with:audit_time_end|date_format:"Y-m-d H:i:s"',
            'audit_time_end'    => 'nullable|required_with:audit_time_start|date_format:"Y-m-d H:i:s"|after:audit_time_start',
            'refund_time_start' => 'nullable|required_with:refund_time_end|date_format:"Y-m-d H:i:s"',
            'refund_time_end'   => 'nullable|required_with:refund_time_start|date_format:"Y-m-d H:i:s"|after:refund_time_start',
            'sort_name'         => 'nullable|in:id,created_at,updated_at',
            'sort_by'           => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['with_trashed'] = true;
        $records = GoodIndentRefund::getList($formData);
        return ResponseHelper::success($records);
    }

    public function nums(Request $request)
    {
        $request->validate([
            'keyword'           => 'nullable|max:255',
            'created_start'     => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'       => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'audit_time_start'  => 'nullable|required_with:audit_time_end|date_format:"Y-m-d H:i:s"',
            'audit_time_end'    => 'nullable|required_with:audit_time_start|date_format:"Y-m-d H:i:s"|after:audit_time_start',
            'refund_time_start' => 'nullable|required_with:refund_time_end|date_format:"Y-m-d H:i:s"',
            'refund_time_end'   => 'nullable|required_with:refund_time_start|date_format:"Y-m-d H:i:s"|after:refund_time_start',
        ]);
        $formData = $request->all();
        $formData['with_trashed'] = true;
        return ResponseHelper::success(GoodIndentRefund::nums($formData));
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(GoodIndentRefund::getDetail($id));
    }

    public function audit(Request $request)
    {
        $rules = [
            'refund'          => 'required|array',
            'refund.*.id'     => 'required|integer|gt:0',
            'refund.*.state'  => 'required|integer|in:' . GoodIndentRefund::GOOD_INDENT_REFUND_STATE_PASS . ',' . GoodIndentRefund::GOOD_INDENT_REFUND_STATE_REJECT,
            'refund.*.remark' => 'nullable|string|max:200',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['refund'] = $request->input('refund');
        $result = GoodIndentRefund::audit($formData, $request->user());
        return ResponseHelper::result(...$result);
    }

    public function refund(Request $request)
    {
        $rules = [
            'commodity'                            => 'required|array',
            'commodity.*.good_indent_commodity_id' => 'required|integer|gt:0',
            'commodity.*.number'                   => 'required|integer|gt:0',
            'commodity.*.total'                    => 'required|numeric|gte:0',
            'commodity.*.remark'                   => 'nullable|string|max:200',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['commodity'] = $request->input('commodity');
        $result = GoodIndentRefund::refund($formData, $request->user());
        return ResponseHelper::result(...$result);
    }
}
