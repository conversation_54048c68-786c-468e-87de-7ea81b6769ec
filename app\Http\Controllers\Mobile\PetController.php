<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\Pet;
use App\Models\PetBreed;
use App\Models\PetCode;
use App\Models\PetMaster;
use App\Models\PetMasterRequest;
use App\Models\PetType;
use Illuminate\Http\Request;

class PetController extends Controller
{
    public function petTypeBreed(Request $request)
    {
        //todo 缓存
        $types = PetType::orderBy('id')->get();
        // 使用中文友好排序獲取品種列表
        $breeds = PetBreed::orderByRaw('name COLLATE utf8mb4_unicode_ci ASC')->get();

        $group = [];
        foreach ($types as $type) {
            $group[$type->id] = [
                'id'      => $type->id,
                'name'    => $type->name,
                'name_tc' => $type->name_tc,
                'name_en' => $type->name_en,
                'breeds'  => [],
            ];
            foreach ($breeds as $breed) {
                if ($breed->type_id == $type->id) {
                    $group[$type->id]['breeds'][] = [
                        'id'      => $breed->id,
                        'name'    => $breed->name,
                        'name_tc' => $breed->name_tc,
                        'name_en' => $breed->name_en,
                    ];
                }
            }
        }
        $group = array_values($group);

        return ResponseHelper::success($group);
    }

    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'   => 'nullable|in:asc,desc',
            'master_id' => 'nullable|integer|gt:0',
            'sex'       => 'nullable|in:1,2',
            'type_id'   => 'nullable|integer|gt:0|exists:pets_types,id',
            'breed_id'  => 'nullable|integer|gt:0|exists:pets_breeds,id',
        ]);
        $formData = $request->all();
        $records = Pet::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        $info = Pet::getDetail($id);
        $info and Pet::renderDetail($info, $request->user()->id);
        return ResponseHelper::success($info);
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                    => 'sometimes|integer|gte:0',
            'name'                  => 'required|max:32',
            'description'           => 'nullable|string|max:200',
            'sex'                   => 'required|in:1,2',
            'avatar'                => 'required|url|pic|trust_host',  // APP端直接传URL
            'breed_id'              => 'required|integer|gt:0|exists:pets_breeds,id',
            'birthday'              => 'required|date_format:Y-m-d',
            'neutered'              => 'required|in:0,1,2',  //绝育情况:0=未绝育,1=已绝育,2=计划中
            'vaccinated'            => 'sometimes|in:0,1', //疫苗接种情况:0=未接种,1=已接种
            'father_id'             => 'nullable|integer|gt:0',
            'mother_id'             => 'nullable|integer|gt:0',
            'longitude'             => 'nullable|numeric',
            'latitude'              => 'nullable|numeric',
            'city_code'             => 'nullable|string',
            'location'              => 'nullable|string|max:255',
            'birth_certificate'     => 'nullable|url|pic|trust_host',  // 出生证明
            'bloodline_certificate' => 'nullable|url|pic|trust_host',  // 血统证明
            'vaccine_certificate'   => 'nullable|url|pic|trust_host',  // 疫苗证明
            'health_report'         => 'nullable|url|pic|trust_host',  // 健康报告
            'weight'                => 'nullable|numeric|gt:0',
            'weight_status'         => 'nullable|integer|between:1,9',
            'active_status'         => 'nullable|in:0,1,2',
            'is_pregnant'           => 'nullable|in:0,1',
            'is_ill'                => 'nullable|in:0,1',
            'is_dead'               => 'nullable|in:0,1',
            'family_members'        => 'nullable|array',
            'family_members.*.id'   => 'required|integer|gt:0|exists:app_users,id'
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['type_id'] = PetBreed::withTrashed()->where('id', $formData['breed_id'])->value('type_id');
        if ($formData['type_id'] == 2 && !empty($formData['weight']) && $formData['weight'] < 2) {
            return ResponseHelper::error('狗狗體重不能設置低於2KG');
        }
        if ($request->has('family_members')) {
            $formData['family_members'] = $request->input('family_members', []);
        }
        $result = Pet::saveDetail($formData, $request->user()->id, 2);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Pet::del($request->input('id'), $request->user()->id, 2);
        return ResponseHelper::result(...$result);
    }

    public function sendFamilyRequest(Request $request)
    {
        $validated = $request->validate([
            'to_user_id' => 'required|integer|exists:app_users,id',
            'pet_id'     => 'required|integer',
        ]);

        $result = PetMasterRequest::sendRequest(
            $request->user()->id,
            $validated['to_user_id'],
            $validated['pet_id']
        );

        return ResponseHelper::result(...$result);
    }

    public function getFamilyRequest(Request $request)
    {
        $validated = $request->validate([
            'request_id' => 'required|integer|exists:pets_masters_requests,id',
        ]);

        $result = PetMasterRequest::getRequest(
            $request->user()->id,
            $validated['request_id'],
        );

        return ResponseHelper::result(...$result);
    }

    public function handleFamilyRequest(Request $request)
    {
        $validated = $request->validate([
            'request_id' => 'required|integer|exists:pets_masters_requests,id',
            'action'     => 'required|in:accept,reject'
        ]);

        $result = PetMasterRequest::handleRequest(
            $request->user()->id,
            $validated['request_id'],
            $validated['action']
        );

        return ResponseHelper::result(...$result);
    }

    public function createCode(Request $request)
    {
        $rules = [
            'pet_id' => 'required|integer|gt:0',
            'type'   => 'required|integer|in:1,2', //类型:1=转让宠物,2=添加家人
            'role'   => 'required_if:type,1|in:1,2', //转让后身份:1=家人,2=朋友
            'images' => 'nullable|array',  //给新主人生成动态的图片
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = PetCode::saveDetail($formData, $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    public function getCode(Request $request)
    {
        $rule = [
            'code' => 'required',
        ];
        $request->validate($rule);
        $result = PetCode::getDetail($request->input('code'));
        return ResponseHelper::result(...$result);
    }

    public function cancelCode(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = PetCode::cancelCode($request->input('id'), $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    public function receivePet(Request $request)
    {
        $rule = [
            'code' => 'required',
        ];
        $request->validate($rule);
        $result = PetCode::receivePet($request->input('code'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function getPetFamily(Request $request)
    {
        $request->validate([
            'pet_id' => 'required|integer|gt:0',
        ]);
        $pet_id = $request->input('pet_id');
        $familyUserIds = PetMaster::getPetFamilyUserIds($pet_id);
        $list = $familyUserIds ? AppUser::getBaseList($familyUserIds) : [];
        $owner_id = Pet::where('id', $pet_id)->value('owner_id');
        foreach ($list as &$v) {
            $v['is_owner'] = $v['id'] == $owner_id;
        }
        return ResponseHelper::success($list);
    }

    public function removeFamilyMember(Request $request)
    {
        $rule = [
            'user_id' => 'required|integer|gt:0',
            'pet_id'  => 'required|integer|gt:0',
        ];
        $request->validate($rule);
        $result = PetMaster::removeFamilyMember($request->input('pet_id'), $request->input('user_id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function transferOwner(Request $request)
    {
        $rule = [
            'user_id' => 'required|integer|gt:0',
            'pet_id'  => 'required|integer|gt:0',
        ];
        $request->validate($rule);
        $result = Pet::transferOwner($request->input('pet_id'), $request->input('user_id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function updatePetSort(Request $request)
    {
        $rule = [
            'pet_ids' => 'required|number_combine',
        ];
        $request->validate($rule);
        $result = PetMaster::updatePetSort($request->input('pet_ids'), $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 获取宠物营养需求分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function nutritionRequirements(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'pet_id' => 'required|integer|exists:pets,id',
        ]);

        try {
            $petId = $request->input('pet_id');
            $userId = $request->user()->id;

            // 获取宠物详细信息
            $pet = Pet::with(['petType', 'petBreed'])
                ->where('id', $petId)
                ->first();

            if (!$pet) {
                return ResponseHelper::error('寵物不存在');
            }

            // 检查权限 - 只有宠物主人或管理员可以查看
            if (!Pet::hasEditPermission($petId, $userId)) {
                return ResponseHelper::error('沒有查看權限');
            }

            // 转换为数组格式以兼容现有的计算逻辑
            $petArray = $pet->toArray();

            // 计算营养需求
            $nutritionRequirements = Pet::getNutritionRequirements($petArray);

            // 检查是否有错误
            if (isset($nutritionRequirements['error']) && $nutritionRequirements['error']) {
                return ResponseHelper::error($nutritionRequirements['message']);
            }

            // 添加计算时间戳
            $nutritionRequirements['calculated_at'] = now()->toDateTimeString();
            $nutritionRequirements['calculation_note'] = '基于AAFCO标准计算，仅供参考，具体营养需求请咨询兽医';

            return ResponseHelper::success($nutritionRequirements);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('营养需求计算失败: ' . $e->getMessage(), [
                'pet_id'  => $request->input('pet_id'),
                'user_id' => $request->user()->id,
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString()
            ]);

            return ResponseHelper::error('營養需求計算失敗: ' . $e->getMessage());
        }
    }

    /**
     * 批量获取多只宠物的营养需求分析
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchNutritionRequirements(Request $request)
    {
        // 验证请求参数
        $request->validate([
            'pet_ids'   => 'required|array|min:1|max:10',
            'pet_ids.*' => 'integer|exists:pets,id',
        ]);

        try {
            $petIds = $request->input('pet_ids');
            $userId = $request->user()->id;

            // 获取宠物详细信息
            $pets = Pet::with(['petType', 'petBreed'])
                ->whereIn('id', $petIds)
                ->get();

            if ($pets->isEmpty()) {
                return ResponseHelper::error('未找到指定的寵物');
            }

            $results = [];
            $errors = [];

            foreach ($pets as $pet) {
                // 检查权限
                if (!Pet::hasEditPermission($pet->id, $userId)) {
                    $errors[] = "宠物 {$pet->name} 没有查看权限";
                    continue;
                }

                try {
                    // 转换为数组格式
                    $petArray = $pet->toArray();

                    // 计算营养需求
                    $nutritionRequirements = Pet::getNutritionRequirements($petArray);

                    // 检查是否有错误
                    if (isset($nutritionRequirements['error']) && $nutritionRequirements['error']) {
                        $errors[] = "宠物 {$pet->name}: " . $nutritionRequirements['message'];
                        continue;
                    }

                    $results[] = [
                        'pet_id'                 => $pet->id,
                        'pet_name'               => $pet->name,
                        'nutrition_requirements' => $nutritionRequirements
                    ];

                } catch (\Exception $e) {
                    $errors[] = "宠物 {$pet->name}: " . $e->getMessage();
                    \Illuminate\Support\Facades\Log::error('单个宠物营养需求计算失败', [
                        'pet_id'   => $pet->id,
                        'pet_name' => $pet->name,
                        'error'    => $e->getMessage()
                    ]);
                }
            }

            $response = [
                'results'          => $results,
                'calculated_at'    => now()->toDateTimeString(),
                'calculation_note' => '基于AAFCO标准计算，仅供参考，具体营养需求请咨询兽医'
            ];

            // 如果有错误，也包含在响应中
            if (!empty($errors)) {
                $response['errors'] = $errors;
            }

            return ResponseHelper::success($response);

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('批量营养需求计算失败: ' . $e->getMessage(), [
                'pet_ids' => $request->input('pet_ids'),
                'user_id' => $request->user()->id,
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString()
            ]);

            return ResponseHelper::error('批量營養需求計算失敗: ' . $e->getMessage());
        }
    }
}
