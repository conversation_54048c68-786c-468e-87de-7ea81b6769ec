<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetFamilyRequestEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 请求 ID
     *
     * @var int
     */
    public $requestId;

    /**
     * 发起人 ID
     *
     * @var int
     */
    public $fromUserId;

    /**
     * 家人 ID
     *
     * @var int
     */
    public $toUserId;

    /**
     * 宠物 ID
     *
     * @var int
     */
    public $petId;

    /**
     * Create a new event instance.
     *
     * @param int $requestId  请求 ID
     * @param int $fromUserId 被关注的用户 ID
     * @param int $toUserId   家人 ID
     * @param int $petId      宠物 ID
     * @return void
     */
    public function __construct($requestId, $fromUserId, $toUserId, $petId)
    {
        $this->requestId = $requestId;
        $this->fromUserId = $fromUserId;
        $this->toUserId = $toUserId;
        $this->petId = $petId;
    }
}
