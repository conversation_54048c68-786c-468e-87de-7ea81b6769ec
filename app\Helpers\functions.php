<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

if (!function_exists('getDistance')) {
    /**
     * 计算两点地理坐标之间的距离
     * @param float $longitude1 起点经度
     * @param float $latitude1  起点纬度
     * @param float $longitude2 终点经度
     * @param float $latitude2  终点纬度
     * @param int   $unit       单位 1:米 2:公里
     * @param int   $decimal    精度 保留小数位数
     * @return float
     */
    function getDistance($longitude1, $latitude1, $longitude2, $latitude2, $unit = 1, $decimal = 2)
    {
        $EARTH_RADIUS = 6370.996; // 地球半径系数
        $PI = 3.1415926;

        $radLat1 = $latitude1 * $PI / 180.0;
        $radLat2 = $latitude2 * $PI / 180.0;

        $radLng1 = $longitude1 * $PI / 180.0;
        $radLng2 = $longitude2 * $PI / 180.0;

        $a = $radLat1 - $radLat2;
        $b = $radLng1 - $radLng2;

        $distance = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2)));
        $distance = $distance * $EARTH_RADIUS * 1000;

        if ($unit == 2) {
            $distance = $distance / 1000;
        }

        return round($distance, $decimal);
    }
}

if (!function_exists('redis')) {
    /**
     * @return \Illuminate\Redis\Connections\PhpRedisConnection
     */
    function redis()
    {
        return \Illuminate\Support\Facades\Redis::connection();
    }
}

if (!function_exists('logErr')) {
    /**
     * 记录应用错误日志
     * @param \Throwable | string $exception
     * @param array               $context
     * @return void
     */
    function logErr($exception, $context = [])
    {
        $isCli = \App::runningInConsole();
        $commandName = \App\Console\Scheduling\CaptureCommandName::$currentCommand;
        $request = \Request::instance();
        $user = $request->user();
        $requestId = $isCli ? \Illuminate\Support\Str::uuid()->toString() : $request->requestId;
        $str = '应用发生异常：';
        $cutError = 500;  //保证长度不超过 500
        if ($exception instanceof \Throwable) {
            $err = $exception->getMessage();
            $errOverSize = mb_strlen($err) > $cutError;
            $str .= mb_substr($err, 0, $cutError) . ($errOverSize ? ' (已截断，详情查看trace)' : '') . PHP_EOL;
            $str .= '错误位置：' . $exception->getFile() . '(' . $exception->getLine() . ')' . PHP_EOL;
        } else {
            $err = (string)$exception;
            $errOverSize = mb_strlen($err) > $cutError;
            $str .= mb_substr($err, 0, $cutError) . ($errOverSize ? ' (已截断，详情查看trace)' : '') . PHP_EOL;
        }

        $param = json_encode($request->all(), JSON_UNESCAPED_UNICODE);
        $cutParam = 2000;  //保证长度不超过 2000
        $paramOverSize = mb_strlen($param) > $cutParam;

        \Log::channel('custom_error')
            ->error(trim(
                $str . '请求ID: ' . $requestId . PHP_EOL
                . ($isCli ? '' : ('请求地址: ' . $request->fullUrl() . PHP_EOL))
                . ($isCli ? '' : ('请求头: ' . json_encode($request->header(), JSON_UNESCAPED_UNICODE) . PHP_EOL))
                . ($isCli ? '' : ('请求参数: ' . mb_substr($param, 0, $cutParam) . ($paramOverSize ? ' (已截断，详情查看trace)' : '') . PHP_EOL))
                . ($isCli ? '' : ('用户ID: ' . $user?->id . PHP_EOL))
                . (!$commandName ? '' : ('命令名称: ' . $commandName . PHP_EOL))
            ), $context);
        \Log::channel('custom_error')->info(LOG_SPLIT_LINE);

        if ($errOverSize) {
            //错误过长，也写到trace
            \Config::set('logging.channels.custom_trace.path', storage_path('logs/trace/' . $requestId . '.log'));
            \Log::channel('custom_trace')->error($err);
        }

        if ($paramOverSize) {
            //参数过长，也写到trace
            \Config::set('logging.channels.custom_trace.path', storage_path('logs/trace/' . $requestId . '.log'));
            \Log::channel('custom_trace')->error($param);
        }

        if ($exception instanceof \Throwable) {
            //laravel trace实在太长了，先看 error.log 查看错误信息，一般通过文件名和行数就能确定哪里错了，如果还看不出问题再看trace
            \Config::set('logging.channels.custom_trace.path', storage_path('logs/trace/' . $requestId . '.log'));
            \Log::channel('custom_trace')->error($exception->getTraceAsString());
        }
    }
}

if (!function_exists('bytesToHuman')) {
    function bytesToHuman($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $bytes /= 1024, $i++) ;

        return round($bytes, 2) . $units[$i];
    }
}

if (!function_exists('random_str')) {
    /**
     * 获取随机字符串
     * @param int $length
     * @return string
     */
    function random_str($length = 4)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $string = '';
        for ($i = 0; $i < $length; $i++) {
            $string .= $characters[rand(0, strlen($characters) - 1)];
        }
        return substr(sha1($string), 0, $length);
    }
}

if (!function_exists('getExportPath')) {
    /**
     * 获取导出路径
     */
    function getExportPath($user_id, $filename, $user_type = 'user')
    {
        return 'export' . DIRECTORY_SEPARATOR . $user_type . '_' . $user_id . DIRECTORY_SEPARATOR . date('Ymd') . DIRECTORY_SEPARATOR . random_str() . DIRECTORY_SEPARATOR . $filename . '.xlsx';
    }
}

if (!function_exists('getAgeByBirth')) {
    /**
     * 根据生日获取年龄
     */
    function getAgeByBirth($birthday)
    {
        $birth = new \DateTime($birthday);
        $now = new \DateTime();
        $diff = $now->diff($birth);

        return $diff->y . '岁' . $diff->m . '月';
    }
}

if (!function_exists('genTree')) {
    /**
     * 获取无限分级
     * @param array  $items 数据源
     * @param string $pid   父类键值
     * @param string $son   子类键名
     * @return array
     */
    function genTree($items, $pid = "pid", $son = "children")
    {
        $map = [];
        $tree = [];
        foreach ($items as &$it) {
            $map[$it['id']] = &$it;
        }
        foreach ($items as &$it) {
            $parent = &$map[$it[$pid]];
            if ($parent) {
                $parent[$son][] = &$it;
            } else {
                $tree[] = &$it;
            }
        }
        return $tree;
    }
}

if (!function_exists('allSublevel')) {
    /**
     * 获取所有子级及同父级数据
     * @param array $items 数据源
     * @param array $map   当前的id
     * @return array
     */
    function allSublevel($items, $map)
    {
        $i = 0;
        foreach ($items as $id => $it) {
            // 获取子级数据
            if (in_array($it['pid'], $map)) {
                $i++;
                array_unshift($map, $it['id']);
                unset($items[$id]);
            }
        }
        if ($i > 0) {
            allSublevel($items, $map);
        }
        return $map;
    }
}

if (!function_exists('orderNumber')) {
    function orderNumber()
    {
        $order = preg_replace('/\./', '', microtime(true) . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT));
        if (strlen($order) == 18) {
            $order = $order . '0';
        }
        return $order;
    }
}

if (!function_exists('time_diff')) {
    /**
     * 计算时间差
     * @param $date
     * @return array
     */
    function time_diff($date)
    {
        $t = strtotime($date) - time();//单位"秒"
        $arr = [];
        $day = intval($t / 86400);//天
        $arr['day'] = $day;
        $hour = intval((($t / 86400) - $day) * 24);//小时
        $arr['hour'] = $hour;
        $minute = intval((((($t / 86400) - $day) * 24) - $hour) * 60);//分钟
        $arr['minute'] = $minute;
        $second = intval(((((((($t / 86400) - $day) * 24) - $hour) * 60) - $minute) * 60));//秒
        $arr['second'] = $second;
        return $arr;
    }
}
