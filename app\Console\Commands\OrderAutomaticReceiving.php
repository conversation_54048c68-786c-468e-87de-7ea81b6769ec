<?php

namespace App\Console\Commands;

use App\Models\GoodIndent;
use Illuminate\Console\Command;

class OrderAutomaticReceiving extends Command
{
    protected $signature = 'order:automatic-receiving';

    protected $description = '商品订单自动收货';

    public function handle()
    {
        GoodIndent::where('state', GoodIndent::GOOD_INDENT_STATE_TAKE)
            ->where('receiving_time', '<=', date('Y-m-d H:i:s'))
            ->select('id')
            ->get()
            ->each(function ($goodIndent) {
                GoodIndent::receipt($goodIndent->id);
            });
    }
}
