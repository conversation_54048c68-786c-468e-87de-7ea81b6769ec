<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'google' => [
        'application_name' => env('GOOGLE_APPLICATION_NAME', '好碗力'),
        'credentials_path' => env('GOOGLE_APPLICATION_CREDENTIALS', '/home/<USER>/credentials.json'),
    ],

    'alioss' => [
        'key_id' => env('ALIOSS_KEYID'),
        'key_secret' => env('ALIOSS_KEYSECRET'),
        'server' => env('ALIOSS_SERVER'),
        'bucket' => env('ALIOSS_BUCKETNAME'),
        'image_max_size' => env('ALIOSS_IMAGE_MAXSIZE', '15m'),
        'video_max_size' => env('ALIOSS_VIDEO_MAXSIZE', '200m'),
        'img_max_width' => env('ALIOSS_IMGMAXWIDTH', 5000),
        'img_max_height' => env('ALIOSS_IMGMAXHEIGHT', 5000),
        'serverinternal' => env('ALIOSS_SERVERINTERNAL'),
        'ocr' => env('ALIOCR_SERVER'),
        'ocr_key'=>env('ALIOCR_KEYID'),
        'ocr_secert'=>env('ALIOCR_KEYSECRET')
    ],

    'getui' => [
        'url' => env('GETUI_URL'),
        'app_id' => env('GETUI_APPID'),
        'app_key' => env('GETUI_APPKEY'),
        'master_secret' => env('GETUI_MASTERSECRET'),
    ],

    'ali_bailian' => [
        'api_key' => env('ALI_BAILIAN_API_KEY'),
        'base_url' => env('ALI_BAILIAN_BASE_URL', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1'),
        'default_model' => env('ALI_BAILIAN_DEFAULT_MODEL', 'qwen-max-latest'),
        'vision_model' => env('ALI_BAILIAN_VISION_MODEL', 'qwen-vl-max'),
        'unified_model' => env('ALI_BAILIAN_UNIFIED_MODEL', 'qwen-vl-max-latest'),
    ],

    'go_easy'     => [
        'url'        => env('GO_EASY_URL', 'https://rest-hz.goeasy.io'),
        'rest_key'   => env('GO_EASY_REST_KEY', ''),
        'secret_key' => env('GO_EASY_SECRET_KEY', ''),
    ]
];
