<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\SystemMenu;
use Illuminate\Http\Request;

class SystemMenuController extends Controller
{
    public function getList(Request $request)
    {
        $records = SystemMenu::getTree();
        return ResponseHelper::success($records);
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'        => 'sometimes|integer|gte:0',
            'parent_id' => 'nullable|integer|gte:0',
            'name'      => 'required|max:50',
            'path'      => 'required|max:255',
            'api'       => 'sometimes|max:255',
            'weigh'     => 'nullable|numeric',
            'status'    => 'required|in:1,-1'
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = SystemMenu::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = SystemMenu::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
