<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class OperateLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ip',
        'operate_type',
        'link_type',
        'link_id',
        'title',
        'content',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function supervisor()
    {
        return $this->belongsTo(User::class, 'user_id')->withTrashed();
    }

    public static function getList($search_data = array())
    {
        //遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $operateType = $search_data['operate_type'] ?? "";
        $linkType = $search_data['link_type'] ?? "";
        $linkId = $search_data['link_id'] ?? "";
        $supervisorId = $search_data['supervisor_id'] ?? "";
        $userId = $search_data['user_id'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('title', 'like', "%$keyword%")
                    ->orWhere('content', 'like', "%$keyword%");
            });
        })
            ->when($operateType, function (Builder $query) use ($operateType) {
                $query->where('operate_type', $operateType);
            })
            ->when($linkType, function (Builder $query) use ($linkType) {
                $query->where('link_type', $linkType);
            })
            ->when($linkId, function (Builder $query) use ($linkId) {
                $query->where('link_id', $linkId);
            })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->with('supervisor', function (Builder $query) {
                $query->select('id', 'name', 'phone');
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('created_at', 'desc');
        if ($limit == -1) {
            return $query->get()->toArray();
        }
        return $query->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)->first();
    }
}
