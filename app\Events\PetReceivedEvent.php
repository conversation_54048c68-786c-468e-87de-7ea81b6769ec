<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetReceivedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 宠物 ID
     *
     * @var int
     */
    public $petId;

    /**
     * 原所有者 ID
     *
     * @var int
     */
    public $originalOwnerId;

    /**
     * 新所有者 ID
     *
     * @var int
     */
    public $newOwnerId;

    /**
     * 接收类型（转让或家庭成员）
     *
     * @var string
     */
    public $receiveType;

    /**
     * Create a new event instance.
     *
     * @param int $petId 宠物 ID
     * @param int $originalOwnerId 原所有者 ID
     * @param int $newOwnerId 新所有者 ID
     * @param string $receiveType 接收类型（'transfer' 或 'family'）
     * @return void
     */
    public function __construct($petId, $originalOwnerId, $newOwnerId, $receiveType)
    {
        $this->petId = $petId;
        $this->originalOwnerId = $originalOwnerId;
        $this->newOwnerId = $newOwnerId;
        $this->receiveType = $receiveType;
    }
}
