<?php

namespace Database\Seeders;

use App\Models\AnalysisQuickQuestion;
use Illuminate\Database\Seeder;

class AnalysisQuickQuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 食物分析快捷问题
        $foodQuestions = [
            [
                'analysis_type' => 'food',
                'question' => '这个食物我的宠物能吃吗？',
                'icon' => '/images/questions/food_can_eat.png',
                'pet_type' => 'general',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'analysis_type' => 'food',
                'question' => '这个食物对宠物有什么好处？',
                'icon' => '/images/questions/food_benefits.png',
                'pet_type' => 'general',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'analysis_type' => 'food',
                'question' => '这个食物对宠物有什么坏处？',
                'icon' => '/images/questions/food_risks.png',
                'pet_type' => 'general',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'analysis_type' => 'food',
                'question' => '这个食物怎么给宠物吃最好？',
                'icon' => '/images/questions/food_how_to_feed.png',
                'pet_type' => 'general',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'analysis_type' => 'food',
                'question' => '这个食物一次给多少合适？',
                'icon' => '/images/questions/food_portion.png',
                'pet_type' => 'general',
                'sort_order' => 5,
                'status' => 1
            ]
        ];
        
        // 排泄物分析快捷问题
        $stoolQuestions = [
            [
                'analysis_type' => 'stool',
                'question' => '这个排泄物正常吗？',
                'icon' => '/images/questions/stool_normal.png',
                'pet_type' => 'general',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'analysis_type' => 'stool',
                'question' => '这个排泄物表明什么健康问题？',
                'icon' => '/images/questions/stool_health_issue.png',
                'pet_type' => 'general',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'analysis_type' => 'stool',
                'question' => '需要去看兽医吗？',
                'icon' => '/images/questions/stool_vet_visit.png',
                'pet_type' => 'general',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'analysis_type' => 'stool',
                'question' => '怎么改善这种情况？',
                'icon' => '/images/questions/stool_improve.png',
                'pet_type' => 'general',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'analysis_type' => 'stool',
                'question' => '需要调整饮食吗？',
                'icon' => '/images/questions/stool_diet.png',
                'pet_type' => 'general',
                'sort_order' => 5,
                'status' => 1
            ]
        ];
        
        // 健康分析快捷问题
        $healthQuestions = [
            [
                'analysis_type' => 'health',
                'question' => '这个症状正常吗？',
                'icon' => '/images/questions/health_normal.png',
                'pet_type' => 'general',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'analysis_type' => 'health',
                'question' => '这个症状表明什么健康问题？',
                'icon' => '/images/questions/health_issue.png',
                'pet_type' => 'general',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'analysis_type' => 'health',
                'question' => '需要去看兽医吗？',
                'icon' => '/images/questions/health_vet_visit.png',
                'pet_type' => 'general',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'analysis_type' => 'health',
                'question' => '怎么护理才能改善？',
                'icon' => '/images/questions/health_care.png',
                'pet_type' => 'general',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'analysis_type' => 'health',
                'question' => '需要调整饮食吗？',
                'icon' => '/images/questions/health_diet.png',
                'pet_type' => 'general',
                'sort_order' => 5,
                'status' => 1
            ]
        ];
        
        // 合并所有快捷问题
        $allQuestions = array_merge($foodQuestions, $stoolQuestions, $healthQuestions);
        
        // 添加到数据库
        foreach ($allQuestions as $question) {
            AnalysisQuickQuestion::updateOrCreate(
                [
                    'analysis_type' => $question['analysis_type'],
                    'question' => $question['question'],
                    'pet_type' => $question['pet_type']
                ],
                $question
            );
        }
    }
}
