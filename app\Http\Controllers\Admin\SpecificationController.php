<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\Specification;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * @group   specification
 * 规格管理
 * Class SpecificationController
 * @package App\Http\Controllers\Admin
 */
class SpecificationController extends Controller
{

    public function getList(Request $request)
    {
        $request->validate([
            'page'                   => 'sometimes|integer|gt:0',
            'per_page'               => 'sometimes|integer|between:1,200',
            'keyword'                => 'nullable|max:255',
            'sort_name'              => 'nullable|in:id,created_at,updated_at',
            'sort_by'                => 'nullable|in:asc,desc',
            'specification_group_id' => 'nullable|integer|gt:0|exists:specifications_groups,id',
        ]);
        $formData = $request->all();
        $records = Specification::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Specification::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                     => 'sometimes|integer|gte:0',
            'specification_group_id' => 'nullable|integer|gt:0|exists:specifications_groups,id',
            'name'                   => 'required|max:60',
            'name_tc'                => 'required|max:60',
            'name_en'                => 'required|max:255',
            'type'                   => 'required|in:1,2,3',
            'is_search'              => 'required|in:0,1',
            'location'               => 'required|in:0,1,2',
            'value'                  => 'nullable|string',
            'label'                  => 'nullable|string',
            'sort_order'             => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = Specification::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Specification::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
