<?php

namespace App\Listeners;

use App\Events\PetFamilyRequestEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPetFamilyRequestNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\PetFamilyRequestEvent $event
     * @return void
     */
    public function handle(PetFamilyRequestEvent $event)
    {
        try {
            // 获取家人信息
            $owner = AppUser::find($event->fromUserId);

            if (!$owner) {
                Log::error('Family request notification failed: Owner not found', [
                    'requestId'  => $event->requestId,
                    'toUserId'   => $event->toUserId,
                    'fromUserId' => $event->fromUserId,
                    'petId'      => $event->petId,
                ]);
                return;
            }

            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Family request notification failed: Pet not found', [
                    'requestId'  => $event->requestId,
                    'toUserId'   => $event->toUserId,
                    'fromUserId' => $event->fromUserId,
                    'petId'      => $event->petId,
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->toUserId, // 接收通知的用户ID（被关注的用户）
                NotificationSetting::TYPE_PARENT_INVITE, // 通知类型
                '新的家人申請',
                "用戶 {$owner->username} 邀請你成爲寵物 {$pet->name} 的家人",
                $event->fromUserId, // 发送者ID
                $event->requestId, // 相关ID
                NotificationSetting::RELATION_TYPE_FAMILY_REQUEST // 相关类型
            );

            Log::info('Family request notification sent', [
                'requestId'  => $event->requestId,
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId,
                'petId'      => $event->petId,
            ]);
        } catch (\Exception $e) {
            Log::error('Family request notification failed', [
                'error'      => $e->getMessage(),
                'requestId'  => $event->requestId,
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId,
                'petId'      => $event->petId,
            ]);
        }
    }
}
