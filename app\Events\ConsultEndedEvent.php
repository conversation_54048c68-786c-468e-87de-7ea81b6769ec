<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ConsultEndedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 咨询ID
     *
     * @var int
     */
    public $consultId;

    /**
     * 操作类型: user, dietitian, system
     * @var string
     */
    public $operateType;

    public function __construct($consultId, $operateType)
    {
        $this->consultId = $consultId;
        $this->operateType = $operateType;
    }
}
