<?php

namespace App\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Events\MessageSent;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class LogSentMessage
{
    public function handle(MessageSent $event)
    {
        // 获取邮件头中的自定义标记
        $headers = $event->message->getHeaders();
        $taskType = $headers->getHeaderBody('X-Task-Type');
        $taskID = $headers->getHeaderBody('X-Task-ID');
        Log::channel('cli')->info('LogSentMessage, type: ' . $taskType . ', id:' . $taskID);
        if ($taskType && $taskID) {
            app($taskType)->where('id', $taskID)->update(['email_status' => 2, 'sent_at' => date('Y-m-d H:i:s')]);
        }
    }
}
