<?php

/**
 * 简化后的晶片编号验证测试
 */

echo "🧪 简化后的晶片编号验证测试\n";
echo "============================\n\n";

// 简化后的验证函数
function validateChipCodeData(array $data): array
{
    $result = [
        'chip_number' => null
    ];

    // 直接返回晶片编号，不进行格式验证
    if (isset($data['chip_number']) && !empty($data['chip_number']) && $data['chip_number'] !== 'null') {
        $chipNumber = trim($data['chip_number']);
        $result['chip_number'] = $chipNumber;
        echo "✅ 晶片编号: {$chipNumber}\n";
    } else {
        echo "❌ 晶片编号为空或null\n";
    }

    return $result;
}

// 测试用例 - 现在所有格式都应该被接受
$testCases = [
    ['chip_number' => '100999998'],           // 9位数字
    ['chip_number' => '123456789012345'],     // 15位数字
    ['chip_number' => '123456'],              // 6位数字
    ['chip_number' => '12345'],               // 5位数字
    ['chip_number' => '123456789012345678901'], // 21位数字
    ['chip_number' => 'ABC123456789'],        // 字母数字混合
    ['chip_number' => 'ABC-123-456'],         // 包含特殊字符
    ['chip_number' => '中文123'],              // 包含中文
    ['chip_number' => ''],                    // 空字符串
    ['chip_number' => 'null'],                // 字符串null
    ['chip_number' => null],                  // null值
];

echo "测试各种晶片编号格式（简化验证）:\n";
echo str_repeat('-', 50) . "\n";

foreach ($testCases as $index => $testData) {
    echo "测试 " . ($index + 1) . ": ";
    if (isset($testData['chip_number'])) {
        $displayValue = $testData['chip_number'] === null ? 'null' : "'{$testData['chip_number']}'";
        echo "输入: {$displayValue} ";
    }
    
    $result = validateChipCodeData($testData);
    echo "-> 结果: " . ($result['chip_number'] === null ? 'null' : "'{$result['chip_number']}'") . "\n";
}

echo "\n" . str_repeat('-', 50) . "\n";

// 特别测试原问题
echo "\n🎯 测试原问题:\n";
$problemCase = ['chip_number' => '100999998'];
echo "AI解析结果: " . json_encode($problemCase) . "\n";

$result = validateChipCodeData($problemCase);
echo "验证结果: " . json_encode($result) . "\n";

if ($result['chip_number'] === '100999998') {
    echo "✅ 原问题已解决！晶片编号直接返回，无需验证\n";
} else {
    echo "❌ 仍有问题\n";
}

echo "\n📋 简化说明:\n";
echo "1. 删除了所有正则表达式验证\n";
echo "2. 删除了长度限制\n";
echo "3. 删除了格式限制\n";
echo "4. 只要AI识别出内容就直接返回\n";
echo "5. 简化了AI提示词\n";

echo "\n✅ 晶片编号验证已完全简化！\n";
