<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OwnerTransferEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 宠物 ID
     *
     * @var int
     */
    public $petId;

    /**
     * 原宠物所有者 ID
     *
     * @var int
     */
    public $oldOwnerId;

    /**
     * 新宠物所有者 ID
     *
     * @var int
     */
    public $newOwnerId;

    /**
     * Create a new event instance.
     *
     * @param int $petId 宠物 ID
     * @param int $ownerId 原宠物所有者 ID
     * @param int $parentId 新宠物所有者 ID
     * @return void
     */
    public function __construct($petId, $oldOwnerId, $newOwnerId)
    {
        $this->petId = $petId;
        $this->oldOwnerId = $oldOwnerId;
        $this->newOwnerId = $newOwnerId;
    }
}
