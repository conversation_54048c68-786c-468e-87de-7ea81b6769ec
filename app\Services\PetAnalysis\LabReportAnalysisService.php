<?php

namespace App\Services\PetAnalysis;

use App\Models\Pet;
use App\Services\AliBailian\AliBailianService;
use App\Services\PetAnalysis\LabReportCacheService;
use App\Services\PetAnalysis\ImagePreprocessingService;
use Illuminate\Support\Facades\Log;

/**
 * 实验室报告（血液/尿液）图像直出结构化分析服务
 * - 直接将多张图片交给视觉大模型
 * - 输出 organs -> indicators 三态（少/偏多/正常）
 * - 每个指标包含 increase_implications / decrease_implications
 */
class LabReportAnalysisService extends BaseAnalysisService
{
    protected LabReportCacheService $cacheService;
    protected ImagePreprocessingService $preprocessingService;

    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);
        $this->cacheService = new LabReportCacheService();
        $this->preprocessingService = new ImagePreprocessingService();
    }

    /**
     * 执行实验室报告分析（实现接口签名，内部忽略除 images/petId 外的参数）
     *
     * @param string $query 未使用
     * @param array|null $images 图片URL数组（必填）
     * @param int|null $petId 宠物ID（可选，用于 basic_info 与权限层已校验）
     * @param string|null $sessionId 未使用
     * @param array|null $context 未使用
     * @param bool $stream 未使用（固定false）
     * @param string|null $responseSessionId 未使用
     * @param string|null $bodyPart 未使用
     * @param array|null $symptoms 未使用
     * @param array|null $petsInfo 未使用
     * @return array { status, message, data: { basic_info, organs } }
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, ?array $context = null, bool $stream = false, ?string $responseSessionId = null, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        $images = $images ?? [];

        // 🚀 性能优化：检查缓存
        $imageHash = $this->cacheService->generateImageHash($images);
        $cacheKey = $this->cacheService->generateCacheKey($imageHash, $petId);

        // 尝试从缓存获取结果
        $cachedResult = $this->cacheService->getCachedResult($cacheKey);
        if ($cachedResult !== null) {
            // 更新缓存状态标识
            if (isset($cachedResult['meta'])) {
                $cachedResult['meta']['cache_status'] = 'hit';
                $cachedResult['meta']['processing_time'] = 0.01; // 缓存命中几乎无耗时
            }

            \Log::info('🚀 缓存命中，直接返回结果', [
                'cache_key' => $cacheKey,
                'pet_id' => $petId,
                'images_count' => count($images),
                'cached_organs_count' => count($cachedResult['data']['organs'] ?? []),
                'cached_indicators_count' => $this->countTotalIndicators($cachedResult['data']['organs'] ?? []),
                'cache_quality_score' => $cachedResult['meta']['quality_score'] ?? 'unknown',
                'cached_at' => $cachedResult['meta']['cached_at'] ?? 'unknown'
            ]);
            return $cachedResult;
        }

        // 获取宠物信息（用于 basic_info 与物种兜底范围）
        $petInfo = $this->getPetInfo($petId);

        // 🔧 图片预处理分析
        $imageAnalysis = $this->preprocessingService->analyzeImageQuality($images);
        $suitabilityCheck = $this->preprocessingService->isImageSuitableForAI($images);

        \Log::info('图片预处理分析', [
            'pet_id' => $petId,
            'image_count' => count($images),
            'quality_score' => $imageAnalysis['quality_score'],
            'suitable' => $suitabilityCheck['suitable'],
            'confidence' => $suitabilityCheck['confidence']
        ]);

        // 构建优化的提示词
        $prompt = $this->buildLabReportPrompt($petInfo, $imageAnalysis);

        // 调用AI（增加错误处理和性能监控）
        try {
            $startTime = microtime(true);
            $response = $this->aiService->chat(
                $prompt,
                $images,
                null, // 不复用会话
                0.1   // 🚀 性能优化：更稳定的结构化输出 0.2→0.1
            );
            $duration = microtime(true) - $startTime;

            // 记录性能指标
            \Log::info('AI分析耗时', [
                'duration' => round($duration, 2),
                'images_count' => count($images),
                'pet_id' => $petId
            ]);

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            \Log::error('AI服务连接失败', [
                'error' => $e->getMessage(),
                'pet_id' => $petId,
                'images_count' => count($images)
            ]);
            return [
                'status' => 'error',
                'message' => '网络连接失败，请检查网络后重试',
                'data' => []
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            \Log::error('AI服务请求失败', [
                'error' => $e->getMessage(),
                'code' => $e->getCode(),
                'pet_id' => $petId
            ]);
            return [
                'status' => 'error',
                'message' => 'AI服务暂时不可用，请稍后重试',
                'data' => []
            ];
        } catch (\Exception $e) {
            \Log::error('AI调用异常', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'pet_id' => $petId
            ]);
            return [
                'status' => 'error',
                'message' => '分析服务异常，请稍后重试',
                'data' => []
            ];
        }

        $content = $response['choices'][0]['message']['content'] ?? '';
        if (!$content) {
            \Log::error('❌ AI未返回任何内容', [
                'pet_id' => $petId,
                'images_count' => count($images),
                'response_structure' => array_keys($response),
                'choices_count' => count($response['choices'] ?? []),
                'reason' => 'ai_no_content_returned'
            ]);
            return [
                'status' => 'error',
                'message' => 'AI未返回内容',
                'data' => [
                    'failure_reason' => 'ai_no_content_returned',
                    'failure_reasons' => ['ai_no_content_returned'],
                    'response_structure' => array_keys($response)
                ]
            ];
        }

        \Log::info('✅ AI返回内容成功', [
            'pet_id' => $petId,
            'content_length' => strlen($content),
            'content_preview' => substr($content, 0, 100),
            'processing_time' => round($duration ?? 0, 2)
        ]);

        // 解析JSON（增强错误处理和容错机制）
        $json = $this->extractJson($content);
        if (is_null($json)) {
            \Log::warning('🚨 初次JSON解析失败', [
                'pet_id' => $petId,
                'content_length' => strlen($content),
                'content_preview' => substr($content, 0, 200),
                'reason' => 'initial_json_parse_failed'
            ]);

            // 尝试清理和修复JSON
            $cleanedContent = $this->cleanJsonContent($content);
            $json = $this->extractJson($cleanedContent);

            if (is_null($json)) {
                \Log::warning('🚨 清理后JSON解析仍失败', [
                    'pet_id' => $petId,
                    'cleaned_content_preview' => substr($cleanedContent, 0, 200),
                    'reason' => 'cleaned_json_parse_failed'
                ]);

                // 嘗試從部分內容中提取有用信息
                $partialJson = $this->extractPartialJson($content);
                if ($partialJson !== null) {
                    $json = $partialJson;
                    \Log::info('✅ 從部分內容中成功提取JSON', [
                        'pet_id' => $petId,
                        'extracted_organs' => count($json['organs'] ?? []),
                        'reason' => 'partial_json_extraction_success'
                    ]);
                } else {
                    \Log::error('❌ JSON解析完全失敗 - 所有方法都無效', [
                        'pet_id' => $petId,
                        'content_preview' => substr($content, 0, 500),
                        'content_length' => strlen($content),
                        'has_curly_braces' => (strpos($content, '{') !== false && strpos($content, '}') !== false),
                        'has_organs_keyword' => strpos($content, 'organs') !== false,
                        'reason' => 'complete_json_parse_failure'
                    ]);
                    return [
                        'status' => 'error',
                        'message' => '图片未识别到完整的参数与数值',
                        'data' => [
                            'raw_content' => $content,
                            'failure_reason' => 'json_parse_failure',
                            'failure_reasons' => ['json_parse_failure'],
                            'debug_info' => [
                                'content_length' => strlen($content),
                                'has_json_structure' => (strpos($content, '{') !== false && strpos($content, '}') !== false),
                                'has_organs_keyword' => strpos($content, 'organs') !== false
                            ]
                        ]
                    ];
                }
            } else {
                \Log::info('✅ 清理后JSON解析成功', [
                    'pet_id' => $petId,
                    'organs_count' => count($json['organs'] ?? []),
                    'reason' => 'cleaned_json_parse_success'
                ]);
            }
        } else {
            \Log::info('✅ 初次JSON解析成功', [
                'pet_id' => $petId,
                'organs_count' => count($json['organs'] ?? []),
                'reason' => 'initial_json_parse_success'
            ]);
        }

        // 组装 basic_info
        $basicInfo = [
            'pet_id' => $petInfo['id'] ?? null,
            'pet_name' => $petInfo['name'] ?? null,
            'pet_type' => $petInfo['type'] ?? null,
            'pet_type_en' => $petInfo['type_en'] ?? null,
            'breed' => $petInfo['breed'] ?? null,
            'sex' => $petInfo['sex'] ?? null,
            'age' => $petInfo['age'] ?? null,
            'weight' => $petInfo['weight'] ?? null,
            'images_count' => count($images),
            'analyzed_at' => now()->format('Y-m-d H:i:s'),
        ];

        // 标准化器官/指标结构，确保与前端图表约定一致，且边界值判定正确且三态合规
        $organs = $this->normalizeLabOrgans($json['organs'] ?? []);

        $totalIndicators = $this->countTotalIndicators($organs);

        \Log::info('🔍 器官数据标准化完成', [
            'pet_id' => $petId,
            'organs_count' => count($organs),
            'organs_keys' => array_keys($organs),
            'total_indicators' => $totalIndicators
        ]);

        // 🔍 检查是否存在"有器官但无指标"的问题，尝试重新分析
        if (count($organs) > 0 && $totalIndicators === 0) {
            \Log::warning('🚨 检测到空指标问题，尝试重新分析', [
                'pet_id' => $petId,
                'organs_count' => count($organs),
                'total_indicators' => $totalIndicators,
                'attempting_retry' => true
            ]);

            // 尝试用更强化的提示词重新分析
            $retryResult = $this->retryAnalysisWithEnhancedPrompt($images, $petId);
            if ($retryResult !== null && $this->countTotalIndicators($retryResult) > 0) {
                \Log::info('🔧 重新分析成功', [
                    'pet_id' => $petId,
                    'retry_indicators_count' => $this->countTotalIndicators($retryResult)
                ]);
                $organs = $retryResult;
                $totalIndicators = $this->countTotalIndicators($organs);
            } else {
                \Log::warning('🚨 重新分析仍然失败', [
                    'pet_id' => $petId,
                    'retry_indicators_count' => $retryResult ? $this->countTotalIndicators($retryResult) : 0
                ]);
            }
        }

        // 检查是否为空结果或图片拍摄不完整
        $isEmpty = empty($organs);
        $isIncomplete = $this->isImageIncomplete($json);

        if ($isEmpty || $isIncomplete) {
            // 详细记录失败原因
            $failureReason = [];
            $debugInfo = [];

            if ($isEmpty) {
                $failureReason[] = 'organs_empty';
                $debugInfo['organs_empty'] = true;
                $debugInfo['raw_organs_data'] = $json['organs'] ?? null;
            }

            if ($isIncomplete) {
                $failureReason[] = 'image_incomplete';
                $debugInfo['image_incomplete'] = true;
                $debugInfo['incomplete_analysis'] = $this->getIncompleteAnalysisDetails($json);
            }

            \Log::error('❌ 图片识别失败 - 数据不完整', [
                'pet_id' => $petId,
                'failure_reasons' => $failureReason,
                'organs_empty' => $isEmpty,
                'image_incomplete' => $isIncomplete,
                'debug_info' => $debugInfo,
                'images_count' => count($images)
            ]);

            return [
                'status' => 'error',
                'message' => '图片未识别到完整的参数与数值',
                'data' => [
                    'raw_content' => $content,
                    'failure_reasons' => $failureReason,
                    'debug_info' => $debugInfo
                ]
            ];
        }

        // 数据质量评估
        $qualityScore = $this->calculateDataQuality($organs);

        $result = [
            'status' => 'success',
            'message' => '分析成功',
            'data' => [
                'basic_info' => $basicInfo,
                'organs' => $organs,
            ],
            'meta' => [
                'processing_time' => round($duration ?? 0, 2),
                'quality_score' => $qualityScore,
                'indicators_count' => $this->countTotalIndicators($organs),
                'cache_status' => 'miss'
            ]
        ];

        // 🚀 性能优化：保存到缓存
        $this->cacheService->cacheResult($cacheKey, $result);

        \Log::info('✅ 实验室报告分析成功完成', [
            'cache_key' => $cacheKey,
            'pet_id' => $petId,
            'processing_time' => round($duration ?? 0, 2),
            'quality_score' => $qualityScore,
            'total_organs' => count($organs),
            'total_indicators' => $this->countTotalIndicators($organs),
            'abnormal_indicators' => $this->countAbnormalIndicators($organs),
            'images_processed' => count($images),
            'success_metrics' => [
                'organs_identified' => array_keys($organs),
                'quality_score' => $qualityScore,
                'cache_status' => 'miss'
            ]
        ]);

        return $result;
    }

    /**
     * 从模型返回提取严格JSON（兼容包含多余文本/代码块）
     */
    protected function extractJson(string $text): ?array
    {
        $trimmed = trim($text);
        // 直接尝试解析
        $decoded = json_decode($trimmed, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }
        // 尝试提取第一个花括号 JSON 块
        $start = strpos($trimmed, '{');
        $end = strrpos($trimmed, '}');
        if ($start !== false && $end !== false && $end > $start) {
            $candidate = substr($trimmed, $start, $end - $start + 1);
            $decoded2 = json_decode($candidate, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded2)) {
                return $decoded2;
            }
        }
        Log::warning('LabReportAnalysisService JSON parse failed', ['content' => $text]);
        return null;
    }

    /**
     * 从部分内容中提取JSON（容错机制）
     * 当完整JSON解析失败时，尝试从文本中提取有用的指标信息
     */
    protected function extractPartialJson(string $text): ?array
    {
        // 尝试查找organs部分
        if (preg_match('/"organs"\s*:\s*\{([^}]+)\}/', $text, $matches)) {
            $organsContent = $matches[1];

            // 构建基本的JSON结构
            $result = ['organs' => []];

            // 尝试提取器官信息
            if (preg_match_all('/"(\w+)"\s*:\s*\{([^}]+)\}/', $organsContent, $organMatches, PREG_SET_ORDER)) {
                foreach ($organMatches as $organMatch) {
                    $organName = $organMatch[1];
                    $organContent = $organMatch[2];

                    // 基本器官结构
                    $organ = [
                        'name' => $this->getOrganChineseName($organName),
                        'name_en' => $organName,
                        'indicators' => []
                    ];

                    // 尝试提取指标信息（简化版）
                    if (preg_match_all('/"(\w+)"\s*:\s*"([^"]+)"/', $organContent, $indicatorMatches, PREG_SET_ORDER)) {
                        $indicator = [];
                        for ($i = 0; $i < count($indicatorMatches); $i += 2) {
                            if (isset($indicatorMatches[$i], $indicatorMatches[$i+1])) {
                                $key = $indicatorMatches[$i][1];
                                $value = $indicatorMatches[$i][2];
                                $indicator[$key] = $value;
                            }
                        }

                        if (!empty($indicator)) {
                            $organ['indicators'][] = $indicator;
                        }
                    }

                    if (!empty($organ['indicators'])) {
                        $result['organs'][$organName] = $organ;
                    }
                }
            }

            // 如果提取到了有效信息，返回結果
            if (!empty($result['organs'])) {
                return $result;
            }
        }

        return null;
    }

    /**
     * 獲取器官中文名稱
     */
    protected function getOrganChineseName(string $organEn): string
    {
        $map = [
            'kidney' => '腎臟',
            'liver' => '肝臟',
            'pancreas' => '胰腺',
            'electrolytes' => '電解質',
            'others' => '其他',
            'cbc' => '血常規',
            'urine' => '尿液'
        ];

        return $map[$organEn] ?? $organEn;
    }

    /**
     * 规范化器官与指标结构
     * - 仅三态：偏少/偏多/正常
     * - 边界值(min/max)按区间内处理，等于边界视为“正常”
     * - 计算 abnormal_count 与 summary_status
     * - 纠正模型可能给出的同义状态（低/高/偏低/偏高/少/多等）
     *
     * @param mixed $organs
     * @return array
     */
    protected function normalizeLabOrgans(mixed $organs): array
    {
        // 统一为数组结构
        if (is_object($organs)) {
            $organs = (array)$organs;
        }
        if (!is_array($organs)) {
            return [];
        }

        $normalized = [];

        // 🩸 血液学检验器官标准化映射
        $hematologyMapping = [
            'cbc' => ['name' => '紅細胞系列', 'name_en' => 'cbc'],
            'wbc' => ['name' => '白細胞系列', 'name_en' => 'wbc'],
            'platelet' => ['name' => '血小板系列', 'name_en' => 'platelet']
        ];

        // 检测是否为血液学检验报告
        $isHematologyReport = $this->detectHematologyReport($organs);
        if ($isHematologyReport) {
            \Log::info('🩸 检测到血液学检验报告，使用血液学分类', [
                'organs_keys' => array_keys($organs)
            ]);

            // 重新分类血液学指标
            $organs = $this->reclassifyHematologyIndicators($organs);
        }

        // 如果存在 protein_profile，合并 TP/GLOB 到 others 后删除
        if (isset($organs['protein_profile'])) {
            $pp = is_array($organs['protein_profile']) ? $organs['protein_profile'] : (array)$organs['protein_profile'];
            $ppIndicators = $pp['indicators'] ?? [];
            if (is_object($ppIndicators)) { $ppIndicators = (array)$ppIndicators; }
            // 简化：直接处理为数组
            if (!is_array($ppIndicators)) { $ppIndicators = []; }

            // 仅迁移 TP/GLOB
            $toMove = [];
            foreach ($ppIndicators as $ind) {
                if (!is_array($ind)) { $ind = is_object($ind) ? (array)$ind : []; }
                $code = (string)($ind['code'] ?? $ind['name_en'] ?? $ind['name'] ?? '');
                if (in_array(strtoupper($code), ['TP','GLOB'])) {
                    $toMove[] = $ind;
                }
            }

            // 确保 others 存在并合并
            if (!isset($organs['others'])) {
                $organs['others'] = ['name' => '其他', 'name_en' => 'others', 'indicators' => []];
            }
            if (!is_array($organs['others'])) { $organs['others'] = (array)$organs['others']; }
            $othersIndicators = $organs['others']['indicators'] ?? [];
            if (!is_array($othersIndicators)) { $othersIndicators = []; }
            $organs['others']['indicators'] = array_merge($othersIndicators, $toMove);

            // 移除 protein_profile
            unset($organs['protein_profile']);
        }

        foreach ($organs as $organKey => $organData) {
            // 兼容对象/数组
            if (is_object($organData)) {
                $organData = (array)$organData;
            }
            $indicators = $organData['indicators'] ?? [];
            if (is_object($indicators)) {
                // 有些模型会返回对象形式，将其转为数组
                $indicators = (array)$indicators;
            }
            // 若是关联数组的指标，转为索引数组
            if ($this->isAssoc($indicators)) {
                $indicators = array_values($indicators);
            }

            $abnormalCount = 0;
            $normalizedIndicators = [];

            foreach ($indicators as $indicator) {
                if (is_object($indicator)) {
                    $indicator = (array)$indicator;
                }

                $code = (string)($indicator['code'] ?? ($indicator['name_en'] ?? $indicator['name'] ?? ''));
                $valueRaw = $indicator['value'] ?? null;
                $statusRaw = $indicator['status'] ?? null;
                $unit = $indicator['unit'] ?? null;
                $ref = $indicator['reference_range'] ?? [];
                if (is_object($ref)) { $ref = (array)$ref; }

                $min = isset($ref['min']) ? $this->toFloatOrNull($ref['min']) : null;
                $max = isset($ref['max']) ? $this->toFloatOrNull($ref['max']) : null;
                $value = $this->toFloatOrNull($valueRaw);

                // 🎯 判定优先级：优先使用数值范围计算，图片标记作为兜底
                $status = null;

                // 优先级1：数值范围计算（主要依据）
                if ($value !== null && $min !== null && $max !== null) {
                    if ($value < $min) {
                        $status = '偏少';
                    } elseif ($value > $max) {
                        $status = '偏多';
                    } else { // 包含等于边界时视为正常
                        $status = '正常';
                    }
                }

                // 优先级2：图片标记兜底（仅在数值范围无法计算时使用）
                if ($status === null && $statusRaw !== null) {
                    $status = $this->normalizeStatusString((string)$statusRaw);
                }

                // 兜底：未能判断时默认为正常（但如果缺少关键字段则跳过该指标）
                if ($status === null) {
                    // 如果连基本的 code 和 value 都没有，跳过该指标
                    if (empty($code) || $value === null) {
                        continue;
                    }
                    $status = '正常';
                }

                if ($status !== '正常') {
                    $abnormalCount++;
                }

                // 修正 reference_range 结构，允许缺失 min/max
                $refStruct = [
                    'min' => $min,
                    'max' => $max,
                    'text' => $ref['text'] ?? ($min !== null && $max !== null ? ($min . ' - ' . $max . ' ' . (string)($unit ?? '')) : ''),
                ];

                // 填回标准化后的指标
                $indicator['code'] = $code;
                $indicator['unit'] = $unit;
                $indicator['reference_range'] = $refStruct;
                $indicator['status'] = $status;

                // 保证 implications 为数组
                $indicator['increase_implications'] = isset($indicator['increase_implications']) && is_array($indicator['increase_implications'])
                    ? $indicator['increase_implications'] : [];
                $indicator['decrease_implications'] = isset($indicator['decrease_implications']) && is_array($indicator['decrease_implications'])
                    ? $indicator['decrease_implications'] : [];

                $normalizedIndicators[] = $indicator;
            }


            // 计算器官汇总状态
            $organData['indicators'] = $normalizedIndicators;
            $organData['abnormal_count'] = $abnormalCount;
            $organData['summary_status'] = $abnormalCount > 0 ? 'abnormal' : 'normal';

            $normalized[$organKey] = $organData;
        }

        return $normalized;
    }

    /**
     * 判断图片是否拍摄不完整（仅在明显缺失关键信息时返回true）
     * - 完全没有器官数据
     * - 所有指标都缺少基本字段（code/value）
     * - 有效指标数量过少（可能是图片模糊、遮挡等）
     *
     * 🔧 优化策略：降低检测阈值，减少误判，重点检测真正的识别失败
     */
    protected function isImageIncomplete(array $json): bool
    {
        $organs = $json['organs'] ?? [];
        if (empty($organs)) {
            \Log::warning('🚨 图片不完整检查 - 器官数据为空', [
                'reason' => 'no_organs_data',
                'json_keys' => array_keys($json)
            ]);
            return true;
        }

        $totalIndicators = 0;
        $validIndicators = 0;
        $completeIndicators = 0; // 完整指标（有code、value、unit）
        $hasAnyValue = false; // 是否有任何数值被识别
        $organDetails = []; // 每个器官的详细信息

        foreach ($organs as $organKey => $organ) {
            if (!is_array($organ)) { $organ = is_object($organ) ? (array)$organ : []; }
            $indicators = $organ['indicators'] ?? [];
            if (!is_array($indicators)) { $indicators = is_object($indicators) ? (array)$indicators : []; }

            $organInfo = [
                'name' => $organ['name'] ?? $organKey,
                'indicators_count' => count($indicators),
                'valid_indicators' => 0,
                'has_values' => false,
                'sample_indicators' => []
            ];

            foreach ($indicators as $ind) {
                if (!is_array($ind)) { $ind = is_object($ind) ? (array)$ind : []; }
                $totalIndicators++;

                $code = $ind['code'] ?? ($ind['name_en'] ?? $ind['name'] ?? null);
                $value = $ind['value'] ?? null;
                $unit = $ind['unit'] ?? null;

                // 记录前3个指标的详细信息用于调试
                if (count($organInfo['sample_indicators']) < 3) {
                    $organInfo['sample_indicators'][] = [
                        'code' => $code,
                        'value' => $value,
                        'unit' => $unit,
                        'has_code' => !empty($code),
                        'has_value' => $value !== null && $value !== '',
                        'is_numeric' => is_numeric($value)
                    ];
                }

                // 检查是否有任何数值被识别
                if ($value !== null && $value !== '') {
                    $hasAnyValue = true;
                    $organInfo['has_values'] = true;
                }

                // 基本有效：有 code 和 value
                if (!empty($code) && $value !== null) {
                    $validIndicators++;
                    $organInfo['valid_indicators']++;

                    // 完整指标：还要有 unit
                    if (!empty($unit)) {
                        $completeIndicators++;
                    }
                }
            }

            $organDetails[$organKey] = $organInfo;
        }

        // 记录详细的分析统计
        \Log::info('🔍 图片完整性分析统计', [
            'total_organs' => count($organs),
            'total_indicators' => $totalIndicators,
            'valid_indicators' => $validIndicators,
            'complete_indicators' => $completeIndicators,
            'has_any_value' => $hasAnyValue,
            'valid_ratio' => $totalIndicators > 0 ? round($validIndicators / $totalIndicators, 3) : 0,
            'organ_details' => $organDetails
        ]);

        // 🚨 严格检查：仅在明显失败时返回true

        // 1. 完全没有任何指标
        if ($totalIndicators === 0) {
            \Log::warning('🚨 图片不完整 - 完全没有指标', [
                'reason' => 'no_indicators',
                'organs_count' => count($organs)
            ]);
            return true;
        }

        // 2. 完全没有识别到任何数值（最关键的检查）
        if (!$hasAnyValue) {
            \Log::warning('🚨 图片不完整 - 没有任何数值', [
                'reason' => 'no_values',
                'total_indicators' => $totalIndicators
            ]);
            return true;
        }

        // 3. 有效指标比例极低（<10%）- 大幅降低阈值，仅检测严重失败
        if ($totalIndicators > 0 && ($validIndicators / $totalIndicators) < 0.1) {
            \Log::warning('🚨 图片不完整 - 有效指标比例过低', [
                'reason' => 'low_valid_ratio',
                'total_indicators' => $totalIndicators,
                'valid_indicators' => $validIndicators,
                'valid_ratio' => round($validIndicators / $totalIndicators, 3)
            ]);
            return true;
        }

        // 4. 指标总数极少（<1个）- 明显完全失败
        if ($totalIndicators < 1) {
            \Log::warning('🚨 图片不完整 - 指标总数过少', [
                'reason' => 'too_few_indicators',
                'total_indicators' => $totalIndicators
            ]);
            return true;
        }

        // 5. 有效指标数量绝对值极少（<1个）- 完全没有有效识别
        if ($validIndicators < 1) {
            \Log::warning('🚨 图片不完整 - 有效指标数量过少', [
                'reason' => 'too_few_valid_indicators',
                'valid_indicators' => $validIndicators
            ]);
            return true;
        }

        // 🎯 新增：检查是否所有指标都只有名称没有数值（OCR部分失败）
        $hasValidValue = false;
        $numericValueCount = 0;
        foreach ($organs as $organ) {
            if (!is_array($organ)) { $organ = is_object($organ) ? (array)$organ : []; }
            $indicators = $organ['indicators'] ?? [];
            if (!is_array($indicators)) { $indicators = is_object($indicators) ? (array)$indicators : []; }

            foreach ($indicators as $ind) {
                if (!is_array($ind)) { $ind = is_object($ind) ? (array)$ind : []; }
                $value = $ind['value'] ?? null;

                // 检查是否有真正的数值（不是空字符串或null）
                if ($value !== null && $value !== '' && is_numeric($value)) {
                    $hasValidValue = true;
                    $numericValueCount++;
                }
            }
        }

        // 如果没有任何有效数值，认为是识别失败
        if (!$hasValidValue) {
            \Log::warning('🚨 图片不完整 - 没有数值型数据', [
                'reason' => 'no_numeric_values',
                'total_indicators' => $totalIndicators,
                'numeric_value_count' => $numericValueCount
            ]);
            return true;
        }

        // 其他情况认为识别成功，交给后续处理
        \Log::info('✅ 图片完整性检查通过', [
            'total_indicators' => $totalIndicators,
            'valid_indicators' => $validIndicators,
            'numeric_value_count' => $numericValueCount,
            'valid_ratio' => round($validIndicators / $totalIndicators, 3)
        ]);
        return false;
    }

    /**
     * 获取不完整分析的详细信息（用于调试）
     */
    protected function getIncompleteAnalysisDetails(array $json): array
    {
        $details = [
            'has_organs_key' => isset($json['organs']),
            'organs_type' => gettype($json['organs'] ?? null),
            'organs_count' => 0,
            'total_indicators' => 0,
            'indicators_with_code' => 0,
            'indicators_with_value' => 0,
            'indicators_with_numeric_value' => 0,
            'sample_data' => []
        ];

        $organs = $json['organs'] ?? [];
        if (is_array($organs) || is_object($organs)) {
            $organs = (array)$organs;
            $details['organs_count'] = count($organs);

            $sampleCount = 0;
            foreach ($organs as $organKey => $organ) {
                if (!is_array($organ)) { $organ = is_object($organ) ? (array)$organ : []; }
                $indicators = $organ['indicators'] ?? [];
                if (!is_array($indicators)) { $indicators = is_object($indicators) ? (array)$indicators : []; }

                foreach ($indicators as $ind) {
                    if (!is_array($ind)) { $ind = is_object($ind) ? (array)$ind : []; }
                    $details['total_indicators']++;

                    $code = $ind['code'] ?? ($ind['name_en'] ?? $ind['name'] ?? null);
                    $value = $ind['value'] ?? null;

                    if (!empty($code)) $details['indicators_with_code']++;
                    if ($value !== null && $value !== '') $details['indicators_with_value']++;
                    if (is_numeric($value)) $details['indicators_with_numeric_value']++;

                    // 收集前5个指标的样本数据
                    if ($sampleCount < 5) {
                        $details['sample_data'][] = [
                            'organ' => $organKey,
                            'code' => $code,
                            'value' => $value,
                            'unit' => $ind['unit'] ?? null,
                            'status' => $ind['status'] ?? null
                        ];
                        $sampleCount++;
                    }
                }
            }
        }

        return $details;
    }

    /**
     * 状态字符串归一化为：偏少/偏多/正常
     */
    protected function normalizeStatusString(string $status): string
    {
        $s = trim(mb_strtolower($status));

        // 明确的异常标记
        $low = ['低','偏低','过低','過低','不足','減少','减少','少','偏少','low','decrease','decreased','l','↓'];
        $high = ['高','偏高','过高','過高','升高','增高','多','偏多','high','increase','increased','elevated','h','↑'];

        // 明确的正常标记（包括边界情况）
        $normal = [
            '正常','normal','within range','in-range','in range','陰性','negative',
            'n','wnl','within normal limits','borderline',
            'acceptable','ok','fine'
        ];

        // 严格匹配异常标记
        if ($this->inArrayLoose($s, $low)) {
            return '偏少';
        }
        if ($this->inArrayLoose($s, $high)) {
            return '偏多';
        }
        if ($this->inArrayLoose($s, $normal)) {
            return '正常';
        }

        // 特殊处理：Subnormal 表示偏低
        if (strpos($s, 'subnormal') !== false || strpos($s, 'sub-normal') !== false) {
            return '偏少'; // Subnormal应该识别为偏少
        }

        // 默认正常（保守处理）
        return '正常';
    }

    /** 数值提取：支持字符串中提取首个浮点数，失败返回null */
    protected function toFloatOrNull($v): ?float
    {
        if ($v === null) return null;
        if (is_numeric($v)) return (float)$v;
        if (is_string($v)) {
            if (preg_match('/[-+]?[0-9]*\.?[0-9]+/', $v, $m)) {
                return (float)$m[0];
            }
        }
        return null;
    }

    /** 判断数组是否为关联数组 */
    protected function isAssoc(array $arr): bool
    {
        return array_keys($arr) !== range(0, count($arr) - 1);
    }

    /** 松散包含：考虑去除空格 */
    protected function inArrayLoose(string $needle, array $haystack): bool
    {
        $needle = preg_replace('/\s+/', '', $needle);
        foreach ($haystack as $h) {
            $h = preg_replace('/\s+/', '', (string)$h);
            if ($needle === mb_strtolower($h)) return true;
        }
        return false;
    }


    /**
     * 构建 AI 提示词（要求严格 JSON 输出 + 固定字典 + 诱因映射）
     */
    protected function buildLabReportPrompt(?array $petInfo, ?array $imageAnalysis = null): string
    {
        $implications = $this->getImplicationMapping();

        $schema = json_encode([
            'organs' => (object)[]
        ], JSON_UNESCAPED_UNICODE);

        $dict = [
            'kidney' => ['BUN','CREA','PHOS','Ca'],
            'liver' => ['ALT','ALKP','GGT','ALB','TBIL','BileAcids'],
            'pancreas' => ['AMYL','LIPA'],
            'electrolytes' => ['Na','K','Cl'],
            'others' => ['GLU','AST','CK','CHOL','TRIG','Cortisol','T4','LACTATE','TP','GLOB'],
            'cbc' => ['RBC','HCT','HGB','MCV','MCH','MCHC','RDW','RETIC','WBC','NEU','LYM','MONO','EOS','BASO','PLT','PCT','MPV','PDW'],
            'urine' => ['USG','pH','PRO','GLU','KET','UBG','BIL','RBC_Hb','WBC','UPC']
        ];

        $petPart = '';
        if ($petInfo) {
            $petPart = sprintf(
                "宠物资料: 名称=%s, 物种(EN)=%s, 年龄=%s, 体重=%.2fkg。\n",
                $petInfo['name'] ?? '未知',
                $petInfo['type_en'] ?? 'dog/cat',
                $petInfo['age'] !== null ? $petInfo['age'] . '岁' : '未知',
                (float)($petInfo['weight'] ?? 0)
            );
        }

        // 集成图片预处理优化建议
        $optimizationPart = '';
        if ($imageAnalysis) {
            $optimizationPart = $this->preprocessingService->generateOptimizedPromptFragment([]);
            $optimizationPart .= sprintf(
                "【圖片質量評估】：質量分數 %d/100，建議使用容錯識別模式。\n\n",
                $imageAnalysis['quality_score'] ?? 70
            );
        }

        // 关键约束：
        // - 严格输出JSON，顶层仅包含 {"organs": {...}}，不要任何额外文本
        // - 器官与指标键名必须使用 fixed dict 中的键
        // - 每个指标提供 status(偏少|偏多|正常)、value、unit、reference_range, increase_implications, decrease_implications, rationale
        // - 仅返回识别成功的指标；未识别不返回
        // - 多图重复指标取包含参考范围者；冲突时取更窄范围并在 rationale 说明
        // 🎯 核心策略：保持详细提示词，强化JSON格式约束
        $jsonExample = $this->buildJsonExample();

        $prompt = <<<PROMPT
你是資深獸醫臨床助理，使用qwen-vl-max-2025-08-13分析寵物血液/尿液報告。

$petPart

$optimizationPart

【🔍 圖片識別策略升級】：
針對複雜表格+圖表混合格式的化驗報告，採用以下識別策略：

1. **區域分割識別**：
   - 左側表格區域：重點識別數值、單位、參考範圍
   - 右側圖表區域：忽略圖形，專注文字信息
   - 分區掃描，避免圖表干擾文字識別

2. **容錯識別機制**：
   - 優先識別清晰可見的指標
   - 對於部分遮擋或模糊的指標，嘗試多角度識別
   - 允許部分指標識別失敗，但確保識別成功的指標準確無誤

3. **智能過濾策略**：
   - 過濾明顯的圖表元素（線條、柱狀圖、刻度等）
   - 專注於表格行列結構中的文字和數字
   - 識別表格邊界，避免跨行錯位

【核心要求】：精確識別實驗室指標，每個指標的名稱、數值、單位、狀態、參考範圍必須來自同一行。

【關鍵要求】：
1. 逐行掃描，不遺漏指標，但允許部分指標因圖片質量問題無法識別
2. 數值精度與圖片一致（如153.9不寫成152）
3. 狀態按圖片標記（HIGH/LOW/NORMAL）
4. 參考範圍從圖片對應位置讀取，不得推測
5. 🚨 重要：即使只能識別到部分指標，也要確保識別成功的指標完全準確
🚨 **JSON格式严格要求 - 防止字符串化对象错误**：

器官分類：kidney(BUN,CREA,PHOS,Ca), liver(ALT,ALKP,GGT,ALB,TBIL), pancreas(AMYL,LIPA), electrolytes(Na,K,Cl), others(GLU,AST,CK,CHOL,T4,TP,GLOB)
⚠️ **关键约束**：indicators数组中的每个元素都必须是JSON对象，绝对不能是字符串！

【視覺識別訓練】：
❌ **错误示例**（绝对禁止）：
```json
{
  "indicators": [
    {"code": "BUN", "value": "46"},
    "{\"code\":\"CREA\",\"value\":\"3.6\"}"  // 这是字符串，错误！
  ]
}
```

請想像你正在用放大鏡逐字逐句地閱讀實驗室報告：
```json
{
  "indicators": [
    {"code": "BUN", "value": "46"},
    {"code": "CREA", "value": "3.6"}  // 这是对象，正确！
  ]
}
```

1. 找到指標名稱（如BUN、GLU、Na等）
2. 向右看，找到該指標的數值（保持所有小數位）
3. 繼續向右，找到單位（mg/dL、mmol/dL等）
4. 查看是否有狀態標記（HIGH、LOW、NORMAL等）
5. 找到括號中的參考範圍（必須是該指標對應的範圍）

【常見錯誤避免】：
❌ 錯誤：將BUN的數值配上GLU的參考範圍
🔧 **JSON生成检查清单**：
在生成每个indicator时，确保：
1. 使用花括号 {} 而不是引号包围
2. 每个字段都是对象的属性，不是字符串
3. 数组中的每个元素都是独立的JSON对象
4. 不要将整个对象序列化为字符串

📋 **标准JSON结构模板**：
```json
{
  "organs": {
    "kidney": {
      "name": "腎臟",
      "name_en": "kidney",
      "summary_status": "abnormal",
      "abnormal_count": 2,
      "indicators": [
        {
          "code": "BUN",
          "name": "尿素氮",
          "name_en": "BUN",
          "value": "46",
          "unit": "mg/dL",
          "reference_range": {"min": 15, "max": 36, "text": "(15-36)"},
          "status": "偏多",
          "increase_implications": ["腎功能下降"],
          "decrease_implications": ["過度水分補充"],
          "rationale": "數值46高於參考範圍"
        }
      ]
    }
  }
}
```

🔍 **識別步驟**：
1. 掃描圖片，識別檢驗報告類型（生化檢驗 or 血液學檢驗）
2. 逐行識別：指標名稱 → 數值 → 單位 → 狀態 → 參考範圍
3. 按類型分類指標：

**生化檢驗分類**：
- kidney(BUN,CREA,PHOS,Ca): 腎臟相關
- liver(ALT,ALKP,ALB,TBIL,GGT): 肝臟相關
- others(GLU,T4,TP,CHOL): 其他生化指標

**血液學檢驗分類**：
- cbc(RBC,HCT,HGB,MCV,MCH,MCHC,RDW): 紅細胞系列
- wbc(WBC,NEU,LYM,MONO,EOS,BASO): 白細胞系列
- platelet(PLT,MPV,PDW,PCT): 血小板系列

4. 構建JSON對象（🚨重要：indicators是對象數組，每個元素都是JSON對象）

⚠️ **JSON格式錯誤預防**：
- ❌ 絕對禁止：`"{"code":"BUN","value":"46"}"`（字符串化對象）
- ✅ 必須使用：`{"code":"BUN","value":"46"}`（純JSON對象）
- ❌ 絕對禁止：`[{...}, "{"code":"BUN"}", {...}]`（混合格式）
- ✅ 必須使用：`[{...}, {"code":"BUN"}, {...}]`（統一對象格式）

🔧 **JSON對象生成規則**：
每當你要添加一個indicator到數組中時：
1. 使用花括號 {} 開始和結束
2. 每個屬性用雙引號包圍
3. 屬性值根據類型決定是否用引號
4. 絕對不要將整個對象用引號包圍

📝 **輸出檢查清單**：
輸出前必須確認：
1. ✅ 是否為純JSON格式（無額外文字）
2. ✅ indicators是否為對象數組（不是字符串數組）
3. ✅ 每個indicator是否包含完整字段
4. ✅ 數值和參考範圍是否來自同一行
5. ✅ JSON結構是否可以正常解析
✅ 正確：BUN 46 mg/dL HIGH (15-36) - 同一行的所有信息

❌ 錯誤：Na 152（丟失小數位）
✅ 正確：Na 153.9（保持原始精度）

❌ 錯誤：使用記憶中的"標準"參考範圍
✅ 正確：完全按照當前圖片中顯示的範圍

❌ 錯誤：認為某個範圍"不對"而自行修正
✅ 正確：即使範圍看起來異常，也要按圖片記錄

【參考範圍特別提醒】：
- 不同實驗室的設備、方法、標準都可能不同
- 同一指標在不同報告中的範圍可能完全不同
- 你的任務是忠實記錄，不是判斷範圍是否"正確"

【識別檢查點】：
每識別一個指標後，請自問：
- 這個數值真的是圖片中這個指標旁邊的數值嗎？
- 這個參考範圍真的是圖片中這個指標對應的範圍嗎？
- 我有沒有把不同指標的信息混淆？

【qwen-vl-max常見錯誤預防】：
基於視覺模型的特點，以下是常見錯誤和預防措施：

1. 🚨 範圍混淆錯誤（最常見）：
   - BUN範圍絕不可能是(71-159) → 這是GLU的範圍！
   - GLU範圍絕不可能是(15-36) → 這是BUN的範圍！
   - 預防：每次識別後檢查範圍是否符合該指標的生理常識

2. 🚨 數值精度丟失：
   - Na: 153.9 不能寫成 152 或 152.0
   - 預防：仔細觀察小數點，保持原始精度

3. 🚨 垂直對齊錯誤：
   - 不要將第2行的數值配上第1行的參考範圍
   - 預防：使用表格的垂直線作為對齊參考

4. 🚨 特殊格式誤讀：
   - T4: 0.8 不能寫成 <0.8
   - 預防：仔細區分數字和符號

【嚴格要求】：
1. 數值精度：必須與圖片完全一致，包括小數點位數
   - 153.9 ≠ 152 ≠ 154
   - 116.0 ≠ 116 ≠ 115
   - 3.6 ≠ 4 ≠ 3

2. 參考範圍：必須與圖片完全一致，不得混淆不同指標的範圍
   - BUN: (15-36) 【腎臟指標】
   - GLU: (71-159) 【血糖指標】
   - Na: (150-160) 【電解質】
   - Cl: (112-120) 【電解質，注意不是(110-120)】

3. 逐個指標核對：每個指標的數值和範圍都要與圖片中該指標對應的位置完全一致

【禁止行為】：
- 禁止將一個指標的參考範圍用於另一個指標
- 禁止四捨五入或估算數值
- 禁止添加或刪除小數點
- 禁止修改參考範圍的數字

【圖片識別方法論】：

【qwen-vl-max-2025-08-13複雜格式識別策略】：

🎯 **針對圖表混合格式的特殊處理**：

第一步：智能區域分割
1. **表格區域識別**（重點關注）：
   - 左側數據表格：包含指標名稱、數值、單位、狀態、參考範圍
   - 識別表格邊界線，確定行列結構
   - 忽略右側圖表區域的視覺干擾

2. **分區掃描策略**：
   - 上部區域：血液學指標（RBC、WBC、PLT等）
   - 中部區域：生化指標（BUN、CREA、ALT等）
   - 下部區域：電解質指標（Na、K、Cl等）
   - 每個區域獨立處理，避免跨區域混淆

3. **圖表過濾機制**：
   - 自動識別並忽略圖表元素（柱狀圖、線條、刻度）
   - 專注於表格中的文字和數字信息
   - 避免將圖表中的數字誤認為檢測值

第二步：容錯識別機制
1. **優先級識別**：
   - 優先識別清晰、完整的指標行
   - 對於部分模糊或遮擋的指標，嘗試多角度識別
   - 確保識別成功的指標100%準確

2. **質量控制**：
   - 每個識別的指標必須包含：指標名稱、數值、單位
   - 參考範圍和狀態標記為可選（如果清晰可見則包含）
   - 不確定的信息寧可不識別，也不要錯誤識別

3. **智能補償**：
   - 如果某個指標的參考範圍不清晰，可以只識別數值和單位
   - 如果狀態標記模糊，通過數值與常見範圍比較來判斷
   - 保證核心信息（指標名稱和數值）的準確性

第三步：分層掃描策略
1. **第一層：核心指標掃描**
   - 重點識別常見的關鍵指標：BUN、CREA、ALT、GLU、Na、K、Cl
   - 確保這些指標的識別準確率

2. **第二層：擴展指標掃描**
   - 識別其他可見的指標：PHOS、ALB、TBIL、CHOL等
   - 允許部分指標因圖片質量問題無法識別

3. **第三層：狀態標記掃描**
   - 重點尋找HIGH、LOW、Subnormal等異常標記
   - 確保異常指標不被遺漏

【容錯目標調整】：
- 🎯 目標：識別至少5-10個有效指標（降低期望，提高準確性）
- 🚨 質量優於數量：寧可識別少量準確指標，也不要大量錯誤信息
- ✅ 成功標準：每個識別的指標都必須準確無誤

第二步：逐行精確讀取（嚴格同行原則）
- 對每個指標，必須確保所有信息來自圖片中的同一行：
  * 指標名稱（如BUN）
  * 數值（如46）
  * 單位（如mg/dL）
  * 狀態標記（如HIGH、LOW、Subnormal）
  * 參考範圍（如(15-36)）
- 🚨 絕對禁止：將一個指標的數值配上另一個指標的參考範圍
- 數值精度：153.9不能寫成153或152，116.0不能寫成116
- 特殊格式：保持原樣，如0.8不能寫成<0.8

【特殊狀態標記處理】：
- HIGH = 偏多
- LOW = 偏少
- Subnormal = 偏少（重要！）
- NORMAL或無標記 = 正常
- 🚨 Subnormal必須識別為偏少，不能誤判為正常

第三步：強制驗證檢查
- 每識別一個指標後，立即自問：
  * 這個參考範圍真的是這個指標旁邊的嗎？
  * BUN的範圍不可能是(71-159)，應該在(10-50)左右
  * GLU的範圍不可能是(15-36)，應該在(70-160)左右
- 如發現範圍異常，必須重新檢查該指標的位置

第四步：格式標準化
- 狀態標記：HIGH/LOW/NORMAL/Subnormal → 偏多/偏少/正常
- 保持原始數值精度和參考範圍格式

輸出要求：
- 只輸出如下 JSON 結構：{"organs": {...}}
- 每個器官物件包含: name(中文), name_en, summary_status(normal|abnormal), abnormal_count(本器官少/偏多項數), indicators(指標對象集合)
- 每個指標包含: code, name, name_en, value, unit, reference_range{min,max,text}, status(偏少|偏多|正常), increase_implications[], decrease_implications[], rationale
- 必須輸出圖片中所有可見的指標，不得遺漏任何檢測項目
- 數值精度必須與圖片完全一致，保持相同的小數位數
- 狀態判定優先級（重要！）：
  * 優先級1：數值範圍計算 - value < min → 偏少，value > max → 偏多，範圍內 → 正常
  * 優先級2：圖片標記兜底 - 僅在缺少數值或範圍時使用圖片標記
  * HIGH/H/↑/偏高/升高/Elevated → 偏多
  * LOW/L/↓/偏低/降低/Decreased → 偏少
  * NORMAL/N/正常/WNL/Within Normal → 正常
  * Subnormal → 偏少
- 特別注意GLU、PHOS、電解質等經常被遺漏的關鍵指標
- 多張圖片如同一指標有多條，優先採用“帶參考範圍”的，若範圍衝突取更窄範圍，並在 rationale 註明
- 將 increase_implications / decrease_implications 嚴格按下述映射填充（使用繁體中文用語）：
{$implications}

【最終檢查清單】：
輸出前請逐項確認：

1. ✓ 指標完整性檢查：
   - 是否遺漏了圖片中可見的任何指標？
   - GLU、PHOS、電解質等關鍵指標是否都已識別？

2. ✓ 數值精度檢查：
   - 每個數值是否與圖片中該指標對應位置的數值完全一致？
   - 小數位數是否保持原樣？
   - 沒有進行四捨五入或估算？

3. ✓ 參考範圍準確性檢查：
   - 每個指標的參考範圍是否來自圖片中該指標同一行的括號內容？
   - 沒有將不同指標的參考範圍混淆？
   - 沒有使用預設或記憶中的"標準"範圍？
   - 完全按照當前圖片顯示的範圍，即使看起來"異常"？
   - 括號格式是否與圖片完全一致？

4. ✓ 狀態標記檢查：
   - 狀態判定是否嚴格按圖片中該指標旁邊的標記執行？
   - HIGH→偏多，LOW/Subnormal→偏少，NORMAL→正常

5. ✓ 交叉驗證檢查：
   - 重新檢查每個指標，確認code、value、unit、reference_range都來自圖片中同一行
   - 確認沒有張冠李戴的情況

【qwen-vl-max最終驗證】：
作為視覺語言模型，在輸出前請進行以下驗證：

1. 視覺對齊檢查：
   - 重新掃描每個指標，確認數值和參考範圍在同一水平線上
   - 檢查是否有垂直錯位導致的信息混淆

2. 常識性檢查：
   - BUN的參考範圍應該在(10-50)左右，不可能是(71-159)
   - GLU的參考範圍應該在(70-160)左右，不可能是(15-36)
   - Na的數值通常有小數點，如153.9而不是152

3. 格式一致性檢查：
   - 數值格式是否與圖片完全一致？
   - 參考範圍的括號格式是否正確？
   - 特殊符號（如<、>）是否準確識別？

4. 最終自我質疑：
   - 我是否將不同行的信息混淆了？
   - 每個參考範圍是否真的來自該指標的位置？
   - 我是否保持了原始數值的精確度？

5. 完整性檢查：
   - 我是否識別了圖片中所有可見的指標？
   - VetStat區域的HCO3、AnGap等是否都識別了？
   - 是否有任何LOW或Subnormal狀態被遺漏？
   - 總的HIGH和LOW指標數量是否合理？

【調整後的識別目標】：
針對複雜格式圖片，調整識別期望：

1. **最低成功標準**：
   - 識別至少3-5個有效指標
   - 每個識別的指標必須100%準確
   - 至少識別到1-2個異常狀態指標

2. **理想識別目標**：
   - 識別8-15個有效指標
   - 包含主要器官系統的代表性指標
   - 準確識別所有可見的異常狀態

3. **質量控制原則**：
   - 寧可識別少量準確指標，也不要大量錯誤信息
   - 不確定的指標寧可不識別
   - 確保每個輸出的指標都經過嚴格驗證

【靈活完整性檢查】：
識別完成後進行合理性檢查：
1. 檢查是否有基本的生化指標（如BUN、CREA、ALT等）
2. 檢查是否有電解質指標（如Na、K、Cl等）
3. 檢查異常狀態標記是否合理
4. 如果識別指標過少（<3個），重新嘗試識別
5. 如果識別指標合理（≥3個且準確），則接受結果

注意：
- 僅返回 JSON，不得添加任何解釋、標題或標點以外內容
- JSON 頂層鍵僅允許 organs
- 如果圖片模糊或無法識別關鍵信息，返回空的 organs 對象
🚨 **最終JSON格式驗證**：
輸出前必須進行以下檢查：

1. **JSON結構驗證**：
   - 確保輸出的是有效的JSON格式
   - 頂層只有 "organs" 鍵
   - 所有的indicators都是對象數組，不包含任何字符串

2. **indicators數組檢查**：
   - 每個indicator都是 {...} 格式，不是 "{...}" 格式
   - 數組中沒有混合對象和字符串
   - 每個對象都包含必要的字段

3. **字符串化對象檢測**：
   - 檢查是否有 "{"code":"XXX"}" 這樣的字符串
   - 如果發現，立即修正為 {"code":"XXX"} 對象格式
   - 確保所有嵌套結構都是純JSON對象

4. **最終自檢問題**：
   - 我的indicators數組中是否包含了字符串化的對象？
   - 每個indicator是否都是正確的JSON對象格式？
   - 整個JSON是否可以被標準JSON解析器正確解析？

⚠️ **輸出要求**：
- 僅返回純JSON，無任何額外文字
- JSON頂層鍵僅允許 organs
- indicators必須是對象數組，絕對不能包含字符串
- 如果圖片無法識別，返回空的 organs 對象

🎯 **標準輸出格式**：
{$schema}
PROMPT;

        return $prompt;
    }

    /**
     * 构建JSON示例，强化格式要求
     */
    protected function buildJsonExample(): string
    {
        return json_encode([
            'organs' => [
                'kidney' => [
                    'name' => '腎臟',
                    'name_en' => 'kidney',
                    'summary_status' => 'abnormal',
                    'abnormal_count' => 2,
                    'indicators' => [
                        [
                            'code' => 'BUN',
                            'name' => '尿素氮',
                            'name_en' => 'BUN',
                            'value' => '46',
                            'unit' => 'mg/dL',
                            'reference_range' => [
                                'min' => 15,
                                'max' => 36,
                                'text' => '(15-36)'
                            ],
                            'status' => '偏多',
                            'increase_implications' => ['腎功能下降'],
                            'decrease_implications' => ['過度水分補充'],
                            'rationale' => '數值46高於參考範圍'
                        ],
                        [
                            'code' => 'CREA',
                            'name' => '肌酸酐',
                            'name_en' => 'CREA',
                            'value' => '3.6',
                            'unit' => 'mg/dL',
                            'reference_range' => [
                                'min' => 0.8,
                                'max' => 2.4,
                                'text' => '(0.8-2.4)'
                            ],
                            'status' => '偏多',
                            'increase_implications' => ['腎功能下降'],
                            'decrease_implications' => ['過度水分補充'],
                            'rationale' => '數值3.6高於參考範圍'
                        ]
                    ]
                ]
            ]
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 诱因映射（繁体中文），供模型直接填充
     */
    protected function getImplicationMapping(): string
    {
        $map = [
            'BUN' => [
                'increase' => ['腎功能下降','脫水','心臟病','休克或泌尿道阻塞','高蛋白飲食後'],
                'decrease' => ['過度水分補充']
            ],
            'CREA' => [
                'increase' => ['腎功能下降及與 BUN 類似之脫水/阻塞等狀況'],
                'decrease' => ['過度水分補充']
            ],
            'PHOS' => [
                'increase' => ['腎臟疾病等導致腎臟流失減少之狀況','胃腸道攝取增加','受傷組織釋放增加','成長期生理性升高'],
                'decrease' => ['流失增加','攝取減少']
            ],
            'Ca' => [
                'increase' => ['腎臟疾病','某些癌症類型','毒性','副甲狀腺疾病'],
                'decrease' => ['某些副甲狀腺疾病','低白蛋白']
            ],
            'ALT' => [
                'increase' => ['肝細胞損傷（敏感指標）'],
                'decrease' => []
            ],
            'ALKP' => [
                'increase' => ['肝臟異常（膽汁淤積）','庫興氏症','幼齡活躍骨骼生長','骨損傷後骨重塑','藥物/非特異性狀況誘發'],
                'decrease' => []
            ],
            'GGT' => [
                'increase' => ['膽汁淤積型肝臟異常'],
                'decrease' => []
            ],
            'ALB' => [
                'increase' => ['脫水'],
                'decrease' => ['肝功能下降','失血','胃腸疾病','腎臟疾病']
            ],
            'TBIL' => [
                'increase' => ['肝臟疾病（膽汁淤積及功能不全）','某些類型之貧血'],
                'decrease' => []
            ],
            'AMYL' => [
                'increase' => ['胰臟炎','腎臟疾病','胃腸疾病','某些藥物治療'],
                'decrease' => []
            ],
            'LIPA' => [
                'increase' => ['胰臟炎','腎臟疾病','胃腸疾病','某些藥物治療'],
                'decrease' => []
            ],
            'TP' => [
                'increase' => ['脫水','炎症'],
                'decrease' => ['肝功能下降','失血','胃腸流失','腎臟流失']
            ],
            'GLOB' => [
                'increase' => ['炎症','潛在慢性感染'],
                'decrease' => ['失血','胃腸流失','免疫缺陷']
            ],
            'Na' => [
                'increase' => ['脫水'],
                'decrease' => ['腹瀉/嘔吐流失','愛迪森氏症','腎臟疾病']
            ],
            'K' => [
                'increase' => ['腎排泄減少','愛迪森氏症','脫水','腎臟阻塞'],
                'decrease' => ['腹瀉/嘔吐流失']
            ],
            'Cl' => [
                'increase' => ['脫水'],
                'decrease' => ['腹瀉/嘔吐流失']
            ],
            'GLU' => [
                'increase' => ['糖尿病'],
                'decrease' => ['肝臟/胰臟疾病等，可能導致虛脫/癲癇/昏迷']
            ],
            'AST' => [
                'increase' => ['肝臟或肌肉損傷'],
                'decrease' => []
            ],
            'CK' => [
                'increase' => ['肌肉損傷'],
                'decrease' => []
            ],
            'CHOL' => [
                'increase' => ['多種代謝障礙（糖尿病、甲狀腺機能低下、庫興氏症、胰臟炎、某些腎臟疾病）'],
                'decrease' => ['肝功能不全','腸道疾病']
            ],
            'TRIG' => [
                'increase' => ['未禁食樣本','迷你雪納瑞犬','胰臟炎','糖尿病','庫興氏症','甲狀腺機能低下'],
                'decrease' => []
            ],
            'Cortisol' => [
                'increase' => ['庫興氏症'],
                'decrease' => ['愛迪森氏症']
            ],
            'T4' => [
                'increase' => ['甲狀腺機能亢進（主要見於貓）'],
                'decrease' => ['甲狀腺機能低下（主要見於狗）']
            ],
            'LACTATE' => [
                'increase' => ['局部或全身血流灌注減少，重症預後指標'],
                'decrease' => []
            ],
            // 尿液
            'USG' => [
                'increase' => ['脫水（相對濃縮）'],
                'decrease' => ['水分負荷/腎濃縮功能下降']
            ],
            'PRO' => [
                'increase' => ['腎臟疾病等'],
                'decrease' => []
            ],
            'KET' => [
                'increase' => ['體內脂質分解增加'],
                'decrease' => []
            ],
            'UBG' => [
                'increase' => ['肝臟或溶血性疾病'],
                'decrease' => []
            ],
            'BIL' => [
                'increase' => ['肝臟或溶血性疾病（狗常見生理性膽紅素尿；貓具意義）'],
                'decrease' => []
            ],
            'RBC_Hb' => [
                'increase' => ['炎症/感染/創傷（血尿/血紅蛋白尿/肌紅蛋白尿可能性）'],
                'decrease' => []
            ],
            'WBC' => [
                'increase' => ['泌尿道某處炎症'],
                'decrease' => []
            ],
            'UPC' => [
                'increase' => ['腎臟顯著蛋白質流失（早期腎病篩檢/治療監測）'],
                'decrease' => []
            ],
        ];

        return json_encode($map, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT);
    }

    /**
     * 清理JSON内容，移除常见的格式问题
     */
    protected function cleanJsonContent(string $content): string
    {
        // 移除代码块标记
        $content = preg_replace('/```json\s*/', '', $content);
        $content = preg_replace('/```\s*$/', '', $content);

        // 移除多余的换行和空格
        $content = trim($content);

        // 修复AI常见的JSON格式错误

        // 1. 移除尾随逗号
        $content = preg_replace('/,\s*}/', '}', $content);
        $content = preg_replace('/,\s*]/', ']', $content);

        // 2. 修复转义字符问题
        // 修复字符串中的转义斜杠：\/ -> /
        $content = preg_replace('/\\\\\//', '/', $content);

        // 修复多余的转义引号
        $content = preg_replace('/\\\\\"/', '"', $content);

        // 修复数组中多余的引号问题
        $content = preg_replace('/},\s*"\s*{/', '},{', $content);
        $content = preg_replace('/],\s*"\s*{/', '],[{', $content);
        $content = preg_replace('/}\s*,\s*"\s*{/', '},{', $content);

        // 修复引号包围的完整JSON对象
        $content = preg_replace('/,\s*"\s*(\{[^}]*\})/', ',$1', $content);

        // 修复字符串值中的问题
        $content = preg_replace('/\\\\n/', '', $content); // 移除换行符
        $content = preg_replace('/\\\\t/', '', $content); // 移除制表符

        // 确保JSON结构完整
        $content = trim($content);
        if (!str_starts_with($content, '{')) {
            $content = '{' . $content;
        }
        if (!str_ends_with($content, '}')) {
            $content = $content . '}';
        }

        // 5. 修复字符串值中的转义问题
        $content = preg_replace('/\\\\\//', '/', $content);

        // 6. 修复多余的转义引号
        $content = preg_replace('/\\"([^"]*)\\"/', '"$1"', $content);

        return $content;
    }

    /**
     * 计算数据质量评分（0-100）
     */
    protected function calculateDataQuality(array $organs): int
    {
        $totalIndicators = 0;
        $completeIndicators = 0;

        foreach ($organs as $organ) {
            $indicators = $organ['indicators'] ?? [];
            foreach ($indicators as $indicator) {
                $totalIndicators++;

                // 评估指标完整性
                $score = 0;
                if (!empty($indicator['code'])) $score += 25;
                if ($indicator['value'] !== null) $score += 25;
                if (!empty($indicator['unit'])) $score += 20;
                if (!empty($indicator['reference_range']['min']) && !empty($indicator['reference_range']['max'])) $score += 20;
                if (!empty($indicator['status'])) $score += 10;

                if ($score >= 70) $completeIndicators++; // 70分以上认为是完整的
            }
        }

        if ($totalIndicators === 0) return 0;

        return round(($completeIndicators / $totalIndicators) * 100);
    }

    /**
     * 统计总指标数量
     */
    protected function countTotalIndicators(array $organs): int
    {
        $count = 0;
        foreach ($organs as $organ) {
            $count += count($organ['indicators'] ?? []);
        }
        return $count;
    }

    /**
     * 统计异常指标数量
     */
    protected function countAbnormalIndicators(array $organs): int
    {
        $count = 0;
        foreach ($organs as $organ) {
            $indicators = $organ['indicators'] ?? [];
            foreach ($indicators as $indicator) {
                $status = $indicator['status'] ?? '正常';
                if ($status !== '正常') {
                    $count++;
                }
            }
        }
        return $count;
    }

    /**
     * 检测内容中是否包含字符串化对象
     */
    protected function detectStringifiedObjects(string $content): array
    {
        $patterns = [];

        // 检测indicators数组中的字符串化对象
        if (preg_match('/\"indicators\"\s*:\s*\[[^]]*\"\{[^}]*\}\"[^]]*\]/', $content)) {
            $patterns[] = 'stringified_objects_in_indicators';
        }

        // 检测一般的字符串化JSON对象
        if (preg_match('/\"\{[^}]*\}\"/', $content)) {
            $patterns[] = 'general_stringified_objects';
        }

        // 检测混合数组（对象和字符串混合）
        if (preg_match('/\[\s*\{[^}]*\}\s*,\s*\"\{[^}]*\}\"\s*\]/', $content)) {
            $patterns[] = 'mixed_array_objects_strings';
        }

        return $patterns;
    }

    /**
     * 验证JSON结构是否包含字符串化对象
     */
    protected function validateJsonStructure(array $json): array
    {
        $issues = [];

        if (!isset($json['organs']) || !is_array($json['organs'])) {
            return $issues;
        }

        foreach ($json['organs'] as $organKey => $organ) {
            if (!is_array($organ) || !isset($organ['indicators'])) {
                continue;
            }

            $indicators = $organ['indicators'];
            if (!is_array($indicators)) {
                continue;
            }

            foreach ($indicators as $index => $indicator) {
                // 检查是否为字符串化对象
                if (is_string($indicator)) {
                    // 尝试解析字符串是否为JSON
                    $decoded = json_decode($indicator, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                        $issues[] = [
                            'type' => 'stringified_object',
                            'organ' => $organKey,
                            'indicator_index' => $index,
                            'stringified_content' => $indicator
                        ];
                    }
                }
            }
        }

        return $issues;
    }

    /**
     * 修复JSON结构中的字符串化对象问题
     */
    protected function fixJsonStructureIssues(array $json, array $issues): array
    {
        foreach ($issues as $issue) {
            if ($issue['type'] === 'stringified_object') {
                $organKey = $issue['organ'];
                $indicatorIndex = $issue['indicator_index'];
                $stringifiedContent = $issue['stringified_content'];

                // 解析字符串化的对象
                $decodedObject = json_decode($stringifiedContent, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedObject)) {
                    // 替换字符串化对象为真正的对象
                    $json['organs'][$organKey]['indicators'][$indicatorIndex] = $decodedObject;

                    \Log::info('🔧 修复字符串化对象', [
                        'organ' => $organKey,
                        'indicator_index' => $indicatorIndex,
                        'original_string' => substr($stringifiedContent, 0, 100),
                        'decoded_code' => $decodedObject['code'] ?? 'unknown'
                    ]);
                }
            }
        }

        return $json;
    }

    /**
     * 检测是否为血液学检验报告
     */
    protected function detectHematologyReport(array $organs): bool
    {
        $hematologyIndicators = [
            'RBC', 'HCT', 'HGB', 'MCV', 'MCH', 'MCHC', 'RDW',  // 红细胞系列
            'WBC', 'NEU', 'LYM', 'MONO', 'EOS', 'BASO',         // 白细胞系列
            'PLT', 'MPV', 'PDW', 'PCT'                          // 血小板系列
        ];

        $foundHematologyCount = 0;
        $totalIndicators = 0;

        foreach ($organs as $organ) {
            if (!is_array($organ)) continue;
            $indicators = $organ['indicators'] ?? [];
            if (!is_array($indicators)) continue;

            foreach ($indicators as $indicator) {
                if (!is_array($indicator)) continue;
                $totalIndicators++;

                $code = strtoupper($indicator['code'] ?? $indicator['name_en'] ?? $indicator['name'] ?? '');
                if (in_array($code, $hematologyIndicators)) {
                    $foundHematologyCount++;
                }
            }
        }

        // 如果血液学指标占比超过50%，认为是血液学检验报告
        return $totalIndicators > 0 && ($foundHematologyCount / $totalIndicators) > 0.5;
    }

    /**
     * 重新分类血液学指标
     */
    protected function reclassifyHematologyIndicators(array $organs): array
    {
        $reclassified = [
            'cbc' => [
                'name' => '紅細胞系列',
                'name_en' => 'cbc',
                'summary_status' => 'normal',
                'abnormal_count' => 0,
                'indicators' => []
            ],
            'wbc' => [
                'name' => '白細胞系列',
                'name_en' => 'wbc',
                'summary_status' => 'normal',
                'abnormal_count' => 0,
                'indicators' => []
            ],
            'platelet' => [
                'name' => '血小板系列',
                'name_en' => 'platelet',
                'summary_status' => 'normal',
                'abnormal_count' => 0,
                'indicators' => []
            ]
        ];

        $cbcCodes = ['RBC', 'HCT', 'HGB', 'MCV', 'MCH', 'MCHC', 'RDW'];
        $wbcCodes = ['WBC', 'NEU', 'LYM', 'MONO', 'EOS', 'BASO'];
        $plateletCodes = ['PLT', 'MPV', 'PDW', 'PCT'];

        // 收集所有指标
        $allIndicators = [];
        foreach ($organs as $organ) {
            if (!is_array($organ)) continue;
            $indicators = $organ['indicators'] ?? [];
            if (!is_array($indicators)) continue;
            $allIndicators = array_merge($allIndicators, $indicators);
        }

        // 重新分类
        foreach ($allIndicators as $indicator) {
            if (!is_array($indicator)) continue;

            $code = strtoupper($indicator['code'] ?? $indicator['name_en'] ?? $indicator['name'] ?? '');

            if (in_array($code, $cbcCodes)) {
                $reclassified['cbc']['indicators'][] = $indicator;
            } elseif (in_array($code, $wbcCodes)) {
                $reclassified['wbc']['indicators'][] = $indicator;
            } elseif (in_array($code, $plateletCodes)) {
                $reclassified['platelet']['indicators'][] = $indicator;
            }
        }

        // 移除空的器官
        foreach ($reclassified as $key => $organ) {
            if (empty($organ['indicators'])) {
                unset($reclassified[$key]);
            }
        }

        return $reclassified;
    }

    /**
     * 使用增强提示词重新分析（专门针对空指标问题）
     */
    protected function retryAnalysisWithEnhancedPrompt(array $images, ?int $petId = null): ?array
    {
        try {
            \Log::info('🔄 开始重新分析，使用增强提示词', [
                'pet_id' => $petId,
                'images_count' => count($images)
            ]);

            // 构建增强的提示词，专门强调指标提取
            $enhancedPrompt = $this->buildEnhancedPromptForRetry($petId);

            // 调用AI服务
            $response = $this->aiService->chat($enhancedPrompt, $images);
            $content = $response['choices'][0]['message']['content'] ?? '';

            if (empty($content)) {
                \Log::warning('🚨 重新分析：AI未返回内容', ['pet_id' => $petId]);
                return null;
            }

            // 解析JSON
            $json = $this->extractJson($content);
            if (is_null($json)) {
                // 尝试清理
                $cleanedContent = $this->cleanJsonContent($content);
                $json = $this->extractJson($cleanedContent);

                if (is_null($json)) {
                    \Log::warning('🚨 重新分析：JSON解析失败', ['pet_id' => $petId]);
                    return null;
                }
            }

            // 标准化器官数据
            $organs = $this->normalizeLabOrgans($json['organs'] ?? []);
            $indicatorsCount = $this->countTotalIndicators($organs);

            \Log::info('🔄 重新分析结果', [
                'pet_id' => $petId,
                'organs_count' => count($organs),
                'indicators_count' => $indicatorsCount
            ]);

            return $indicatorsCount > 0 ? $organs : null;

        } catch (\Exception $e) {
            \Log::error('🚨 重新分析异常', [
                'pet_id' => $petId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 构建增强的提示词（专门针对指标提取问题）
     */
    protected function buildEnhancedPromptForRetry(?int $petId = null): string
    {
        $petInfo = $this->getPetInfo($petId);
        $petPart = $petInfo ? "宠物信息：{$petInfo['name']} ({$petInfo['type']}, {$petInfo['sex']}, {$petInfo['age']})" : '';

        return <<<PROMPT
你是专业的兽医检验报告分析师。请仔细分析图片中的实验室检验报告。

{$petPart}

🎯 **核心任务**：从图片中提取所有可见的检验指标数据

⚠️ **重要提醒**：
1. 必须仔细扫描图片中的每一行数据
2. 不要遗漏任何检验指标
3. 每个指标必须包含：指标代码、数值、单位、状态
4. 即使只能识别到部分指标，也要全部输出

📋 **输出格式**：
```json
{
  "organs": {
    "kidney": {
      "name": "腎臟",
      "name_en": "kidney",
      "summary_status": "normal",
      "abnormal_count": 0,
      "indicators": [
        {
          "code": "BUN",
          "name": "尿素氮",
          "name_en": "BUN",
          "value": "25",
          "unit": "mg/dL",
          "reference_range": {"min": 15, "max": 36, "text": "(15-36)"},
          "status": "正常",
          "increase_implications": ["腎功能下降"],
          "decrease_implications": ["過度水分補充"],
          "rationale": "數值25在參考範圍內"
        }
      ]
    }
  }
}
```

🔍 **识别步骤**：
1. 仔细查看图片中的表格，识别检验类型
2. 逐行扫描每个检验项目
3. 提取：项目名称、数值、单位、参考范围、状态标记
4. 按检验类型分类：

**生化检验分类**：
- kidney(BUN,CREA,PHOS), liver(ALT,ALKP,ALB), others(GLU,T4)

**血液学检验分类**：
- cbc(RBC,HCT,HGB,MCV,MCH,MCHC,RDW): 红细胞系列
- wbc(WBC,NEU,LYM,MONO,EOS,BASO): 白细胞系列
- platelet(PLT,MPV,PDW,PCT): 血小板系列

⚠️ **严格要求**：
- 只输出JSON，无其他文字
- indicators必须是对象数组，不能为空
- 必须识别出至少1个有效指标
- 每个指标必须有完整的字段

请开始分析：
PROMPT;
    }
}

