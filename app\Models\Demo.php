<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Demo extends Model
{
    use HasFactory;

    /**
     * 关闭批量赋值保护
     * 由于每个字段需要独立操作，允许所有字段批量赋值
     */
    protected $guarded = [];

    /**
     * 时间戳自动维护配置
     * @var bool
     */
    public $timestamps = true;

    /**
     * 模型对应的数据表
     * @var string
     */
    protected $table = 'demos';

    /**
     * 主键名称
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 主键类型
     * @var string
     */
    protected $keyType = 'int';

    /**
     * 自动递增主键
     * @var bool
     */
    public $incrementing = true;

    /**
     * 日期字段格式
     * @var string
     */
    protected $dateFormat = 'Y-m-d H:i:s';

    /**
     * 默认字段值
     * @var array
     */
    protected $attributes = [
        'field1' => null,
        'field2' => null,
        // ...其他字段默认值...
        'field10' => null
    ];

    /**
     * 字段类型转换（安全防护）
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // 如果需要数字类型可以添加：
        // 'numeric_field' => 'integer'
    ];

    /**
     * 自定义创建方法（可选扩展）
     * @param string $field 字段名称
     * @param mixed $value 字段值
     */
    public static function createField($field, $value)
    {
        return static::create([$field => $value]);
    }



    /**
     * 字段修改器示例（可选）
     * 存储field2时自动处理数据
     */
    public function setField2Attribute($value)
    {
        $this->attributes['field2'] = trim($value); // 示例：去除首尾空格
    }

    // 在Demo模型中添加
    protected static function booted()
    {
        static::created(function ($model) {
            // 确保始终存在ID=1的记录
            if (!static::where('id', 1)->exists()) {
                static::create(['id' => 1]);
            }
        });

        static::deleted(function ($model) {
            // 当ID=1被删除时自动重建
            if ($model->id == 1) {
                static::create(['id' => 1]);
            }
        });
    }
}
