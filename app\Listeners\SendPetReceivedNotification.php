<?php

namespace App\Listeners;

use App\Events\PetReceivedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPetReceivedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PetReceivedEvent  $event
     * @return void
     */
    public function handle(PetReceivedEvent $event)
    {
        try {
            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Pet received notification failed: Pet not found', [
                    'pet_id' => $event->petId,
                    'original_owner_id' => $event->originalOwnerId,
                    'new_owner_id' => $event->newOwnerId
                ]);
                return;
            }

            // 获取新所有者信息
            $newOwner = AppUser::find($event->newOwnerId);

            if (!$newOwner) {
                Log::error('Pet received notification failed: New owner not found', [
                    'pet_id' => $event->petId,
                    'new_owner_id' => $event->newOwnerId
                ]);
                return;
            }

            // 获取原所有者信息
            $originalOwner = AppUser::find($event->originalOwnerId);

            if (!$originalOwner) {
                Log::error('Pet received notification failed: Original owner not found', [
                    'pet_id' => $event->petId,
                    'original_owner_id' => $event->originalOwnerId
                ]);
                return;
            }

            // 向原所有者发送通知
            $typeText = $event->receiveType == 'transfer' ? '转让' : '家庭成员';

            NotificationService::createNotification(
                $event->originalOwnerId, // 接收通知的用户ID（原所有者）
                NotificationSetting::TYPE_PET_RECEIVED, // 通知类型
                '宠物接收通知',
                "您的宠物 {$pet->name} 已被 {$newOwner->username} 作为{$typeText}接收。",
                $event->newOwnerId, // 发送者ID（新所有者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            // 向新所有者发送通知
            NotificationService::createNotification(
                $event->newOwnerId, // 接收通知的用户ID（新所有者）
                NotificationSetting::TYPE_PET_RECEIVED, // 通知类型
                '宠物接收通知',
                "您已成功接收 {$originalOwner->username} 的宠物 {$pet->name} 作为{$typeText}。",
                $event->originalOwnerId, // 发送者ID（原所有者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            Log::info('Pet received notification sent', [
                'pet_id' => $event->petId,
                'original_owner_id' => $event->originalOwnerId,
                'new_owner_id' => $event->newOwnerId,
                'receive_type' => $event->receiveType
            ]);
        } catch (\Exception $e) {
            Log::error('Pet received notification failed', [
                'error' => $e->getMessage(),
                'pet_id' => $event->petId,
                'original_owner_id' => $event->originalOwnerId,
                'new_owner_id' => $event->newOwnerId
            ]);
        }
    }
}
