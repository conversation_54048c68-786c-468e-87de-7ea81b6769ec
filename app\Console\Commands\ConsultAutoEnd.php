<?php

namespace App\Console\Commands;

use App\Models\Consult;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\Log;

class ConsultAutoEnd extends Command
{
    protected $signature = 'consult:auto-end';

    protected $description = '自动结束咨询会话';

    public function handle()
    {
        Consult::query()
            ->select('id')
            ->where('status', Consult::CONSULT_STATUS_ACCEPTED)
            ->where(function (Builder $query) {
                $query->where(function (Builder $q) {
                    $q->whereNull('first_ask_time')
                        ->where('accept_time', '<=', now()->subMinutes(Consult::CONSULT_AUTO_CLOSE_TIME_LIMIT));
                })->orWhere(function (Builder $q) {
                    $q->whereNotNull('first_ask_time')
                        ->where('first_ask_time', '<=', now()->subMinutes(Consult::CONSULT_CONVERSATION_TIME_LIMIT));
                });
            })
            ->where('created_at', '>=', now()->subDays(30))
            ->chunk(100, function ($consults) {
                foreach ($consults as $consult) {
                    list($success, , $message) = Consult::end($consult->id, null);
                    if ($success) {
                        Log::info("自动结束咨询会话成功：{$consult->id}");
                    } else {
                        Log::error("自动结束咨询会话失败：{$consult->id}，{$message}");
                    }
                }
            });
    }
}
