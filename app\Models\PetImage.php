<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use DateTimeInterface;

/**
 * @mixin Builder
 */
class PetImage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_images';

    protected $fillable = [
        'image_url',
        'image_hash',
        'original_name',
        'image_info',
        'upload_user_id',
        'reference_count',
        'status',
    ];

    protected $casts = [
        'image_info' => 'array',
        'status' => 'integer',
        'reference_count' => 'integer',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联上传用户
    public function uploadUser()
    {
        return $this->belongsTo(AppUser::class, 'upload_user_id');
    }

    // 关联相册记录
    public function albums()
    {
        return $this->hasMany(PetAlbum::class, 'pet_image_id');
    }

    // 关联宠物（通过相册表）
    public function pets()
    {
        return $this->belongsToMany(Pet::class, 'pets_albums', 'pet_image_id', 'pet_id')
                    ->withPivot('user_id', 'image_type', 'description', 'status', 'created_at')
                    ->wherePivot('status', 1);
    }

    /**
     * 创建或获取图片记录
     */
    public static function createOrGet($imageData)
    {
        try {
            // 如果有哈希值，先查找是否已存在
            if (!empty($imageData['image_hash'])) {
                $existingImage = self::where('image_hash', $imageData['image_hash'])->first();
                if ($existingImage) {
                    return [true, $existingImage, '图片已存在'];
                }
            }

            // 创建新图片记录
            $image = self::create($imageData);
            return [true, $image, '图片创建成功'];
        } catch (\Exception $e) {
            return [false, null, $e->getMessage()];
        }
    }

    /**
     * 增加引用计数
     */
    public function incrementReference($count = 1)
    {
        $this->increment('reference_count', $count);
    }

    /**
     * 减少引用计数
     */
    public function decrementReference($count = 1)
    {
        $this->decrement('reference_count', $count);

        // 如果引用计数为0，可以考虑标记为删除
        if ($this->reference_count <= 0) {
            $this->update(['status' => 0]);
        }
    }

    /**
     * 获取图片的所有关联宠物
     */
    public function getAssociatedPets()
    {
        return $this->pets()->get();
    }

    /**
     * 检查图片是否被指定用户使用
     */
    public function isUsedByUser($userId)
    {
        return $this->albums()->where('user_id', $userId)->exists();
    }

    /**
     * 获取图片的使用统计
     */
    public function getUsageStats()
    {
        $albums = $this->albums()->with('pet:id,name')->get();

        return [
            'total_albums' => $albums->count(),
            'unique_pets' => $albums->pluck('pet_id')->unique()->count(),
            'unique_users' => $albums->pluck('user_id')->unique()->count(),
            'pets' => $albums->pluck('pet')->filter()->unique('id')->values(),
        ];
    }
}
