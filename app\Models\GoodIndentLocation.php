<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class GoodIndentLocation extends Model
{
    protected $table = 'goods_indents_locations';

    protected $fillable = [
        'good_indent_id',
        'cellphone',
        'name',
        'location',
        'address',
        'latitude',
        'longitude',
        'house',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function saveByGoodIndent($GoodIndent)
    {
        $Location = new self;
        $Location->good_indent_id = $GoodIndent->id;
        $Location->cellphone = $GoodIndent->shipping->cellphone;
        $Location->name = $GoodIndent->shipping->name;
        $Location->location = $GoodIndent->shipping->location;
        $Location->address = $GoodIndent->shipping->address;
        $Location->latitude = $GoodIndent->shipping->latitude;
        $Location->longitude = $GoodIndent->shipping->longitude;
        $Location->house = $GoodIndent->shipping->house;
        $Location->save() or throw_if(true, 'RuntimeException', '收货地址保存失败');
    }
}
