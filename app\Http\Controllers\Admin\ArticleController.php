<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;

class ArticleController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'sort_name'     => 'nullable|in:id,created_at,updated_at,sort_order,view_count',
            'sort_by'       => 'nullable|in:asc,desc',
            'status'        => 'nullable|in:0,1',
            'category_id'   => 'nullable|integer|gt:0',
        ]);
        $formData = $request->all();
        $records = Article::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Article::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'          => 'sometimes|integer|gte:0',
            'title'       => 'required|max:255',
            'title_tc'    => 'required|max:255',
            'title_en'    => 'required|max:255',
            'content'     => 'nullable|string',
            'content_tc'  => 'nullable|string',
            'content_en'  => 'nullable|string',
            'image'       => 'nullable|url|pic|trust_host',
            'images'      => 'nullable|array',
            'images.*'    => 'nullable|url|pic|trust_host',
            'category_id' => 'nullable|gte:0|exists:articles_categorys,id',
            'good_id'     => 'nullable|gte:0|exists:goods,id',
            'price'       => 'nullable|numeric|gte:0',
            'line_price'  => 'nullable|numeric|gte:0',
            'status'      => 'required|integer|in:0,1',
            'sort_order'  => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['images'] = $request->post('images');
        $result = Article::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Article::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
