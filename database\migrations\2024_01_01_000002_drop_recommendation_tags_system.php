<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropRecommendationTagsSystem extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 删除推荐标签相关表
        Schema::dropIfExists('pet_recommendation_tags');
        Schema::dropIfExists('goods_recommendation_tags');
        Schema::dropIfExists('recommendation_tags');
        
        // 删除旧的商品关联表（数据已迁移到新字段）
        Schema::dropIfExists('goods_pet_types');
        Schema::dropIfExists('goods_pet_breeds');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 重新创建推荐标签表（如果需要回滚）
        Schema::create('recommendation_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name', 50)->comment('标签名称');
            $table->string('name_tc', 50)->nullable()->comment('标签名称(繁体)');
            $table->string('name_en', 50)->nullable()->comment('标签名称(英文)');
            $table->string('category', 30)->comment('标签类别');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();

            $table->index('category');
        });

        Schema::create('goods_recommendation_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('good_id')->comment('商品ID');
            $table->unsignedBigInteger('tag_id')->comment('标签ID');
            $table->tinyInteger('recommendation_level')->default(1)->comment('推荐级别：1-一般推荐，2-强烈推荐');
            $table->timestamps();

            $table->index('good_id');
            $table->index('tag_id');
        });

        Schema::create('pet_recommendation_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('pet_id')->comment('宠物ID');
            $table->unsignedBigInteger('tag_id')->comment('标签ID');
            $table->timestamps();

            $table->unique(['pet_id', 'tag_id']);
            $table->index('pet_id');
            $table->index('tag_id');
        });

        Schema::create('goods_pet_types', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('good_id')->comment('商品ID');
            $table->unsignedBigInteger('pet_type_id')->comment('宠物类型ID');
            $table->tinyInteger('recommendation_level')->default(1)->comment('推荐级别：1-一般推荐，2-强烈推荐');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();

            $table->index('good_id');
            $table->index('pet_type_id');
        });

        Schema::create('goods_pet_breeds', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('good_id')->comment('商品ID');
            $table->unsignedBigInteger('pet_breed_id')->comment('宠物品种ID');
            $table->tinyInteger('recommendation_level')->default(1)->comment('推荐级别：1-一般推荐，2-强烈推荐');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            $table->softDeletes();

            $table->index('good_id');
            $table->index('pet_breed_id');
        });
    }
}
