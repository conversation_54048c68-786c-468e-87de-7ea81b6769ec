<?php

namespace App\Services\PetAnalysis;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class LabReportCacheService
{
    private const CACHE_PREFIX = 'lab_report:';
    private const CACHE_TTL = 3600; // 1小时
    private const HASH_CACHE_TTL = 86400; // 24小时
    
    /**
     * 生成图片内容的哈希值
     */
    public function generateImageHash(array $images): string
    {
        $hashData = [];
        foreach ($images as $image) {
            if (is_string($image)) {
                // 如果是base64编码的图片
                $hashData[] = md5($image);
            } elseif (is_array($image) && isset($image['data'])) {
                // 如果是包含data字段的数组
                $hashData[] = md5($image['data']);
            }
        }
        
        return md5(implode('|', $hashData));
    }
    
    /**
     * 生成缓存键
     */
    public function generateCacheKey(string $imageHash, ?int $petId = null): string
    {
        $key = self::CACHE_PREFIX . $imageHash;
        if ($petId) {
            $key .= ':pet:' . $petId;
        }
        return $key;
    }
    
    /**
     * 获取缓存的分析结果
     */
    public function getCachedResult(string $cacheKey): ?array
    {
        try {
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('实验室报告缓存命中', ['cache_key' => $cacheKey]);
                
                // 添加缓存标记
                $cached['meta'] = $cached['meta'] ?? [];
                $cached['meta']['from_cache'] = true;
                $cached['meta']['cached_at'] = $cached['meta']['cached_at'] ?? now()->toISOString();
                
                return $cached;
            }
        } catch (\Exception $e) {
            Log::warning('缓存读取失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
        }
        
        return null;
    }
    
    /**
     * 缓存分析结果
     */
    public function cacheResult(string $cacheKey, array $result): void
    {
        try {
            // 添加缓存时间戳
            $result['meta'] = $result['meta'] ?? [];
            $result['meta']['cached_at'] = now()->toISOString();
            
            Cache::put($cacheKey, $result, self::CACHE_TTL);
            
            Log::info('实验室报告结果已缓存', [
                'cache_key' => $cacheKey,
                'ttl' => self::CACHE_TTL
            ]);
        } catch (\Exception $e) {
            Log::warning('缓存写入失败', [
                'cache_key' => $cacheKey,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清除特定宠物的所有缓存
     */
    public function clearPetCache(int $petId): void
    {
        try {
            $pattern = self::CACHE_PREFIX . '*:pet:' . $petId;
            
            // 注意：这里需要根据实际的缓存驱动实现
            // Redis可以使用SCAN命令，其他驱动可能需要不同的实现
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                    Log::info('已清除宠物缓存', ['pet_id' => $petId, 'keys_count' => count($keys)]);
                }
            }
        } catch (\Exception $e) {
            Log::warning('清除宠物缓存失败', [
                'pet_id' => $petId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getCacheStats(): array
    {
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys(self::CACHE_PREFIX . '*');
                
                $stats = [
                    'total_cached_reports' => count($keys),
                    'cache_prefix' => self::CACHE_PREFIX,
                    'cache_ttl' => self::CACHE_TTL,
                    'cache_driver' => config('cache.default')
                ];
                
                // 分析缓存键的分布
                $petCaches = 0;
                $generalCaches = 0;
                
                foreach ($keys as $key) {
                    if (strpos($key, ':pet:') !== false) {
                        $petCaches++;
                    } else {
                        $generalCaches++;
                    }
                }
                
                $stats['pet_specific_caches'] = $petCaches;
                $stats['general_caches'] = $generalCaches;
                
                return $stats;
            }
        } catch (\Exception $e) {
            Log::warning('获取缓存统计失败', ['error' => $e->getMessage()]);
        }
        
        return [
            'total_cached_reports' => 0,
            'cache_prefix' => self::CACHE_PREFIX,
            'cache_ttl' => self::CACHE_TTL,
            'cache_driver' => config('cache.default'),
            'error' => '无法获取统计信息'
        ];
    }
    
    /**
     * 预热缓存（批量处理时使用）
     */
    public function warmupCache(array $imageHashList, ?int $petId = null): array
    {
        $results = [];
        
        foreach ($imageHashList as $imageHash) {
            $cacheKey = $this->generateCacheKey($imageHash, $petId);
            $cached = $this->getCachedResult($cacheKey);
            
            $results[$imageHash] = [
                'cache_key' => $cacheKey,
                'cached' => $cached !== null,
                'result' => $cached
            ];
        }
        
        return $results;
    }
    
    /**
     * 清理过期缓存（定时任务使用）
     */
    public function cleanupExpiredCache(): int
    {
        $cleaned = 0;
        
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                $keys = $redis->keys(self::CACHE_PREFIX . '*');
                
                foreach ($keys as $key) {
                    $ttl = $redis->ttl($key);
                    if ($ttl === -1) { // 没有过期时间的键
                        $redis->expire($key, self::CACHE_TTL);
                    } elseif ($ttl === -2) { // 已过期的键
                        $redis->del($key);
                        $cleaned++;
                    }
                }
                
                Log::info('缓存清理完成', ['cleaned_keys' => $cleaned]);
            }
        } catch (\Exception $e) {
            Log::error('缓存清理失败', ['error' => $e->getMessage()]);
        }
        
        return $cleaned;
    }
}
