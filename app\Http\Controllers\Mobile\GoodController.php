<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\GoodCategory;
use App\Models\Good;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * goods
 * 商品
 * Class GoodController
 * @package App\Http\Controllers\Mobile
 */
class GoodController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'        => 'sometimes|integer|gt:0',
            'per_page'    => 'sometimes|integer|between:1,200',
            'keyword'     => 'nullable|max:255',
            'sort_name'   => 'nullable|in:price,sales_nums',
            'sort_by'     => 'nullable|in:asc,desc',
            'category_id' => 'nullable|integer|gt:0|exists:goods_categorys,id',
            'brand_id'    => 'nullable|integer|gt:0|exists:brands,id',
        ]);
        $formData = $request->all();
        $formData['is_show'] = Good::GOOD_SHOW_PUTAWAY;
        $records = Good::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function detail(Request $request)
    {
        $info = Good::getDetail($request->input('id'), $request->user()->id);
        return ResponseHelper::success($info);
    }

    public function category(Request $request)
    {
        $request->validate([
            'tree'         => 'nullable|integer|in:0,1',
            'sort_name'    => 'nullable|in:created_at,updated_at',
            'sort_by'      => 'nullable|in:asc,desc',
            'category_id'  => 'nullable|integer|gt:0|exists:goods_categorys,id',  // 按分类ID筛选
        ]);
        $formData = $request->all();
        $records = GoodCategory::getListForApp($formData);
        return ResponseHelper::success($records);
    }
}
