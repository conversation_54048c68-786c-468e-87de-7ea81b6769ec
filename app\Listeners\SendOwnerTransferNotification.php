<?php

namespace App\Listeners;

use App\Events\OwnerTransferEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendOwnerTransferNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\OwnerTransferEvent $event
     * @return void
     */
    public function handle(OwnerTransferEvent $event)
    {
        try {
            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Send owner transfer notification failed: Pet not found', [
                    'pet_id'       => $event->petId,
                    'old_owner_id' => $event->oldOwnerId,
                    'new_owner_id' => $event->newOwnerId
                ]);
                return;
            }

            // 获取新家长信息
            $new_owner = AppUser::find($event->newOwnerId);

            if (!$new_owner) {
                Log::error('Send owner transfer notification failed: New owner not found', [
                    'pet_id'       => $event->petId,
                    'new_owner_id' => $event->newOwnerId
                ]);
                return;
            }

            // 获取宠物所有者信息
            $old_owner = AppUser::find($event->oldOwnerId);

            if (!$old_owner) {
                Log::error('Send owner transfer notification failed: Old owner not found', [
                    'pet_id'       => $event->petId,
                    'old_owner_id' => $event->oldOwnerId
                ]);
                return;
            }

            // 向宠物所有者发送通知
            NotificationService::createNotification(
                $event->oldOwnerId, // 接收通知的用户ID（宠物所有者）
                NotificationSetting::TYPE_PARENT_ADDED, // 通知类型
                '更换主人通知',
                "您已成功将 {$new_owner->username} 设置为宠物 {$pet->name} 的新主人",
                null, // 发送者ID（系统通知，无发送者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            // 向新家长发送通知
            NotificationService::createNotification(
                $event->newOwnerId, // 接收通知的用户ID（新家长）
                NotificationSetting::TYPE_PARENT_ADDED, // 通知类型
                '更换主人通知',
                "您已被 {$old_owner->username} 设置为宠物 {$pet->name} 的新主人",
                $event->oldOwnerId, // 发送者ID（宠物所有者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            Log::info('Send owner transfer notification sent', [
                'pet_id'       => $event->petId,
                'old_owner_id' => $event->oldOwnerId,
                'new_owner_id' => $event->newOwnerId
            ]);
        } catch (\Exception $e) {
            Log::error('Send owner transfer notification failed', [
                'error'        => $e->getMessage(),
                'pet_id'       => $event->petId,
                'old_owner_id' => $event->oldOwnerId,
                'new_owner_id' => $event->newOwnerId
            ]);
        }
    }
}
