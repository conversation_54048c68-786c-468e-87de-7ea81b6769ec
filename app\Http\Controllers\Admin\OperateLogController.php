<?php

namespace App\Http\Controllers\Admin;

use App\Events\DataChanged;
use App\Exports\OperateLogExport;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\OperateLog;
use App\Providers\OperateLinkModelProvider;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class OperateLogController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,weigh,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'operate_type'  => 'nullable|integer|gt:0|in:' . implode(',', array_keys(DataChanged::OPERATE_TYPE_TEXT_LIST)),
            'link_type'     => 'nullable|integer|gt:0|in:' . implode(',', OperateLinkModelProvider::LINK_TYPES),
            'link_id'       => 'nullable|integer|gt:0',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
        ]);
        $formData = $request->all();
        $records = OperateLog::getList($formData)->toArray();
        foreach (OperateLinkModelProvider::LINK_TYPES as $v) {
            $records['link_type_list'][$v] = OperateLinkModelProvider::getLinkName($v);
        }
        $records['operate_type_list'] = DataChanged::OPERATE_TYPE_TEXT_LIST;
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(OperateLog::getDetail($id));
    }

    public function exportList(Request $request)
    {
        $request->validate([
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,weigh,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'operate_type'  => 'nullable|integer|gt:0|in:' . implode(',', array_keys(DataChanged::OPERATE_TYPE_TEXT_LIST)),
            'link_type'     => 'nullable|integer|gt:0|in:' . implode(',', OperateLinkModelProvider::LINK_TYPES),
            'link_id'       => 'nullable|integer|gt:0',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
            'created_start' => 'required|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'required|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $formData['role_id'] = $request->user()->role_id;
        $formData['per_page'] = -1;
        try {
            $startDate = date('Y-m-d', strtotime($request->created_start));
            $endDate = date('Y-m-d', strtotime($request->created_end));
            $filePath = app()->make('exportPath', ["操作記錄記錄{$startDate}至{$endDate}"]);
            if (!Excel::store(new OperateLogExport($formData), $filePath)) {
                throw new \Exception(__('common.file_write_error'));
            }
            $download_url = asset('storage/' . $filePath);
            return ResponseHelper::success(['download_url' => $download_url]);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage() . '[' . $e->getFile() . '-' . $e->getLine() . ']');
        }
    }
}
