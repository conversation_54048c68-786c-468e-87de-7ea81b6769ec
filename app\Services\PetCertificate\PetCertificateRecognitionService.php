<?php

namespace App\Services\PetCertificate;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;

/**
 * 宠物证件识别服务类
 */
class PetCertificateRecognitionService
{
    /**
     * AI服务实例
     *
     * @var AliBailianService
     */
    protected $aiService;

    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        $this->aiService = $aliBailianService;
    }

    /**
     * 识别宠物证件信息
     *
     * @param string $imageUrl 图片URL
     * @param int $certificateType 证件类型
     * @return array
     */
    public function recognizeCertificate(string $imageUrl, int $certificateType): array
    {
        try {
            // 根据证件类型构建不同的识别提示词
            $prompt = $this->buildRecognitionPrompt($certificateType);

            // 记录识别开始时间
            $startTime = microtime(true);

            // 调用AI服务进行识别
            $response = $this->aiService->chat(
                $prompt,
                [$imageUrl],
                null, // 不需要会话ID
                0.3  // 较低的温度值，提高准确性
            );

            // 记录识别耗时
            $duration = round(microtime(true) - $startTime, 2);
            Log::info("证件识别耗时: {$duration} 秒", [
                'certificate_type' => $certificateType,
                'image_url' => $imageUrl
            ]);

            // 解析AI响应
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return $this->getEmptyResult($certificateType);
            }

            // 解析识别结果
            return $this->parseRecognitionResult($content, $certificateType);

        } catch (\Exception $e) {
            Log::error('证件识别失败: ' . $e->getMessage(), [
                'certificate_type' => $certificateType,
                'image_url' => $imageUrl,
                'error' => $e->getMessage()
            ]);

            return $this->getEmptyResult($certificateType);
        }
    }

    /**
     * 根据证件类型构建识别提示词
     *
     * @param int $certificateType
     * @return string
     */
    protected function buildRecognitionPrompt(int $certificateType): string
    {
        switch ($certificateType) {
            case 1: // 出生证明
                return "请仔细分析这张宠物出生证明图片，提取以下信息并以纯JSON格式返回：
{
    \"birth_date\": \"出生年月日(YYYY-MM-DD格式，如无法识别则为null)\",
    \"gender\": \"性别(公/母，如无法识别则为null)\",
    \"breed\": \"品种(如无法识别则为null)\",
    \"color\": \"毛色(如无法识别则为null)\",
    \"certificate_number\": \"证书编号(如无法识别则为null)\"
}

重要要求：
1. 只返回纯JSON格式，不要使用markdown代码块标记（```json）
2. 不要添加任何说明文字或注释
3. 如果某个字段无法识别，请设置为null
4. 日期格式必须是YYYY-MM-DD
5. 性别只能是'公'或'母'
6. 直接返回JSON对象，不要包装在代码块中";

            case 3: // 晶片编码
                return "请仔细分析这张宠物晶片编码图片，提取晶片编号信息并以纯JSON格式返回：
{
    \"chip_number\": \"晶片编号(如无法识别则为null)\"
}

重要要求：
1. 只返回纯JSON格式，不要使用markdown代码块标记（```json）
2. 不要添加任何说明文字或注释
3. 如果无法识别晶片编号，请设置为null
4. 请提取图片中显示的完整晶片编号
5. 直接返回JSON对象，不要包装在代码块中";

            case 2: // 疫苗针卡
            case 4: // 医疗档案
            default:
                return "抱歉，该类型证件暂不支持AI识别。";
        }
    }

    /**
     * 解析AI识别结果
     *
     * @param string $content AI返回的内容
     * @param int $certificateType 证件类型
     * @return array
     */
    protected function parseRecognitionResult(string $content, int $certificateType): array
    {
        // 清理AI返回的内容，移除markdown代码块标记
        $cleanContent = $this->cleanAIResponse($content);

        // 尝试解析JSON
        $jsonData = json_decode($cleanContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::warning('AI返回内容不是有效JSON', [
                'original_content' => $content,
                'cleaned_content' => $cleanContent,
                'certificate_type' => $certificateType,
                'json_error' => json_last_error_msg()
            ]);
            return $this->getEmptyResult($certificateType);
        }

        Log::info('AI识别结果解析成功', [
            'certificate_type' => $certificateType,
            'parsed_data' => $jsonData
        ]);

        // 根据证件类型验证和清理数据
        switch ($certificateType) {
            case 1: // 出生证明
                return $this->validateBirthCertificateData($jsonData);

            case 3: // 晶片编码
                return $this->validateChipCodeData($jsonData);

            default:
                return $this->getEmptyResult($certificateType);
        }
    }

    /**
     * 清理AI返回的内容，移除markdown标记和多余的文本
     *
     * @param string $content
     * @return string
     */
    protected function cleanAIResponse(string $content): string
    {
        // 移除markdown代码块标记
        $content = preg_replace('/```json\s*/', '', $content);
        $content = preg_replace('/```\s*$/', '', $content);

        // 移除开头和结尾的空白字符
        $content = trim($content);

        // 尝试提取JSON部分（从第一个{到最后一个}）
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $content = $matches[0];
        }

        return $content;
    }

    /**
     * 验证出生证明数据
     *
     * @param array $data
     * @return array
     */
    protected function validateBirthCertificateData(array $data): array
    {
        $result = [
            'birth_date' => null,
            'gender' => null,
            'breed' => null,
            'color' => null,
            'certificate_number' => null
        ];

        // 验证出生日期格式
        if (isset($data['birth_date']) && !empty($data['birth_date'])) {
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $data['birth_date'])) {
                $result['birth_date'] = $data['birth_date'];
            }
        }

        // 验证性别
        if (isset($data['gender']) && in_array($data['gender'], ['公', '母'])) {
            $result['gender'] = $data['gender'];
        }

        // 其他字段直接赋值（如果不为空）
        foreach (['breed', 'color', 'certificate_number'] as $field) {
            if (isset($data[$field]) && !empty($data[$field]) && $data[$field] !== 'null') {
                $result[$field] = trim($data[$field]);
            }
        }

        return $result;
    }

    /**
     * 验证晶片编码数据
     *
     * @param array $data
     * @return array
     */
    protected function validateChipCodeData(array $data): array
    {
        $result = [
            'chip_number' => null
        ];

        // 直接返回晶片编号，不进行格式验证
        if (isset($data['chip_number']) && !empty($data['chip_number']) && $data['chip_number'] !== 'null') {
            $chipNumber = trim($data['chip_number']);
            $result['chip_number'] = $chipNumber;
        }

        return $result;
    }

    /**
     * 获取空结果
     *
     * @param int $certificateType
     * @return array
     */
    protected function getEmptyResult(int $certificateType): array
    {
        switch ($certificateType) {
            case 1: // 出生证明
                return [
                    'birth_date' => null,
                    'gender' => null,
                    'breed' => null,
                    'color' => null,
                    'certificate_number' => null
                ];

            case 3: // 晶片编码
                return [
                    'chip_number' => null
                ];

            default:
                return [];
        }
    }

    /**
     * 检查证件类型是否支持AI识别
     *
     * @param int $certificateType
     * @return bool
     */
    public function isSupportedForRecognition(int $certificateType): bool
    {
        return in_array($certificateType, [1, 3]); // 只支持出生证明和晶片编码
    }
}
