<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AnalysisBodyPart;
use App\Models\BodyPartSymptom;

class AnalysisBodyPartsSeederFixed extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 通用部位
        $generalParts = [
            [
                'code' => 'general',
                'name' => '全身/综合',
                'description' => '全面评估宠物的整体健康状况',
                'icon' => '/images/parts/general.png',
                'pet_type' => 'general',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'head',
                'name' => '头部',
                'description' => '头部健康状况分析',
                'icon' => '/images/parts/head.png',
                'pet_type' => 'general',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'eyes',
                'name' => '眼睛',
                'description' => '眼睛健康状况分析',
                'icon' => '/images/parts/eyes.png',
                'pet_type' => 'general',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'ears',
                'name' => '耳朵',
                'description' => '耳朵健康状况分析',
                'icon' => '/images/parts/ears.png',
                'pet_type' => 'general',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'code' => 'mouth',
                'name' => '口腔/牙齿',
                'description' => '口腔和牙齿健康状况分析',
                'icon' => '/images/parts/mouth.png',
                'pet_type' => 'general',
                'sort_order' => 5,
                'status' => 1
            ],
            [
                'code' => 'nose',
                'name' => '鼻子',
                'description' => '鼻子健康状况分析',
                'icon' => '/images/parts/nose.png',
                'pet_type' => 'general',
                'sort_order' => 6,
                'status' => 1
            ],
            [
                'code' => 'skin',
                'name' => '皮肤/被毛',
                'description' => '皮肤和被毛健康状况分析',
                'icon' => '/images/parts/skin.png',
                'pet_type' => 'general',
                'sort_order' => 7,
                'status' => 1
            ],
            [
                'code' => 'limbs',
                'name' => '四肢/关节',
                'description' => '四肢和关节健康状况分析',
                'icon' => '/images/parts/limbs.png',
                'pet_type' => 'general',
                'sort_order' => 8,
                'status' => 1
            ],
            [
                'code' => 'digestive',
                'name' => '消化系统',
                'description' => '消化系统健康状况分析',
                'icon' => '/images/parts/digestive.png',
                'pet_type' => 'general',
                'sort_order' => 9,
                'status' => 1
            ],
            [
                'code' => 'respiratory',
                'name' => '呼吸系统',
                'description' => '呼吸系统健康状况分析',
                'icon' => '/images/parts/respiratory.png',
                'pet_type' => 'general',
                'sort_order' => 10,
                'status' => 1
            ],
            [
                'code' => 'urinary',
                'name' => '泌尿系统',
                'description' => '泌尿系统健康状况分析',
                'icon' => '/images/parts/urinary.png',
                'pet_type' => 'general',
                'sort_order' => 11,
                'status' => 1
            ],
            [
                'code' => 'reproductive',
                'name' => '生殖系统',
                'description' => '生殖系统健康状况分析',
                'icon' => '/images/parts/reproductive.png',
                'pet_type' => 'general',
                'sort_order' => 12,
                'status' => 1
            ],
            [
                'code' => 'behavior',
                'name' => '行为/精神状态',
                'description' => '行为和精神状态分析',
                'icon' => '/images/parts/behavior.png',
                'pet_type' => 'general',
                'sort_order' => 13,
                'status' => 1
            ]
        ];
        
        // 狗特有部位
        $dogParts = [
            [
                'code' => 'paws',
                'name' => '爪子/足垫',
                'description' => '爪子和足垫健康状况分析',
                'icon' => '/images/parts/dog_paws.png',
                'pet_type' => 'dog',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'back',
                'name' => '背部/脊椎',
                'description' => '背部和脊椎健康状况分析',
                'icon' => '/images/parts/dog_back.png',
                'pet_type' => 'dog',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'tail',
                'name' => '尾巴',
                'description' => '尾巴健康状况分析',
                'icon' => '/images/parts/dog_tail.png',
                'pet_type' => 'dog',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'anal',
                'name' => '肛门/肛门腺',
                'description' => '肛门和肛门腺健康状况分析',
                'icon' => '/images/parts/dog_anal.png',
                'pet_type' => 'dog',
                'sort_order' => 4,
                'status' => 1
            ]
        ];
        
        // 猫特有部位
        $catParts = [
            [
                'code' => 'paws',
                'name' => '爪子/足垫',
                'description' => '爪子和足垫健康状况分析',
                'icon' => '/images/parts/cat_paws.png',
                'pet_type' => 'cat',
                'sort_order' => 1,
                'status' => 1
            ],
            [
                'code' => 'back',
                'name' => '背部/脊椎',
                'description' => '背部和脊椎健康状况分析',
                'icon' => '/images/parts/cat_back.png',
                'pet_type' => 'cat',
                'sort_order' => 2,
                'status' => 1
            ],
            [
                'code' => 'tail',
                'name' => '尾巴',
                'description' => '尾巴健康状况分析',
                'icon' => '/images/parts/cat_tail.png',
                'pet_type' => 'cat',
                'sort_order' => 3,
                'status' => 1
            ],
            [
                'code' => 'grooming',
                'name' => '清洁行为',
                'description' => '猫咪清洁行为分析',
                'icon' => '/images/parts/cat_grooming.png',
                'pet_type' => 'cat',
                'sort_order' => 4,
                'status' => 1
            ],
            [
                'code' => 'whiskers',
                'name' => '胡须/触须',
                'description' => '猫咪胡须和触须状态分析',
                'icon' => '/images/parts/cat_whiskers.png',
                'pet_type' => 'cat',
                'sort_order' => 5,
                'status' => 1
            ]
        ];
        
        // 合并所有部位并添加到数据库
        $allParts = array_merge($generalParts, $dogParts, $catParts);
        
        foreach ($allParts as $part) {
            $bodyPart = AnalysisBodyPart::updateOrCreate(
                ['code' => $part['code'], 'pet_type' => $part['pet_type']],
                $part
            );
            
            // 为每个部位添加一些常见症状
            $this->addSymptomsForPart($bodyPart);
        }
    }
    
    /**
     * 为部位添加常见症状
     *
     * @param AnalysisBodyPart $bodyPart
     * @return void
     */
    private function addSymptomsForPart(AnalysisBodyPart $bodyPart)
    {
        $symptoms = [];
        
        // 通用症状 - 使用部位代码作为前缀
        $generalSymptoms = [
            $bodyPart->code . '_pain' => '疼痛',
            $bodyPart->code . '_swelling' => '肿胀',
            $bodyPart->code . '_redness' => '发红',
            $bodyPart->code . '_itching' => '瘙痒',
            $bodyPart->code . '_discharge' => '分泌物',
            $bodyPart->code . '_abnormal_behavior' => '异常行为'
        ];
        
        // 根据部位代码添加特定症状
        switch ($bodyPart->code) {
            case 'head':
                $symptoms = [
                    'head_tilt' => '头部倾斜',
                    'head_shaking' => '摇头',
                    'head_pressing' => '头部顶压',
                    'head_asymmetry' => '不对称'
                ];
                break;
                
            case 'eyes':
                $symptoms = [
                    'eyes_squinting' => '眯眼',
                    'eyes_cloudiness' => '混浊',
                    'eyes_tearing' => '流泪',
                    'eyes_third_eyelid_showing' => '第三眼睑外露'
                ];
                break;
                
            case 'ears':
                $symptoms = [
                    'ears_scratching' => '抓耳朵',
                    'ears_odor' => '异味'
                ];
                break;
                
            case 'mouth':
                $symptoms = [
                    'mouth_bad_breath' => '口臭',
                    'mouth_drooling' => '流口水',
                    'mouth_difficulty_eating' => '进食困难',
                    'mouth_bleeding_gums' => '牙龈出血',
                    'mouth_tartar' => '牙垢',
                    'mouth_loose_teeth' => '牙齿松动'
                ];
                break;
                
            case 'nose':
                $symptoms = [
                    'nose_sneezing' => '打喷嚏',
                    'nose_dryness' => '干燥',
                    'nose_bleeding' => '流血',
                    'nose_breathing_difficulty' => '呼吸困难'
                ];
                break;
                
            case 'skin':
                $symptoms = [
                    'skin_hair_loss' => '脱毛',
                    'skin_dandruff' => '皮屑',
                    'skin_rash' => '皮疹',
                    'skin_lumps' => '肿块',
                    'skin_parasites' => '寄生虫'
                ];
                break;
                
            case 'paws':
                $symptoms = [
                    'paws_limping' => '跛行',
                    'paws_licking' => '舔爪子',
                    'paws_broken_nails' => '指甲断裂',
                    'paws_pad_injuries' => '足垫受伤'
                ];
                break;
                
            case 'limbs':
                $symptoms = [
                    'limbs_limping' => '跛行',
                    'limbs_stiffness' => '僵硬',
                    'limbs_reluctance_to_move' => '不愿活动',
                    'limbs_abnormal_gait' => '步态异常'
                ];
                break;
                
            case 'back':
                $symptoms = [
                    'back_arched' => '拱背',
                    'back_stiffness' => '僵硬',
                    'back_pain_when_touched' => '触摸时疼痛',
                    'back_reluctance_to_jump' => '不愿跳跃',
                    'back_difficulty_standing' => '站立困难'
                ];
                break;
                
            case 'tail':
                $symptoms = [
                    'tail_chasing' => '追尾巴',
                    'tail_limp' => '尾巴无力',
                    'tail_hair_loss' => '尾巴脱毛',
                    'tail_pain_when_touched' => '触摸时疼痛'
                ];
                break;
                
            case 'digestive':
                $symptoms = [
                    'digestive_vomiting' => '呕吐',
                    'digestive_diarrhea' => '腹泻',
                    'digestive_constipation' => '便秘',
                    'digestive_loss_of_appetite' => '食欲不振',
                    'digestive_excessive_gas' => '胀气',
                    'digestive_abdominal_pain' => '腹痛',
                    'digestive_weight_loss' => '体重减轻'
                ];
                break;
                
            case 'respiratory':
                $symptoms = [
                    'respiratory_coughing' => '咳嗽',
                    'respiratory_sneezing' => '打喷嚏',
                    'respiratory_wheezing' => '喘息',
                    'respiratory_labored_breathing' => '呼吸困难',
                    'respiratory_nasal_discharge' => '鼻涕',
                    'respiratory_rapid_breathing' => '呼吸急促'
                ];
                break;
                
            case 'urinary':
                $symptoms = [
                    'urinary_frequent_urination' => '频繁排尿',
                    'urinary_straining_to_urinate' => '排尿困难',
                    'urinary_blood_in_urine' => '尿血',
                    'urinary_inappropriate_urination' => '不当排尿',
                    'urinary_excessive_thirst' => '过度饮水'
                ];
                break;
                
            case 'reproductive':
                $symptoms = [
                    'reproductive_excessive_licking' => '过度舔舐',
                    'reproductive_behavioral_changes' => '行为改变'
                ];
                break;
                
            case 'behavior':
                $symptoms = [
                    'behavior_lethargy' => '嗜睡',
                    'behavior_aggression' => '攻击性',
                    'behavior_anxiety' => '焦虑',
                    'behavior_excessive_vocalization' => '过度发声',
                    'behavior_hiding' => '躲藏',
                    'behavior_changes_in_sleep' => '睡眠改变',
                    'behavior_disorientation' => '迷失方向'
                ];
                break;
                
            case 'anal':
                if ($bodyPart->pet_type === 'dog') {
                    $symptoms = [
                        'anal_scooting' => '蹭地',
                        'anal_licking' => '舔肛门',
                        'anal_odor' => '异味'
                    ];
                }
                break;
                
            case 'grooming':
                if ($bodyPart->pet_type === 'cat') {
                    $symptoms = [
                        'grooming_excessive' => '过度舔毛',
                        'grooming_lack' => '不舔毛',
                        'grooming_hair_loss' => '脱毛',
                        'grooming_dandruff' => '皮屑',
                        'grooming_matted_fur' => '毛发打结'
                    ];
                }
                break;
                
            case 'whiskers':
                if ($bodyPart->pet_type === 'cat') {
                    $symptoms = [
                        'whiskers_broken' => '胡须断裂',
                        'whiskers_asymmetrical' => '胡须不对称',
                        'whiskers_fatigue' => '胡须疲劳',
                        'whiskers_behavioral_changes' => '行为改变'
                    ];
                }
                break;
                
            case 'general':
                $symptoms = [
                    'general_lethargy' => '嗜睡',
                    'general_fever' => '发热',
                    'general_loss_of_appetite' => '食欲不振',
                    'general_weight_loss' => '体重减轻',
                    'general_increased_thirst' => '饮水增加',
                    'general_weakness' => '虚弱',
                    'general_behavioral_changes' => '行为改变'
                ];
                break;
        }
        
        // 合并通用症状和特定症状
        if ($bodyPart->code !== 'general') {
            $symptoms = array_merge($symptoms, $generalSymptoms);
        }
        
        // 添加症状到数据库
        $sortOrder = 1;
        foreach ($symptoms as $code => $name) {
            BodyPartSymptom::updateOrCreate(
                ['body_part_id' => $bodyPart->id, 'code' => $code],
                [
                    'name' => $name,
                    'description' => $name . '症状描述',
                    'pet_type' => $bodyPart->pet_type,
                    'sort_order' => $sortOrder++,
                    'status' => 1
                ]
            );
        }
    }
}
