<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 宠物排序配置
    |--------------------------------------------------------------------------
    |
    | 这里配置宠物排序功能的行为设置
    |
    */

    /*
    |--------------------------------------------------------------------------
    | 历史数据排序策略
    |--------------------------------------------------------------------------
    |
    | 控制历史数据（如AI对话记录、分享内容等）中宠物的显示顺序
    |
    | 可选值:
    | 'original' - 保持创建时的原始顺序（推荐，保证历史数据真实性）
    | 'current'  - 使用用户当前的排序设置（界面一致性更好）
    |
    */
    'historical_data_sort_strategy' => env('PET_HISTORICAL_SORT_STRATEGY', 'original'),

    /*
    |--------------------------------------------------------------------------
    | 功能开关
    |--------------------------------------------------------------------------
    */
    
    // 是否启用宠物自定义排序功能
    'enable_custom_sort' => env('PET_ENABLE_CUSTOM_SORT', true),
    
    // 是否在AI对话中使用当前排序
    'ai_chat_use_current_sort' => env('PET_AI_CHAT_USE_CURRENT_SORT', false),
    
    // 是否在分享内容中使用当前排序
    'content_share_use_current_sort' => env('PET_CONTENT_SHARE_USE_CURRENT_SORT', false),
    
    // 是否在相册中使用当前排序
    'album_use_current_sort' => env('PET_ALBUM_USE_CURRENT_SORT', false),

    /*
    |--------------------------------------------------------------------------
    | 默认排序设置
    |--------------------------------------------------------------------------
    */
    
    // 新宠物的默认排序值策略
    // 'front' - 排在最前面, 'back' - 排在最后面, 'auto' - 自动计算
    'new_pet_sort_position' => env('PET_NEW_PET_SORT_POSITION', 'front'),
    
    // 排序值的步长（用于自动计算排序值）
    'sort_order_step' => env('PET_SORT_ORDER_STEP', 10),

    /*
    |--------------------------------------------------------------------------
    | 性能优化设置
    |--------------------------------------------------------------------------
    */
    
    // 是否启用排序查询缓存
    'enable_sort_cache' => env('PET_ENABLE_SORT_CACHE', false),
    
    // 缓存时间（分钟）
    'sort_cache_ttl' => env('PET_SORT_CACHE_TTL', 60),
];
