<?php

namespace App\Models;

use App\Events\ParentAddedEvent;
use App\Events\ParentRemovedEvent;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetMaster extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_masters';

    protected $fillable = [
        'pet_id',
        'user_id',
        'deleted_type',
        'sort_order',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public function pet()
    {
        return $this->belongsTo(Pet::class, 'pet_id');
    }

    public static function addMaster($petId, $userId)
    {
        return self::create(['pet_id' => $petId, 'user_id' => $userId]);
    }

    //判断用户是否是宠物的家人
    public static function isMaster($petId, $userId)
    {
        return self::where('pet_id', $petId)->where('user_id', $userId)->value('id');
    }

    //删除家人 删除原因:1=主人移除,2=主动退出
    public static function delMaster($id, $deleteType)
    {
        return self::where('id', $id)->update(['delete_type' => $deleteType, 'deleted_at' => date('Y-m-d H:i:s')]);
    }

    //宠物转让 删除原因:3=宠物转让,4=删除宠物
    public static function resetMasters($petId, $deleteType)
    {
        return self::where('pet_id', $petId)->update(['delete_type' => $deleteType, 'deleted_at' => date('Y-m-d H:i:s')]);
    }

    //判断用户是否是宠物的家人（检查双向关系）
    public static function isFamily($userId1, $userId2)
    {
        if (!$userId1 || !$userId2 || $userId1 == $userId2) {
            return false;
        }

        // 检查两个用户是否有共同宠物
        $sharedPets = self::where('user_id', $userId1)
            ->whereIn('pet_id', function($query) use ($userId2) {
                $query->select('pet_id')
                    ->from('pets_masters')
                    ->where('user_id', $userId2)
                    ->whereNull('deleted_at');
            })
            ->whereNull('deleted_at')
            ->exists();

        return $sharedPets;
    }

    //获取用户的家人
    public static function getMyFamilyUserIds($userId, $withSelf = false)
    {
        $petIds = self::where('user_id', $userId)->pluck('pet_id')->toArray();
        $arr = self::whereIn('pet_id', $petIds)->where('user_id', '<>', $userId)->pluck('user_id')->toArray();
        return $withSelf ? array_merge($arr, [$userId]) : $arr;
    }

    //获取宠物的家人
    public static function getPetFamilyUserIds($petId)
    {
        return self::where('pet_id', $petId)->pluck('user_id')->toArray();
    }

    //获取用户的宠物
    public static function getPetIds($masterId, $sortBy = 'desc')
    {
        $masterPetData = $masterId ? PetMaster::where('user_id', $masterId)->select('pet_id', 'sort_order')->orderBy('sort_order', $sortBy)->get()->toArray() : [];
        $masterPetIds = [];
        foreach ($masterPetData as $item) {
            $masterPetIds[$item['pet_id']] = $item['sort_order'] ?? 0;
        }
        return $masterPetIds;
    }

    public static function removeFamilyMember($petId, $masterId, $user)
    {
        DB::beginTransaction();
        try {
            Pet::isOwner($petId, $user->id) or throw_if(true, 'RuntimeException', "沒有刪除權限");
            $accessId = self::isMaster($petId, $masterId) or throw_if(true, 'RuntimeException', "该用户不是宠物的家人");
            $success = PetMaster::delMaster($accessId, 1);  //主人移除
            if ($success) {
                $record = [];
                $message = '删除成功';

                // 触发家长移除事件
                event(new ParentRemovedEvent($petId, $user->id, $masterId));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    /**
     * 更新宠物家人关系
     *
     * @param int   $petId   宠物ID
     * @param array $userIds 用户ID数组，包含所有应该成为家人的用户ID
     * @return bool 是否成功
     */
    public static function updatePetFamily($petId, $userIds)
    {
        // 获取当前宠物的所有家人
        $currentFamilyMembers = self::where('pet_id', $petId)->get();
        $currentUserIds = $currentFamilyMembers->pluck('user_id')->toArray();

        // 需要添加的用户
        $usersToAdd = array_diff($userIds, $currentUserIds);

        // 需要移除的用户
        $usersToRemove = array_diff($currentUserIds, $userIds);

        // 获取宠物主人 ID
        $ownerId = Pet::where('id', $petId)->value('owner_id');

        // 添加新家人
        foreach ($usersToAdd as $userId) {
            if (!self::isMaster($petId, $userId)) {
                self::addMaster($petId, $userId);

                // 触发家长添加事件，但不触发主人自己的事件
                if ($userId != $ownerId) {
                    event(new ParentAddedEvent($petId, $ownerId, $userId));
                }
            }
        }

        // 移除家人
        foreach ($usersToRemove as $userId) {
            // 不允许移除宠物主人
            if ($userId != $ownerId) {
                $accessId = self::isMaster($petId, $userId);
                if ($accessId) {
                    self::delMaster($accessId, 2); // 2=主人移除

                    // 触发家长移除事件
                    event(new ParentRemovedEvent($petId, $ownerId, $userId));
                }
            }
        }

        return true;
    }

    public static function updatePetSort($petIds, $userId)
    {
        DB::beginTransaction();
        try {
            //4,13,10,6,16
            $petIds = explode(',', $petIds);
            $list = self::where('user_id', $userId)->select(['id', 'pet_id', 'sort_order'])->get()->toArray();
            throw_if(count($petIds) != count($list), 'RuntimeException', "必须传递所有的宠物ID");
            $petSorts = array_column($list, 'sort_order', 'pet_id');
            $petMapIds = array_column($list, 'id', 'pet_id');
            $newSorts = [];
            $i = 1;
            foreach ($petIds as $petId) {
                isset($petSorts[$petId]) or throw_if(true, 'RuntimeException', "宠物ID非法：" . $petId);
                $newSort = $i++;
                if ($newSort != $petSorts[$petId]) {
                    $newSorts[$petMapIds[$petId]] = $newSort;
                }
            }
            $success = true;
            foreach ($newSorts as $petId => $sortOrder) {
                $success = $success && self::where('id', $petId)->update(['sort_order' => $sortOrder]);
            }
            if ($success) {
                $record = [];
                $message = '更新成功';
            } else {
                throw new Exception('更新失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
