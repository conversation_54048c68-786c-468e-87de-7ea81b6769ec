<?php

namespace App\Services\PetCalculator;

/**
 * AAFCO营养标准常量类
 * 基于AAFCO Dog Food Nutrient Profiles和Cat Food Nutrient Profiles
 * 所有数值以每1000kcal ME (代谢能)为基准
 */
class AAFCOStandards
{
    /**
     * 狗的营养标准 (每1000kcal ME)
     */
    const DOG_STANDARDS = [
        // 幼犬标准 (0-12个月)
        'puppy' => [
            'macronutrients' => [
                'protein' => ['min' => 56.25, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'],
                'fat' => ['min' => 21.25, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'carbohydrate' => ['min' => 0, 'unit' => 'g', 'name' => '碳水化合物', 'name_en' => 'Carbohydrate'],
                'fiber' => ['max' => 12.5, 'unit' => 'g', 'name' => '纤维', 'name_en' => 'Fiber'],
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 1265, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 125, 'max' => 750, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 12.5, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'],
                'vitamin_k' => ['min' => 0.41, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 0.56, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.3, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 3.4, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 3.0, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 0.38, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.054, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'vitamin_b12' => ['min' => 0.007, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 340, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 3.0, 'max' => 4.5, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 2.5, 'max' => 4.0, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.4, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.75, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'],
                'chloride' => ['min' => 1.19, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.15, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 22, 'max' => 100, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 25, 'max' => 300, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 1.83, 'max' => 7.5, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'],
                'manganese' => ['min' => 1.25, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.087, 'max' => 0.5, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.22, 'max' => 2.2, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 2.0, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.59, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 0.95, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 1.72, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 1.77, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 1.06, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'phenylalanine_tyrosine' => ['min' => 2.12, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'threonine' => ['min' => 1.31, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.45, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.23, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
            ],
        ],
        // 成犬标准 (1-7岁)
        'adult' => [
            'macronutrients' => [
                'protein' => ['min' => 45.0, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'],
                'fat' => ['min' => 13.8, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'carbohydrate' => ['min' => 0, 'unit' => 'g', 'name' => '碳水化合物', 'name_en' => 'Carbohydrate'],
                'fiber' => ['max' => 10.0, 'unit' => 'g', 'name' => '纤维', 'name_en' => 'Fiber'],
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 1515, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 136, 'max' => 750, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 12.0, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'],
                'vitamin_k' => ['min' => 0.41, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 0.56, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.3, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 3.4, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 3.0, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 0.38, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.054, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'vitamin_b12' => ['min' => 0.007, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 340, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 1.25, 'max' => 4.5, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 1.0, 'max' => 4.0, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.5, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.3, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'],
                'chloride' => ['min' => 0.45, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.15, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 18, 'max' => 100, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 30, 'max' => 300, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 2.9, 'max' => 7.5, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'],
                'manganese' => ['min' => 1.4, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.11, 'max' => 0.5, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.22, 'max' => 2.2, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 1.28, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.43, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 0.95, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 1.72, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 1.5, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 1.06, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'phenylalanine_tyrosine' => ['min' => 1.89, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'threonine' => ['min' => 1.04, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.39, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.23, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
            ],
        ],
        // 老犬标准 (7岁以上) - 基于成犬标准调整
        'senior' => [
            'macronutrients' => [
                'protein' => ['min' => 50.0, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'], // 增加蛋白质
                'fat' => ['min' => 13.8, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'carbohydrate' => ['min' => 0, 'unit' => 'g', 'name' => '碳水化合物', 'name_en' => 'Carbohydrate'],
                'fiber' => ['max' => 8.0, 'unit' => 'g', 'name' => '纤维', 'name_en' => 'Fiber'], // 减少纤维
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 1515, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 136, 'max' => 750, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 15.0, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'], // 增加抗氧化
                'vitamin_k' => ['min' => 0.41, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 0.56, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.3, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 3.4, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 3.0, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 0.38, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.054, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'vitamin_b12' => ['min' => 0.007, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 340, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 1.25, 'max' => 4.5, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 1.0, 'max' => 4.0, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.5, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.25, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'], // 减少钠
                'chloride' => ['min' => 0.45, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.15, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 18, 'max' => 100, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 30, 'max' => 300, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 2.9, 'max' => 7.5, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'],
                'manganese' => ['min' => 1.4, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.11, 'max' => 0.5, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.22, 'max' => 2.2, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 1.28, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.43, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 0.95, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 1.72, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 1.5, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 1.06, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'phenylalanine_tyrosine' => ['min' => 1.89, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'threonine' => ['min' => 1.04, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.39, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.23, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
            ],
        ],
    ];

    /**
     * 猫的营养标准 (每1000kcal ME)
     */
    const CAT_STANDARDS = [
        // 幼猫标准 (0-12个月) - 基于官方AAFCO文档 (每1000kcal ME)
        'kitten' => [
            'macronutrients' => [
                'protein' => ['min' => 75.0, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'],
                'fat' => ['min' => 22.5, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'linoleic_acid' => ['min' => 1.4, 'unit' => 'g', 'name' => '亚油酸', 'name_en' => 'Linoleic Acid'],
                'arachidonic_acid' => ['min' => 0.05, 'unit' => 'g', 'name' => '花生四烯酸', 'name_en' => 'Arachidonic Acid'],
                'alpha_linolenic_acid' => ['min' => 0.05, 'unit' => 'g', 'name' => 'α-亚麻酸', 'name_en' => 'Alpha-Linolenic Acid'],
                'epa_dha' => ['min' => 0.03, 'unit' => 'g', 'name' => 'EPA+DHA', 'name_en' => 'EPA+DHA'],
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 1667, 'max' => 83325, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 70, 'max' => 7520, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 10, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'],
                'vitamin_k' => ['min' => 0.025, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 1.4, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.0, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 15.0, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 1.44, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 1.0, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.2, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'biotin' => ['min' => 0.018, 'unit' => 'mg', 'name' => '生物素', 'name_en' => 'Biotin'],
                'vitamin_b12' => ['min' => 0.005, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 600, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 2.5, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 2.0, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.5, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.5, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'],
                'chloride' => ['min' => 0.75, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.2, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 20.0, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 18.8, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 3.75, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'], // 幼猫需要更多铜
                'manganese' => ['min' => 1.9, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.075, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.45, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 3.1, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.83, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 1.4, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 3.2, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 3.0, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 2.75, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'methionine' => ['min' => 1.55, 'max' => 3.75, 'unit' => 'g', 'name' => '蛋氨酸', 'name_en' => 'Methionine'],
                'phenylalanine_tyrosine' => ['min' => 4.8, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'phenylalanine' => ['min' => 1.3, 'unit' => 'g', 'name' => '苯丙氨酸', 'name_en' => 'Phenylalanine'],
                'threonine' => ['min' => 1.83, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.63, 'max' => 4.25, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.55, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
                'taurine' => ['min' => 250, 'unit' => 'mg', 'name' => '牛磺酸', 'name_en' => 'Taurine'], // 干粮0.25g/1000kcal
            ],
        ],
        // 成猫标准 (1-7岁) - 基于官方AAFCO文档 (每1000kcal ME)
        'adult' => [
            'macronutrients' => [
                'protein' => ['min' => 65.0, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'],
                'fat' => ['min' => 22.5, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'linoleic_acid' => ['min' => 1.4, 'unit' => 'g', 'name' => '亚油酸', 'name_en' => 'Linoleic Acid'],
                'arachidonic_acid' => ['min' => 0.05, 'unit' => 'g', 'name' => '花生四烯酸', 'name_en' => 'Arachidonic Acid'],
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 833, 'max' => 83325, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 70, 'max' => 7520, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 10, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'],
                'vitamin_k' => ['min' => 0.025, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 1.4, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.0, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 15.0, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 1.44, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 1.0, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.2, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'biotin' => ['min' => 0.018, 'unit' => 'mg', 'name' => '生物素', 'name_en' => 'Biotin'],
                'vitamin_b12' => ['min' => 0.005, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 600, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 1.5, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 1.25, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.5, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.5, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'],
                'chloride' => ['min' => 0.75, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.1, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 20.0, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 18.8, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 1.25, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'],
                'manganese' => ['min' => 1.9, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.075, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.15, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 2.6, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.78, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 1.3, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 3.1, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 2.08, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 1.0, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'methionine' => ['min' => 0.5, 'max' => 3.75, 'unit' => 'g', 'name' => '蛋氨酸', 'name_en' => 'Methionine'],
                'phenylalanine_tyrosine' => ['min' => 3.83, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'phenylalanine' => ['min' => 1.05, 'unit' => 'g', 'name' => '苯丙氨酸', 'name_en' => 'Phenylalanine'],
                'threonine' => ['min' => 1.83, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.4, 'max' => 4.25, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.55, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
                'taurine' => ['min' => 250, 'unit' => 'mg', 'name' => '牛磺酸', 'name_en' => 'Taurine'], // 干粮0.25g/1000kcal
            ],
        ],
        // 老猫标准 (7岁以上) - 基于成猫标准调整
        'senior' => [
            'macronutrients' => [
                'protein' => ['min' => 70.0, 'unit' => 'g', 'name' => '蛋白质', 'name_en' => 'Protein'], // 增加蛋白质
                'fat' => ['min' => 22.5, 'unit' => 'g', 'name' => '脂肪', 'name_en' => 'Fat'],
                'carbohydrate' => ['min' => 0, 'unit' => 'g', 'name' => '碳水化合物', 'name_en' => 'Carbohydrate'],
                'fiber' => ['max' => 8.0, 'unit' => 'g', 'name' => '纤维', 'name_en' => 'Fiber'], // 减少纤维
            ],
            'vitamins' => [
                'vitamin_a' => ['min' => 2500, 'unit' => 'IU', 'name' => '维生素A', 'name_en' => 'Vitamin A'],
                'vitamin_d' => ['min' => 63, 'max' => 750, 'unit' => 'IU', 'name' => '维生素D', 'name_en' => 'Vitamin D'],
                'vitamin_e' => ['min' => 12.0, 'unit' => 'IU', 'name' => '维生素E', 'name_en' => 'Vitamin E'], // 增加抗氧化
                'vitamin_k' => ['min' => 0.25, 'unit' => 'mg', 'name' => '维生素K', 'name_en' => 'Vitamin K'],
                'thiamine' => ['min' => 1.4, 'unit' => 'mg', 'name' => '硫胺素(B1)', 'name_en' => 'Thiamine'],
                'riboflavin' => ['min' => 1.0, 'unit' => 'mg', 'name' => '核黄素(B2)', 'name_en' => 'Riboflavin'],
                'niacin' => ['min' => 15.0, 'unit' => 'mg', 'name' => '烟酸', 'name_en' => 'Niacin'],
                'pantothenic_acid' => ['min' => 1.4, 'unit' => 'mg', 'name' => '泛酸', 'name_en' => 'Pantothenic Acid'],
                'pyridoxine' => ['min' => 1.0, 'unit' => 'mg', 'name' => '吡哆醇(B6)', 'name_en' => 'Pyridoxine'],
                'folic_acid' => ['min' => 0.2, 'unit' => 'mg', 'name' => '叶酸', 'name_en' => 'Folic Acid'],
                'vitamin_b12' => ['min' => 0.005, 'unit' => 'mg', 'name' => '维生素B12', 'name_en' => 'Vitamin B12'],
                'choline' => ['min' => 637, 'unit' => 'mg', 'name' => '胆碱', 'name_en' => 'Choline'],
            ],
            'minerals' => [
                'calcium' => ['min' => 1.5, 'max' => 6.0, 'unit' => 'g', 'name' => '钙', 'name_en' => 'Calcium'],
                'phosphorus' => ['min' => 1.25, 'max' => 5.0, 'unit' => 'g', 'name' => '磷', 'name_en' => 'Phosphorus'],
                'potassium' => ['min' => 1.67, 'unit' => 'g', 'name' => '钾', 'name_en' => 'Potassium'],
                'sodium' => ['min' => 0.35, 'unit' => 'g', 'name' => '钠', 'name_en' => 'Sodium'], // 减少钠
                'chloride' => ['min' => 0.63, 'unit' => 'g', 'name' => '氯', 'name_en' => 'Chloride'],
                'magnesium' => ['min' => 0.1, 'max' => 0.25, 'unit' => 'g', 'name' => '镁', 'name_en' => 'Magnesium'],
                'iron' => ['min' => 20, 'max' => 100, 'unit' => 'mg', 'name' => '铁', 'name_en' => 'Iron'],
                'zinc' => ['min' => 18.5, 'max' => 300, 'unit' => 'mg', 'name' => '锌', 'name_en' => 'Zinc'],
                'copper' => ['min' => 1.25, 'max' => 7.5, 'unit' => 'mg', 'name' => '铜', 'name_en' => 'Copper'],
                'manganese' => ['min' => 1.9, 'unit' => 'mg', 'name' => '锰', 'name_en' => 'Manganese'],
                'selenium' => ['min' => 0.075, 'max' => 0.5, 'unit' => 'mg', 'name' => '硒', 'name_en' => 'Selenium'],
                'iodine' => ['min' => 0.44, 'max' => 2.2, 'unit' => 'mg', 'name' => '碘', 'name_en' => 'Iodine'],
            ],
            'amino_acids' => [
                'arginine' => ['min' => 2.62, 'unit' => 'g', 'name' => '精氨酸', 'name_en' => 'Arginine'],
                'histidine' => ['min' => 0.83, 'unit' => 'g', 'name' => '组氨酸', 'name_en' => 'Histidine'],
                'isoleucine' => ['min' => 1.31, 'unit' => 'g', 'name' => '异亮氨酸', 'name_en' => 'Isoleucine'],
                'leucine' => ['min' => 3.07, 'unit' => 'g', 'name' => '亮氨酸', 'name_en' => 'Leucine'],
                'lysine' => ['min' => 2.12, 'unit' => 'g', 'name' => '赖氨酸', 'name_en' => 'Lysine'],
                'methionine_cystine' => ['min' => 1.1, 'unit' => 'g', 'name' => '蛋氨酸+胱氨酸', 'name_en' => 'Methionine + Cystine'],
                'methionine' => ['min' => 0.44, 'unit' => 'g', 'name' => '蛋氨酸', 'name_en' => 'Methionine'],
                'phenylalanine_tyrosine' => ['min' => 2.38, 'unit' => 'g', 'name' => '苯丙氨酸+酪氨酸', 'name_en' => 'Phenylalanine + Tyrosine'],
                'phenylalanine' => ['min' => 1.04, 'unit' => 'g', 'name' => '苯丙氨酸', 'name_en' => 'Phenylalanine'],
                'threonine' => ['min' => 1.79, 'unit' => 'g', 'name' => '苏氨酸', 'name_en' => 'Threonine'],
                'tryptophan' => ['min' => 0.4, 'unit' => 'g', 'name' => '色氨酸', 'name_en' => 'Tryptophan'],
                'valine' => ['min' => 1.67, 'unit' => 'g', 'name' => '缬氨酸', 'name_en' => 'Valine'],
                'taurine' => ['min' => 100, 'unit' => 'mg', 'name' => '牛磺酸', 'name_en' => 'Taurine'], // 猫特有
            ],
        ],
    ];

    /**
     * 获取指定宠物类型和年龄阶段的营养标准
     *
     * @param string $petType 宠物类型 (dog/cat)
     * @param string $ageStage 年龄阶段 (puppy/adult/senior 或 kitten/adult/senior)
     * @return array|null
     */
    public static function getStandards(string $petType, string $ageStage): ?array
    {
        $petType = strtolower($petType);
        $ageStage = strtolower($ageStage);

        if ($petType === 'dog') {
            return self::DOG_STANDARDS[$ageStage] ?? null;
        } elseif ($petType === 'cat') {
            return self::CAT_STANDARDS[$ageStage] ?? null;
        }

        return null;
    }

    /**
     * 获取所有可用的宠物类型
     *
     * @return array
     */
    public static function getAvailablePetTypes(): array
    {
        return ['dog', 'cat'];
    }

    /**
     * 获取指定宠物类型的所有年龄阶段
     *
     * @param string $petType
     * @return array
     */
    public static function getAgeStages(string $petType): array
    {
        $petType = strtolower($petType);

        if ($petType === 'dog') {
            return array_keys(self::DOG_STANDARDS);
        } elseif ($petType === 'cat') {
            return array_keys(self::CAT_STANDARDS);
        }

        return [];
    }

    /**
     * 根据年龄确定年龄阶段
     *
     * @param string $petType 宠物类型
     * @param int $ageInYears 年龄（年）
     * @return string
     */
    public static function determineAgeStage(string $petType, int $ageInYears): string
    {
        $petType = strtolower($petType);

        if ($petType === 'dog') {
            if ($ageInYears < 1) {
                return 'puppy';
            } elseif ($ageInYears <= 7) {
                return 'adult';
            } else {
                return 'senior';
            }
        } elseif ($petType === 'cat') {
            if ($ageInYears < 1) {
                return 'kitten';
            } elseif ($ageInYears <= 7) {
                return 'adult';
            } else {
                return 'senior';
            }
        }

        return 'adult'; // 默认返回成年阶段
    }
}
