<?php

namespace App\Http\Controllers\Admin;

use App\Exports\GoodIndentExport;
use App\Helpers\ResponseHelper;
use App\Models\GoodIndent;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;

/**
 * indent
 * 订单管理
 * Class IndentController
 * @package App\Http\Controllers\Admin
 */
class GoodIndentController extends Controller
{
    /**
     * IndentList
     * 订单列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'                 => 'sometimes|integer|gt:0',
            'per_page'             => 'sometimes|integer|between:1,200',
            'keyword'              => 'nullable|max:255',
            'state'                => 'nullable|in:' . implode(',', array_keys(GoodIndent::STATE_TEXT_LIST)),
            'created_start'        => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'          => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'pay_time_start'       => 'nullable|required_with:pay_time_end|date_format:"Y-m-d H:i:s"',
            'pay_time_end'         => 'nullable|required_with:pay_time_start|date_format:"Y-m-d H:i:s"|after:pay_time_start',
            'shipping_time_start'  => 'nullable|required_with:shipping_time_end|date_format:"Y-m-d H:i:s"',
            'shipping_time_end'    => 'nullable|required_with:shipping_time_start|date_format:"Y-m-d H:i:s"|after:shipping_time_start',
            'confirm_time_start'   => 'nullable|required_with:confirm_time_end|date_format:"Y-m-d H:i:s"',
            'confirm_time_end'     => 'nullable|required_with:confirm_time_start|date_format:"Y-m-d H:i:s"|after:confirm_time_start',
            'refund_time_start'    => 'nullable|required_with:refund_time_end|date_format:"Y-m-d H:i:s"',
            'refund_time_end'      => 'nullable|required_with:refund_time_start|date_format:"Y-m-d H:i:s"|after:refund_time_start',
            'receiving_time_start' => 'nullable|required_with:receiving_time_end|date_format:"Y-m-d H:i:s"',
            'receiving_time_end'   => 'nullable|required_with:receiving_time_start|date_format:"Y-m-d H:i:s"|after:receiving_time_start',
            'sort_name'            => 'nullable|in:id,created_at,updated_at',
            'sort_by'              => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['with_trashed'] = true;
        $records = GoodIndent::getList($formData)->toArray();
        $records['state_list'] = GoodIndent::STATE_TEXT_LIST;
        return ResponseHelper::success($records);
    }

    public function exportList(Request $request)
    {
        $request->validate([
            'keyword'              => 'nullable|max:255',
            'state'                => 'nullable|in:' . implode(',', array_keys(GoodIndent::STATE_TEXT_LIST)),
            'created_start'        => 'required|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'          => 'required|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'pay_time_start'       => 'nullable|required_with:pay_time_end|date_format:"Y-m-d H:i:s"',
            'pay_time_end'         => 'nullable|required_with:pay_time_start|date_format:"Y-m-d H:i:s"|after:pay_time_start',
            'shipping_time_start'  => 'nullable|required_with:shipping_time_end|date_format:"Y-m-d H:i:s"',
            'shipping_time_end'    => 'nullable|required_with:shipping_time_start|date_format:"Y-m-d H:i:s"|after:shipping_time_start',
            'confirm_time_start'   => 'nullable|required_with:confirm_time_end|date_format:"Y-m-d H:i:s"',
            'confirm_time_end'     => 'nullable|required_with:confirm_time_start|date_format:"Y-m-d H:i:s"|after:confirm_time_start',
            'refund_time_start'    => 'nullable|required_with:refund_time_end|date_format:"Y-m-d H:i:s"',
            'refund_time_end'      => 'nullable|required_with:refund_time_start|date_format:"Y-m-d H:i:s"|after:refund_time_start',
            'receiving_time_start' => 'nullable|required_with:receiving_time_end|date_format:"Y-m-d H:i:s"',
            'receiving_time_end'   => 'nullable|required_with:receiving_time_start|date_format:"Y-m-d H:i:s"|after:receiving_time_start',
            'sort_name'            => 'nullable|in:id,created_at,updated_at',
            'sort_by'              => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['per_page'] = -1;
        try {
            $startDate = date('Y-m-d', strtotime($request->created_start));
            $endDate = date('Y-m-d', strtotime($request->created_end));
            $filePath = app()->make('exportPath', ["商品訂單{$startDate}至{$endDate}"]);
            if (!Excel::store(new GoodIndentExport($formData), $filePath)) {
                throw new \Exception(__('common.file_write_error'));
            }
            $download_url = asset('storage/' . $filePath);
            return ResponseHelper::success(['download_url' => $download_url]);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage() . '[' . $e->getFile() . '-' . $e->getLine() . ']');
        }
    }

    /**
     * GoodIndentQuantity
     * 订单数量统计
     */
    public function nums(Request $request)
    {
        $request->validate([
            'keyword'              => 'nullable|max:255',
            'created_start'        => 'required|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'          => 'required|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'pay_time_start'       => 'nullable|required_with:pay_time_end|date_format:"Y-m-d H:i:s"',
            'pay_time_end'         => 'nullable|required_with:pay_time_start|date_format:"Y-m-d H:i:s"|after:pay_time_start',
            'shipping_time_start'  => 'nullable|required_with:shipping_time_end|date_format:"Y-m-d H:i:s"',
            'shipping_time_end'    => 'nullable|required_with:shipping_time_start|date_format:"Y-m-d H:i:s"|after:shipping_time_start',
            'confirm_time_start'   => 'nullable|required_with:confirm_time_end|date_format:"Y-m-d H:i:s"',
            'confirm_time_end'     => 'nullable|required_with:confirm_time_start|date_format:"Y-m-d H:i:s"|after:confirm_time_start',
            'refund_time_start'    => 'nullable|required_with:refund_time_end|date_format:"Y-m-d H:i:s"',
            'refund_time_end'      => 'nullable|required_with:refund_time_start|date_format:"Y-m-d H:i:s"|after:refund_time_start',
            'receiving_time_start' => 'nullable|required_with:receiving_time_end|date_format:"Y-m-d H:i:s"',
            'receiving_time_end'   => 'nullable|required_with:receiving_time_start|date_format:"Y-m-d H:i:s"|after:receiving_time_start',
        ]);
        $formData = $request->all();
        $formData['with_trashed'] = true;
        return ResponseHelper::success(GoodIndent::nums($formData));
    }

    /**
     * IndentDetail
     * 订单详情
     * @queryParam  id int 订单ID
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(GoodIndent::getDetail($id));
    }

    /**
     * GoodIndentList
     * 人工创建商品订单
     */
    public function create(Request $request)
    {
        $rules = [
            'shipping_id'             => 'required|integer|gte:0',
            'user_id'                 => 'required|integer|exists:app_users,id',
            'is_paid'                 => 'required|in:0,1',
            'commodity'               => 'required|array',
            'commodity.*.good_id'     => 'required|integer|gt:0',
            'commodity.*.good_sku_id' => 'required|integer|gte:0',
            'commodity.*.number'      => 'required|integer|gt:0',
            'commodity.*.remark'      => 'nullable|string|max:200',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['is_cart'] = 0;
        $formData['commodity'] = $request->input('commodity');
        $result = GoodIndent::createGoodIndent($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * 设为已支付
     */
    public function paid(Request $request)
    {
        $rules = [
            'id' => 'required|integer|gt:0',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = GoodIndent::paid($formData, $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * 发货
     */
    public function shipment(Request $request)
    {
        $rules = [
            'id'     => 'required|integer|gt:0',
            'dhl_id' => 'required|integer|gt:0|exists:dhls,id',
            'odd'    => 'required|max:255',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = GoodIndent::shipment($formData, $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * 延长收货时间
     */
    public function receiving(Request $request)
    {
        $rules = [
            'id'             => 'required|integer|gt:0',
            'receiving_time' => 'required|date_format:"Y-m-d H:i:s"',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = GoodIndent::receiving($formData, $request->user());
        return ResponseHelper::result(...$result);
    }
}
