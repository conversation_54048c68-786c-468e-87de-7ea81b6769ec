<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class PetLog extends Model
{
    use HasFactory;

    protected $table = 'pets_logs';

    protected $fillable = [
        'user_id',
        'pet_id',
        'log_field',
        'old_value',
        'new_value',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getList($search_data = array())
    {
        //遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $logField = $search_data['log_field'] ?? "";
        $userId = $search_data['user_id'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('title', 'like', "%$keyword%")
                    ->orWhere('content', 'like', "%$keyword%");
            });
        })
            ->when($logField, function (Builder $query) use ($logField) {
                $query->where('log_field', $logField);
            })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('created_at', 'desc');
        if ($limit == -1) {
            return $query->get()->toArray();
        }
        return $query->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)->first();
    }

    /**
     * 获取每个petId的最后一条数据（最新记录）
     */
    public static function getLastRecords($petIds)
    {
        $list = self::query()
            // 子查询：获取每个type的最大created_at（或id）
            ->whereIn('id', function ($query) use ($petIds) {
                $query->selectRaw('MAX(id)')
                    ->from((new self())->getTable()) // 引用当前表
                    ->where('log_field', 'weight')
                    ->whereIn('pet_id', $petIds)
                    ->groupBy('pet_id'); // 按pet_id分组取每组最大id
            })
            ->where('log_field', 'weight')
            ->whereIn('pet_id', $petIds)
            ->get()
            ->toArray();
        return array_column($list, 'created_at', 'pet_id');
    }
}
