<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetReminderMemo extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_reminders_memos';

    protected $fillable = [
        'user_id',
        'config_id',
        'reminder_id',
        'read_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public function config()
    {
        return $this->belongsTo(PetReminderConfig::class, 'config_id');
    }

    public function reminder()
    {
        return $this->belongsTo(PetReminder::class, 'reminder_id');
    }

    public static function initList($userId)
    {
        $tutorialIds = PetReminderConfig::where('is_tutorial', 1)->pluck('id')->toArray();
        $existIds = self::where('user_id', $userId)->pluck('config_id')->toArray();
        if (!$addIds = array_diff($tutorialIds, $existIds)) {
            return;
        }
        self::insert(array_map(function ($id) use ($userId) {
            return [
                'user_id'    => $userId,
                'config_id'  => $id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, $addIds));
    }

    /**
     * 获取提醒列表
     */
    public static function getList($userId, $page = 1, $perPage = 20)
    {
        try {
            PetReminder::markMemoRead($userId, PetReminder::TYPE_TUTORIAL_REMINDER_MEMO);

            $query = self::with([
                'config'   => function ($query) {
                    $query->select('id', 'details', 'details_tc', 'details_en');
                },
                'reminder' => function ($query) {
                    $query->select('id', 'content', 'content_tc', 'content_en');
                },
            ])
                ->where('user_id', $userId);

            $list = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $list->getCollection()->transform(function ($item) {
                $item = $item->toArray();
                if ($item['config']) {
                    $item['content'] = $item['config']['details'];
                    $item['content_tc'] = $item['config']['details_tc'];
                    $item['content_en'] = $item['config']['details_en'];
                } else {
                    $item['content'] = $item['reminder']['content'];
                    $item['content_tc'] = $item['reminder']['content_tc'];
                    $item['content_en'] = $item['reminder']['content_en'];
                }
                unset($item['config_id'], $item['config'], $item['reminder_id'], $item['reminder']);
                return $item;
            });

            return [true, $list, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get pet reminder list failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 获取未读提醒数量
     */
    public static function getUnreadCount($userId)
    {
        try {
            $counts = self::where('user_id', $userId)->whereNull('read_at')->count();
            $result['counts'] = $counts;
            return [true, $result, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get pet memo unread count failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 标记提醒已读
     */
    public static function markRead($userId, $memoId = null)
    {
        try {
            $query = self::where('user_id', $userId)
                ->whereNull('read_at');

            if ($memoId) {
                $query->where('id', $memoId);
            }

            $query->update(['read_at' => now()]);

            return [true, [], '标记成功'];
        } catch (\Exception $e) {
            logErr('Mark pet memo read failed:', [
                'user_id' => $userId,
                'memo_id' => $memoId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '标记失败'];
        }
    }

}
