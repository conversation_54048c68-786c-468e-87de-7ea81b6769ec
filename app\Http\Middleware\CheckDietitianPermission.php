<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class CheckDietitianPermission
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        if (!$user->is_dietitian) {
            throw ValidationException::withMessages([
                'message' => __('common.no_permission'),
            ]);
        }

        return $next($request);
    }
}
