<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Good;
use App\Models\GoodSku;
use App\Models\Pet;
use App\Services\PetMatchingService;
use Illuminate\Http\Request;

/**
 * SKU宠物匹配测试控制器
 * 用于验证移动端商品列表的宠物匹配功能
 */
class SkuPetMatchingTestController extends Controller
{
    protected $matchingService;

    public function __construct(PetMatchingService $matchingService)
    {
        $this->matchingService = $matchingService;
    }

    /**
     * 测试单个用户的宠物与SKU匹配
     */
    public function testUserPetMatching(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:app_users,id',
            'sku_id' => 'required|integer|exists:goods_skus,id'
        ]);

        $userId = $request->input('user_id');
        $skuId = $request->input('sku_id');

        try {
            // 获取SKU信息
            $sku = GoodSku::with('good')->find($skuId);
            if (!$sku || empty($sku->pet_filter_tags)) {
                return ResponseHelper::error('SKU不存在或未设置筛选标签');
            }

            // 获取用户宠物
            $userPets = Pet::where('owner_id', $userId)
                ->where('deleted_at', null)
                ->with(['petType:id,name', 'petBreed:id,name'])
                ->select(['id', 'name', 'avatar', 'type_id', 'breed_id', 'birthday', 'weight', 'weight_status'])
                ->get();

            // 执行匹配
            $matchedPets = $this->matchingService->matchUserPetsForSku($sku->pet_filter_tags, $userId);

            return ResponseHelper::success([
                'sku_info' => [
                    'id' => $sku->id,
                    'good_name' => $sku->good->name ?? '',
                    'product_sku' => $sku->product_sku,
                    'filter_tags' => $sku->pet_filter_tags,
                    'matching_pets_count' => $sku->matching_pets_count
                ],
                'user_pets' => $userPets->map(function($pet) {
                    return [
                        'id' => $pet->id,
                        'name' => $pet->name,
                        'avatar' => $pet->avatar,
                        'type_name' => $pet->petType->name ?? '',
                        'breed_name' => $pet->petBreed->name ?? '',
                        'age' => $pet->birthday ? round((now()->diffInMonths($pet->birthday)) / 12, 1) : null,
                        'weight' => $pet->weight,
                        'weight_status' => $pet->weight_status
                    ];
                })->toArray(),
                'matched_pets' => $matchedPets,
                'matching_summary' => [
                    'total_user_pets' => $userPets->count(),
                    'matched_count' => count($matchedPets),
                    'match_rate' => $userPets->count() > 0 ? round(count($matchedPets) / $userPets->count() * 100, 1) . '%' : '0%'
                ]
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('测试失败：' . $e->getMessage());
        }
    }

    /**
     * 测试商品列表API的宠物匹配功能
     */
    public function testGoodListMatching(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:app_users,id',
            'per_page' => 'sometimes|integer|between:1,10'
        ]);

        $userId = $request->input('user_id');
        $perPage = $request->input('per_page', 5);

        try {
            // 模拟商品列表查询
            $goods = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
                ->with([
                    'goodSku' => function ($q) {
                        $q->select('id', 'good_id', 'price', 'inventory', 'image', 'pet_filter_tags', 'matching_pets_count')
                          ->whereNotNull('pet_filter_tags');
                    }
                ])
                ->whereHas('goodSku', function($q) {
                    $q->whereNotNull('pet_filter_tags');
                })
                ->select(['id', 'name', 'image', 'price'])
                ->limit($perPage)
                ->get();

            // 批量处理宠物匹配
            $allSkus = [];
            foreach ($goods as $good) {
                foreach ($good->goodSku as $sku) {
                    $allSkus[] = [
                        'id' => $sku->id,
                        'pet_filter_tags' => $sku->pet_filter_tags ?? []
                    ];
                }
            }

            $skuMatchingData = [];
            if (!empty($allSkus)) {
                $skuMatchingData = $this->matchingService->batchMatchUserPetsForSkus($allSkus, $userId);
            }

            // 格式化响应数据
            $formattedGoods = $goods->map(function($good) use ($skuMatchingData) {
                $goodArray = $good->toArray();

                // 为每个SKU添加匹配的宠物信息
                if (isset($goodArray['good_sku'])) {
                    foreach ($goodArray['good_sku'] as &$sku) {
                        $sku['matched_pets'] = $skuMatchingData[$sku['id']] ?? [];
                        $sku['has_matched_pets'] = !empty($sku['matched_pets']);
                    }
                    unset($sku);
                }

                return $goodArray;
            });

            // 统计信息
            $totalSkus = collect($allSkus)->count();
            $skusWithMatches = collect($skuMatchingData)->filter(function($matches) {
                return !empty($matches);
            })->count();

            return ResponseHelper::success([
                'goods' => $formattedGoods,
                'statistics' => [
                    'total_goods' => $goods->count(),
                    'total_skus' => $totalSkus,
                    'skus_with_matches' => $skusWithMatches,
                    'match_rate' => $totalSkus > 0 ? round($skusWithMatches / $totalSkus * 100, 1) . '%' : '0%'
                ],
                'user_id' => $userId
            ]);

        } catch (\Exception $e) {
            return ResponseHelper::error('测试失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户宠物信息（用于测试参考）
     */
    public function getUserPets(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|exists:app_users,id'
        ]);

        $userId = $request->input('user_id');

        $pets = Pet::where('owner_id', $userId)
            ->where('deleted_at', null)
            ->with(['petType:id,name', 'petBreed:id,name'])
            ->select([
                'id', 'name', 'avatar', 'type_id', 'breed_id',
                'birthday', 'weight', 'weight_status'
            ])
            ->get();

        $formattedPets = $pets->map(function($pet) {
            $age = $pet->birthday ? now()->diffInMonths($pet->birthday) : null;

            return [
                'id' => $pet->id,
                'name' => $pet->name,
                'avatar' => $pet->avatar,
                'type_name' => $pet->petType->name ?? '',
                'breed_name' => $pet->petBreed->name ?? '',
                'age_months' => $age,
                'age_years' => $age ? round($age / 12, 1) : null,
                'weight' => $pet->weight,
                'weight_status' => $pet->weight_status,
                'weight_status_text' => $this->getWeightStatusText($pet->weight_status)
            ];
        });

        return ResponseHelper::success([
            'user_id' => $userId,
            'pets' => $formattedPets,
            'total_count' => $pets->count()
        ]);
    }

    /**
     * 获取体重状态文本（9分制BCS系统 - 5个阶段）
     */
    private function getWeightStatusText($status)
    {
        if ($status === null) return '未知';

        // 简化为5个阶段
        if ($status >= 1 && $status <= 2) return '非常瘦';
        if ($status >= 3 && $status <= 4) return '瘦';
        if ($status == 5) return '正常';
        if ($status >= 6 && $status <= 7) return '胖';
        if ($status >= 8 && $status <= 9) return '非常胖';

        return '未知';
    }
}
