<?php

namespace App\Models;

use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class AppUserLog extends Model
{
    use HasFactory;

    protected $table = 'app_users_logs';

    protected $fillable = [
        'user_id',
        'path',
        'method',
        'ip',
        'user_agent',
        'token',
        'input',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function getTodayVisitedUsers($userIds)
    {
        if (!$userIds) {
            return [];
        }
        return self::whereIn('user_id', $userIds)
            ->whereDate('created_at', Carbon::today()->format('Y-m-d'))
            ->distinct()->pluck('user_id')->toArray();
    }
}
