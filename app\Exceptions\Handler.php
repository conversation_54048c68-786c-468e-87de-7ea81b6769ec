<?php

namespace App\Exceptions;

use App\Helpers\ResponseHelper;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Reflector;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (\Throwable $e) {
            //
        });
    }

    public function render($request, \Throwable $e)
    {
        if ($e instanceof \Illuminate\Auth\AuthenticationException) {
            return ResponseHelper::error(__('common.unauthenticated'), ResponseHelper::STATUS_NO_AUTH);
        }
        if ($e instanceof NoPermissionException) {
            return ResponseHelper::error(__('common.no_permission'), ResponseHelper::STATUS_NO_PERMISSION);
        }
        if ($e instanceof \Illuminate\Validation\ValidationException) {
            return ResponseHelper::error($e->getMessage(), ResponseHelper::STATUS_ERR);
        }
        if ($e instanceof \Illuminate\Http\Exceptions\ThrottleRequestsException) {
            return ResponseHelper::error(__('common.too_many_attempts'), ResponseHelper::STATUS_ERR);
        }
        if ($e instanceof \App\Exceptions\AIServiceException) {
            // 處理AI服務異常
            $errorMessage = $e->getMessage();
            $errorType = $e->getErrorType();
            $sessionId = $e->getSessionId();
            $extraData = $e->getExtraData();

            // 構建響應數據
            $responseData = [
                'error_type' => $errorType
            ];

            // 添加會話 ID（如果有）
            if ($sessionId) {
                $responseData['session_id'] = $sessionId;
            }

            // 添加額外數據（如果有）
            if (!empty($extraData)) {
                $responseData = array_merge($responseData, $extraData);
            }

            // 使用修改後的ResponseHelper::error方法，現在可以接受第三個參數作為數據
            return ResponseHelper::error($errorMessage, ResponseHelper::STATUS_ERR, $responseData);
        }
//        if (!\App::runningInConsole() && !\App::isLocal()) { //出于安全考虑，api请求不显示详细信息 （本地可以直接看异常，更快一点）
//            return ResponseHelper::error(__('common.server_error'), ResponseHelper::STATUS_SERVER_ERROR);
//        }
        return parent::render($request, $e);
    }

    public function report(\Throwable $e)
    {
        $e = $this->mapException($e);

        if ($this->shouldntReport($e)) {
            return;
        }

        if (Reflector::isCallable($reportCallable = [$e, 'report']) &&
            $this->container->call($reportCallable) !== false) {
            return;
        }

        foreach ($this->reportCallbacks as $reportCallback) {
            if ($reportCallback->handles($e) && $reportCallback($e) === false) {
                return;
            }
        }

        try {
            logErr($e);
        } catch (\Throwable $customException) {
            //極端特殊情況，發生異常，避免完全定位不到錯誤，用一下父類report
            parent::report($e);
            parent::report($customException);
        }
    }
}
