<?php

namespace App\Listeners;

use App\Events\PostCommentedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPostCommentedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PostCommentedEvent  $event
     * @return void
     */
    public function handle(PostCommentedEvent $event)
    {
        try {
            // 如果帖子所有者和评论者是同一人，不发送通知
            if ($event->postOwnerId == $event->commenterId) {
                return;
            }

            // 获取评论者信息
            $commenter = AppUser::find($event->commenterId);

            if (!$commenter) {
                Log::error('Post commented notification failed: Commenter not found', [
                    'commenter_id' => $event->commenterId,
                    'post_id' => $event->postId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->postOwnerId, // 接收通知的用户ID（帖子所有者）
                NotificationSetting::TYPE_POST_COMMENTED, // 通知类型
                '帖子评论通知',
                "用户 {$commenter->username} 评论了您的帖子",
                $event->commenterId, // 发送者ID（评论者）
                $event->postId, // 相关ID（帖子ID）
                NotificationSetting::RELATION_TYPE_CONTENT // 相关类型
            );

            Log::info('Post commented notification sent', [
                'post_id' => $event->postId,
                'comment_id' => $event->commentId,
                'owner_id' => $event->postOwnerId,
                'commenter_id' => $event->commenterId
            ]);
        } catch (\Exception $e) {
            Log::error('Post commented notification failed', [
                'error' => $e->getMessage(),
                'post_id' => $event->postId,
                'comment_id' => $event->commentId,
                'owner_id' => $event->postOwnerId,
                'commenter_id' => $event->commenterId
            ]);
        }
    }
}
