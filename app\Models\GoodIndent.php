<?php

namespace App\Models;

use App\Helpers\RedisLock;
use App\Services\GoodIndent\GoodIndentService;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class GoodIndent extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'consignee',
        'state',
        'good_amount',
        'carriage_amount',
        'coupon_amount',
        'pay_amount',
        'identification',
        'dhl_id',
        'odd',
        'refund_amount',
        'overtime',
        'pay_time',
        'shipping_time',
        'confirm_time',
        'refund_time',
        'is_automatic_receiving',
        'receiving_time',
        'is_cart',
        'pay_method',
        'pay_type',
    ];

    const ORDER_OVER_TIME = 120;  //订单超时时间(5-120分钟)
    const AUTOMATIC_RECEIVING_STATE = true; // 是否开启自动收货
    const AUTOMATIC_RECEIVING_DAY = 7; // 多少天后自动收货(天)

    const GOOD_INDENT_STATE_PAY = 1; //状态：待付款
    const GOOD_INDENT_STATE_DELIVER = 2; //状态：待发货
    const GOOD_INDENT_STATE_TAKE = 3; //状态：待收货
    const GOOD_INDENT_STATE_FAILURE = 4; //状态：已失效
    const GOOD_INDENT_STATE_ACCOMPLISH = 5; //状态：已完成
    const GOOD_INDENT_STATE_CANCEL = 6; //状态：已取消
    const GOOD_INDENT_STATE_REFUND = 7; //状态：已退款
    const GOOD_INDENT_STATE_REFUND_PROCESSING = 8; //状态：退款处理中
    const GOOD_INDENT_IS_AUTOMATIC_RECEIVING_YES = 1; //自动发货：是
    const GOOD_INDENT_IS_AUTOMATIC_RECEIVING_NO = 0; //自动发货：否

    const STATE_TEXT_LIST = [
        //self::GOOD_INDENT_STATE_PAY               => '待付款',
        self::GOOD_INDENT_STATE_DELIVER           => '待发货',
        self::GOOD_INDENT_STATE_TAKE              => '待收货',
        self::GOOD_INDENT_STATE_FAILURE           => '已失效',
        self::GOOD_INDENT_STATE_ACCOMPLISH        => '已完成',
        //self::GOOD_INDENT_STATE_CANCEL            => '已取消',
        self::GOOD_INDENT_STATE_REFUND            => '已退款',
        self::GOOD_INDENT_STATE_REFUND_PROCESSING => '退款处理中',
    ];

    protected $table = 'goods_indents';

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取订单商品列表
     */
    public function Commodities()
    {
        return $this->hasMany(GoodIndentCommodity::class, 'good_indent_id', 'id');
    }

    /**
     * 用户
     */
    public function user()
    {
        return $this->hasOne(AppUser::class, 'id', 'user_id');
    }

    /**
     * 获取订单收货地址
     */
    public function Location()
    {
        return $this->hasOne(GoodIndentLocation::class, 'good_indent_id', 'id');
    }

    /**
     * 物流公司
     */
    public function Dhl()
    {
        return $this->hasOne(Dhl::class, 'id', 'dhl_id');
    }

    /**
     * 获取订单操作记录
     */
    public function Logs()
    {
        return $this->hasMany(GoodIndentLog::class, 'good_indent_id', 'id');
    }

    /**
     * 获取订单支付记录
     */
    public function PaymentLog()
    {
        return $this->morphOne('App\Models\PaymentLog', 'pay');
    }

    /**
     * 获取订单支付记录列表
     */
    public function PaymentLogAll()
    {
        return $this->morphMany('App\Models\PaymentLog', 'pay');
    }

    public static function getListQuery($search_data = array())
    {
        // 遍历筛选条件
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";
        $state = $search_data['state'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $payTimeStart = $search_data['pay_time_start'] ?? "";
        $payTimeEnd = $search_data['pay_time_end'] ?? "";
        $shippingTimeStart = $search_data['shipping_time_start'] ?? "";
        $shippingTimeEnd = $search_data['shipping_time_end'] ?? "";
        $confirmTimeStart = $search_data['confirm_time_start'] ?? "";
        $confirmTimeEnd = $search_data['confirm_time_end'] ?? "";
        $refundTimeStart = $search_data['refund_time_start'] ?? "";
        $refundTimeEnd = $search_data['refund_time_end'] ?? "";
        $receivingTimeStart = $search_data['receiving_time_start'] ?? "";
        $receivingTimeEnd = $search_data['receiving_time_end'] ?? "";

        if (!empty($search_data['with_trashed'])) {
            $query = self::withTrashed()->with(['Dhl']);
        } else {
            $query = self::whereNull('deleted_at');
        }

        return $query->when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $q) use ($keyword) {
                $q->where(function (Builder $q1) use ($keyword) {
                    $q1->where('identification', $keyword)->orWhere('odd', $keyword);
                });
                $q->orWhereHas('Location', function ($query) use ($keyword) {
                    $query->where('cellphone', $keyword)->orWhere('name', $keyword);
                });
                $q->orWhereHas('Commodities', function ($query) use ($keyword) {
                    $query->withTrashed()->where('name', 'like', "%$keyword%");
                });
            });
        })
            ->whereNotIn('state', [self::GOOD_INDENT_STATE_PAY, self::GOOD_INDENT_STATE_CANCEL])
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($state, function (Builder $query) use ($state) {
                $query->where('state', $state);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($payTimeStart && $payTimeEnd, function (Builder $query) use ($payTimeStart, $payTimeEnd) {
                $query->whereBetween('pay_time', [$payTimeStart, $payTimeEnd]);
            })
            ->when($shippingTimeStart && $shippingTimeEnd, function (Builder $query) use ($shippingTimeStart, $shippingTimeEnd) {
                $query->whereBetween('shipping_time', [$shippingTimeStart, $shippingTimeEnd]);
            })
            ->when($confirmTimeStart && $confirmTimeEnd, function (Builder $query) use ($confirmTimeStart, $confirmTimeEnd) {
                $query->whereBetween('confirm_time', [$confirmTimeStart, $confirmTimeEnd]);
            })
            ->when($refundTimeStart && $refundTimeEnd, function (Builder $query) use ($refundTimeStart, $refundTimeEnd) {
                $query->whereBetween('refund_time', [$refundTimeStart, $refundTimeEnd]);
            })
            ->when($receivingTimeStart && $receivingTimeEnd, function (Builder $query) use ($receivingTimeStart, $receivingTimeEnd) {
                $query->whereBetween('receiving_time', [$receivingTimeStart, $receivingTimeEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            });
    }

    public static function getList($search_data = array())
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $query = self::getListQuery($search_data)
            ->with([
                'user'        => function (Builder $query) {
                    $query->select('id', 'username', 'phone', 'avatar');
                },
                'Commodities' => function (Builder $q) {
                    $q->select([
                        'id',
                        'good_indent_id',
                        'good_id',
                        'good_sku_id',
                        'freight_id',
                        'image',
                        'name',
                        'name_tc',
                        'name_en',
                        'product_code',
                        'product_sku',
                        'price',
                        'number',
                        'weight',
                        'remark',
                        'good_total',
                        'carriage_total',
                        'coupon_total',
                        'pay_total',
                        'refund_number',
                        'refund_total',
                    ]);
                },
                'Location'    => function (Builder $q) {
                    $q->select([
                        'good_indent_id',
                        'cellphone',
                        'name',
                        'location',
                        'address',
                        'latitude',
                        'longitude',
                        'house',
                    ]);
                },
                'PaymentLog'  => function (Builder $q) {
                    $q->where('state', PaymentLog::PAYMENT_LOG_STATE_COMPLETE)->where('type', PaymentLog::PAYMENT_LOG_TYPE_GOODS_INDENT);
                },
            ])
            ->orderBy('id', 'desc');
        if ($limit == -1) {
            return $query->get()->toArray();
        }
        return $query->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail($id)
    {
        return self::withTrashed()
            ->where('id', $id)
            ->with([
                'user'          => function (Builder $query) {
                    $query->select('id', 'username', 'phone', 'avatar');
                },
                'Commodities'   => function (Builder $query) {
                    $query->with('pets', function (Builder $q) {
                        $q->select([
                            'pets.id',
                            'pets.name',
                            'pets.sex',
                            'pets.avatar',
                            'pets.type_id',
                            'pets.breed_id',
                            'pets.birthday',
                            'pets.owner_id',
                            'pets.created_at',
                            'pets.deleted_at',
                        ]);
                    });
                },
                'Location',
                'Dhl',
                'Logs',
                'PaymentLog'    => function (Builder $q) {
                    $q->where('state', PaymentLog::PAYMENT_LOG_STATE_COMPLETE)->where('type', PaymentLog::PAYMENT_LOG_TYPE_GOODS_INDENT);
                },
                'PaymentLogAll' => function (Builder $q) {
                    $q->select('id', 'type', 'name', 'money', 'trade_no', 'transaction_id', 'platform', 'state', 'pay_id', 'pay_type', 'created_at');
                }
            ])->first();
    }

    public static function getDetailForApp($id, $userId)
    {
        $info = self::where('id', $id)->where('user_id', $userId)
            ->with([
                'Commodities' => function (Builder $query) {
                    $query->with('pets', function (Builder $q) {
                        $q->select([
                            'pets.id',
                            'pets.name',
                            'pets.sex',
                            'pets.avatar',
                            'pets.type_id',
                            'pets.breed_id',
                            'pets.birthday',
                            'pets.owner_id',
                            'pets.created_at',
                            'pets.deleted_at',
                        ]);
                    });
                },
                'Location',
                'Dhl',
            ])->first();
        if ($info) {
            $info = $info->toArray();
            foreach ($info['commodities'] as &$commodity) {
                if (!empty($commodity['pets'])) {
                    foreach ($commodity['pets'] as &$pet) {
                        unset($pet['pivot']);
                    }
                }
            }
        }
        return $info;
    }

    public static function confirmGoodIndent($data = array())
    {
        $GoodIndent = new self();
        $Shipping = Shipping::getDetailForApp($data['shipping_id'], $data['user_id']);
        $carriage = Shipping::getCarriage($Shipping, $data['commodity']);
        if ($error = $carriage['error']) {
            $GoodIndent->error = $error;
        }
        $Shipping and $Shipping->setHidden(['created_at', 'updated_at', 'deleted_at']);
        $GoodIndent->shipping = $Shipping;
        $GoodIndent->good_amount = 0;
        $Commodities = GoodIndentCommodity::getCommodities($data['commodity'], $data['user_id']);
        $GoodIndent->carriage_amount = $carriage['carriage'];
        foreach ($Commodities as $indentCommodity) {
            //按比例计算运费
            GoodIndentCommodity::setCarriageTotal($indentCommodity, $carriage['list']);
            //订单
            $GoodIndent->good_amount = +bcadd($GoodIndent->good_amount, $indentCommodity->good_total, 2);
            $indentCommodity->pay_total = +bcadd($indentCommodity->good_total, $indentCommodity->carriage_total, 2);
        }
        $GoodIndent->commodities = $Commodities;
        $GoodIndent->pay_amount = +bcadd($GoodIndent->good_amount, $GoodIndent->carriage_amount, 2);
        return $GoodIndent;
    }

    public static function createGoodIndent($data = array())
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $lock = RedisLock::lock($redis, 'CreateGoodIndent_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            $isCart = $data['is_cart'];
            $GoodIndent = self::confirmGoodIndent($data);
            if ($error = $GoodIndent['error']) {
                //throw new Exception($error);
            }
            //保存主单
            $GoodIndent->state = self::GOOD_INDENT_STATE_PAY;
            $GoodIndent->user_id = $data['user_id'];
            $GoodIndent->identification = orderNumber();
            $GoodIndent->overtime = date('Y-m-d H:i:s', time() + self::ORDER_OVER_TIME * 60);
            $GoodIndent->is_cart = $data['is_cart'];
            $GoodIndent->pay_method = $data['pay_method'];
            $GoodIndent->pay_type = $data['pay_type'] ?? null;
            if ($GoodIndent->pay_amount == 0 || !empty($data['is_paid'])) {
                self::setPaid($GoodIndent);
            }
            $GoodIndent->id = self::create($GoodIndent->toArray())->id;
            $success = $GoodIndent->id > 0;
            //保存子订单
            GoodIndentCommodity::saveByGoodIndent($GoodIndent);
            //保存收货地址
            if ($GoodIndent->shipping) {
                GoodIndentLocation::saveByGoodIndent($GoodIndent);
            }
            if ($success) {
                $success = true;
                $record = self::getPaymentInfo($GoodIndent);
                $message = '下单成功';
            } else {
                throw new Exception('下单失敗');
            }
            DB::commit();
            $isCart and self::clearCartByGoodIndent($GoodIndent);
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($data['user_id'] . '用户下单失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'CreateGoodIndent_' . $userId);
        }
        return [$success, $record, $message];
    }

    //删除购物车对应商品
    private static function clearCartByGoodIndent($GoodIndent)
    {
        $cartKey = "shoppingCart:{$GoodIndent->user_id}";
        foreach ($GoodIndent->commodities as $indentCommodity) {
            $itemKey = "{$indentCommodity['good_id']}:{$indentCommodity['good_sku_id']}";
            redis()->hDel($cartKey, $itemKey);
        }
    }

    public static function getPaymentInfo($GoodIndent)
    {
        $info = [
            'need_pay'       => $GoodIndent->state == self::GOOD_INDENT_STATE_PAY,
            'id'             => $GoodIndent->id,
            'identification' => $GoodIndent->identification,
            'pay_amount'     => $GoodIndent->pay_amount,
        ];
        if ($info['need_pay']) {
            $info['payment'] = PaymentLog::saveByGoodIndent($GoodIndent);
        }
        return $info;
    }

    public static function setPaid($GoodIndent, $payTime = null)
    {
        throw_if($GoodIndent->state != self::GOOD_INDENT_STATE_PAY, 'RuntimeException', "订单：{$GoodIndent->id}状态不正确");
        $updateIndent = [
            'state'    => self::GOOD_INDENT_STATE_DELIVER,  //付款成功，进入待发货状态
            'pay_time' => $payTime ?: date('Y-m-d H:i:s'),
        ];
        foreach ($updateIndent as $k => $v) {
            $GoodIndent->$k = $v;
        }
        GoodIndentCommodity::saveByPaid($GoodIndent);
        return $updateIndent;
    }

    public static function repay($data = array(), $user = null)
    {
        $service = new GoodIndentService($data['id'], $user);
        return [$service->repay($data), self::getPaymentInfo($service->getGoodIndent()), $service->getMessage()];
    }

    public static function paid($data = array(), $user = null)
    {
        $service = new GoodIndentService($data['id'], $user);
        return [$service->paid($data), $service->getData(), $service->getMessage()];
    }

    public static function shipment($data = array(), $user = null)
    {
        $service = new GoodIndentService($data['id'], $user);
        return [$service->shipment($data), $service->getData(), $service->getMessage()];
    }

    public static function receiving($data = array(), $user = null)
    {
        $service = new GoodIndentService($data['id'], $user);
        return [$service->receiving($data), $service->getData(), $service->getMessage()];
    }

    public static function receipt($id, $user = null)
    {
        $service = new GoodIndentService($id, $user);
        return [$service->receipt(), $service->getData(), $service->getMessage()];
    }

    public static function cancel($id, $user = null)
    {
        $service = new GoodIndentService($id, $user);
        return [$service->cancel(), $service->getData(), $service->getMessage()];
    }

    public static function del($id, $user = null)
    {
        $service = new GoodIndentService($id, $user);
        return [$service->del(), $service->getData(), $service->getMessage()];
    }

    public static function nums($data = array())
    {
        $query = self::getListQuery($data);

        $result = $query->selectRaw('
            COUNT(*) as `all`,
            SUM(CASE WHEN state = ? AND deleted_at IS NULL THEN 1 ELSE 0 END) as obligation,
            SUM(CASE WHEN state = ? AND deleted_at IS NULL THEN 1 ELSE 0 END) as waitdeliver,
            SUM(CASE WHEN state = ? AND deleted_at IS NULL THEN 1 ELSE 0 END) as waitforreceiving,
            SUM(CASE WHEN state = ? AND deleted_at IS NULL THEN 1 ELSE 0 END) as refunding
        ', [
            self::GOOD_INDENT_STATE_PAY,
            self::GOOD_INDENT_STATE_DELIVER,
            self::GOOD_INDENT_STATE_TAKE,
            self::GOOD_INDENT_STATE_REFUND_PROCESSING,
        ])
            ->first();

        return [
            'all'              => $result->all,
            'obligation'       => (int)$result->obligation,       //待付款
            'waitdeliver'      => (int)$result->waitdeliver,      //待发货
            'waitforreceiving' => (int)$result->waitforreceiving, //待收货
            'refunding'        => (int)$result->refunding,        //退款处理中
        ];
    }
}
