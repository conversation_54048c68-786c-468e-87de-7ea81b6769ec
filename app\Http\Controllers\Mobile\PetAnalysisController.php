<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Controllers\UploadOSSController;
use App\Models\AnalysisBodyPart;
use App\Models\AnalysisQuickQuestion;
use App\Models\AppUserLog;
use App\Models\BodyPartSymptom;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\ChatSessionPetHistory;
use App\Models\Pet;
use App\Models\PetCertificate;
use App\Models\PetMaster;
use App\Models\PetMedicalRecord;
use App\Models\PetReminder;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Services\PetAnalysis\AnalysisServiceFactory;
use App\Services\AliBailian\AliBailianService;
use App\Services\PetCertificate\PetCertificateRecognitionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PetAnalysisController extends Controller
{
    /**
     * AI服务
     *
     * @var AliBailianService
     */
    protected $aiService;

    /**
     * 证件识别服务
     *
     * @var PetCertificateRecognitionService
     */
    protected $certificateService;

    /**
     * 上传服务
     *
     * @var UploadOSSController
     */
    protected $uploadService;

    /**
     * 构造函数
     *
     * @param AliBailianService                $aliBailianService
     * @param PetCertificateRecognitionService $certificateService
     * @param UploadOSSController              $uploadService
     */
    public function __construct(
        AliBailianService                $aliBailianService,
        PetCertificateRecognitionService $certificateService,
        UploadOSSController              $uploadService
    )
    {
        $this->aiService = $aliBailianService;
        $this->certificateService = $certificateService;
        $this->uploadService = $uploadService;
    }

    /**
     * 实验室报告（血液/尿液）AI分析
     * - 必传：pet_id, images(URL数组)
     * - 直接将多张图片交给AI进行结构化分析（无需OCR）
     * - 返回 organs -> indicators 三态（少/偏多/正常）并附带诱因说明
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function analyzeLabReport(Request $request)
    {
        // 参数校验：pet_id 与 images[] URL
        $validator = Validator::make($request->all(), [
            'pet_id'   => 'required|integer|exists:pets,id',
            'images'   => 'required|array|min:1|max:10',
            'images.*' => 'required|string|url|pic|trust_host',
        ], [
            'pet_id.required' => '请选择要分析的宠物',
            'pet_id.exists'   => '宠物不存在',
            'images.required' => '请提供报告图片URL',
            'images.array'    => '图片URL必须是数组格式',
            'images.min'      => '至少需要上传1张图片',
            'images.max'      => '最多只能上传10张图片',
            'images.*.url'    => '请提供有效的图片URL',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $userId = $request->user()->id ?: 0;
            $petId = (int)$request->input('pet_id');
            $images = array_values(array_filter($request->input('images', [])));

            // 获取宠物信息并校验权限
            if (!Pet::hasEditPermission($petId, $userId)) {
                return ResponseHelper::error('沒有查看權限');
            }

            // 调用实验室报告分析服务
            /** @var \App\Services\PetAnalysis\LabReportAnalysisService $service */
            $service = app(\App\Services\PetAnalysis\LabReportAnalysisService::class);
            $result = $service->analyze('', $images, $petId);

            if (($result['status'] ?? 'error') !== 'success') {
                \Illuminate\Support\Facades\Log::warning('Lab report analyze failed', ['pet_id' => $petId, 'msg' => $result['message'] ?? 'unknown']);
                return ResponseHelper::error($result['message'] ?? '分析失敗');
            }

            // 附带基础信息（已由服务层填充）直接返回
            return ResponseHelper::success($result['data'], '分析成功');
        } catch (\App\Exceptions\AIServiceException $e) {
            throw $e; // 交由全局异常处理
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Lab report analyze exception: ' . $e->getMessage(), [
                'pet_id'       => $request->input('pet_id'),
                'images_count' => is_array($request->input('images')) ? count($request->input('images')) : 0,
            ]);
            throw new \App\Exceptions\AIServiceException('分析失败: ' . $e->getMessage(), 'lab_report_analysis_error');
        }
    }


    /**
     * 执行宠物智能分析
     *
     * @param Request     $request
     * @param string|null $type 分析类型 (food, stool, health)
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     */
    public function analyze(Request $request, ?string $type = null)
    {
        $type = $type ?? $request->input('type', 'food');

        // 验证请求
        $validator = Validator::make($request->all(), [
            'message'           => 'nullable|string|max:1000', //不用必穿message 默认内置消息回复
            'pet_ids'           => 'nullable|array', // 多宠物ID数组
            'pet_ids.*'         => 'integer|exists:pets,id', // 验证数组中的每个宠物ID
            'images'            => 'nullable|array',
            'images.*'          => 'nullable|string|url|pic|trust_host', // 图片URL地址
            'session_id'        => 'nullable|string',
            'stream'            => 'nullable|boolean', // 添加流式响应参数
            'body_part'         => 'nullable|string', // 添加部位参数
            'symptoms'          => 'nullable|array', // 添加症状参数
            'symptoms.*'        => 'string', // 症状数组元素验证
            'quick_question_id' => 'nullable|integer|exists:analysis_quick_questions,id' // 快捷提问 ID
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        // 获取请求参数
        $petIds = $request->input('pet_ids', []);
        $sessionId = $request->input('session_id');

        // 获取用户原始消息
        $userMessage = $request->input('message');

        // 为AI分析准备消息，如果用户没有发送文字消息，根据分析类型设置默认问题
        $aiMessage = $userMessage;
        if (empty($aiMessage)) {
            if ($type === 'food') {
                $aiMessage = "这个食物我的宠物能吃吗？";
                PetReminder::markMemoRead($request->user()->id, PetReminder::TYPE_TUTORIAL_PET_FOOD_ANALYSIS);
            } elseif ($type === 'stool') {
                $aiMessage = "这个排泄物健康吗？";
            } else {
                $aiMessage = "请分析我宠物的健康状况";
                PetReminder::markMemoRead($request->user()->id, PetReminder::TYPE_TUTORIAL_PET_HEALTH_ANALYSIS);
            }
        }

        // 处理快捷提问
        if ($request->has('quick_question_id')) {
            $quickQuestionId = $request->input('quick_question_id');
            $quickQuestion = AnalysisQuickQuestion::find($quickQuestionId);

            if ($quickQuestion && $quickQuestion->analysis_type === $type) {
                // 将快捷提问添加到AI消息中
                if (!empty($aiMessage)) {
                    $aiMessage .= "\n\n";
                }
                $aiMessage .= $quickQuestion->question;

                // 如果用户没有发送文字消息，将快捷提问也保存为用户消息
                if (empty($userMessage)) {
                    $userMessage = $quickQuestion->question;
                }
            }
        }

        // 检查是否请求流式响应
        $isStream = (bool)$request->input('stream', false);

        // 如果是健康分析且有部位参数，验证部位和症状
        $bodyPart = null;
        if ($type === 'health' && $request->has('body_part')) {
            $petType = 'general';

            // 如果有宠物ID，使用第一个宠物来确定宠物类型
            if (!empty($petIds)) {
                $pet = Pet::with(['petType'])->find($petIds[0]);
                if ($pet && $pet->petType) {
                    if (stripos($pet->petType->name_en, 'dog') !== false) {
                        $petType = 'dog';
                    } elseif (stripos($pet->petType->name_en, 'cat') !== false) {
                        $petType = 'cat';
                    }
                }
            }

            $bodyPart = $request->input('body_part');

            // 先尝试从数据库获取部位
            $bodyPartModel = AnalysisBodyPart::where('code', $bodyPart)
                ->where(function ($query) use ($petType) {
                    $query->where('pet_type', $petType)
                        ->orWhere('pet_type', 'general');
                })
                ->first();

            // 如果数据库中没有找到，验证静态部位列表
            if (!$bodyPartModel) {
                $validBodyParts = array_keys($this->getBodyPartsByPetType($petType));
                if (!in_array($bodyPart, $validBodyParts)) {
                    return ResponseHelper::error('無效的部位選擇');
                }
            }

            // 如果提供了症状，验证症状是否有效
            if ($request->has('symptoms') && is_array($request->input('symptoms'))) {
                $symptoms = $request->input('symptoms');

                // 如果有数据库部位记录，验证症状
                if ($bodyPartModel) {
                    $validSymptoms = BodyPartSymptom::where('body_part_id', $bodyPartModel->id)
                        ->where('status', 1)
                        ->where(function ($query) use ($petType) {
                            $query->where('pet_type', $petType)
                                ->orWhere('pet_type', 'general');
                        })
                        ->whereIn('code', $symptoms)
                        ->pluck('code')
                        ->toArray();

                    // 检查是否所有提供的症状都是有效的
                    $invalidSymptoms = array_diff($symptoms, $validSymptoms);
                    if (!empty($invalidSymptoms)) {
                        return ResponseHelper::error('包含無效的症狀: ' . implode(', ', $invalidSymptoms));
                    }

                    // 将症状信息添加到AI消息中
                    $symptomNames = BodyPartSymptom::whereIn('code', $validSymptoms)
                        ->pluck('name')
                        ->toArray();

                    $symptomText = implode('、', $symptomNames);
                    $aiMessage .= "\n\n症状: " . $symptomText;
                }
            }
        }
        try {
            $userId = $request->user()->id ?: 0; // 如果未登录，使用默认用户ID
            $analysisTypeId = $this->getAnalysisTypeId($type);

            // 获取图片URL数组
            $images = [];

            // 记录原始请求数据
            Log::info('Original request data:', [
                'all'        => $request->all(),
                'images'     => $request->input('images'),
                'has_images' => $request->has('images'),
                'files'      => $request->allFiles()
            ]);

            // 处理图片URL - 特别处理multipart/form-data格式的数组参数
            // 尝试直接获取images[]参数
            $imagesArray = $request->input('images');
            Log::info('Images[] parameter:', ['value' => $imagesArray]);

            if (!empty($imagesArray)) {
                if (is_array($imagesArray)) {
                    $images = array_filter($imagesArray);
                } else {
                    $images[] = $imagesArray;
                }
            }

            // 验证图片URL格式
            foreach ($images as $key => $image) {
                if (!filter_var($image, FILTER_VALIDATE_URL) || !Str::startsWith($image, ['http://', 'https://'])) {
                    // 如果不是有效的URL，删除该图片
                    unset($images[$key]);
                    Log::warning('Invalid image URL removed: ' . $image);
                }
            }

            // 重新索引数组
            $images = array_values($images);

            Log::info('Final images array:', $images);

            // 处理会话管理
            $session = null;

            // 如果提供了会话ID，尝试获取现有会话
            if (!empty($sessionId)) {
                $session = ChatSession::where('session_id', $sessionId)->first();
                // 如果会话存在，获取历史消息作为上下文
                if ($session) {
                    // 如果本次调用的宠物列表与会话中的不同，更新会话的宠物列表
                    if (!empty($petIds) && $session->pet_ids !== $petIds) {
                        $oldPetIds = $session->pet_ids;
                        $session->pet_ids = $petIds;
                        $session->save();

                        // 记录宠物列表变更到数据表
                        ChatSessionPetHistory::recordChange(
                            $sessionId,
                            $userId,
                            $oldPetIds,
                            $petIds,
                            'API调用更新宠物列表'
                        );

                        Log::info('Updated session pet_ids', [
                            'session_id'  => $sessionId,
                            'old_pet_ids' => $oldPetIds,
                            'new_pet_ids' => $petIds
                        ]);
                    }
                }
            }

            // 如果会话不存在，创建新会话
            if (!$session) {
                // 根据宠物ID数组创建会话
                if (!empty($petIds)) {
                    $session = ChatSession::createSession($userId, $petIds, $analysisTypeId);
                } else {
                    // 无宠物情况
                    $session = ChatSession::createSession($userId, null, $analysisTypeId);
                }
                $sessionId = $session->session_id;

                // 记录新会话的初始宠物列表
                ChatSessionPetHistory::recordChange(
                    $sessionId,
                    $userId,
                    null, // 新会话没有旧的宠物列表
                    $petIds,
                    '创建新会话'
                );
            }

            // 保存用户消息（只保存用户实际发送的内容，不包含默认问题）
            ChatMessage::addUserMessage($sessionId, $userId, $userMessage, $images, $session->pet_ids);

            // 通过工厂创建分析服务
            $analysisService = AnalysisServiceFactory::create($type, $this->aiService);

            // 获取宠物信息
            $petsInfo = [];
            if (!empty($petIds)) {
                $pets = Pet::with(['petType', 'petBreed'])->whereIn('id', $petIds)->get();
                foreach ($pets as $pet) {
                    $petsInfo[] = [
                        'id'      => $pet->id,
                        'name'    => $pet->name,
                        'type'    => $pet->petType ? $pet->petType->name : null,
                        'type_en' => $pet->petType ? $pet->petType->name_en : null,
                        'breed'   => $pet->petBreed ? $pet->petBreed->name : null,
                        'sex'     => $pet->sex == 1 ? '公' : '母',
                        'age'     => $pet->birthday ? now()->diffInYears($pet->birthday) : null,
                        'weight'  => $pet->weight
                    ];
                }
            }
            // 获取症状参数（如果有）
            $symptoms = $request->input('symptoms');

            // 记录分析开始时间
            $analysisStartTime = microtime(true);
            $result = $analysisService->analyze(
                $aiMessage,
                $images,
                !empty($petIds) ? $petIds[0] : null, // 使用第一个宠物ID作为主要宠物ID
                $sessionId,
                $isStream, // 启用流式响应
                $bodyPart, // 传递部位参数
                $symptoms, // 传递症状参数
                $petsInfo // 传递多宠物信息
            );

            // 如果是流式响应，直接返回流式响应
            if ($isStream) {
                // 注意：在流式响应中，我们无法在返回响应之前保存
                // 执行分析，传入上下文和流式参数
                // 将会话 ID 传递给分析服务，以便在流式响应中返回
                return $result;
            }

            // 非流式响应处理
            // 记录分析结束时间和耗时
            $analysisEndTime = microtime(true);
            $analysisDuration = round($analysisEndTime - $analysisStartTime, 2);
            \Illuminate\Support\Facades\Log::info("分析服务总耗时: {$analysisDuration} 秒", [
                'analysis_type' => $analysisTypeId,
                'has_images'    => !empty($images),
                'pet_count'     => count($petsInfo)
            ]);

            // 保存AI回复（使用会话的宠物ID列表）
            $aiContent = $result['data']['analysis'] ?? $result['raw_content'] ?? '抱歉，我无法理解您的问题。';
            ChatMessage::addAIMessage($sessionId, $userId, $aiContent, $result, $session->pet_ids);

            // 将会话信息和宠物信息添加到响应中
            $result['session'] = [
                'session_id'     => $sessionId,
                'is_new_session' => !$session->wasRecentlyCreated,
                'pet_ids'        => $session->pet_ids,
                'analysis_type'  => $analysisTypeId
            ];

            // 添加所有宠物信息
            if (!empty($petsInfo)) {
                $result['pets_info'] = $petsInfo;
            }

            return ResponseHelper::success($result);
        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常，让全局异常处理器处理
            throw $e;
        } catch (\Exception $e) {
            // 记录错误日志
            \Illuminate\Support\Facades\Log::error('Pet analysis failed: ' . $e->getMessage());

            // 检查是否是超时错误
            if (str_contains($e->getMessage(), 'cURL error 28') || str_contains($e->getMessage(), 'timed out')) {

                // 如果已经创建了会话和用户消息，添加一个错误消息
                if (isset($sessionId) && isset($userId)) {
                    ChatMessage::addAIMessage(
                        $sessionId,
                        $userId,
                        '抱歉，服务器正忙，请稍后再试。',
                        ['error' => 'timeout']
                    );

                    // 抛出AI服务异常
                    throw new \App\Exceptions\AIServiceException(
                        '服务器响应超时，请稍后再试。',
                        'timeout',
                        $sessionId,
                        ['session_id' => $sessionId]
                    );
                }
            }

            // 将其他异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '分析失败: ' . $e->getMessage(),
                'general',
                $sessionId ?? null,
                ['original_error' => $e->getMessage()]
            );
        }
    }

    /**
     * 获取所有支持的分析类型及其参数
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAnalysisTypes(Request $request)
    {
        $petIds = $request->input('pet_ids', []);
        $petType = 'general'; // 默认通用

        // 如果提供了宠物ID数组，使用第一个宠物来确定宠物类型
        if (!empty($petIds)) {
            $pet = Pet::with(['petType'])->find($petIds[0]);
            if ($pet && $pet->petType) {
                if (stripos($pet->petType->name_en, 'dog') !== false) {
                    $petType = 'dog';
                } elseif (stripos($pet->petType->name_en, 'cat') !== false) {
                    $petType = 'cat';
                }
            }
        }

        // 获取基本分析类型
        $types = AnalysisServiceFactory::getAvailableTypes();

        // 为每种分析类型添加可选参数
        $typesWithParams = [];
        foreach ($types as $key => $name) {
            $params = [];

            // 为各类分析添加参数
            if ($key === 'health') {
                // 从数据库获取部位信息
                $bodyParts = $this->getBodyPartsFromDatabase($petType);
                if (empty($bodyParts)) {
                    // 如果数据库中没有数据，返回空数组
                    Log::warning('数据库中没有找到宠物部位数据，宠物类型: ' . $petType);
                }
                $params['body_parts'] = $bodyParts;
            }

            // 获取快捷问题
            $quickQuestions = AnalysisQuickQuestion::where('analysis_type', $key)
                ->where('status', 1)
                ->where(function ($query) use ($petType) {
                    $query->where('pet_type', $petType)
                        ->orWhere('pet_type', 'general');
                })
                ->orderBy('sort_order')
                ->get()
                ->map(function ($item) {
                    return [
                        'id'       => $item->id,
                        'question' => $item->question,
                        'icon'     => $item->icon
                    ];
                })
                ->toArray();

            if (!empty($quickQuestions)) {
                $params['quick_questions'] = $quickQuestions;
            }

            // 例如，食物分析可能有食物类别参数等

            $typesWithParams[$key] = [
                'name'   => $name,
                'params' => $params
            ];
        }

        return ResponseHelper::success([
            'types'    => $typesWithParams,
            'pet_type' => $petType
        ]);
    }

    /**
     * 从数据库获取部位信息
     *
     * @param string $petType 宠物类型
     * @return array
     */
    protected function getBodyPartsFromDatabase(string $petType): array
    {
        try {
            // 获取适用于该宠物类型的部位或通用部位
            $bodyParts = AnalysisBodyPart::where('status', 1)
                ->where(function ($query) use ($petType) {
                    $query->where('pet_type', $petType)
                        ->orWhere('pet_type', 'general');
                })
                ->orderBy('sort_order')
                ->with([
                    'symptoms' => function ($query) use ($petType) {
                        $query->where('status', 1)
                            ->where(function ($q) use ($petType) {
                                $q->where('pet_type', $petType)
                                    ->orWhere('pet_type', 'general');
                            })
                            ->orderBy('sort_order');
                    }
                ])
                ->get();

            // 转换为前端需要的格式
            $bodyPartsArray = [];
            foreach ($bodyParts as $part) {
                $symptoms = [];
                foreach ($part->symptoms as $symptom) {
                    $symptoms[$symptom->code] = [
                        'name'        => $symptom->name,
                        'icon'        => $symptom->icon,
                        'description' => $symptom->description
                    ];
                }

                $bodyPartsArray[$part->code] = [
                    'name'        => $part->name,
                    'icon'        => $part->icon,
                    'description' => $part->description,
                    'symptoms'    => $symptoms
                ];
            }

            return $bodyPartsArray;
        } catch (\Exception $e) {
            Log::error('Failed to get body parts from database: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取分析类型的ID
     *
     * @param string $type 分析类型名称
     * @return int 分析类型ID
     */
    protected function getAnalysisTypeId(string $type): int
    {
        switch ($type) {
            case 'food':
                return 1;
            case 'stool':
                return 2;
            case 'health':
                return 3;
            case 'ingredient':
                return 4;
            default:
                return 0; // 普通聊天
        }
    }

    /**
     * 根据宠物类型获取可选部位
     *
     * @param string $petType
     * @return array
     */
    protected function getBodyPartsByPetType(string $petType): array
    {
        switch ($petType) {
            case 'dog':
                return [
                    'head'         => '头部',
                    'eyes'         => '眼睛',
                    'ears'         => '耳朵',
                    'mouth'        => '口腔/牙齿',
                    'nose'         => '鼻子',
                    'skin'         => '皮肤/被毛',
                    'paws'         => '爪子/足垫',
                    'limbs'        => '四肢/关节',
                    'back'         => '背部/脊椎',
                    'tail'         => '尾巴',
                    'digestive'    => '消化系统',
                    'respiratory'  => '呼吸系统',
                    'urinary'      => '泌尿系统',
                    'reproductive' => '生殖系统',
                    'anal'         => '肛门/肛门腺', // 狗特有
                    'behavior'     => '行为/精神状态',
                    'general'      => '全身/综合'
                ];

            case 'cat':
                return [
                    'head'         => '头部',
                    'eyes'         => '眼睛',
                    'ears'         => '耳朵',
                    'mouth'        => '口腔/牙齿',
                    'nose'         => '鼻子',
                    'skin'         => '皮肤/被毛',
                    'paws'         => '爪子/足垫',
                    'limbs'        => '四肢/关节',
                    'back'         => '背部/脊椎',
                    'tail'         => '尾巴',
                    'digestive'    => '消化系统',
                    'respiratory'  => '呼吸系统',
                    'urinary'      => '泌尿系统',
                    'reproductive' => '生殖系统',
                    'grooming'     => '清洁行为', // 猫特有
                    'whiskers'     => '胡须/触须', // 猫特有
                    'behavior'     => '行为/精神状态',
                    'general'      => '全身/综合'
                ];

            default:
                // 通用部位列表
                return [
                    'head'         => '头部',
                    'eyes'         => '眼睛',
                    'ears'         => '耳朵',
                    'mouth'        => '口腔/牙齿',
                    'nose'         => '鼻子',
                    'skin'         => '皮肤/被毛',
                    'limbs'        => '四肢/关节',
                    'digestive'    => '消化系统',
                    'respiratory'  => '呼吸系统',
                    'urinary'      => '泌尿系统',
                    'reproductive' => '生殖系统',
                    'behavior'     => '行为/精神状态',
                    'general'      => '全身/综合'
                ];
        }
    }

    /**
     * 保存AI回复消息（用于流式响应后由前端调用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveAIMessage(Request $request)
    {
        // 验证请求
        $validator = Validator::make($request->all(), [
            'session_id'    => 'required|string',
            'content'       => 'nullable|string', // 从required改为nullable
            'analysis_type' => 'nullable|string',
            'result'        => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            // 获取参数
            $sessionId = $request->input('session_id');
            $content = $request->input('content'); // 可以为空
            $result = $request->input('result', []);

            // 获取当前用户ID
            $userId = Auth::id();
            if (!$userId) {
                return ResponseHelper::error('用戶未登錄');
            }

            // 获取会话信息以获取宠物ID列表
            $session = ChatSession::where('session_id', $sessionId)->first();
            $sessionPetIds = $session ? $session->pet_ids : null;

            // 保存AI回复消息，内容可以为空
            ChatMessage::addAIMessage($sessionId, $userId, $content, $result, $sessionPetIds);

            return ResponseHelper::success(['message' => 'AI回覆消息保存成功']);

        } catch (\Exception $e) {
            // 记录错误日志
            \Illuminate\Support\Facades\Log::error('Save AI message failed: ' . $e->getMessage());
            return ResponseHelper::error('保存AI回覆消息失敗: ' . $e->getMessage());
        }
    }

    /**
     * 宠物证件上传识别接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadCertificate(Request $request)
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'file'           => 'required|file|mimes:jpg,jpeg,png,webp|max:15360', // 15MB限制
            'type'           => 'required|integer|in:1,2,3,4', // 证件类型：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案
            'ai_recognition' => 'nullable|boolean', // 是否启用AI识别，默认true
        ], [
            'file.required'          => '请选择要上传的证件图片',
            'file.file'              => '上传的文件格式不正确',
            'file.mimes'             => '只支持jpg、jpeg、png、webp格式的图片',
            'file.max'               => '图片大小不能超过15MB',
            'type.required'          => '请选择证件类型',
            'type.integer'           => '证件类型必须是数字',
            'type.in'                => '证件类型无效，支持的类型：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案',
            'ai_recognition.boolean' => 'AI识别参数必须是布尔值',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            // 获取验证后的参数
            $file = $request->file('file');
            $certificateType = $request->input('type');
            $aiRecognition = $request->input('ai_recognition', true);

            // 记录上传开始时间
            $uploadStartTime = microtime(true);

            // 调用UploadOSSController的upload方法
            $uploadRequest = new Request();
            $uploadRequest->files->set('file', $file);
            $uploadRequest->merge(['type' => 'certificate']);

            $uploadResponse = $this->uploadService->upload($uploadRequest);
            $uploadData = $uploadResponse->getData(true);

            if ($uploadData['status'] !== 200) {
                return ResponseHelper::error('文件上傳失敗：' . $uploadData['message']);
            }

            $imageUrl = $uploadData['data']['url'];

            // 记录上传耗时
            $uploadDuration = round(microtime(true) - $uploadStartTime, 2);
            Log::info("证件文件上传耗时: {$uploadDuration} 秒", [
                'certificate_type' => $certificateType,
                'file_size'        => $file->getSize()
            ]);

            // 准备响应数据
            $responseData = [
                'image_url'             => $imageUrl,
                'certificate_type'      => $certificateType,
                'certificate_type_name' => $this->getCertificateTypeName($certificateType),
                'ai_recognition'        => $aiRecognition,
                'upload_time'           => now()->format('Y-m-d H:i:s')
            ];

            // 如果启用AI识别且支持该证件类型
            if ($aiRecognition && $this->certificateService->isSupportedForRecognition($certificateType)) {
                // 执行AI识别
                $recognitionResult = $this->certificateService->recognizeCertificate($imageUrl, $certificateType);
                $responseData['recognition_result'] = $recognitionResult;

                Log::info('证件AI识别完成', [
                    'certificate_type'   => $certificateType,
                    'recognition_result' => $recognitionResult
                ]);
            } else {
                // 不支持识别的类型
                if ($aiRecognition) {
                    Log::info('证件类型暂不支持AI识别', [
                        'certificate_type' => $certificateType
                    ]);
                }
                $responseData['recognition_result'] = null;
            }

            return ResponseHelper::success($responseData, '證件上傳成功');

        } catch (\Exception $e) {
            Log::error('证件上传失败: ' . $e->getMessage(), [
                'certificate_type' => $request->input('type'),
                'error'            => $e->getMessage(),
                'trace'            => $e->getTraceAsString()
            ]);

            return ResponseHelper::error('證件上傳失敗: ' . $e->getMessage());
        }
    }

    /**
     * 获取证件类型名称
     *
     * @param int $type
     * @return string
     */
    protected function getCertificateTypeName(int $type): string
    {
        $types = [
            1 => '出生证明',
            2 => '疫苗针卡',
            3 => '晶片编码',
            4 => '医疗档案'
        ];

        return $types[$type] ?? '未知类型';
    }

    /**
     * 绑定宠物证件接口
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bindCertificate(Request $request)
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'pet_id'                => 'required|integer|exists:pets,id',
            'certificate_type'      => 'required|integer|in:1,2,3,4',
            'image_url'             => 'required|array|min:1|max:10',
            'image_url.*'           => 'required|string|max:500|url|pic|trust_host',
            'ai_recognition_result' => 'nullable|array',
        ], [
            'pet_id.required'           => '请选择要绑定的宠物',
            'pet_id.exists'             => '宠物不存在',
            'certificate_type.required' => '请选择证件类型',
            'certificate_type.in'       => '证件类型无效',
            'image_url.required'        => '请提供证件图片URL',
            'image_url.array'           => '图片URL必须是数组格式',
            'image_url.min'             => '至少需要上传1张图片',
            'image_url.max'             => '最多只能上传10张图片',
            'image_url.*.required'      => '图片URL不能为空',
            'image_url.*.string'        => '图片URL必须是字符串',
            'image_url.*.max'           => '图片URL长度不能超过500字符',
            'image_url.*.url'           => '请提供有效的图片URL',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            DB::beginTransaction();

            $petId = $request->input('pet_id');
            $certificateType = $request->input('certificate_type');
            $imageUrls = $request->input('image_url');
            $aiRecognitionResult = $request->input('ai_recognition_result', []);
            $userId = $request->user()->id;

            // 检查宠物是否存在
            $pet = Pet::find($petId);
            if (!$pet) {
                DB::rollBack();
                return ResponseHelper::error('寵物不存在');
            }

            // 检查权限 - 宠物主人或家人可以绑定证件
            if (!Pet::hasEditPermission($petId, $userId)) {
                DB::rollBack();
                return ResponseHelper::error('沒有操作權限');
            }

            // 处理不同类型的证件
            if ($certificateType == PetCertificate::TYPE_MEDICAL_RECORD) {
                // 医疗档案特殊处理
                $result = $this->handleMedicalRecord($pet, $imageUrls, $aiRecognitionResult);
            } else {
                // 其他证件类型
                $result = $this->handleCertificate($pet, $certificateType, $imageUrls, $aiRecognitionResult);
            }

            //添加提醒
            $familyUserIds = PetMaster::where('pet_id', $petId)->where('user_id', '!=', $userId)->pluck('user_id')->toArray();
            $visitedUserIds = AppUserLog::getTodayVisitedUsers($familyUserIds);
            foreach ($visitedUserIds as $familyUserId) {
                PetReminder::create([
                    'user_id'      => $familyUserId,
                    'pet_id'       => $petId,
                    'type'         => PetReminder::TYPE_FAMILY_UPLOAD_FILE_REMINDER,
                    'title'        => PetReminder::MAPPING[PetReminder::TYPE_FAMILY_UPLOAD_FILE_REMINDER]['label'],
                    'content'      => "您的家人上傳了" . ($certificateType == PetCertificate::TYPE_MEDICAL_RECORD ? "醫療檔案" : "證件") . "，请查看",
                    'trigger_date' => Carbon::today()->format('Y-m-d'),
                    'extra'        => json_encode($result),
                ]);
            }

            DB::commit();

            return ResponseHelper::success($result, '證件綁定成功');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('证件绑定失败: ' . $e->getMessage(), [
                'pet_id'           => $request->input('pet_id'),
                'certificate_type' => $request->input('certificate_type'),
                'image_count'      => count($request->input('image_url', [])),
                'error'            => $e->getMessage(),
                'trace'            => $e->getTraceAsString()
            ]);

            return ResponseHelper::error('證件綁定失敗: ' . $e->getMessage());
        }
    }

    /**
     * 处理医疗档案
     *
     * @param Pet   $pet
     * @param array $imageUrls
     * @return array
     */
    protected function handleMedicalRecord(Pet $pet, array $imageUrls): array
    {
        // 创建医疗档案记录，直接设置为通过状态
        $medicalRecord = PetMedicalRecord::create([
            'pet_id'       => $pet->id,
            'record_title' => '医疗档案',
            'record_date'  => now()->format('Y-m-d'),
            'image_url'    => $imageUrls,
            'description'  => '用户上传的医疗档案',
            'status'       => PetMedicalRecord::STATUS_APPROVED, // 自动审核通过
            'admin_remark' => '系统自动审核通过',
            'audited_at'   => now(),
        ]);

        // 更新宠物医疗档案状态
        $this->updatePetMedicalRecordStatus($pet);

        Log::info('医疗档案创建并自动审核通过', [
            'pet_id'            => $pet->id,
            'medical_record_id' => $medicalRecord->id,
            'record_title'      => $medicalRecord->record_title,
            'auto_audit'        => true
        ]);

        return [
            'medical_record_id' => $medicalRecord->id,
            'record_title'      => $medicalRecord->record_title,
            'record_date'       => $medicalRecord->record_date->format('Y-m-d'),
            'status'            => $medicalRecord->status,
            'status_name'       => $medicalRecord->status_name,
            'auto_audit_result' => [
                'auto_audit'   => true,
                'audit_result' => 'approved',
                'audit_reason' => '医疗档案自动审核通过'
            ]
        ];
    }

    /**
     * 处理其他类型证件
     *
     * @param Pet   $pet
     * @param int   $certificateType
     * @param array $imageUrls
     * @param array $aiRecognitionResult
     * @return array
     */
    protected function handleCertificate(Pet $pet, int $certificateType, array $imageUrls, array $aiRecognitionResult): array
    {
        // 检查是否已存在该类型证件，如果存在则更新状态为待审核
        $existingCertificate = PetCertificate::where('pet_id', $pet->id)
            ->where('certificate_type', $certificateType)
            ->first();

        if ($existingCertificate) {
            // 重新上传，更新证件信息
            $existingCertificate->update([
                'image_url'             => $imageUrls,
                'ai_recognition_result' => $aiRecognitionResult,
                'status'                => PetCertificate::STATUS_PENDING,
                'admin_remark'          => null,
                'admin_id'              => null,
                'audited_at'            => null,
            ]);
            $certificate = $existingCertificate;
        } else {
            // 首次上传，创建新记录
            $certificate = PetCertificate::create([
                'pet_id'                => $pet->id,
                'certificate_type'      => $certificateType,
                'image_url'             => $imageUrls,
                'ai_recognition_result' => $aiRecognitionResult,
                'status'                => PetCertificate::STATUS_PENDING,
            ]);
        }

        // 执行自动审核
        $autoAuditResult = $this->performAutoAudit($pet, $certificate);

        // 更新宠物对应的证件状态
        $this->updatePetCertificateStatus($pet, $certificateType, $certificate->status);

        Log::info('证件处理完成', [
            'pet_id'            => $pet->id,
            'certificate_type'  => $certificateType,
            'certificate_id'    => $certificate->id,
            'auto_audit_result' => $autoAuditResult,
            'final_status'      => $certificate->status
        ]);

        return [
            'certificate_id'        => $certificate->id,
            'certificate_type'      => $certificateType,
            'certificate_type_name' => $certificate->certificate_type_name,
            'status'                => $certificate->status,
            'status_name'           => $certificate->status_name,
            'auto_audit_result'     => $autoAuditResult,
            'ai_recognition_result' => $certificate->ai_recognition_result,
        ];
    }

    /**
     * 执行自动审核
     *
     * @param Pet            $pet
     * @param PetCertificate $certificate
     * @return array
     */
    protected function performAutoAudit(Pet $pet, PetCertificate $certificate): array
    {
        $result = [
            'auto_audit'   => false,
            'audit_result' => 'pending',
            'audit_reason' => '',
        ];

        // 只有出生证明和晶片编码支持自动审核
        if ($certificate->certificate_type == PetCertificate::TYPE_BIRTH_CERTIFICATE) {
            $result = $this->auditBirthCertificate($pet, $certificate);
        } elseif ($certificate->certificate_type == PetCertificate::TYPE_CHIP_CODE) {
            $result = $this->auditChipCode($pet, $certificate);
        }

        return $result;
    }

    /**
     * 审核出生证明
     *
     * @param Pet            $pet
     * @param PetCertificate $certificate
     * @return array
     */
    protected function auditBirthCertificate(Pet $pet, PetCertificate $certificate): array
    {
        $aiResult = $certificate->ai_recognition_result;

        if (empty($aiResult)) {
            return [
                'auto_audit'   => false,
                'audit_result' => 'pending',
                'audit_reason' => 'AI识别结果为空，需要人工审核',
            ];
        }

        $matchResults = [];
        $allMatched = true;

        // 检查性别匹配
        if (isset($aiResult['gender']) && !empty($aiResult['gender'])) {
            $genderMatch = $this->compareGender($pet->sex, $aiResult['gender']);
            $matchResults['gender'] = $genderMatch;
            if (!$genderMatch['matched']) {
                $allMatched = false;
            }
        } else {
            $allMatched = false;
            $matchResults['gender'] = ['matched' => false, 'reason' => 'AI未识别出性别'];
        }

        // 检查出生日期匹配
        if (isset($aiResult['birth_date']) && !empty($aiResult['birth_date'])) {
            $birthdayMatch = $this->compareBirthday($pet->birthday, $aiResult['birth_date']);
            $matchResults['birthday'] = $birthdayMatch;
            if (!$birthdayMatch['matched']) {
                $allMatched = false;
            }
        } else {
            $allMatched = false;
            $matchResults['birthday'] = ['matched' => false, 'reason' => 'AI未识别出出生日期'];
        }

        // 检查品种匹配
        if (isset($aiResult['breed']) && !empty($aiResult['breed'])) {
            $breedMatch = $this->compareBreed($pet, $aiResult['breed']);
            $matchResults['breed'] = $breedMatch;
            if (!$breedMatch['matched']) {
                $allMatched = false;
            }
        } else {
            $allMatched = false;
            $matchResults['breed'] = ['matched' => false, 'reason' => 'AI未识别出品种'];
        }

        if ($allMatched) {
            // 自动通过
            $certificate->update([
                'status'       => PetCertificate::STATUS_APPROVED,
                'admin_remark' => '系统自动审核通过：性别、出生日期、品种信息匹配',
                'audited_at'   => now(),
            ]);

            return [
                'auto_audit'    => true,
                'audit_result'  => 'approved',
                'audit_reason'  => '所有信息匹配，自动审核通过',
                'match_details' => $matchResults,
            ];
        } else {
            return [
                'auto_audit'    => false,
                'audit_result'  => 'pending',
                'audit_reason'  => '信息不匹配，需要人工审核',
                'match_details' => $matchResults,
            ];
        }
    }

    /**
     * 审核晶片编码
     *
     * @param Pet            $pet
     * @param PetCertificate $certificate
     * @return array
     */
    protected function auditChipCode(Pet $pet, PetCertificate $certificate): array
    {
        // 从AI识别结果中获取晶片编码
        $aiResult = $certificate->ai_recognition_result ?? [];
        $chipCode = $aiResult['chip_code'] ?? null;

        if ($chipCode) {
            // 检查晶片编码是否已被其他宠物使用
            $existingPet = Pet::where('chip_code', $chipCode)
                ->where('id', '!=', $pet->id)
                ->first();

            if ($existingPet) {
                $auditResult = 'pending';
                $auditReason = '晶片编码已被其他宠物使用，需要人工审核';
            } else {
                // 晶片编码验证通过，更新宠物信息
                $pet->update([
                    'chip_code'         => $chipCode,
                    'chip_implant_date' => $aiResult['implant_date'] ?? now()->format('Y-m-d'),
                    'chip_code_status'  => PetCertificate::STATUS_APPROVED,
                ]);

                $auditResult = 'approved';
                $auditReason = "晶片编码验证通过：{$chipCode}";
            }
        } else {
            // 没有AI识别到晶片编码，设为待审核状态
            $auditResult = 'pending';
            $auditReason = '未识别到晶片编码，需要人工审核';
        }

        // 更新证件状态
        $status = $auditResult === 'approved' ? PetCertificate::STATUS_APPROVED : PetCertificate::STATUS_PENDING;
        $certificate->update([
            'status'       => $status,
            'admin_remark' => "系统自动审核：{$auditReason}",
            'audited_at'   => $auditResult === 'approved' ? now() : null,
        ]);

        return [
            'auto_audit'   => true,
            'audit_result' => $auditResult,
            'audit_reason' => $auditReason,
            'chip_code'    => $chipCode,
        ];
    }

    /**
     * 比较性别
     *
     * @param string $petGender
     * @param string $aiGender
     * @return array
     */
    protected function compareGender(string $petGender, string $aiGender): array
    {
        $petGender = trim($petGender);
        $aiGender = trim($aiGender);

        $matched = ($petGender === $aiGender);

        return [
            'matched'   => $matched,
            'pet_value' => $petGender,
            'ai_value'  => $aiGender,
            'reason'    => $matched ? '性别匹配' : "性别不匹配：宠物({$petGender}) vs AI识别({$aiGender})",
        ];
    }

    /**
     * 比较出生日期
     *
     * @param string $petBirthday
     * @param string $aiBirthday
     * @return array
     */
    protected function compareBirthday(string $petBirthday, string $aiBirthday): array
    {
        $petDate = date('Y-m-d', strtotime($petBirthday));
        $aiDate = date('Y-m-d', strtotime($aiBirthday));

        $matched = ($petDate === $aiDate);

        return [
            'matched'   => $matched,
            'pet_value' => $petDate,
            'ai_value'  => $aiDate,
            'reason'    => $matched ? '出生日期匹配' : "出生日期不匹配：宠物({$petDate}) vs AI识别({$aiDate})",
        ];
    }

    /**
     * 比较品种
     *
     * @param Pet    $pet
     * @param string $aiBreed
     * @return array
     */
    protected function compareBreed(Pet $pet, string $aiBreed): array
    {
        $petBreed = $pet->breed;

        if (!$petBreed) {
            return [
                'matched'   => false,
                'pet_value' => null,
                'ai_value'  => $aiBreed,
                'reason'    => '宠物品种信息缺失',
            ];
        }

        $matched = $petBreed->matchesBreedName($aiBreed);

        return [
            'matched'   => $matched,
            'pet_value' => $petBreed->name,
            'ai_value'  => $aiBreed,
            'reason'    => $matched ? '品种匹配' : "品种不匹配：宠物({$petBreed->name}) vs AI识别({$aiBreed})",
        ];
    }

    /**
     * 更新宠物证件状态
     *
     * @param Pet $pet
     * @param int $certificateType
     * @param int $status
     */
    protected function updatePetCertificateStatus(Pet $pet, int $certificateType, int $status): void
    {
        $statusField = match ($certificateType) {
            PetCertificate::TYPE_BIRTH_CERTIFICATE => 'birth_certificate_status',
            PetCertificate::TYPE_VACCINE_CARD => 'vaccine_card_status',
            PetCertificate::TYPE_CHIP_CODE => 'chip_code_status',
            PetCertificate::TYPE_MEDICAL_RECORD => 'medical_record_status',
            default => null,
        };

        if ($statusField) {
            $pet->update([$statusField => $status]);
        }
    }

    /**
     * 更新宠物医疗档案状态
     *
     * @param Pet $pet
     */
    protected function updatePetMedicalRecordStatus(Pet $pet): void
    {
        // 统计医疗档案状态
        $statusCounts = PetMedicalRecord::where('pet_id', $pet->id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $totalCount = array_sum($statusCounts);

        if ($totalCount == 0) {
            $petStatus = 0; // 未上传
        } elseif (isset($statusCounts[PetMedicalRecord::STATUS_PENDING]) && $statusCounts[PetMedicalRecord::STATUS_PENDING] > 0) {
            $petStatus = 1; // 有待审核
        } elseif (isset($statusCounts[PetMedicalRecord::STATUS_REJECTED]) && $statusCounts[PetMedicalRecord::STATUS_REJECTED] > 0) {
            $petStatus = 3; // 有被拒绝
        } else {
            $petStatus = 2; // 全部通过
        }

        $pet->update(['medical_record_status' => $petStatus]);
    }


    /**
     * 获取用户上传的证件文件列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserCertificateList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id'           => 'nullable',
            'certificate_type' => 'nullable|integer|in:1,2,3,4',
            'status'           => 'nullable|integer|in:1,2,3',
            'page'             => 'nullable|integer|min:1',
            'per_page'         => 'nullable|integer|min:1|max:50',
        ], [
            'certificate_type.in' => '证件类型无效：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案',
            'status.in'           => '状态无效：1=待审核,2=通过,3=拒绝',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $user = Auth::user();
            $petId = $request->input('pet_id');
            $certificateType = $request->input('certificate_type');
            $status = $request->input('status');
            $page = $request->input('page', 1);
            $perPage = $request->input('per_page', 20);

            // 获取用户的宠物ID列表（包括作为主人和家人的宠物）
            $ownedPetIds = Pet::where('owner_id', $user->id)->pluck('id')->toArray();
            $familyPetIds = PetMaster::where('user_id', $user->id)->pluck('pet_id')->toArray();
            $userPetIds = array_unique(array_merge($ownedPetIds, $familyPetIds));

            if (empty($userPetIds)) {
                // 构建标准分页格式（空数据）
                $emptyPaginationData = [
                    'current_page'   => 1,
                    'data'           => [],
                    'first_page_url' => request()->url() . '?page=1',
                    'from'           => null,
                    'last_page'      => 1,
                    'last_page_url'  => request()->url() . '?page=1',
                    'next_page_url'  => null,
                    'path'           => request()->url(),
                    'per_page'       => $perPage,
                    'prev_page_url'  => null,
                    'to'             => null,
                    'total'          => 0,
                    'statistics'     => [
                        'total_files' => 0,
                        'by_type'     => [
                            '1' => ['type_name' => '出生证明', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                            '2' => ['type_name' => '疫苗针卡', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                            '3' => ['type_name' => '晶片编码', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                            '4' => ['type_name' => '医疗档案', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                        ],
                        'by_status'   => [
                            'pending'  => 0,
                            'approved' => 0,
                            'rejected' => 0,
                        ],
                    ],
                ];
                return ResponseHelper::success($emptyPaginationData, '暫無數據');
            }

            // 如果指定了pet_id，验证是否属于当前用户
            if ($petId && !in_array($petId, $userPetIds)) {
                return ResponseHelper::error('無權限查看該寵物的證件');
            }

            // 构建查询条件
            $queryPetIds = $petId ? [$petId] : $userPetIds;

            // 查询证件记录
            $certificateQuery = PetCertificate::with(['pet'])
                ->whereIn('pet_id', $queryPetIds)
                ->orderBy('created_at', 'desc');

            // 查询医疗档案记录
            $medicalQuery = PetMedicalRecord::with(['pet'])
                ->whereIn('pet_id', $queryPetIds)
                ->orderBy('created_at', 'desc');

            // 应用筛选条件
            if ($status) {
                $certificateQuery->where('status', $status);
                $medicalQuery->where('status', $status);
            }

            if ($certificateType) {
                if ($certificateType == 4) {
                    // 只查询医疗档案
                    $certificates = collect();
                    $medicals = $medicalQuery->get();
                } else {
                    // 查询指定类型的证件
                    $certificateQuery->where('certificate_type', $certificateType);
                    $certificates = $certificateQuery->get();
                    $medicals = collect();
                }
            } else {
                // 查询所有类型
                $certificates = $certificateQuery->get();
                $medicals = $medicalQuery->get();
            }

            // 合并数据并格式化
            $allRecords = $this->formatUserCertificateList($certificates, $medicals);

            // 按创建时间排序
            $allRecords = $allRecords->sortByDesc('created_at');

            // 手动分页
            $total = $allRecords->count();
            $offset = ($page - 1) * $perPage;
            $paginatedRecords = $allRecords->slice($offset, $perPage)->values();

            // 构建标准分页格式
            $paginationData = [
                'current_page'   => $page,
                'data'           => $paginatedRecords,
                'first_page_url' => request()->url() . '?page=1',
                'from'           => $offset + 1,
                'last_page'      => ceil($total / $perPage),
                'last_page_url'  => request()->url() . '?page=' . ceil($total / $perPage),
                'next_page_url'  => $page < ceil($total / $perPage) ? request()->url() . '?page=' . ($page + 1) : null,
                'path'           => request()->url(),
                'per_page'       => $perPage,
                'prev_page_url'  => $page > 1 ? request()->url() . '?page=' . ($page - 1) : null,
                'to'             => min($offset + $perPage, $total),
                'total'          => $total,
                'statistics'     => $this->getUserCertificateStatistics($queryPetIds),
            ];

            return ResponseHelper::success($paginationData, '獲取成功');

        } catch (\Exception $e) {
            Log::error('获取用户证件列表失败: ' . $e->getMessage());
            return ResponseHelper::error('獲取失敗: ' . $e->getMessage());
        }
    }


    /**
     * 获取宠物证件类型概览（按类型分组）
     * 一次性返回该宠物的所有证件类型数据，疫苗针卡包含疫苗记录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPetCertificateOverview(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id' => 'required|integer|exists:pets,id',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $user = Auth::user();
            $petId = (int)$request->input('pet_id');

            // 验证宠物权限
            $pet = Pet::find($petId);
            if (!$pet) {
                return ResponseHelper::error('寵物不存在');
            }

            if (!Pet::hasEditPermission($pet->toArray(), $user->id)) {
                return ResponseHelper::error('沒有查看權限');
            }

            // 获取所有证件类型的数据
            $certificateTypes = PetCertificate::getCertificateTypes();
            $result = [];

            foreach ($certificateTypes as $typeId => $typeName) {
                $typeData = [
                    'type_id'        => $typeId,
                    'type_name'      => $typeName,
                    'has_data'       => false,
                    'records'        => [],
                    'latest_record'  => null,
                    'status_summary' => [
                        'pending'  => 0,
                        'approved' => 0,
                        'rejected' => 0,
                        'total'    => 0
                    ]
                ];

                if ($typeId == PetCertificate::TYPE_MEDICAL_RECORD) {
                    // 医疗档案处理
                    $records = PetMedicalRecord::where('pet_id', $petId)
                        ->orderBy('created_at', 'desc')
                        ->get();

                    if ($records->isNotEmpty()) {
                        $typeData['has_data'] = true;
                        $typeData['records'] = $records->map(function ($record) {
                            return $this->formatMedicalRecord($record);
                        })->toArray();
                        $typeData['latest_record'] = $typeData['records'][0];
                        $typeData['status_summary'] = $this->calculateStatusSummary($records);
                    }
                } elseif ($typeId == PetCertificate::TYPE_VACCINE_CARD) {
                    // 疫苗针卡特殊处理，包含疫苗记录
                    $certificates = PetCertificate::where('pet_id', $petId)
                        ->where('certificate_type', $typeId)
                        ->orderBy('created_at', 'desc')
                        ->get();

                    // 获取疫苗记录
                    $vaccineRecords = \App\Models\PetVaccineRecord::where('pet_id', $petId)
                        ->orderBy('vaccine_date', 'desc')
                        ->get();

                    if ($certificates->isNotEmpty() || $vaccineRecords->isNotEmpty()) {
                        $typeData['has_data'] = true;

                        // 格式化证件记录
                        $formattedCertificates = $certificates->map(function ($cert) {
                            return $this->formatCertificateRecord($cert);
                        })->toArray();

                        // 格式化疫苗记录
                        $formattedVaccineRecords = $vaccineRecords->map(function ($record) {
                            return [
                                'id'                => $record->id,
                                'type'              => 'vaccine_record',
                                'vaccine_name'      => $record->vaccine_name,
                                'vaccine_date'      => $record->vaccine_date,
                                'vaccine_batch'     => $record->vaccine_batch,
                                'hospital_name'     => $record->hospital_name,
                                'doctor_name'       => $record->doctor_name,
                                'next_vaccine_date' => $record->next_vaccine_date,
                                'remark'            => $record->remark,
                                'created_at'        => $record->created_at->format('Y-m-d H:i:s'),
                            ];
                        })->toArray();

                        $typeData['records'] = $formattedCertificates;
                        $typeData['vaccine_records'] = $formattedVaccineRecords;
                        $typeData['latest_record'] = !empty($formattedCertificates) ? $formattedCertificates[0] : null;
                        $typeData['status_summary'] = $this->calculateStatusSummary($certificates);
                    }
                } else {
                    // 其他证件类型
                    $records = PetCertificate::where('pet_id', $petId)
                        ->where('certificate_type', $typeId)
                        ->orderBy('created_at', 'desc')
                        ->get();

                    if ($records->isNotEmpty()) {
                        $typeData['has_data'] = true;
                        $typeData['records'] = $records->map(function ($record) {
                            return $this->formatCertificateRecord($record);
                        })->toArray();
                        $typeData['latest_record'] = $typeData['records'][0];
                        $typeData['status_summary'] = $this->calculateStatusSummary($records);
                    }
                }

                $result[] = $typeData;
            }

            // 计算汇总信息
            $summary = [
                'total_types'    => count($certificateTypes),
                'uploaded_types' => count(array_filter($result, function ($item) {
                    return $item['has_data'];
                })),
                'pending_count'  => array_sum(array_column($result, 'status_summary.pending')),
                'approved_count' => array_sum(array_column($result, 'status_summary.approved')),
                'rejected_count' => array_sum(array_column($result, 'status_summary.rejected')),
            ];

            $responseData = [
                'pet'               => [
                    'id'     => $pet->id,
                    'name'   => $pet->name,
                    'avatar' => $pet->avatar,
                ],
                'certificate_types' => $result,
                'summary'           => $summary
            ];

            return ResponseHelper::success($responseData, '獲取成功');

        } catch (\Exception $e) {
            \Log::error('获取宠物证件概览失败: ' . $e->getMessage(), [
                'pet_id'  => $request->input('pet_id'),
                'user_id' => Auth::id(),
                'error'   => $e->getMessage()
            ]);
            return ResponseHelper::error('獲取失敗: ' . $e->getMessage());
        }
    }

    /**
     * 计算状态汇总
     *
     * @param \Illuminate\Database\Eloquent\Collection $records
     * @return array
     */
    protected function calculateStatusSummary($records): array
    {
        $summary = [
            'pending'  => 0,
            'approved' => 0,
            'rejected' => 0,
            'total'    => $records->count()
        ];

        foreach ($records as $record) {
            switch ($record->status) {
                case PetCertificate::STATUS_PENDING:
                    $summary['pending']++;
                    break;
                case PetCertificate::STATUS_APPROVED:
                    $summary['approved']++;
                    break;
                case PetCertificate::STATUS_REJECTED:
                    $summary['rejected']++;
                    break;
            }
        }

        return $summary;
    }

    /**
     * 格式化证件记录
     *
     * @param PetCertificate $certificate
     * @return array
     */
    protected function formatCertificateRecord($certificate)
    {
        return [
            'id'                    => $certificate->id,
            'type'                  => 'certificate',
            'certificate_type'      => $certificate->certificate_type,
            'certificate_type_name' => $certificate->certificate_type_name,
            'image_urls'            => $certificate->image_urls,
            'image_url'             => $certificate->first_image_url,
            'image_count'           => count($certificate->image_urls),
            'status'                => $certificate->status,
            'status_name'           => $certificate->status_name,
            'ai_recognition_result' => $certificate->ai_recognition_result,
            'admin_remark'          => $certificate->admin_remark,
            'created_at'            => $certificate->created_at->format('Y-m-d H:i:s'),
            'audited_at'            => $certificate->audited_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * 格式化医疗档案记录
     *
     * @param PetMedicalRecord $medical
     * @return array
     */
    protected function formatMedicalRecord($medical)
    {
        return [
            'id'                    => $medical->id,
            'type'                  => 'medical',
            'certificate_type'      => PetCertificate::TYPE_MEDICAL_RECORD,
            'certificate_type_name' => '医疗档案',
            'record_date'           => $medical->record_date,
            'hospital_name'         => $medical->hospital_name,
            'doctor_name'           => $medical->doctor_name,
            'diagnosis'             => $medical->diagnosis,
            'treatment'             => $medical->treatment,
            'prescription'          => $medical->prescription,
            'image_urls'            => $medical->image_urls,
            'image_url'             => $medical->first_image_url,
            'image_count'           => count($medical->image_urls),
            'status'                => $medical->status,
            'status_name'           => $medical->status_name,
            'admin_remark'          => $medical->admin_remark,
            'created_at'            => $medical->created_at->format('Y-m-d H:i:s'),
            'audited_at'            => $medical->audited_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * 格式化用户证件列表数据
     *
     * @param $certificates
     * @param $medicals
     * @return \Illuminate\Support\Collection
     */
    protected function formatUserCertificateList($certificates, $medicals)
    {
        $allRecords = collect();

        // 添加证件记录
        foreach ($certificates as $certificate) {
            $allRecords->push([
                'id'                    => $certificate->id,
                'type'                  => 'certificate',
                'certificate_type'      => $certificate->certificate_type,
                'certificate_type_name' => $certificate->certificate_type_name,
                'image_urls'            => $certificate->image_urls,
                'image_url'             => $certificate->first_image_url, // 向后兼容
                'image_count'           => count($certificate->image_urls),
                'status'                => $certificate->status,
                'status_name'           => $certificate->status_name,
                'ai_recognition_result' => $certificate->ai_recognition_result,
                'admin_remark'          => $certificate->admin_remark,
                'created_at'            => $certificate->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $certificate->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'     => $certificate->pet->id,
                    'name'   => $certificate->pet->name,
                    'avatar' => $certificate->pet->avatar,
                ],
            ]);
        }

        // 添加医疗档案记录
        foreach ($medicals as $medical) {
            $allRecords->push([
                'id'                    => $medical->id,
                'type'                  => 'medical',
                'certificate_type'      => 4,
                'certificate_type_name' => '医疗档案',
                'image_urls'            => $medical->image_urls,
                'image_url'             => $medical->first_image_url, // 向后兼容
                'image_count'           => count($medical->image_urls),
                'status'                => $medical->status,
                'status_name'           => $medical->status_name,
                'record_title'          => $medical->record_title,
                'record_date'           => $medical->record_date->format('Y-m-d'),
                'description'           => $medical->description,
                'hospital_name'         => $medical->hospital_name,
                'doctor_name'           => $medical->doctor_name,
                'admin_remark'          => $medical->admin_remark,
                'created_at'            => $medical->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $medical->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'     => $medical->pet->id,
                    'name'   => $medical->pet->name,
                    'avatar' => $medical->pet->avatar,
                ],
            ]);
        }

        return $allRecords;
    }

    /**
     * 获取用户证件统计数据
     *
     * @param array $petIds
     * @return array
     */
    protected function getUserCertificateStatistics(array $petIds): array
    {
        // 证件统计
        $certificateStats = PetCertificate::whereIn('pet_id', $petIds)
            ->selectRaw('certificate_type, status, COUNT(*) as count')
            ->groupBy('certificate_type', 'status')
            ->get()
            ->groupBy('certificate_type');

        // 医疗档案统计
        $medicalStats = PetMedicalRecord::whereIn('pet_id', $petIds)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $statistics = [
            'total_files' => 0,
            'by_type'     => [
                '1' => ['type_name' => '出生证明', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                '2' => ['type_name' => '疫苗针卡', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                '3' => ['type_name' => '晶片编码', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
                '4' => ['type_name' => '医疗档案', 'pending' => 0, 'approved' => 0, 'rejected' => 0],
            ],
            'by_status'   => [
                'pending'  => 0,
                'approved' => 0,
                'rejected' => 0,
            ]
        ];

        // 统计证件数据
        foreach ($certificateStats as $type => $statusData) {
            foreach ($statusData as $item) {
                $statusName = match ($item->status) {
                    1 => 'pending',
                    2 => 'approved',
                    3 => 'rejected',
                    default => 'pending'
                };

                $statistics['by_type'][$type][$statusName] = $item->count;
                $statistics['by_status'][$statusName] += $item->count;
                $statistics['total_files'] += $item->count;
            }
        }

        // 统计医疗档案数据
        foreach ($medicalStats as $status => $count) {
            $statusName = match ($status) {
                1 => 'pending',
                2 => 'approved',
                3 => 'rejected',
                default => 'pending'
            };

            $statistics['by_type']['4'][$statusName] = $count;
            $statistics['by_status'][$statusName] += $count;
            $statistics['total_files'] += $count;
        }

        return $statistics;
    }
}
