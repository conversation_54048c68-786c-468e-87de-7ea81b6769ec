<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\GoodCategory;
use App\Models\Good;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * @group   good
 * 商品管理
 * Class GoodController
 * @package App\Http\Controllers\Admin
 */
class GoodController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'         => 'sometimes|integer|gt:0',
            'per_page'     => 'sometimes|integer|between:1,200',
            'keyword'      => 'nullable|max:255',
            'sort_name'    => 'nullable|in:id,created_at,updated_at',
            'sort_by'      => 'nullable|in:asc,desc',
            'category_id'  => 'nullable|integer|gt:0|exists:goods_categorys,id',
            'freight_id'   => 'nullable|integer|gt:0|exists:freights,id',
            'brand_id'     => 'nullable|integer|gt:0|exists:brands,id',
            'is_show'      => 'nullable|in:0,1,2',
            'is_recommend' => 'nullable|in:0,1',
            'is_new'       => 'nullable|in:0,1',
            'is_hot'       => 'nullable|in:0,1',
        ]);
        $formData = $request->all();
        $records = Good::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Good::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                                    => 'sometimes|integer|gte:0',
            'name'                                  => 'required|max:60',
            'name_tc'                               => 'required|max:60',
            'name_en'                               => 'required|max:255',
            'image'                                 => 'nullable|url|pic|trust_host',
            'images'                                => 'nullable|array',
            'images.*'                              => 'nullable|url|pic|trust_host',
            'category_id'                           => 'required|integer|gt:0|exists:goods_categorys,id',
            'product_code'                          => 'required|string|max:50',
            'freight_id'                            => 'required|integer|gt:0|exists:freights,id',
            'brand_id'                              => 'nullable|integer|gt:0|exists:brands,id',
            'inventory'                             => 'nullable|integer|gte:0',
            'market_price'                          => 'nullable|numeric|gte:0',
            'cost_price'                            => 'nullable|numeric|gte:0',
            'price'                                 => 'nullable|numeric|gte:0',
            'sales_initial'                         => 'nullable|numeric|gte:0',
            'keywords'                              => 'nullable|string|max:255',
            'short_description'                     => 'nullable|string|max:160',
            'details'                               => 'nullable|string',
            'weight'                                => 'nullable|numeric|gte:0',
            'is_show'                               => 'required|in:0,1,2',
            'is_recommend'                          => 'nullable|in:0,1',
            'is_new'                                => 'nullable|in:0,1',
            'is_hot'                                => 'nullable|in:0,1',
            'is_refund'                             => 'nullable|in:0,1',
            'refund_day'                            => 'nullable|between:0,365',
            'sale_timing'                           => 'nullable|date',
            'sort_order'                            => 'required|integer',
            'good_sku'                              => 'nullable|array',
            'good_sku.*.image'                      => 'nullable|url|pic|trust_host',
            'good_sku.*.market_price'               => 'required|numeric|gte:0',
            'good_sku.*.cost_price'                 => 'required|numeric|gte:0',
            'good_sku.*.price'                      => 'required|numeric|gte:0',
            'good_sku.*.inventory'                  => 'required|integer',
            'good_sku.*.weight'                     => 'nullable|numeric|gte:0',
            'good_sku.*.product_sku'                => 'required|string',  //规格内容：[{"key":"尺码","value":"M"},{"key":"颜色","value":"白色"}]
            'good_sku.*.pet_filter_tags'            => 'nullable|array',   //宠物筛选标签：["pet_type:1","pet_breed:15"]，空数组表示适合所有宠物
            'good_sku.*.pet_filter_tags.*'          => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // 验证标签格式：type:value 或 type:value1,value2
                    if (!preg_match('/^[a-z_]+:[a-zA-Z0-9\.\-,]+$/', $value)) {
                        $fail('宠物筛选标签格式无效，应为 "类型:值" 或 "类型:值1,值2"');
                        return;
                    }

                    // 验证标签类型
                    [$type, $values] = explode(':', $value, 2);
                    $validTypes = ['pet_type', 'pet_breed', 'size', 'hair', 'age', 'life', 'bcs', 'weight'];

                    if (!in_array($type, $validTypes)) {
                        $fail("不支持的宠物筛选标签类型: {$type}，支持的类型: " . implode(', ', $validTypes));
                        return;
                    }

                    // 验证值格式
                    if ($values === '' || $values === null) {
                        $fail("宠物筛选标签值不能为空");
                        return;
                    }

                    // 对于age类型，验证年龄范围格式
                    if ($type === 'age') {
                        $ageValues = explode(',', $values);
                        foreach ($ageValues as $ageValue) {
                            $ageValue = trim($ageValue);
                            if (!preg_match('/^\d+(\.\d+)?(-\d+(\.\d+)?)?$/', $ageValue)) {
                                $fail("年龄格式无效: {$ageValue}，应为数字或范围（如：2 或 1-7）");
                                return;
                            }
                        }
                    }

                    // 对于weight类型，验证体重范围格式
                    if ($type === 'weight') {
                        $weightValues = explode(',', $values);
                        foreach ($weightValues as $weightValue) {
                            $weightValue = trim($weightValue);
                            if (!preg_match('/^\d+(\.\d+)?-\d+(\.\d+)?$/', $weightValue)) {
                                $fail("体重格式无效: {$weightValue}，应为范围格式（如：5-15 或 2.5-10.5）");
                                return;
                            }

                            // 验证范围的合理性
                            [$minWeight, $maxWeight] = explode('-', $weightValue, 2);
                            $minWeight = (float) $minWeight;
                            $maxWeight = (float) $maxWeight;

                            if ($minWeight <= 0 || $maxWeight <= 0) {
                                $fail("体重值必须大于0: {$weightValue}");
                                return;
                            }

                            if ($minWeight >= $maxWeight) {
                                $fail("体重范围最小值不能大于等于最大值: {$weightValue}");
                                return;
                            }

                            if ($minWeight < 0.1 || $maxWeight > 100) {
                                $fail("体重范围超出合理范围(0.1-100kg): {$weightValue}");
                                return;
                            }
                        }
                    }

                    // 对于bcs类型，验证体况评分数字格式（9分制）
                    if ($type === 'bcs') {
                        $bcsValues = explode(',', $values);
                        foreach ($bcsValues as $bcsValue) {
                            $bcsValue = trim($bcsValue);
                            if (!preg_match('/^[1-9]$/', $bcsValue)) {
                                $fail("体况评分格式无效: {$bcsValue}，应为数字1-9（9分制BCS评分）");
                                return;
                            }
                        }
                    }
                }
            ],
            'good_specification'                    => 'nullable|array',
            'good_specification.*.specification_id' => 'required|integer',
            'good_specification.*.data'             => 'nullable|string',
            // 推荐规则关联
            'recommendation_rule_id'                => 'nullable|integer|exists:recommendation_rules,id',
        ];
        $request->validate($rules);

        $formData = $request->only(array_keys($rules));
        // 检查 good_sku 是否存在且非空
        if (!isset($request->good_sku) || (is_array($request->good_sku) && count($request->good_sku) == 0)) {
            return ResponseHelper::error('请设置产品规格');
        } else {
            // 处理 good_sku 参数，兼容数组和 JSON 字符串格式
            if (is_array($request->good_sku)) {
                $good_sku = $request->good_sku;
            } else {
                if (is_string($request->good_sku)) {
                    $good_sku = json_decode($request->good_sku, true);
                    if (!$good_sku) {
                        return ResponseHelper::error('规格内容不合法，JSON 格式错误');
                    }
                } else {
                    return ResponseHelper::error('规格格式不正确');
                }
            }

            // 验证规格内容
            if (empty($good_sku)) {
                return ResponseHelper::error('规格内容不能为空');
            }

            // 验证必要字段
            foreach ($good_sku as $sku) {
                // 检查必要字段是否存在
                $requiredFields = ['price', 'market_price', 'cost_price', 'inventory'];
                foreach ($requiredFields as $field) {
                    if (!isset($sku[$field])) {
                        return ResponseHelper::error('规格中缺少必要字段: ' . $field);
                    }
                }
            }
        }
        if ($request->is_show == Good::GOOD_SHOW_TIMING && !$request->sale_timing) {
            return ResponseHelper::error('请选择上架时间');
        }
        if ($request->sale_timing && strtotime($request->sale_timing) <= time()) {
            return ResponseHelper::error('上架时间必须大于当前时间');
        }
        $formData['images'] = $request->post('images');
        $formData['good_sku'] = $good_sku; // 使用处理后的 $good_sku 变量
        $formData['good_specification'] = $request->post('good_specification');

        // 推荐规则ID已在验证规则中处理，无需额外处理

        $result = Good::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodSpecification
     * 查询某个分类下可选的商品规格
     * @param int $id
     * @queryParam  id int 分类ID
     */
    public function specification(Request $request)
    {
        $id = $request->input('id');
        $Category = GoodCategory::specification($id);
        return ResponseHelper::success($Category);
    }

    /**
     * GoodState
     * 变更商品状态
     * @param Request $request
     * @queryParam  id int 商品ID
     */
    public function state(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Good::state($request->input('id'));
        return ResponseHelper::result(...$result);
    }





    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Good::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }

    /**
     * 测试SKU筛选标签集成效果
     * 验证商品保存时筛选标签是否正确处理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testSkuFilterIntegration(Request $request)
    {
        $request->validate([
            'good_id' => 'required|integer|exists:goods,id'
        ]);

        $goodId = $request->input('good_id');

        try {
            // 获取商品及其SKU信息
            $good = Good::with(['goodSku' => function($query) {
                $query->select('id', 'good_id', 'product_sku', 'price', 'pet_filter_tags', 'matching_pets_count');
            }])->find($goodId);

            if (!$good) {
                return ResponseHelper::error('商品不存在');
            }

            $result = [
                'good_info' => [
                    'id' => $good->id,
                    'name' => $good->name,
                    'product_code' => $good->product_code,
                    'sku_count' => $good->goodSku->count()
                ],
                'sku_filter_analysis' => []
            ];

            // 分析每个SKU的筛选标签
            foreach ($good->goodSku as $sku) {
                $filterTags = $sku->pet_filter_tags ?? [];
                $isUniversal = empty($filterTags);

                $analysis = [
                    'sku_id' => $sku->id,
                    'product_sku' => $sku->product_sku,
                    'price' => $sku->price,
                    'pet_filter_tags' => $filterTags,
                    'is_universal' => $isUniversal,
                    'matching_pets_count' => $sku->matching_pets_count,
                    'filter_summary' => $isUniversal ? '适合所有宠物' : '有筛选条件'
                ];

                // 如果有筛选标签，尝试重新计算匹配结果进行验证
                if (!$isUniversal) {
                    try {
                        $petMatchingService = new \App\Services\PetMatchingService();
                        $matchingResult = $petMatchingService->calculateMatchingPets($filterTags);

                        $analysis['validation'] = [
                            'calculated_count' => $matchingResult['count'],
                            'stored_count' => $sku->matching_pets_count,
                            'count_matches' => $matchingResult['count'] == $sku->matching_pets_count,
                            'filter_details' => $matchingResult['filter_summary']
                        ];
                    } catch (\Exception $e) {
                        $analysis['validation'] = [
                            'error' => '筛选标签验证失败: ' . $e->getMessage()
                        ];
                    }
                } else {
                    $analysis['validation'] = [
                        'status' => '通用SKU，无需验证筛选条件'
                    ];
                }

                $result['sku_filter_analysis'][] = $analysis;
            }

            // 统计信息
            $universalCount = collect($result['sku_filter_analysis'])->where('is_universal', true)->count();
            $filteredCount = collect($result['sku_filter_analysis'])->where('is_universal', false)->count();

            $result['statistics'] = [
                'total_skus' => $good->goodSku->count(),
                'universal_skus' => $universalCount,
                'filtered_skus' => $filteredCount,
                'integration_status' => '筛选标签集成正常'
            ];

            return ResponseHelper::success($result);

        } catch (\Exception $e) {
            return ResponseHelper::error('测试失败：' . $e->getMessage());
        }
    }


}
