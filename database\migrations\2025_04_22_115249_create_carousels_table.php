<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('carousels', function (Blueprint $table) {
            $table->id();
            $table->string('title', 100)->comment('标题');
            $table->string('image_url', 255)->comment('轮播图片URL');
            $table->tinyInteger('link_type')->default(1)->comment('链接类型: 1=无链接, 2=外部链接, 3=内部页面');
            $table->string('link_url', 255)->nullable()->comment('链接URL');
            $table->string('position', 50)->default('home')->comment('显示位置: home=首页, etc');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->tinyInteger('status')->default(1)->comment('状态: 1=启用, 0=禁用');
            $table->dateTime('start_time')->nullable()->comment('开始展示时间');
            $table->dateTime('end_time')->nullable()->comment('结束展示时间');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['position', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('carousels');
    }
};
