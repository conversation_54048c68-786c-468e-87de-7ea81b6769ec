<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Pet;
use App\Models\PetCertificate;
use App\Models\PetMedicalRecord;
use App\Models\PetVaccineRecord;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 后台宠物证件统一管理控制器
 * 整合证件审核、疫苗记录、医疗档案三大功能模块
 */
class PetCertificateManageController extends Controller
{
    /**
     * 获取列表（统一接口）
     *
     * @param Request $request JSON请求参数
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'certificate_type' => 'nullable|integer|in:0,1,2,3,4,5',
            'page'             => 'nullable|integer|min:1',
            'per_page'         => 'nullable|integer|min:1|max:100',
            'status'           => 'nullable|integer|in:1,2,3',
            'pet_id'           => 'nullable|integer|exists:pets,id',
            'vaccine_name'     => 'nullable|string|max:100',
            'start_date'       => 'nullable|date',
            'end_date'         => 'nullable|date|after_or_equal:start_date',
            'keyword'          => 'nullable|string|max:50',
        ], [
            'certificate_type.in' => '证件类型无效：0=全部证件,1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案,5=疫苗记录',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $certificateType = $request->input('certificate_type', 0);

            if ($certificateType == 5) {
                // 疫苗记录管理
                return $this->getVaccineList($request);
            } elseif ($certificateType == 0) {
                // 全部证件（默认）
                return $this->getAllCertificateList($request);
            } else {
                // 指定类型的证件（1,2,3,4）
                return $this->getCertificateList($request);
            }
        } catch (\Exception $e) {
            Log::error('获取列表失败: ' . $e->getMessage());
            return ResponseHelper::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取详情（统一接口）
     *
     * @param Request $request JSON请求参数
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'certificate_type' => 'required|integer|in:1,2,3,4,5',
            'id'               => 'required|integer|min:1',
        ], [
            'certificate_type.in' => '证件类型无效：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案,5=疫苗记录',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $certificateType = $request->input('certificate_type');
            $id = $request->input('id');

            if ($certificateType == 5) {
                // 疫苗记录
                $vaccine = PetVaccineRecord::with(['pet.owner', 'admin'])->findOrFail($id);
                return ResponseHelper::success($this->formatVaccineDetail($vaccine), '获取成功');
            } elseif ($certificateType == 4) {
                // 医疗档案
                $medical = PetMedicalRecord::with(['pet.owner', 'admin'])->findOrFail($id);
                return ResponseHelper::success($this->formatMedicalDetail($medical), '获取成功');
            } else {
                // 证件（1,2,3）
                $certificate = PetCertificate::with(['pet.owner', 'pet.petBreed', 'admin'])->findOrFail($id);
                return ResponseHelper::success($this->formatCertificateDetail($certificate), '获取成功');
            }
        } catch (\Exception $e) {
            Log::error('获取详情失败: ' . $e->getMessage());
            return ResponseHelper::error('获取失败: ' . $e->getMessage());
        }
    }

    /**
     * 保存（统一接口）
     *
     * @param Request $request JSON请求参数
     * @return \Illuminate\Http\JsonResponse
     */
    public function save(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'certificate_type' => 'required|integer|in:1,2,3,4,5',
            'id'               => 'nullable|integer|min:1',
        ], [
            'certificate_type.in' => '证件类型无效：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案,5=疫苗记录',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $certificateType = $request->input('certificate_type');
            $id = $request->input('id');

            if ($certificateType == 5) {
                // 疫苗记录
                return $this->saveVaccine($request, $id);
            } elseif ($certificateType == 4) {
                // 医疗档案
                return $this->saveMedical($request, $id);
            } else {
                // 证件（1,2,3）
                return $this->saveCertificate($request, $id);
            }
        } catch (\Exception $e) {
            Log::error('保存失败: ' . $e->getMessage());
            return ResponseHelper::error('保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除（统一接口）
     *
     * @param Request $request JSON请求参数
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'certificate_type' => 'required|integer|in:1,2,3,4,5',
            'id'               => 'required|integer|min:1',
        ], [
            'certificate_type.in' => '证件类型无效：1=出生证明,2=疫苗针卡,3=晶片编码,4=医疗档案,5=疫苗记录',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        try {
            $certificateType = $request->input('certificate_type');
            $id = $request->input('id');

            if ($certificateType == 5) {
                // 疫苗记录
                PetVaccineRecord::findOrFail($id)->delete();
                return ResponseHelper::success(null, '疫苗记录删除成功');
            } elseif ($certificateType == 4) {
                // 医疗档案
                $medical = PetMedicalRecord::with('pet')->findOrFail($id);
                $medical->delete();
                $this->updatePetMedicalRecordStatus($medical->pet);
                return ResponseHelper::success(null, '医疗档案删除成功');
            } else {
                // 证件（1,2,3）
                return ResponseHelper::error('证件记录不支持删除，请使用审核功能');
            }
        } catch (\Exception $e) {
            Log::error('删除失败: ' . $e->getMessage());
            return ResponseHelper::error('删除失败: ' . $e->getMessage());
        }
    }


    // ==================== 私有方法 ====================

    private function getCertificateList(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $status = $request->input('status');
        $certificateType = $request->input('certificate_type');
        $keyword = $request->input('keyword');

        // 查询所有证件类型的记录，包括医疗档案
        $certificateQuery = PetCertificate::with(['pet.owner', 'admin'])->orderBy('created_at', 'desc');
        $medicalQuery = PetMedicalRecord::with(['pet.owner', 'admin'])->orderBy('created_at', 'desc');

        // 应用筛选条件
        if ($status) {
            $certificateQuery->where('status', $status);
            $medicalQuery->where('status', $status);
        }

        if ($certificateType) {
            if ($certificateType == 4) {
                // 只查询医疗档案
                $certificates = collect();
                $medicals = $medicalQuery->get();
            } else {
                // 查询指定类型的证件
                $certificateQuery->where('certificate_type', $certificateType);
                $certificates = $certificateQuery->get();
                $medicals = collect();
            }
        } else {
            // 查询所有类型
            $certificates = $certificateQuery->get();
            $medicals = $medicalQuery->get();
        }

        if ($keyword) {
            $certificates = $certificates->filter(function ($certificate) use ($keyword) {
                $ownerName = $this->getUserDisplayName($certificate->pet->owner);
                return stripos($certificate->pet->name, $keyword) !== false ||
                    stripos($ownerName, $keyword) !== false;
            });

            $medicals = $medicals->filter(function ($medical) use ($keyword) {
                $ownerName = $this->getUserDisplayName($medical->pet->owner);
                return stripos($medical->pet->name, $keyword) !== false ||
                    stripos($ownerName, $keyword) !== false ||
                    stripos($medical->record_title, $keyword) !== false;
            });
        }

        // 合并数据并格式化
        $allRecords = collect();

        // 添加证件记录
        foreach ($certificates as $certificate) {
            $allRecords->push([
                'id'                    => $certificate->id,
                'type'                  => 'certificate',
                'certificate_type'      => $certificate->certificate_type,
                'certificate_type_name' => $certificate->certificate_type_name,
                'image_url'             => $certificate->image_url,
                'status'                => $certificate->status,
                'status_name'           => $certificate->status_name,
                'ai_recognition_result' => $certificate->ai_recognition_result,
                'admin_remark'          => $certificate->admin_remark,
                'created_at'            => $certificate->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $certificate->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'         => $certificate->pet->id,
                    'name'       => $certificate->pet->name,
                    'sex'        => $certificate->pet->sex,
                    'birthday'   => $certificate->pet->birthday,
                    'breed_name' => $certificate->pet->petBreed?->name,
                    'owner_id'   => $certificate->pet->owner_id,
                    'owner_name' => $this->getUserDisplayName($certificate->pet->owner),
                ],
                'admin'                 => $certificate->admin ? [
                    'id'   => $certificate->admin->id,
                    'name' => $certificate->admin->name,
                ] : null,
            ]);
        }

        // 添加医疗档案记录
        foreach ($medicals as $medical) {
            $allRecords->push([
                'id'                    => $medical->id,
                'type'                  => 'medical',
                'certificate_type'      => 4,
                'certificate_type_name' => '医疗档案',
                'image_url'             => $medical->image_url,
                'status'                => $medical->status,
                'status_name'           => $medical->status_name,
                'record_title'          => $medical->record_title,
                'record_date'           => $medical->record_date->format('Y-m-d'),
                'description'           => $medical->description,
                'hospital_name'         => $medical->hospital_name,
                'doctor_name'           => $medical->doctor_name,
                'admin_remark'          => null,
                'created_at'            => $medical->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $medical->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'         => $medical->pet->id,
                    'name'       => $medical->pet->name,
                    'sex'        => $medical->pet->sex,
                    'birthday'   => $medical->pet->birthday,
                    'breed_name' => $medical->pet->petBreed?->name,
                    'owner_id'   => $medical->pet->owner_id,
                    'owner_name' => $this->getUserDisplayName($medical->pet->owner),
                ],
                'admin'                 => $medical->admin ? [
                    'id'   => $medical->admin->id,
                    'name' => $medical->admin->name,
                ] : null,
            ]);
        }

        // 按创建时间排序
        $allRecords = $allRecords->sortByDesc('created_at');

        // 手动分页
        $total = $allRecords->count();

        // 防止除零错误
        if ($perPage <= 0) {
            $perPage = 15;
        }

        $offset = ($page - 1) * $perPage;
        $paginatedRecords = $allRecords->slice($offset, $perPage)->values();

        // 计算分页信息，防止除零错误
        $lastPage = $total > 0 && $perPage > 0 ? ceil($total / $perPage) : 1;

        // 构建标准分页格式
        $paginationData = [
            'current_page'   => $page,
            'data'           => $paginatedRecords,
            'first_page_url' => request()->url() . '?page=1',
            'from'           => $total > 0 ? $offset + 1 : null,
            'last_page'      => $lastPage,
            'last_page_url'  => request()->url() . '?page=' . $lastPage,
            'next_page_url'  => $page < $lastPage ? request()->url() . '?page=' . ($page + 1) : null,
            'path'           => request()->url(),
            'per_page'       => $perPage,
            'prev_page_url'  => $page > 1 ? request()->url() . '?page=' . ($page - 1) : null,
            'to'             => $total > 0 ? min($offset + $perPage, $total) : null,
            'total'          => $total,
        ];

        return ResponseHelper::success($paginationData, '获取成功');
    }

    private function getVaccineList(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $petId = $request->input('pet_id');
        $vaccineName = $request->input('vaccine_name');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = PetVaccineRecord::with(['pet.owner', 'admin'])->orderBy('vaccine_date', 'desc');

        if ($petId) {
            $query->where('pet_id', $petId);
        }
        if ($vaccineName) {
            $query->where('vaccine_name', 'like', "%{$vaccineName}%");
        }
        if ($startDate && $endDate) {
            $query->whereBetween('vaccine_date', [$startDate, $endDate]);
        }

        $records = $query->paginate($perPage, ['*'], 'page', $page);

        // 格式化数据
        $formattedData = $records->map(function ($record) {
            return $this->formatVaccineItem($record);
        });

        // 构建标准分页格式
        $paginationData = $records->toArray();
        $paginationData['data'] = $formattedData;

        return ResponseHelper::success($paginationData, '获取成功');
    }

    private function getMedicalList(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $petId = $request->input('pet_id');
        $status = $request->input('status');
        $keyword = $request->input('keyword');

        $query = PetMedicalRecord::with(['pet.owner', 'admin'])->orderBy('created_at', 'desc');

        if ($petId) {
            $query->where('pet_id', $petId);
        }
        if ($status) {
            $query->where('status', $status);
        }
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('record_title', 'like', "%{$keyword}%")
                    ->orWhere('hospital_name', 'like', "%{$keyword}%")
                    ->orWhereHas('pet', function ($petQuery) use ($keyword) {
                        $petQuery->where('name', 'like', "%{$keyword}%");
                    });
            });
        }

        $records = $query->paginate($perPage, ['*'], 'page', $page);

        // 格式化数据
        $formattedData = $records->map(function ($record) {
            return $this->formatMedicalItem($record);
        });

        // 构建标准分页格式
        $paginationData = $records->toArray();
        $paginationData['data'] = $formattedData;

        return ResponseHelper::success($paginationData, '获取成功');
    }

    // ==================== 格式化方法 ====================


    private function formatCertificateDetail($certificate)
    {
        return [
            'id'                    => $certificate->id,
            'certificate_type'      => $certificate->certificate_type,
            'certificate_type_name' => $certificate->certificate_type_name,
            'image_urls'            => $certificate->image_urls,
            'image_url'             => $certificate->first_image_url, // 向后兼容
            'image_count'           => count($certificate->image_urls),
            'status'                => $certificate->status,
            'status_name'           => $certificate->status_name,
            'ai_recognition_result' => $certificate->ai_recognition_result,
            'admin_remark'          => $certificate->admin_remark,
            'created_at'            => $certificate->created_at->format('Y-m-d H:i:s'),
            'audited_at'            => $certificate->audited_at?->format('Y-m-d H:i:s'),
            'pet'                   => [
                'id'         => $certificate->pet->id,
                'name'       => $certificate->pet->name,
                'sex'        => $certificate->pet->sex,
                'birthday'   => $certificate->pet->birthday,
                'breed_id'   => $certificate->pet->breed_id,
                'breed_name' => $certificate->pet->petBreed?->name,
                'type_id'    => $certificate->pet->type_id,
                'owner_id'   => $certificate->pet->owner_id,
                'owner_name' => $this->getUserDisplayName($certificate->pet->owner),
            ],
            'admin'                 => $certificate->admin ? [
                'id'   => $certificate->admin->id,
                'name' => $certificate->admin->name,
            ] : null,
        ];
    }

    private function formatVaccineItem($record)
    {
        return [
            'id'                => $record->id,
            'vaccine_name'      => $record->vaccine_name,
            'vaccine_date'      => $record->vaccine_date->format('Y-m-d'),
            'vaccine_batch'     => $record->vaccine_batch,
            'hospital_name'     => $record->hospital_name,
            'doctor_name'       => $record->doctor_name,
            'next_vaccine_date' => $record->next_vaccine_date?->format('Y-m-d'),
            'remark'            => $record->remark,
            'created_by'        => $record->created_by,
            'created_at'        => $record->created_at->format('Y-m-d H:i:s'),
            'pet'               => [
                'id'         => $record->pet->id,
                'name'       => $record->pet->name,
                'owner_id'   => $record->pet->owner_id,
                'owner_name' => $this->getUserDisplayName($record->pet->owner),
            ],
            'admin'             => $record->admin ? [
                'id'   => $record->admin->id,
                'name' => $record->admin->name,
            ] : null,
        ];
    }

    private function formatVaccineDetail($record)
    {
        return $this->formatVaccineItem($record);
    }

    private function formatMedicalItem($record)
    {
        return [
            'id'            => $record->id,
            'record_title'  => $record->record_title,
            'record_date'   => $record->record_date->format('Y-m-d'),
            'image_urls'    => $record->image_urls,
            'image_url'     => $record->first_image_url, // 向后兼容
            'image_count'   => count($record->image_urls),
            'description'   => $record->description,
            'hospital_name' => $record->hospital_name,
            'doctor_name'   => $record->doctor_name,
            'diagnosis'     => $record->diagnosis,
            'treatment'     => $record->treatment,
            'remark'        => $record->remark,
            'status'        => $record->status,
            'status_name'   => $record->status_name,
            'created_at'    => $record->created_at->format('Y-m-d H:i:s'),
            'audited_at'    => $record->audited_at?->format('Y-m-d H:i:s'),
            'pet'           => [
                'id'         => $record->pet->id,
                'name'       => $record->pet->name,
                'owner_id'   => $record->pet->owner_id,
                'owner_name' => $this->getUserDisplayName($record->pet->owner),
            ],
            'admin'         => $record->admin ? [
                'id'   => $record->admin->id,
                'name' => $record->admin->name,
            ] : null,
        ];
    }

    private function formatMedicalDetail($record)
    {
        return $this->formatMedicalItem($record);
    }

    // ==================== 保存方法 ====================

    private function saveCertificate(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status'                     => 'required|integer|in:2,3',
            'admin_remark'               => 'nullable|string|max:500',
            'pet_data'                   => 'nullable|array',
            'pet_data.name'              => 'nullable|string|max:50',
            'pet_data.sex'               => 'nullable|integer|in:1,2',
            'pet_data.birthday'          => 'nullable|date',
            'pet_data.breed_id'          => 'nullable|integer|exists:pet_breeds,id',
            'pet_data.chip_code'         => 'nullable|string|max:50',
            'pet_data.chip_implant_date' => 'nullable|date',
            'image_urls'                 => 'nullable|array|max:10',
            'image_urls.*'               => 'nullable|string|max:500|url',
        ], [
            'pet_data.chip_code.max'          => '晶片编码长度不能超过50个字符',
            'pet_data.chip_implant_date.date' => '晶片植入日期格式不正确',
            'image_urls.array'                => '图片URL必须是数组格式',
            'image_urls.max'                  => '最多只能上传10张图片',
            'image_urls.*.string'             => '图片URL必须是字符串',
            'image_urls.*.max'                => '图片URL长度不能超过500字符',
            'image_urls.*.url'                => '请提供有效的图片URL',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        DB::beginTransaction();
        try {
            $certificate = PetCertificate::with('pet')->findOrFail($id);

            if ($certificate->status != PetCertificate::STATUS_PENDING) {
                return ResponseHelper::error('该证件已审核，无法重复审核');
            }

            $status = $request->input('status');
            $adminRemark = $request->input('admin_remark', '');
            $petData = $request->input('pet_data', []);
            $imageUrls = $request->input('image_urls');

            $updateData = [
                'status'       => $status,
                'admin_remark' => $adminRemark,
                'admin_id'     => Auth::id(),
                'audited_at'   => now(),
            ];

            // 如果提供了新的图片URL，则更新
            if ($imageUrls !== null) {
                $updateData['image_url'] = $imageUrls;
            }

            $certificate->update($updateData);

            // 如果审核通过且提供了宠物数据，更新宠物信息
            if ($status == PetCertificate::STATUS_APPROVED && !empty($petData)) {
                if ($certificate->certificate_type == PetCertificate::TYPE_BIRTH_CERTIFICATE) {
                    // 出生证明：更新基本信息
                    $this->updatePetInfo($certificate->pet, $petData);
                } elseif ($certificate->certificate_type == PetCertificate::TYPE_CHIP_CODE) {
                    // 晶片编码：更新晶片信息
                    $this->updatePetChipInfo($certificate->pet, $petData);
                }
            }

            // 更新宠物对应的证件状态
            $this->updatePetCertificateStatus($certificate->pet, $certificate->certificate_type, $status);

            DB::commit();

            return ResponseHelper::success([
                'certificate_id' => $certificate->id,
                'status'         => $status,
                'status_name'    => $certificate->fresh()->status_name,
            ], '审核完成');

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function saveVaccine(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'pet_id'            => 'required|integer|exists:pets,id',
            'vaccine_name'      => 'required|string|max:100',
            'vaccine_date'      => 'required|date',
            'vaccine_batch'     => 'nullable|string|max:100',
            'hospital_name'     => 'nullable|string|max:200',
            'doctor_name'       => 'nullable|string|max:100',
            'next_vaccine_date' => 'nullable|date|after:vaccine_date',
            'remark'            => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $data = [
            'pet_id'            => $request->input('pet_id'),
            'vaccine_name'      => $request->input('vaccine_name'),
            'vaccine_date'      => $request->input('vaccine_date'),
            'vaccine_batch'     => $request->input('vaccine_batch'),
            'hospital_name'     => $request->input('hospital_name'),
            'doctor_name'       => $request->input('doctor_name'),
            'next_vaccine_date' => $request->input('next_vaccine_date'),
            'remark'            => $request->input('remark'),
            'created_by'        => PetVaccineRecord::CREATED_BY_ADMIN,
            'admin_id'          => Auth::id(),
        ];

        if ($id) {
            // 双条件校验，防止误更新其他宠物记录
            $record = PetVaccineRecord::where('id', $id)
                ->where('pet_id', $data['pet_id'])
                ->first();
            if (!$record) {
                return ResponseHelper::error('疫苗记录不存在：请确认id有效或不传id以创建新记录');
            }
            $record->update($data);
            $message = '疫苗记录更新成功';
        } else {
            $record = PetVaccineRecord::create($data);
            $message = '疫苗记录添加成功';
        }

        if (!empty($data['next_vaccine_date']) && Carbon::parse($data['next_vaccine_date'])->isSameDay(Carbon::today())) {
            PetVaccineRecord::addReminder($record);
        }

        return ResponseHelper::success([
            'record_id'    => $record->id,
            'vaccine_name' => $record->vaccine_name,
            'vaccine_date' => $record->vaccine_date->format('Y-m-d'),
        ], $message);
    }

    private function saveMedical(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status'        => 'required|integer|in:2,3',
            'record_title'  => 'nullable|string|max:200',
            'record_date'   => 'nullable|date',
            'description'   => 'nullable|string',
            'hospital_name' => 'nullable|string|max:200',
            'doctor_name'   => 'nullable|string|max:100',
            'diagnosis'     => 'nullable|string',
            'treatment'     => 'nullable|string',
            'remark'        => 'nullable|string',
            'image_urls'    => 'nullable|array|max:10',
            'image_urls.*'  => 'nullable|string|max:500|url',
        ], [
            'image_urls.array'    => '图片URL必须是数组格式',
            'image_urls.max'      => '最多只能上传10张图片',
            'image_urls.*.string' => '图片URL必须是字符串',
            'image_urls.*.max'    => '图片URL长度不能超过500字符',
            'image_urls.*.url'    => '请提供有效的图片URL',
        ]);

        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        DB::beginTransaction();
        try {
            $record = PetMedicalRecord::with('pet')->findOrFail($id);

            if ($record->status != PetMedicalRecord::STATUS_PENDING) {
                return ResponseHelper::error('该医疗档案已审核，无法重复审核');
            }

            $status = $request->input('status');

            $updateData = [
                'status'     => $status,
                'admin_id'   => Auth::id(),
                'audited_at' => now(),
            ];

            // 如果审核通过，可以更新档案详细信息
            if ($status == PetMedicalRecord::STATUS_APPROVED) {
                $fields = [
                    'record_title',
                    'record_date',
                    'description',
                    'hospital_name',
                    'doctor_name',
                    'diagnosis',
                    'treatment',
                    'remark'
                ];

                foreach ($fields as $field) {
                    if ($request->has($field)) {
                        $updateData[$field] = $request->input($field);
                    }
                }

                // 如果提供了新的图片URL，则更新
                if ($request->has('image_urls')) {
                    $updateData['image_url'] = $request->input('image_urls');
                }
            }

            $record->update($updateData);

            // 更新宠物医疗档案状态
            $this->updatePetMedicalRecordStatus($record->pet);

            DB::commit();

            return ResponseHelper::success([
                'record_id'   => $record->id,
                'status'      => $status,
                'status_name' => $record->fresh()->status_name,
            ], '审核完成');

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    // ==================== 辅助方法 ====================

    private function updatePetInfo(Pet $pet, array $petData): void
    {
        $updateData = [];

        if (!empty($petData['name'])) {
            $updateData['name'] = $petData['name'];
        }

        if (!empty($petData['sex'])) {
            $updateData['sex'] = $petData['sex'];
        }

        if (!empty($petData['birthday'])) {
            $updateData['birthday'] = $petData['birthday'];
        }

        if (!empty($petData['breed_id'])) {
            $updateData['breed_id'] = $petData['breed_id'];
        }

        if (!empty($updateData)) {
            $pet->update($updateData);
        }
    }

    /**
     * 更新宠物晶片信息
     */
    private function updatePetChipInfo(Pet $pet, array $petData): void
    {
        $updateData = [];

        // 更新晶片编码
        if (!empty($petData['chip_code'])) {
            // 检查晶片编码唯一性（移除格式限制）
            $existingPet = Pet::where('chip_code', $petData['chip_code'])
                ->where('id', '!=', $pet->id)
                ->first();

            if (!$existingPet) {
                $updateData['chip_code'] = $petData['chip_code'];
            }
        }

        // 更新晶片植入日期
        if (!empty($petData['chip_implant_date'])) {
            $updateData['chip_implant_date'] = $petData['chip_implant_date'];
        }

        // 更新晶片状态为已通过
        $updateData['chip_code_status'] = PetCertificate::STATUS_APPROVED;

        if (!empty($updateData)) {
            $pet->update($updateData);
        }
    }

    private function updatePetCertificateStatus(Pet $pet, int $certificateType, int $status): void
    {
        $statusField = match ($certificateType) {
            PetCertificate::TYPE_BIRTH_CERTIFICATE => 'birth_certificate_status',
            PetCertificate::TYPE_VACCINE_CARD => 'vaccine_card_status',
            PetCertificate::TYPE_CHIP_CODE => 'chip_code_status',
            PetCertificate::TYPE_MEDICAL_RECORD => 'medical_record_status',
            default => null,
        };

        if ($statusField) {
            $pet->update([$statusField => $status]);
        }
    }

    private function updatePetMedicalRecordStatus(Pet $pet): void
    {
        $statusCounts = PetMedicalRecord::where('pet_id', $pet->id)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        $totalCount = array_sum($statusCounts);

        if ($totalCount == 0) {
            $petStatus = 0; // 未上传
        } elseif (isset($statusCounts[PetMedicalRecord::STATUS_PENDING]) && $statusCounts[PetMedicalRecord::STATUS_PENDING] > 0) {
            $petStatus = 1; // 有待审核
        } elseif (isset($statusCounts[PetMedicalRecord::STATUS_REJECTED]) && $statusCounts[PetMedicalRecord::STATUS_REJECTED] > 0) {
            $petStatus = 3; // 有被拒绝
        } else {
            $petStatus = 2; // 全部通过
        }

        $pet->update(['medical_record_status' => $petStatus]);
    }

    private function getAllCertificateList(Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $status = $request->input('status');
        $keyword = $request->input('keyword');

        // 查询所有证件类型
        $certificateQuery = PetCertificate::with(['pet.owner', 'admin'])->orderBy('created_at', 'desc');
        $medicalQuery = PetMedicalRecord::with(['pet.owner', 'admin'])->orderBy('created_at', 'desc');

        // 应用筛选条件
        if ($status) {
            $certificateQuery->where('status', $status);
            $medicalQuery->where('status', $status);
        }

        if ($keyword) {
            $certificateQuery->whereHas('pet', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            })->orWhereHas('pet.owner', function ($q) use ($keyword) {
                $q->where('chinese_name', 'like', "%{$keyword}%")
                    ->orWhere('english_name', 'like', "%{$keyword}%")
                    ->orWhere('username', 'like', "%{$keyword}%");
            });

            $medicalQuery->whereHas('pet', function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%");
            })->orWhereHas('pet.owner', function ($q) use ($keyword) {
                $q->where('chinese_name', 'like', "%{$keyword}%")
                    ->orWhere('english_name', 'like', "%{$keyword}%")
                    ->orWhere('username', 'like', "%{$keyword}%");
            })->orWhere('record_title', 'like', "%{$keyword}%");
        }

        // 获取数据
        $certificates = $certificateQuery->get();
        $medicals = $medicalQuery->get();

        // 合并数据并格式化
        $allRecords = collect();

        // 添加证件记录
        foreach ($certificates as $certificate) {
            $allRecords->push([
                'id'                    => $certificate->id,
                'type'                  => 'certificate',
                'certificate_type'      => $certificate->certificate_type,
                'certificate_type_name' => $certificate->certificate_type_name,
                'image_url'             => $certificate->image_url,
                'status'                => $certificate->status,
                'status_name'           => $certificate->status_name,
                'ai_recognition_result' => $certificate->ai_recognition_result,
                'admin_remark'          => $certificate->admin_remark,
                'created_at'            => $certificate->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $certificate->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'         => $certificate->pet->id,
                    'name'       => $certificate->pet->name,
                    'sex'        => $certificate->pet->sex,
                    'birthday'   => $certificate->pet->birthday,
                    'breed_name' => $certificate->pet->petBreed?->name,
                    'owner_id'   => $certificate->pet->owner_id,
                    'owner_name' => $this->getUserDisplayName($certificate->pet->owner),
                ],
                'admin'                 => $certificate->admin ? [
                    'id'   => $certificate->admin->id,
                    'name' => $certificate->admin->name,
                ] : null,
            ]);
        }

        // 添加医疗档案记录
        foreach ($medicals as $medical) {
            $allRecords->push([
                'id'                    => $medical->id,
                'type'                  => 'medical',
                'certificate_type'      => 4,
                'certificate_type_name' => '医疗档案',
                'image_url'             => $medical->image_url,
                'status'                => $medical->status,
                'status_name'           => $medical->status_name,
                'record_title'          => $medical->record_title,
                'record_date'           => $medical->record_date->format('Y-m-d'),
                'description'           => $medical->description,
                'hospital_name'         => $medical->hospital_name,
                'doctor_name'           => $medical->doctor_name,
                'admin_remark'          => $medical->admin_remark,
                'created_at'            => $medical->created_at->format('Y-m-d H:i:s'),
                'audited_at'            => $medical->audited_at?->format('Y-m-d H:i:s'),
                'pet'                   => [
                    'id'         => $medical->pet->id,
                    'name'       => $medical->pet->name,
                    'sex'        => $medical->pet->sex,
                    'birthday'   => $medical->pet->birthday,
                    'breed_name' => $medical->pet->petBreed?->name,
                    'owner_id'   => $medical->pet->owner_id,
                    'owner_name' => $this->getUserDisplayName($medical->pet->owner),
                ],
                'admin'                 => $medical->admin ? [
                    'id'   => $medical->admin->id,
                    'name' => $medical->admin->name,
                ] : null,
            ]);
        }

        // 按创建时间排序
        $allRecords = $allRecords->sortByDesc('created_at');

        // 手动分页
        $total = $allRecords->count();
        $offset = ($page - 1) * $perPage;
        $paginatedRecords = $allRecords->slice($offset, $perPage)->values();

        // 构建标准分页格式
        $paginationData = [
            'current_page'   => $page,
            'data'           => $paginatedRecords,
            'first_page_url' => request()->url() . '?page=1',
            'from'           => $total > 0 ? $offset + 1 : null,
            'last_page'      => ceil($total / $perPage) ?: 1,
            'last_page_url'  => request()->url() . '?page=' . (ceil($total / $perPage) ?: 1),
            'next_page_url'  => $page < ceil($total / $perPage) ? request()->url() . '?page=' . ($page + 1) : null,
            'path'           => request()->url(),
            'per_page'       => $perPage,
            'prev_page_url'  => $page > 1 ? request()->url() . '?page=' . ($page - 1) : null,
            'to'             => $total > 0 ? min($offset + $perPage, $total) : null,
            'total'          => $total,
        ];

        return ResponseHelper::success($paginationData, '获取成功');
    }

    /**
     * 獲取用戶顯示名稱
     * 優先級：中文名 > 英文名 > 用戶名
     *
     * @param $user
     * @return string|null
     */
    private function getUserDisplayName($user): ?string
    {
        if (!$user) {
            return null;
        }

        return $user->chinese_name ?: $user->english_name ?: $user->username;
    }
}
