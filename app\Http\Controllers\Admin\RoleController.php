<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Role;
use Illuminate\Http\Request;

class RoleController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'keyword'   => 'nullable|max:255',
        ]);
        $formData = $request->all();
        $user = $request->user();
        $records = Role::getList($formData, $user);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        $user = $request->user();
        return ResponseHelper::success(Role::getDetail($id, $user));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'              => 'sometimes|integer|gt:1',
            'pid'             => 'required|integer|gte:0',
            'name'            => 'required|max:255',
            'description'     => 'nullable',
            'system_menu_ids' => 'sometimes|number_combine'
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $user = $request->user();
        $result = Role::saveDetail($formData, $user);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:1',
        ]);
        $user = $request->user();
        $result = Role::del($request->input('id'), $user);
        return ResponseHelper::result(...$result);
    }
}
