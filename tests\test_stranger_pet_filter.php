<?php

require_once 'vendor/autoload.php';

// 设置Laravel环境
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== 测试stranger接口的宠物种类筛选功能 ===\n\n";

try {
    // 1. 查找一个有宠物的用户和宠物
    $pet = \App\Models\Pet::with(['petType', 'user'])->first();
    
    if (!$pet) {
        echo "❌ 未找到宠物数据\n";
        exit;
    }
    
    echo "=== 测试数据 ===\n";
    echo "宠物ID: {$pet->id}\n";
    echo "宠物名称: {$pet->name}\n";
    echo "宠物种类: {$pet->petType->name} (ID: {$pet->petType->id})\n";
    echo "宠物主人: {$pet->user->nickname}\n";
    
    // 2. 模拟stranger接口的调用
    echo "\n=== 模拟stranger接口调用 ===\n";
    
    // 不带宠物ID的调用
    $params1 = [
        'status' => 1,
        '_stranger' => 1,
        'page' => 1,
        'per_page' => 5
    ];
    
    echo "1. 不带宠物ID筛选:\n";
    $result1 = \App\Models\AppContent::getList($params1, $pet->user_id);
    echo "   返回帖子数量: " . count($result1['data']) . "\n";
    
    // 带宠物ID的调用（模拟Controller的逻辑）
    $params2 = [
        'status' => 1,
        '_stranger' => 1,
        '_pet_type_filter' => $pet->petType->id,
        '_pet_type_name' => $pet->petType->name,
        'page' => 1,
        'per_page' => 5
    ];
    
    echo "\n2. 带宠物种类筛选 (种类: {$pet->petType->name}):\n";
    $result2 = \App\Models\AppContent::getList($params2, $pet->user_id);
    echo "   返回帖子数量: " . count($result2['data']) . "\n";
    
    // 3. 分析筛选结果
    echo "\n=== 筛选结果分析 ===\n";
    
    if (count($result2['data']) > 0) {
        echo "筛选后的帖子详情:\n";
        foreach ($result2['data'] as $index => $content) {
            echo "帖子 " . ($index + 1) . ":\n";
            echo "  - ID: {$content['id']}\n";
            echo "  - 标题: " . ($content['title'] ?: '无标题') . "\n";
            echo "  - 作者: {$content['user']['nickname']}\n";
            
            if (!empty($content['pets'])) {
                echo "  - 关联宠物: ";
                foreach ($content['pets'] as $contentPet) {
                    echo "{$contentPet['name']}({$contentPet['pet_type']['name']}) ";
                }
                echo "\n";
            } else {
                echo "  - 关联宠物: 无\n";
            }
            echo "\n";
        }
    } else {
        echo "没有找到符合条件的帖子\n";
    }
    
    // 4. 验证筛选逻辑
    echo "=== 验证筛选逻辑 ===\n";
    
    $correctFilter = true;
    foreach ($result2['data'] as $content) {
        if (!empty($content['pets'])) {
            $hasCorrectPetType = false;
            foreach ($content['pets'] as $contentPet) {
                if ($contentPet['pet_type']['id'] == $pet->petType->id) {
                    $hasCorrectPetType = true;
                    break;
                }
            }
            if (!$hasCorrectPetType) {
                $correctFilter = false;
                echo "❌ 帖子ID {$content['id']} 包含了不匹配的宠物种类\n";
            }
        }
    }
    
    if ($correctFilter) {
        echo "✅ 筛选逻辑正确，所有返回的帖子都包含指定种类的宠物\n";
    }
    
    // 5. 测试不同的宠物种类
    echo "\n=== 测试其他宠物种类 ===\n";
    
    $otherPetTypes = \App\Models\PetType::where('id', '!=', $pet->petType->id)->limit(2)->get();
    
    foreach ($otherPetTypes as $petType) {
        $params3 = [
            'status' => 1,
            '_stranger' => 1,
            '_pet_type_filter' => $petType->id,
            '_pet_type_name' => $petType->name,
            'page' => 1,
            'per_page' => 3
        ];
        
        $result3 = \App\Models\AppContent::getList($params3, $pet->user_id);
        echo "种类 '{$petType->name}' 的帖子数量: " . count($result3['data']) . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "✅ stranger接口的宠物种类筛选功能已实现\n";
    echo "📝 使用方法: 在stranger接口中传入pet_id参数即可筛选该宠物种类的帖子\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
