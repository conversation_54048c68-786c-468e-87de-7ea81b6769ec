<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class FreightWay extends Model
{
    protected $table = 'freights_ways';

    protected $fillable = [
        'freight_id',
        'first_piece',
        'first_cost',
        'add_piece',
        'add_cost',
        'location',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 送货到
     */
    public function renderDetail(&$item)
    {
        $provinces = config('provinces');
        $provincesArray = [];
        $location = json_decode($item['location'], true);
        $locationNamej = [];
        foreach ($provinces as $p) {
            $provincesArray[$p['value']] = $p['label'];
        }
        foreach ($location as $l) {
            $locationNamej[] = $provincesArray[$l];
        }
        $item['location_name'] = $locationNamej;
    }
}
