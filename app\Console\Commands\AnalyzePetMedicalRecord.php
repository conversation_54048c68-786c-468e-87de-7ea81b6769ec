<?php

namespace App\Console\Commands;

use App\Models\PetMaster;
use App\Models\PetMedicalRecord;
use App\Models\PetReminder;
use Illuminate\Console\Command;

class AnalyzePetMedicalRecord extends Command
{
    protected $signature = 'analyze:pet_medical_record';

    protected $description = '医疗档案分析';

    public function handle()
    {
        PetMedicalRecord::where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_PENDING)
            ->where('audited_at', '>=', now()->subDays(30))
            ->select('id',  'pet_id', 'image_url')
            ->get()
            ->each(function ($record) {
                $images = $record->image_url;
                $petId = $record->pet_id;
                // 调用实验室报告分析服务
                /** @var \App\Services\PetAnalysis\LabReportAnalysisService $service */
                $service = app(\App\Services\PetAnalysis\LabReportAnalysisService::class);
                $result = $service->analyze('', $images, $petId);

                if (($result['status'] ?? 'error') !== 'success') {
                    // 提取失败原因和详细信息
                    $failureReason = $this->extractFailureReason($result);
                    $failureDetails = $this->extractFailureDetails($result);

                    \Illuminate\Support\Facades\Log::warning('Lab report analyze failed', [
                        'pet_id' => $petId,
                        'msg' => $result['message'] ?? 'unknown',
                        'failure_reason' => $failureReason,
                        'failure_details' => $failureDetails
                    ]);

                    PetMedicalRecord::where('id', $record->id)->update([
                        'analyze_status' => PetMedicalRecord::ANALYZE_STATUS_FAILED,
                        'analyze_failure_reason' => $failureReason,
                        'analyze_failure_details' => $failureDetails
                    ]);
                    return;
                }

                PetMedicalRecord::where('id', $record->id)->update([
                    'analyze_status' => PetMedicalRecord::ANALYZE_STATUS_SUCCESS,
                    'analyze_result' => json_encode($result['data'], JSON_UNESCAPED_UNICODE),
                ]);

                // 创建医疗档案提醒
                $userIds = PetMaster::getPetFamilyUserIds($petId);
                foreach ($userIds as $userId) {
                    //发送给所有家人
                    PetReminder::create([
                        'user_id'      => $userId,
                        'pet_id'       => $petId,
                        'type'         => PetReminder::TYPE_PET_FILE_REPORT,
                        'title'        => PetReminder::MAPPING[PetReminder::TYPE_PET_FILE_REPORT]['label'],
                        'content'      => "您的寵物 {$result['data']['basic_info']['pet_name']} 的醫療分析結果已生產完畢，请前往查看！",
                        'trigger_date' => date('Y-m-d'),
                    ]);
                }
            });
    }

    /**
     * 从分析结果中提取失败原因
     */
    private function extractFailureReason(array $result): string
    {
        // 从 data.failure_reason 中提取
        if (isset($result['data']['failure_reason'])) {
            return $result['data']['failure_reason'];
        }

        // 从 data.failure_reasons 数组中提取第一个
        if (isset($result['data']['failure_reasons']) && is_array($result['data']['failure_reasons'])) {
            return $result['data']['failure_reasons'][0] ?? PetMedicalRecord::FAILURE_REASON_UNKNOWN;
        }

        // 根据错误消息推断失败原因
        $message = $result['message'] ?? '';

        if (strpos($message, 'JSON') !== false || strpos($message, 'json') !== false) {
            return PetMedicalRecord::FAILURE_REASON_JSON_PARSE;
        }

        if (strpos($message, 'AI未返回') !== false || strpos($message, 'AI未返回内容') !== false) {
            return PetMedicalRecord::FAILURE_REASON_AI_NO_CONTENT;
        }

        if (strpos($message, '图片未识别') !== false || strpos($message, '参数与数值') !== false) {
            return PetMedicalRecord::FAILURE_REASON_IMAGE_INCOMPLETE;
        }

        if (strpos($message, '网络') !== false || strpos($message, 'network') !== false) {
            return PetMedicalRecord::FAILURE_REASON_NETWORK_ERROR;
        }

        if (strpos($message, '超时') !== false || strpos($message, 'timeout') !== false) {
            return PetMedicalRecord::FAILURE_REASON_TIMEOUT;
        }

        return PetMedicalRecord::FAILURE_REASON_UNKNOWN;
    }

    /**
     * 从分析结果中提取失败详细信息
     */
    private function extractFailureDetails(array $result): array
    {
        $details = [
            'error_message' => $result['message'] ?? 'unknown',
            'timestamp' => now()->toISOString()
        ];

        // 添加调试信息
        if (isset($result['data']['debug_info'])) {
            $details['debug_info'] = $result['data']['debug_info'];
        }

        // 添加失败原因列表
        if (isset($result['data']['failure_reasons'])) {
            $details['failure_reasons'] = $result['data']['failure_reasons'];
        }

        // 添加原始内容预览（如果有）
        if (isset($result['data']['raw_content'])) {
            $details['raw_content_preview'] = substr($result['data']['raw_content'], 0, 200);
        }

        return $details;
    }
}
