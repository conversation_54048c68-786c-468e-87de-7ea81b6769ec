<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ConsultCreatedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 咨询ID
     *
     * @var int
     */
    public $consultId;

    public function __construct($consultId)
    {
        $this->consultId = $consultId;
    }
}
