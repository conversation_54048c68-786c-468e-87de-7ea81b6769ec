<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\GoodIndentRefund;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * GoodIndentRefund
 * 商品订单退款
 * Class GoodIndentController
 * @package App\Http\Controllers\Mobile
 */
class GoodIndentRefundController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = GoodIndentRefund::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(GoodIndentRefund::getDetailForApp($id, $request->user()->id));
    }

    public function create(Request $request)
    {
        $rules = [
            'commodity'                            => 'required|array',
            'commodity.*.good_indent_commodity_id' => 'required|integer|gt:0',
            'commodity.*.number'                   => 'required|integer|gt:0',
            'commodity.*.total'                    => 'required|numeric|gte:0',
            'commodity.*.reason'                   => 'nullable|string|max:200',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['commodity'] = $request->input('commodity');
        $result = GoodIndentRefund::createGoodIndentRefund($formData, $request->user());
        return ResponseHelper::result(...$result);
    }

    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodIndentRefund::cancel($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodIndentRefund::del($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function nums(Request $request)
    {
        $data['user_id'] = $request->user()->id;
        return ResponseHelper::success(GoodIndentRefund::nums($data));
    }
}
