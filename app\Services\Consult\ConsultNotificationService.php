<?php

namespace App\Services\Consult;

/**
 * 咨詢通知消息定义
 */
class ConsultNotificationService
{
    /**
     * 新咨询接入通知
     */
    public static function getConsultCreatedMessage()
    {
        return [
            'title' => '諮詢接入通知',
            'body'  => '有一個新的諮詢接入，請及時處理',
        ];
    }

    /**
     * 咨询取消通知
     */
    public static function getConsultCanceledMessage()
    {
        return [
            'title' => '諮詢取消通知',
            'body'  => '諮詢已被取消，請及時處理',
        ];
    }

    /**
     * 咨询预约成功通知
     */
    public static function getConsultBookAcceptedMessage()
    {
        return [
            'title' => '諮詢預約成功通知',
            'body'  => '諮詢預約成功，請及時處理',
        ];
    }

    /**
     * 咨询预约即将开始通知
     */
    public static function getConsultBookWillStartMessage()
    {
        return [
            'title' => '諮詢預約即將開始通知',
            'body'  => '諮詢預約即將開始，請及時處理',
        ];
    }

    /**
     * 咨询已经开始通知
     */
    public static function getConsultBookStartedMessage()
    {
        return [
            'title' => '諮詢已經開始通知',
            'body'  => '諮詢已經開始，請及時處理',
        ];
    }

    /**
     * 咨询已接入通知
     */
    public static function getConsultAcceptedMessage()
    {
        return [
            'title' => '諮詢已接入通知',
            'body'  => '諮詢已接入，請及時處理',
        ];
    }

    /**
     * 咨询未接入对话通知
     */
    public static function getConsultNoEnterMessage()
    {
        return [
            'title' => '諮詢未接入對話通知',
            'body'  => '諮詢未接入對話，請及時處理',
        ];
    }

    /**
     * 咨询回复超时通知
     */
    public static function getConsultReplyTimeoutMessage()
    {
        return [
            'title' => '諮詢回覆超時通知',
            'body'  => '諮詢回覆超時，請及時處理',
        ];
    }

    /**
     * 咨询结束通知
     */
    public static function getConsultEndedMessage()
    {
        return [
            'title' => '諮詢結束通知',
            'body'  => '諮詢已結束，如有其他問題，請聯繫客服',
        ];
    }
}
