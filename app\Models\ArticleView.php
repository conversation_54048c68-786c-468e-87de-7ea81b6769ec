<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class ArticleView extends Model
{
    use HasFactory;

    protected $table = 'articles_views';

    protected $fillable = [
        'article_id',
        'pvuv_user_id',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($articleId)
    {
        //文章PVUV列表
        $cachePvKey = "article:{$articleId}:pv";
        $cacheUvKey = "article:{$articleId}:uv";
        $pvuvList = self::where('article_id', $articleId)->select('pvuv_user_id', 'created_at')->get()->toArray();
        redis()->del($cachePvKey, $cacheUvKey);
        if ($pvuvList) {
            redis()->incrBy($cachePvKey, count($pvuvList));
            redis()->sAddArray($cacheUvKey, array_column($pvuvList, 'pvuv_user_id'));
        }
        //更新为初始化
        $cacheInitKey = "article:{$articleId}:init_view";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($articleId)
    {
        $cacheInitKey = "article:{$articleId}:init_view";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($articleId); //数据有问题时把key删除，重新初始化
    }

    public static function getViewCount($articleId)
    {
        self::ensureInitRedis($articleId);
        $cachePvKey = "article:{$articleId}:pv";
        return (int)redis()->get($cachePvKey);
    }

    public static function addPvuv($appUserId, $articleId)
    {
        self::ensureInitRedis($articleId);
        DB::beginTransaction();
        try {
            //pv
            $cachePvKey = "article:{$articleId}:pv";
            redis()->incr($cachePvKey);
            //uv
            $cacheUvKey = "article:{$articleId}:uv";
            redis()->sAdd($cacheUvKey, $appUserId);
            //更新到数据库
            $pvuv = new self();
            $pvuv->article_id = $articleId;
            $pvuv->pvuv_user_id = $appUserId;
            $pvuv->save();
            //更新统计
            Article::incrementView($articleId);
            DB::commit();
            return [true, [], '操作成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('文章PVUV记录失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '操作失敗'];
        }
    }
}
