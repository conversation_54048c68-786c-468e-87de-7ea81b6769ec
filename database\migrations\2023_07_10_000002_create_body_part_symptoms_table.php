<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateBodyPartSymptomsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('body_part_symptoms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('body_part_id')->constrained('analysis_body_parts')->onDelete('cascade');
            $table->string('code', 50)->comment('症状代码');
            $table->string('name', 100)->comment('症状名称');
            $table->text('description')->nullable()->comment('描述');
            $table->string('icon', 255)->nullable()->comment('图标URL');
            $table->string('pet_type', 20)->default('general')->comment('适用宠物类型：general, dog, cat');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->boolean('status')->default(1)->comment('状态：1启用，0禁用');
            $table->timestamps();
            
            $table->unique(['code', 'pet_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('body_part_symptoms');
    }
}
