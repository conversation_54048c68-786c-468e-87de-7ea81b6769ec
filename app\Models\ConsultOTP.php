<?php

namespace App\Models;

use App\Helpers\GoEasy;
use App\Helpers\RedisLock;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class ConsultOTP extends Model
{
    use HasFactory;

    protected $table = 'consults_otps';

    protected $fillable = [
        'consult_id',
        'user_id',
        'role',
        'otp',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取消息所属的咨询
     */
    public function consult()
    {
        return $this->belongsTo(Consult::class, 'consult_id');
    }

    /**
     * 获取消息关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public static function createOTP(array $data)
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $consultId = $data['id'] ?? 0;
            if ($consultId > 0) {
                if ($data['role'] == 1) {
                    if (!$consult = Consult::isOwner($consultId, $userId)) {
                        throw new Exception('用户无权限操作');
                    }
                    if ($consult->status != Consult::CONSULT_STATUS_ACCEPTED) {
                        throw new Exception('未有營養師接入');
                    }
                } else {
                    if (!$consult = Consult::getDetail($consultId)) {
                        throw new Exception('咨询不存在');
                    }
                    if ($data['role'] == 2 && $consult->dietitian_id > 0 && $consult->dietitian_id != $userId) {
                        throw new Exception('營養師无权限操作');
                    }
                    if ($consult->status != Consult::CONSULT_STATUS_WAITING && $consult->status != Consult::CONSULT_STATUS_ACCEPTED) {
                        throw new Exception('咨询状态错误');
                    }
                }
            }
            $lock = RedisLock::lock($redis, 'CreateConsultOTP_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            /**
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data['otp'] = $goEasy->getOTP();
            $result = self::create($data);
            $id = $result->id;
            $success = true;
            if ($success) {
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($data['user_id'] . '创建OTP失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'CreateConsultOTP_' . $userId);
        }
        return [$success, $record, $message];
    }

}
