<?php

namespace App\Services\PetCalculator;

abstract class EnergyDemand
{
    protected $pet;

    public function __construct($pet)
    {
        $this->pet = $pet;
        // 移除强制体重和生日检查，允许在没有这些数据时也能创建实例
        // 具体的计算方法会在需要时检查这些数据
    }

    public function getEnergyDemand(): int
    {
        // 检查必要数据是否存在，如果没有则返回0
        if (empty($this->pet['weight']) || $this->pet['weight'] <= 0) {
            return 0;
        }
        if (empty($this->pet['birthday'])) {
            return 0;
        }

        //RER = 70 × BW^0.75
        if ($this->pet['weight_status'] > 5) {
            //过肥 公式：0.8~1.0 × 70 × BW^0.75
            return intval((0.8 + $this->getWeightAdjust()) * $this->getRestingEnergyRequirement());
        } elseif ($this->pet['weight_status'] < 4) {
            //过瘦 公式：1.2~1.8 × 70 × BW^0.75
            return intval((1.8 + $this->getWeightAdjust()) * $this->getRestingEnergyRequirement());
        } else {
            return $this->getBaseEnergyDemand();
        }
    }

    protected function getWeightAdjust()
    {
        return 0; //根据体重变化，调整基础能量需求
    }

    //计算基础能量需求
    abstract protected function getBaseEnergyDemand(): int;

    protected function _($err)
    {
        throw new \Exception($err);
    }

    //标准體重
    public function getWeightMax()
    {
        $weight_max = $this->pet['sex'] == 1 ? $this->pet['pet_breed']['weight_max_male'] : $this->pet['pet_breed']['weight_max_female'];
        if ($weight_max <= 0) {
            $this->_('未錄入品种體重上限');
        }
        return $weight_max;
    }

    //理想体重
    public function getWeightIdeal()
    {
        // 检查体重数据是否存在，如果没有则返回0
        if (empty($this->pet['weight']) || $this->pet['weight'] <= 0) {
            return 0;
        }

        //标准BCS=5
        if ($this->pet['weight_status'] > 5) {
            //过肥，比如 weight_status=8 则代表超重30%
            $over_weight = $this->pet['weight_status'] - 5;
            //理想體重 = 6 ÷ 1.3 ≈ 4.6 公斤
            $weight_ideal = $this->pet['weight'] / (1 + $over_weight * 0.1);
        } elseif ($this->pet['weight_status'] < 4) {
            //过瘦
            $less_weight = 4 - $this->pet['weight_status'];
            $weight_ideal = $this->pet['weight'] / (1 - $less_weight * 0.1);
        } else {
            $weight_ideal = $this->pet['weight'];
        }
        return round($weight_ideal, 1);
    }

    //静息能量需求 = 70 × BW^0.75
    public function getRestingEnergyRequirement()
    {
        return 70 * pow($this->getWeightIdeal(), 0.75);
    }

    public function getBirthDiff()
    {
        $birth = new \DateTime($this->pet['birthday']);
        $now = new \DateTime();
        return $now->diff($birth);
    }
}
