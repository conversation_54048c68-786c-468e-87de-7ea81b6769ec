<?php

namespace App\Models;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTimeInterface;

/**
 * @mixin Builder
 */
class ConsultMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'consults_messages';

    protected $fillable = [
        'consult_id',
        'user_id',
        'role',
        'content',
        'read_at',
        'status',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取消息所属的咨询
     */
    public function consult()
    {
        return $this->belongsTo(Consult::class, 'consult_id');
    }

    /**
     * 获取消息关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 添加用户消息
     */
    public static function addUserMessage($consultId, $userId, $content, $time = null)
    {
        // 更新会话最后消息时间
        $askTime = $time ?: now();
        Consult::where('id', $consultId)
            ->whereNull('first_ask_time')
            ->update([
                'first_ask_time' => $askTime,
            ]);
        Consult::where('id', $consultId)
            ->whereNull('last_ask_time')
            ->orWhere('last_ask_time', '<', $time)
            ->update([
                'last_ask_time' => $askTime
            ]);

        return self::create([
            'consult_id' => $consultId,
            'user_id'    => $userId,
            'role'       => 1, // 用户
            'content'    => $content,
        ]);
    }

    /**
     * 添加营养师消息
     */
    public static function addDietitianMessage($consultId, $user, $content, $time = null)
    {
        // 更新会话最后消息时间
        $answerTime = $time ?: now();
        Consult::where('id', $consultId)
            ->whereNull('first_answer_time')
            ->update([
                'first_answer_time' => $answerTime,
            ]);
        Consult::where('id', $consultId)
            ->whereNull('last_answer_time')
            ->orWhere('last_answer_time', '<', $time)
            ->update([
                'last_answer_time' => $answerTime,
            ]);

        return self::create([
            'consult_id' => $consultId,
            'user_id'    => $user->id,
            'role'       => $user->is_dietitian == 1 ? 2 : 3, // 营养师
            'content'    => $content,
        ]);
    }


    public static function getList($search_data = array(), $requestAppUserId = null)
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $status = $search_data['status'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $userId = $search_data['user_id'] ?? "";
        $consultId = $search_data['consult_id'] ?? "";

        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('content', 'like', "%{$keyword}%");
            });
        })
            ->when($status, function (Builder $query) use ($status) {
                $query->where('status', $status);
            })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($consultId, function (Builder $query) use ($consultId) {
                $query->where('consult_id', $consultId);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->with([
                'user' => function (Builder $q) {
                    $q->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                },
            ])
            ->orderBy('id', 'desc');

        $list = $query->paginate($limit, ['*'], 'page', $page);

        // 处理返回数据，整理封面图和轮播图
        $list->getCollection()->transform(function ($item) use ($requestAppUserId) {
            $item = $item->toArray();
            return $item;
        });

        return $list;
    }

}
