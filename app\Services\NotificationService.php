<?php

namespace App\Services;

use App\Helpers\GoEasy;
use App\Models\NotificationLog;
use App\Models\NotificationSetting;
use App\Providers\GoEasyServiceProvider;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * 创建通知并根据设置决定是否推送
     *
     * @param int         $userId      接收通知的用户ID
     * @param string      $type        通知类型
     * @param string      $title       通知标题
     * @param string      $content     通知内容
     * @param int|null    $senderId    发送者ID
     * @param int|null    $relatedId   相关对象ID
     * @param string|null $relatedType 相关对象类型:
     * @return array [成功状态, 通知对象, 消息]
     */
    public static function createNotification($userId, $type, $title, $content, $senderId = null, $relatedId = null, $relatedType = null)
    {
        try {
            // 1. 创建通知记录（无论设置如何，都会创建）
            $notification = NotificationLog::create([
                'user_id'      => $userId,
                'sender_id'    => $senderId,
                'type'         => $type,
                'title'        => $title,
                'content'      => $content,
                'related_id'   => $relatedId,
                'related_type' => $relatedType,
                'status'       => 1
            ]);

            // 2. 检查用户是否开启了该类型的推送通知
            $shouldPush = NotificationSetting::isEnabled($userId, $type);

            // 3. 如果用户开启了推送，则发送推送通知
            if ($shouldPush) {
                self::sendPushNotification($userId, $title, $content, $type, $relatedId, $relatedType);
            }

            Log::info("Notification created", [
                'user_id'      => $userId,
                'type'         => $type,
                'push_enabled' => $shouldPush
            ]);

            return [true, $notification, '通知创建成功'];
        } catch (\Exception $e) {
            Log::error("Failed to create notification", [
                'user_id' => $userId,
                'type'    => $type,
                'error'   => $e->getMessage(),
                'trace'   => $e->getTraceAsString()
            ]);

            return [false, null, '通知创建失败: ' . $e->getMessage()];
        }
    }

    /**
     * 发送推送通知
     *
     * @param int         $userId      用户ID
     * @param string      $title       通知标题
     * @param string      $content     通知内容
     * @param string      $type        通知类型
     * @param int|null    $relatedId   相关对象ID
     * @param string|null $relatedType 相关对象类型
     * @return bool 是否发送成功
     */
    public static function sendPushNotification($userId, $title, $content, $type, $relatedId = null, $relatedType = null)
    {
        // 构建推送数据
        $data = [
            'event'        => $type,
            'related_type' => $relatedType,
            'related_id'   => $relatedId,
        ];
        $json = json_encode($data, JSON_UNESCAPED_UNICODE);
        try {
            // 获取用户的设备信息
            $user = \App\Models\AppUser::find($userId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            if ($user->is_dietitian > 0) {
                $channel = GoEasyServiceProvider::getChannelForDietitian($userId);
            } else {
                $channel = GoEasyServiceProvider::getChannelForUser($userId);
            }
            $notification = ['title' => $title, 'body' => $content];
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            // 这里只是记录日志，实际项目中应该根据您的推送服务进行实现
            Log::info("Push notification would be sent", [
                'user_id' => $userId,
                'data'    => $data
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error("Push notification failed", [
                'user_id' => $userId,
                'data'    => $data,
                'error'   => $e->getMessage()
            ]);

            return false;
        }
    }
}
