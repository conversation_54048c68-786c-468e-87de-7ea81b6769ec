<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AppUserLog
{
    public function handle(Request $request, Closure $next)
    {
        //生成访问记录
        $logData = [
            'user_id'    => $this->getOptionalUserId($request),
            'path'       => $request->path(),
            'method'     => $request->method(),
            'ip'         => $request->ip(),
            'user_agent' => $request->userAgent(),
            'token'      => $request->bearerToken(),
            'input'      => json_encode($request->all(), JSON_UNESCAPED_UNICODE),
        ];
        \App\Models\AppUserLog::create($logData);

        return $next($request);
    }

    /**
     * 获取可选的用户ID（如果存在且有效）
     *
     * @param Request $request
     * @return int
     */
    private function getOptionalUserId(Request $request)
    {
        try {
            // 方法1: 直接尝试获取用户
            $user = $request->user();

            if ($user && isset($user->id) && $user->id > 0) {
                return (int)$user->id;
            }

            // 方法2: 如果直接获取失败，尝试手动解析token
            $token = $request->bearerToken();
            if ($token) {
                // 尝试通过 Auth 手动认证
                $authUser = Auth::guard('api')->user();
                if ($authUser && isset($authUser->id) && $authUser->id > 0) {
                    return (int)$authUser->id;
                }
            }

            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }
}
