<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\PetBreed;
use Illuminate\Http\Request;

class PetBreedController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            // 原有参数
            'page'              => 'sometimes|integer|gt:0',
            'per_page'          => 'sometimes|integer|between:1,200',
            'keyword'           => 'nullable|max:255',
            'sort_name'         => 'nullable|in:id,created_at,updated_at,name',
            'sort_by'           => 'nullable|in:asc,desc',
            'type_id'           => 'nullable|integer|gt:0|exists:pets_types,id',

            // 筛选参数
            'size_categories'   => 'nullable|array',
            'size_categories.*' => 'string|in:small,medium,large',
            'hair_types'        => 'nullable|array',
            'hair_types.*'      => 'string|in:short,medium,long',
        ]);
        $formData = $request->all();
        $records = PetBreed::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        $record = PetBreed::getDetail($id);

        // 添加规格结构
        if ($record) {
            $record = $record->toArrayWithSpecifications();
        }

        return ResponseHelper::success($record);
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                => 'sometimes|integer|gte:0',
            'name'              => 'required|max:32',
            'name_tc'           => 'required|max:32',
            'name_en'           => 'required|max:255',
            'type_id'           => 'required|integer|gt:0|exists:pets_types,id',
            'size'              => 'required|string|in:small,medium,large',
            'hair_type'         => 'nullable|string|in:short,medium,long',
            'weight_max_male'   => 'nullable|numeric|gte:0',
            'weight_max_female' => 'nullable|numeric|gte:0',
            'month_ranges'      => 'nullable|array',
            'alias_names'       => 'nullable|array',
            'alias_names.*'     => 'string|max:50',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));

        // 自定义验证 month_ranges 结构
        if (isset($formData['month_ranges']) && is_array($formData['month_ranges'])) {
            $this->validateMonthRanges($formData['month_ranges']);
        }

        // 处理别名数据
        if (isset($formData['alias_names']) && is_array($formData['alias_names'])) {
            // 过滤空值和重复值
            $formData['alias_names'] = array_values(array_unique(array_filter($formData['alias_names'], function ($alias) {
                return !empty(trim($alias));
            })));
        }

        $result = PetBreed::saveDetail($formData);

        // 返回带有规格结构的数据
        if ($result[0] && $result[1] && is_object($result[1]) && method_exists($result[1], 'toArrayWithSpecifications')) {
            $result[1] = $result[1]->toArrayWithSpecifications();
        }

        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = PetBreed::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }


    /**
     * 获取所有可用的体型分类
     */
    public function getAvailableSizes()
    {
        $sizes = PetBreed::getAvailableSizes();

        return ResponseHelper::success([
            'size_categories'     => [
                'small'  => '小型',
                'medium' => '中型',
                'large'  => '大型'
            ],
            'hair_types'          => [
                'short'  => '短毛',
                'medium' => '中等毛',
                'long'   => '长毛'
            ],
            'current_sizes_in_db' => $sizes  // 数据库中实际存在的体型
        ]);
    }

    /**
     * 验证 month_ranges 数据结构
     */
    private function validateMonthRanges($monthRanges)
    {
        $validStages = ['juvenile', 'adult', 'senior', 'elderly'];

        foreach ($monthRanges as $stage => $range) {
            // 检查阶段名称是否有效
            if (!in_array($stage, $validStages)) {
                throw new \InvalidArgumentException("无效的生命阶段: {$stage}");
            }

            // 检查范围数据结构
            if (!is_array($range)) {
                throw new \InvalidArgumentException("生命阶段 {$stage} 的数据必须是数组");
            }

            // 检查 start 字段
            if (isset($range['start']) && (!is_numeric($range['start']) || $range['start'] < 0)) {
                throw new \InvalidArgumentException("生命阶段 {$stage} 的 start 值必须是非负整数");
            }

            // 检查 end 字段（允许 null）
            if (isset($range['end']) && $range['end'] !== null && (!is_numeric($range['end']) || $range['end'] < 0)) {
                throw new \InvalidArgumentException("生命阶段 {$stage} 的 end 值必须是非负整数或null");
            }

            // 检查 start <= end（如果 end 不为 null）
            if (isset($range['start']) && isset($range['end']) && $range['end'] !== null && $range['start'] > $range['end']) {
                throw new \InvalidArgumentException("生命阶段 {$stage} 的 start 值不能大于 end 值");
            }
        }
    }

    /**
     * 更新品种别称
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateAliases(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|exists:pets_breeds,id',
            'alias_names' => 'nullable|array',
            'alias_names.*' => 'string|max:50',
        ]);

        try {
            $breed = PetBreed::findOrFail($request->input('id'));

            $aliasNames = $request->input('alias_names', []);

            // 过滤空值和重复值
            $aliasNames = array_values(array_unique(array_filter($aliasNames, function ($alias) {
                return !empty(trim($alias));
            })));

            $breed->update(['alias_names' => $aliasNames]);

            return ResponseHelper::success([
                'breed_id' => $breed->id,
                'breed_name' => $breed->name,
                'alias_names' => $aliasNames,
            ], '品种别称更新成功');

        } catch (\Exception $e) {
            return ResponseHelper::error('更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 搜索品种（包括别称）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchBreeds(Request $request)
    {
        $request->validate([
            'keyword' => 'required|string|max:50',
            'type_id' => 'nullable|integer|exists:pets_types,id',
        ]);

        try {
            $keyword = $request->input('keyword');
            $typeId = $request->input('type_id');

            $query = PetBreed::with('type');

            if ($typeId) {
                $query->where('type_id', $typeId);
            }

            $breeds = $query->get();
            $results = [];

            foreach ($breeds as $breed) {
                // 檢查標準名稱匹配
                if (stripos($breed->name, $keyword) !== false) {
                    $results[] = [
                        'id' => $breed->id,
                        'name' => $breed->name,
                        'type_name' => $breed->type?->name,
                        'match_type' => 'standard_name',
                        'match_value' => $breed->name,
                    ];
                    continue;
                }

                // 檢查別稱匹配
                if (!empty($breed->alias_names) && is_array($breed->alias_names)) {
                    foreach ($breed->alias_names as $alias) {
                        if (stripos($alias, $keyword) !== false) {
                            $results[] = [
                                'id' => $breed->id,
                                'name' => $breed->name,
                                'type_name' => $breed->type?->name,
                                'match_type' => 'alias_name',
                                'match_value' => $alias,
                            ];
                            break;
                        }
                    }
                }
            }

            return ResponseHelper::success($results, '搜尋完成');

        } catch (\Exception $e) {
            return ResponseHelper::error('搜索失败: ' . $e->getMessage());
        }
    }
}
