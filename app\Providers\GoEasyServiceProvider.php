<?php

namespace App\Providers;

use App\Helpers\GoEasy;
use App\Models\AppUser;
use App\Models\ConsultMessage;
use Illuminate\Support\ServiceProvider;

class GoEasyServiceProvider extends ServiceProvider
{
    // 推送频道
    private const CHANNEL_FOR_USER = 'channel-user-{id}';           //用户订阅
    private const CHANNEL_FOR_DIETITIAN = 'channel-dietitian-{id}'; //营养师订阅
    private const CONNECT_FOR_USER = 'user-{id}';                   //用户连接
    private const GROUP_FOR_CONSULT = 'group-consult-{id}';         //咨询群

    // 推送事件
    const EVENT_NEW_CONSULT = 'new_consult';                        //有新咨询
    const EVENT_CANCEL_CONSULT = 'cancel_consult';                  //取消咨询
    const EVENT_CONSULT_BOOK_ACCEPTED = 'consult_book_accepted';    //预约成功
    const EVENT_CONSULT_BOOK_WILL_START = 'consult_book_will_start';//预约即将开始
    const EVENT_CONSULT_BOOK_STARTED = 'consult_book_started';      //预约已经开始
    const EVENT_CONSULT_ACCEPTED = 'consult_accepted';              //已接入咨询
    const EVENT_CONSULT_BOOK_NO_ENTER = 'consult_book_no_enter';    //用户未进入聊天
    const EVENT_CONSULT_REPLY_TIMEOUT = 'consult_reply_timeout';    //长时间未回复用户
    const EVENT_CONSULT_ENDED = 'consult_ended';                    //咨询结束

    public function register()
    {
        $this->app->bind('goEasy', function () {
            return new GoEasy(config('services.go_easy'));
        });
    }

    public static function getChannelForUser($userId)
    {
        return str_replace('{id}', $userId, self::CHANNEL_FOR_USER);
    }

    public static function getChannelForDietitian($userId)
    {
        return str_replace('{id}', $userId, self::CHANNEL_FOR_DIETITIAN);
    }

    public static function getConnectForUser($userId)
    {
        return str_replace('{id}', $userId, self::CONNECT_FOR_USER);
    }

    public static function getGroupForConsult($consultId)
    {
        return str_replace('{id}', $consultId, self::GROUP_FOR_CONSULT);
    }

    public static function handleMessage(string $payload)
    {
        $list = json_decode($payload, true);
        $res = 0;
        foreach ($list as $message) {
            //咨询消息
            $groupStr = str_replace('{id}', '', self::GROUP_FOR_CONSULT);
            if (isset($message['groupId']) && str_contains($message['groupId'], $groupStr)) {
                $consultId = str_replace($groupStr, '', $message['groupId']);
                $senderData = json_decode($message['senderData'], true);
                //需要保证 ConsultMessage.content 的 messageId 不重复
                if (ConsultMessage::whereJsonContains('content', $message['messageId'])->exists()) {
                    continue;
                }
                $userId = $message['senderId'];
                $content = json_encode($message, JSON_UNESCAPED_UNICODE);
                $time = date('Y-m-d H:i:s', intval($message['timestamp'] / 1000));
                if (isset($senderData['role'])) {
                    if ($senderData['role'] == 'user') {
                        ConsultMessage::addUserMessage($consultId, $userId, $content, $time);
                        $res++;
                    }
                    if ($senderData['role'] == 'dietitian') {
                        $user = AppUser::select('id', 'is_dietitian')->where('id', $userId)->first();
                        ConsultMessage::addDietitianMessage($consultId, $user, $content, $time);
                        $res++;
                    }
                }
            }
        }
        return $res;
    }
}
