<?php

namespace App\Listeners;

use App\Events\PetTransferCodeEvent;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPetTransferCodeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PetTransferCodeEvent  $event
     * @return void
     */
    public function handle(PetTransferCodeEvent $event)
    {
        try {
            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Pet transfer code notification failed: Pet not found', [
                    'pet_id' => $event->petId,
                    'owner_id' => $event->ownerId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->ownerId, // 接收通知的用户ID（宠物所有者）
                NotificationSetting::TYPE_PET_TRANSFER_CODE, // 通知类型
                '宠物转让码生成通知',
                "您的宠物 {$pet->name} 的转让码已生成：{$event->code}，请妥善保管。",
                null, // 发送者ID（系统通知，无发送者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            Log::info('Pet transfer code notification sent', [
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId,
                'code' => $event->code
            ]);
        } catch (\Exception $e) {
            Log::error('Pet transfer code notification failed', [
                'error' => $e->getMessage(),
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId
            ]);
        }
    }
}
