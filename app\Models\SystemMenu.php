<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Helpers\Tree;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Exception;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class SystemMenu extends Model
{
    use HasFactory;

    protected $fillable = [
        'parent_id',
        'name',
        'path',
        'api',
        'weigh',
        'status'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getTree($search_data = array())
    {
        $list = self::select('id', 'parent_id', 'name', 'path', 'api', 'weigh', 'status')->orderBy('weigh', 'asc')->orderBy('id', 'asc')->get()->toArray();
        Tree::instance()->init($list);
        return Tree::instance()->getTreeArray(0);
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "菜单：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $record = array();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "菜单：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
