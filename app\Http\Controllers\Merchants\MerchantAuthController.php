<?php

namespace App\Http\Controllers\Merchants;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Merchant;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MerchantAuthController extends Controller
{

    /**
     * 客戶端商戶登錄
     */
    public function mobileLogin(Request $request)
    {
        $request->validate([
            'phone' => 'required|string',
            'code'  => 'required|string',
        ]);

        $phoneNumber = $request->input('phone');
        $code = $request->input('code');
        $cacheKey = 'sms_' . $phoneNumber;

        // 白名单
        $whitelist = ['85255784288'];

        // 如果商户在白名单中，跳过验证码验证
        if (!in_array($phoneNumber, $whitelist) && !app()->isLocal()) {
            // 验证验证码
            $cachedCode = redis()->get($cacheKey);
            if (!$cachedCode || $cachedCode != $code) {
                return ResponseHelper::error('验证码错误或已过期。');
            }
        }

        try {
            $area = substr($phoneNumber, 0, 3) === '852' ? 2 : 1; // 1-内地 2-香港

            // 查找商户
            $merchant = Merchant::where('phone', $phoneNumber)->first();

            if (!$merchant) {
                // 新商户，创建账号
                $merchant = Merchant::create([
                    'phone'    => $phoneNumber,
                    'username' => substr_replace($phoneNumber, '****', 3, 4),
                    'area'     => $area,
                    'status'   => 1,
                    'uid'      => Str::uuid()->toString()
                ]);
            } else {
                // 已存在商户，检查状态
                if ($merchant->status == 0) {
                    return ResponseHelper::error('账号已被禁用。');
                }
                if ($merchant->status == -1) {
                    return ResponseHelper::error('账号已注销。');
                }
            }

            $tokenResult = Merchant::generateToken($merchant);
            redis()->del($cacheKey); // 删除验证码

            return ResponseHelper::success([
                'access_token' => $tokenResult->accessToken,
                'user'         => $merchant,
                'is_new'       => $merchant->wasRecentlyCreated // 是否新创建的商户
            ]);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage() . '[' . $e->getFile() . '-' . $e->getLine() . ']');
        }
    }

    public function profile(Request $request)
    {
        $merchant = Merchant::getDetailById($request->user()->id);
        return ResponseHelper::success($merchant);
    }


    public function updateMerchant(Request $request)
    {
        $request->validate([
            'username'         => 'nullable|string|max:50',
            'avatar'           => 'nullable|url|pic|trust_host',
            'area'             => 'nullable|integer|in:1,2',
            'memo'             => 'nullable|string|max:255',
            'chinese_name'     => 'nullable|string|max:50',
            'english_name'     => 'nullable|string|max:50',
            'email'            => 'nullable|email|max:100',
            'gender'           => 'nullable|integer|in:0,1,2',
            'birthday'         => 'nullable|date',
            'background_image' => 'nullable|url|pic|trust_host',
        ]);

        // 所有可更新的字段
        $data = $request->only([
            'username',
            'avatar',
            'area',
            'memo',
            'chinese_name',
            'english_name',
            'email',
            'gender',
            'birthday',
            'background_image'
        ]);
        $result = Merchant::updateMerchantInfo($request->user()->id, $data);

        return ResponseHelper::result(...$result);
    }

}
