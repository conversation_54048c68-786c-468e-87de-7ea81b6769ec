<?php

namespace App\Services\Consult;

use App\Events\ConsultAcceptedEvent;
use App\Events\ConsultBookAcceptedEvent;
use App\Events\ConsultCancelledEvent;
use App\Events\ConsultEndedEvent;
use App\Helpers\RedisLock;
use App\Models\AppUser;
use App\Models\Consult;
use App\Models\ConsultOTP;
use App\Models\Dietitian;
use Exception;
use Illuminate\Support\Facades\DB;

class ConsultService
{
    private $id;
    private $user;
    private Consult $consult;
    private string $operateName;
    private string $operateType;
    private array $data;

    private string $message = '';

    public function __construct($id, $user = null)
    {
        $this->id = $id;
        $this->user = $user;
        $this->data = ['id' => $id];
    }

    public function cancel(): bool
    {
        $this->operateName = '取消';
        $this->operateType = 'user';
        return $this->run(function (ConsultService $self): string {
            if ($self->getConsult()->status != Consult::CONSULT_STATUS_WAITING) {
                return "咨詢：{$self->getConsult()->id}状态不正确";
            }
            return '';
        }, function (ConsultService $self): array {
            AppUser::decrementSubmitConsult($self->getUser()->id);
            return [
                'status'      => Consult::CONSULT_STATUS_CANCEL,
                'cancel_time' => now(),
            ];
        }, function (ConsultService $self) {
            return new ConsultCancelledEvent($self->getConsult()->id);
        });
    }

    public function acceptBook(): bool
    {
        $this->operateName = '确认预约';
        $this->operateType = 'dietitian';
        return $this->run(function (ConsultService $self): string {
            if ($self->getConsult()->book_status != Consult::CONSULT_BOOK_STATUS_WAIT) {
                return "咨詢：{$self->getConsult()->id}预约状态不正确";
            }
            if ($self->getConsult()->user_id == $self->getUser()->id) {
                return "用户：{$self->getUser()->id}不能操作自己的咨詢";
            }
            if (!Consult::isConsultTimeConflict($self->getConsult()->id, $self->getUser())) {
                return "用户：{$self->getUser()->id}预约时间冲突";
            }
            return '';
        }, function (ConsultService $self): array {
            return [
                'book_status'  => Consult::CONSULT_BOOK_STATUS_SUCCESS,
                'dietitian_id' => $self->getUser()->id,
            ];
        }, function (ConsultService $self) {
            return new ConsultBookAcceptedEvent($self->getConsult()->id);
        });
    }

    public function acceptConsult(): bool
    {
        $this->operateName = '接入咨詢';
        $this->operateType = 'dietitian';
        return $this->run(function (ConsultService $self): string {
            $consult = $self->getConsult();
            $user = $self->getUser();
            if ($consult->status != Consult::CONSULT_STATUS_WAITING) {
                return "咨詢：{$consult->id}状态不正确";
            }
            if ($consult->user_id == $user->id) {
                return "用户：{$user->id}不能操作自己的咨詢";
            }
            if ($consult->is_book == 1 && time() < strtotime($consult->book_time)) {
                return "咨詢：{$consult->id}未到預約時間";
            }
            $consulting_num = Consult::getConsultingNum($user->id, $consult->id);
            if ($consulting_num >= Consult::CONSULT_ACCEPT_MAX_PEOPLE) {
                return '超出可接入咨詢數量，需結束其他咨詢';
            }
            if (!Dietitian::isOnline($user->id)) {
                return '当前营养师不在线';
            }
            if ($user->is_dietitian == 1 && $consult->dietitian_id > 0 && $consult->dietitian_id != $user->id) {
                return "该咨询已被其他营养师接单";
            }
            return '';
        }, function (ConsultService $self): array {
            $consult = $self->getConsult();
            $user = $self->getUser();
            //增加接单数量
            AppUser::incrementAcceptConsult($user->id);
            //更新咨詢
            $upd = [
                'status' => Consult::CONSULT_STATUS_ACCEPTED,
            ];
            if (!$consult->accept_time) {
                $upd['accept_time'] = now();
            }
            if (!$consult->dietitian_id) {
                $upd['dietitian_id'] = $user->id;
            } else {
                $upd['is_transfer'] = 1;
                $upd['transfer_time'] = now();
            }
            return $upd;
        }, function (ConsultService $self) {
            return new ConsultAcceptedEvent($self->getConsult()->id);
        });
    }

    public function end(): bool
    {
        $this->operateName = '结束咨詢';
        if ($this->user) {
            $this->operateType = $this->user->is_dietitian > 0 ? 'dietitian' : 'user';
        } else {
            $this->operateType = 'system';
        }
        return $this->run(function (ConsultService $self): string {
            if ($self->getConsult()->status != Consult::CONSULT_STATUS_ACCEPTED) {
                return "咨詢：{$self->getConsult()->id}状态不正确";
            }
            return '';
        }, function (ConsultService $self): array {
            return [
                'status'   => Consult::CONSULT_STATUS_END,
                'end_time' => now(),
            ];
        }, function (ConsultService $self) {
            return new ConsultEndedEvent($self->getConsult()->id, $this->operateType);
        });
    }

    public function setSummary($data): bool
    {
        $this->operateName = '设置总结';
        $this->operateType = 'dietitian';
        return $this->run(function (ConsultService $self): string {
            if ($self->getConsult()->status != Consult::CONSULT_STATUS_END) {
                return "咨詢：{$self->getConsult()->id}状态不正确";
            }
            return '';
        }, function (ConsultService $self) use ($data): array {
            return [
                'summary' => $data['summary'],
            ];
        });
    }

    public function del(): bool
    {
        $this->operateName = '删除';
        $this->operateType = 'user';
        return $this->run(function (ConsultService $self): string {
            static $canDeleteStates = [
                Consult::CONSULT_STATUS_CANCEL,
            ];
            if (!in_array($self->getConsult()->status, $canDeleteStates)) {
                return "咨詢：{$self->getConsult()->id}状态不正确";
            }
            return '';
        }, function (ConsultService $self): array {
            return [
                'deleted_at' => date('Y-m-d H:i:s'),
            ];
        });
    }

    private function run(\Closure $validator, \Closure $update, \Closure $event = null): bool
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $id = $this->id;
            if ($this->user instanceof AppUser && $this->operateType == 'user') {
                //避免用户非法操作并锁定其他人咨詢
                $id = Consult::where('id', $this->id)->where('user_id', $this->user->id)->value('id');
                throw_if(!$id, 'RuntimeException', "咨詢：{$this->id}不存在或已删除");
            }

            //锁单
            $lock = RedisLock::lock($redis, 'UpdateConsult_' . $id);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }

            /**
             * 初始化咨詢详情
             * @var Consult $consult
             */
            $consult = Consult::getDetail($id) or throw_if(true, 'RuntimeException', "咨詢：{$id}不存在或已删除");
            $this->consult = $consult;


            if ($this->user instanceof AppUser && $this->operateType == 'dietitian') {
                //避免营养师非法操作并锁定其他人咨詢
                throw_if($consult->dietitian_id > 0 && $consult->dietitian_id != $this->user->id, 'RuntimeException', "咨詢：{$this->id}已被其他人操作");
            }

            //触发验证器
            $message = call_user_func_array($validator, [$this]);
            throw_if($message, 'RuntimeException', $message);

            //获取更新内容
            $updateData = call_user_func_array($update, [$this]);
            $success = empty($updateData) || Consult::withTrashed()->where('id', $consult->id)->update($updateData);
            foreach ($updateData as $k => $v) {
                $this->consult->$k = $v;
            }

            if ($success) {
                $success = true;
                $this->message = $this->operateName . '成功';
                if ($event) {
                    $eventObj = call_user_func_array('event', [$this]);
                    $eventObj and event($eventObj);
                }
            } else {
                throw new Exception($this->operateName . '失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $this->message = $e->getMessage();
            logErr($id . '咨詢' . $this->operateName . '失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'UpdateConsult_' . $id);
        }
        return $success;
    }

    /**
     * @return Consult
     */
    public function getConsult()
    {
        return $this->consult;
    }

    /**
     * @return AppUser
     */
    public function getUser(): mixed
    {
        return $this->user;
    }

    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }
}
