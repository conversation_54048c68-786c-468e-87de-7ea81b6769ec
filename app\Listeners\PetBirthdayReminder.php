<?php

namespace App\Listeners;

use App\Events\PetChanged;
use App\Models\Pet;
use App\Models\PetMaster;
use App\Models\PetReminder;
use Carbon\Carbon;

//写入操作日志
class PetBirthdayReminder
{
    public function handle(PetChanged $event)
    {
        $old_value = $event->oldData['birthday'] ?? '';
        $new_value = $event->newData['birthday'] ?? '';
        if ($new_value && $new_value != $old_value) {
            $targetDate = Carbon::today();
            $targetMonth = $targetDate->month;
            $targetDay = $targetDate->day;
            $birthDate = Carbon::createFromFormat('Y-m-d', $new_value)->startOfDay();
            if ($birthDate->month == $targetMonth && $birthDate->day == $targetDay) {
                $age = $birthDate->diffInYears($targetDate);
                $pet = Pet::find($event->newData['id']);
                $content = "生日快樂！今日是您的寵物 {$pet->name} {$age}岁生日啦！";
                $masterIds = PetMaster::getPetFamilyUserIds($pet->id);
                foreach ($masterIds as $userId) {
                    // 创建提醒
                    PetReminder::create([
                        'user_id'      => $userId,
                        'pet_id'       => $pet->id,
                        'type'         => PetReminder::TYPE_BIRTHDAY_REMINDER,
                        'title'        => PetReminder::MAPPING[PetReminder::TYPE_BIRTHDAY_REMINDER]['label'],
                        'content'      => $content,
                        'trigger_date' => now()->format('Y-m-d'),
                    ]);
                }
            }
        }
    }
}
