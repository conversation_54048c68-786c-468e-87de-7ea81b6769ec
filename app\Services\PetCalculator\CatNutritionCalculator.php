<?php

namespace App\Services\PetCalculator;

/**
 * 猫营养计算器
 * 基于AAFCO标准计算猫的营养需求
 */
class CatNutritionCalculator extends NutritionCalculator
{
    /**
     * 获取营养素功能说明
     *
     * @param string $nutrient
     * @return string
     */
    protected function getNutrientFunction(string $nutrient): string
    {
        $functions = [
            // 宏量营养素
            'protein' => '构建和修复肌肉组织，维持免疫系统功能，猫需要更高的蛋白质',
            'fat' => '提供必需脂肪酸，促进脂溶性维生素吸收，维持皮肤和毛发健康',
            'carbohydrate' => '提供能量，但猫对碳水化合物需求较低',
            'fiber' => '促进消化健康，预防毛球症，维持肠道健康',

            // 维生素
            'vitamin_a' => '维持视力健康，支持免疫系统，猫无法从胡萝卜素转化',
            'vitamin_d' => '促进钙磷吸收，维持骨骼和牙齿健康',
            'vitamin_e' => '抗氧化作用，保护细胞膜，维持肌肉和神经系统健康',
            'vitamin_k' => '参与血液凝固，维持骨骼健康',
            'thiamine' => '能量代谢，神经系统功能，心脏健康',
            'riboflavin' => '能量代谢，细胞生长，维持皮肤和眼睛健康',
            'niacin' => '能量代谢，神经系统功能，皮肤健康',
            'pantothenic_acid' => '能量代谢，激素合成，神经系统功能',
            'pyridoxine' => '蛋白质代谢，神经递质合成，免疫功能',
            'folic_acid' => 'DNA合成，红细胞形成，神经系统发育',
            'vitamin_b12' => '红细胞形成，神经系统功能，DNA合成',
            'choline' => '大脑发育，神经传导，脂肪代谢',

            // 矿物质
            'calcium' => '骨骼和牙齿发育，肌肉收缩，神经传导',
            'phosphorus' => '骨骼和牙齿健康，能量代谢，细胞膜结构',
            'potassium' => '肌肉和神经功能，维持体液平衡',
            'sodium' => '维持体液平衡，神经传导，肌肉功能',
            'chloride' => '维持酸碱平衡，消化液成分',
            'magnesium' => '骨骼健康，肌肉和神经功能，预防尿路结石',
            'iron' => '血红蛋白合成，氧气运输，能量代谢',
            'zinc' => '免疫功能，伤口愈合，蛋白质合成，皮肤健康',
            'copper' => '铁代谢，结缔组织形成，抗氧化酶活性',
            'manganese' => '骨骼发育，酶活性，抗氧化功能',
            'selenium' => '抗氧化功能，免疫系统支持，甲状腺功能',
            'iodine' => '甲状腺激素合成，代谢调节',

            // 氨基酸
            'arginine' => '免疫功能，伤口愈合，氨解毒（猫特别重要）',
            'histidine' => '组织修复，血红蛋白合成，神经保护',
            'isoleucine' => '肌肉代谢，能量产生，血糖调节',
            'leucine' => '肌肉蛋白质合成，血糖调节',
            'lysine' => '蛋白质合成，钙吸收，胶原蛋白形成',
            'methionine_cystine' => '蛋白质合成，毛发健康，抗氧化功能',
            'methionine' => '蛋白质合成，预防脂肪肝，毛发健康',
            'phenylalanine_tyrosine' => '神经递质合成，蛋白质合成',
            'phenylalanine' => '蛋白质合成，神经递质前体',
            'threonine' => '蛋白质合成，免疫功能，脂肪代谢',
            'tryptophan' => '血清素合成，睡眠调节，情绪稳定',
            'valine' => '肌肉代谢，组织修复，能量产生',
            'taurine' => '心脏功能，视网膜健康，生殖功能（猫必需）',
        ];

        return $functions[$nutrient] ?? '维持正常生理功能';
    }

    /**
     * 获取食物来源
     *
     * @param string $nutrient
     * @return array
     */
    protected function getFoodSources(string $nutrient): array
    {
        $sources = [
            // 宏量营养素
            'protein' => ['鸡肉', '鱼肉', '牛肉', '鸡蛋', '内脏'],
            'fat' => ['鱼油', '鸡脂', '动物脂肪'],
            'carbohydrate' => ['糙米', '燕麦', '红薯（少量）'],
            'fiber' => ['南瓜', '胡萝卜', '燕麦'],

            // 维生素
            'vitamin_a' => ['肝脏', '鱼肝油', '蛋黄'],
            'vitamin_d' => ['鱼油', '蛋黄', '肝脏'],
            'vitamin_e' => ['植物油', '鱼类', '蛋类'],
            'vitamin_k' => ['绿叶蔬菜', '肝脏', '鱼类'],
            'thiamine' => ['肉类', '鱼类', '全谷物'],
            'riboflavin' => ['肉类', '蛋类', '内脏'],
            'niacin' => ['肉类', '鱼类', '内脏'],
            'pantothenic_acid' => ['肉类', '蛋类', '内脏'],
            'pyridoxine' => ['肉类', '鱼类', '内脏'],
            'folic_acid' => ['肝脏', '绿叶蔬菜', '豆类'],
            'vitamin_b12' => ['肉类', '鱼类', '内脏'],
            'choline' => ['蛋类', '肉类', '鱼类'],

            // 矿物质
            'calcium' => ['骨粉', '小鱼骨', '蛋壳粉'],
            'phosphorus' => ['肉类', '鱼类', '骨粉'],
            'potassium' => ['肉类', '鱼类', '蔬菜'],
            'sodium' => ['肉类', '鱼类'],
            'chloride' => ['食盐', '海藻'],
            'magnesium' => ['鱼类', '全谷物', '绿叶蔬菜'],
            'iron' => ['肝脏', '红肉', '鱼类'],
            'zinc' => ['肉类', '海鲜', '内脏'],
            'copper' => ['肝脏', '海鲜', '肉类'],
            'manganese' => ['鱼类', '全谷物', '蔬菜'],
            'selenium' => ['鱼类', '肉类', '内脏'],
            'iodine' => ['海鱼', '海藻', '碘化盐'],

            // 氨基酸
            'arginine' => ['肉类', '鱼类', '内脏'],
            'histidine' => ['肉类', '鱼类', '蛋类'],
            'isoleucine' => ['肉类', '蛋类', '鱼类'],
            'leucine' => ['肉类', '蛋类', '鱼类'],
            'lysine' => ['肉类', '鱼类', '蛋类'],
            'methionine_cystine' => ['肉类', '鱼类', '蛋类'],
            'methionine' => ['鱼类', '肉类', '蛋类'],
            'phenylalanine_tyrosine' => ['肉类', '鱼类', '蛋类'],
            'phenylalanine' => ['肉类', '鱼类', '蛋类'],
            'threonine' => ['肉类', '蛋类', '鱼类'],
            'tryptophan' => ['火鸡肉', '鸡肉', '鱼类'],
            'valine' => ['肉类', '蛋类', '鱼类'],
            'taurine' => ['心脏', '肝脏', '海鲜', '肌肉组织'],
        ];

        return $sources[$nutrient] ?? ['均衡饮食'];
    }

    /**
     * 获取推荐建议
     *
     * @return array
     */
    protected function getRecommendations(): array
    {
        $recommendations = [
            'feeding_guidelines' => [
                '选择高质量的商业猫粮，确保符合AAFCO标准',
                '猫是严格的肉食动物，需要高蛋白饮食',
                '提供湿粮有助于增加水分摄入',
                '定时定量喂食，避免自由采食导致肥胖',
                '提供充足的清洁饮水'
            ],
            'special_considerations' => [
                '幼猫需要更高的蛋白质、脂肪和热量',
                '老年猫可能需要易消化的高质量蛋白质',
                '室内猫活动量较少，需要控制热量摄入',
                '绝育猫容易发胖，需要调整食量',
                '长毛猫需要额外的毛球控制'
            ],
            'foods_to_avoid' => [
                '洋葱和大蒜 - 可能导致贫血',
                '巧克力 - 含有可可碱，对猫有毒',
                '葡萄和葡萄干 - 可能导致肾衰竭',
                '酒精 - 对猫的神经系统有害',
                '咖啡因 - 可能导致心律不齐',
                '生鱼 - 可能含有硫胺素酶，破坏维生素B1',
                '牛奶 - 大多数成年猫乳糖不耐受',
                '生鸡蛋 - 可能含有沙门氏菌，生蛋白影响生物素吸收',
                '狗粮 - 营养成分不适合猫的需求'
            ],
            'cat_specific_needs' => [
                '牛磺酸是猫的必需氨基酸，缺乏会导致心脏病和视力问题',
                '花生四烯酸是猫的必需脂肪酸，只存在于动物组织中',
                '猫无法合成足够的烟酸，需要从食物中获取',
                '猫对维生素A的需求较高，且无法从胡萝卜素转化',
                '猫需要较高的蛋白质来维持肌肉量和代谢'
            ],
            'supplementation_notes' => [
                '高质量的商业猫粮通常不需要额外补充',
                '可考虑补充鱼油（Omega-3）改善皮肤和毛发',
                '老年猫可能需要关节保健品',
                '长毛猫可考虑毛球控制补充剂',
                '任何补充剂使用前请咨询兽医'
            ],
            'monitoring_tips' => [
                '定期监测体重和身体状况评分',
                '观察食欲、精神状态和排便情况',
                '注意饮水量，猫容易患泌尿系统疾病',
                '观察毛发光泽和皮肤健康',
                '定期进行兽医检查和血液检测',
                '注意牙齿健康，提供适当的咀嚼玩具'
            ]
        ];

        // 根据年龄阶段添加特殊建议
        $ageStage = $this->getAgeStage();
        if ($ageStage === 'kitten') {
            $recommendations['age_specific'] = [
                '幼猫期营养对终生健康至关重要',
                '选择专门的幼猫粮，蛋白质含量应在35%以上',
                '少量多餐，每天3-4次',
                '确保充足的DHA支持大脑和视力发育',
                '12周前应继续母乳喂养或幼猫奶粉'
            ];
        } elseif ($ageStage === 'senior') {
            $recommendations['age_specific'] = [
                '老年猫代谢较慢，但仍需要高质量蛋白质',
                '可能需要增加抗氧化剂摄入',
                '注意肾脏健康，定期检查肾功能',
                '考虑易消化的食物，减少肠胃负担',
                '增加湿粮比例，帮助维持水分平衡'
            ];
        }

        return $recommendations;
    }
}
