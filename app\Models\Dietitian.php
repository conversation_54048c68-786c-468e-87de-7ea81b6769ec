<?php

namespace App\Models;

use App\Http\Middleware\OnlineRefresh;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Dietitian extends Model
{
    use HasFactory, SoftDeletes;

    public static function setOnlineStatus($user, $onlineStatus)
    {
        $guard = 'dietitian';
        $userId = $user->id;
        if ($user->set_online_status != $onlineStatus) {
            AppUser::where('id', $userId)->update(['set_online_status' => $onlineStatus]);
        }
        return $onlineStatus == 1 ? OnlineRefresh::addMember($userId, $guard) : OnlineRefresh::removeMember($userId, $guard);
    }

    public static function isOnline($userId)
    {
        $guard = 'dietitian';
        $online_users = OnlineRefresh::getOnlineMembers($guard);
        return in_array($userId, $online_users);
    }

    public static function getDietitianList($search_data)
    {
        $guard = 'dietitian';
        $online_users = OnlineRefresh::getOnlineMembers($guard);
        $list = AppUser::select([
            'id',
            'username',
            'chinese_name',
            'english_name',
            'avatar',
            'certificates',
            'work_year',
            'set_online_status',
            'work_clock_start',
            'work_clock_end',
            'accept_consult_count'
        ])->where('is_dietitian', '=', $search_data['is_dietitian'])->get();
        $userIds = $list->pluck('id')->toArray();
        //按天统计咨询次数
        $consultCount = Consult::withTrashed()
            ->select(['dietitian_id', DB::raw('count(*) as count')])
            ->whereIn('dietitian_id', $userIds)
            ->where('status', '<>', Consult::CONSULT_STATUS_CANCEL)
            ->where('created_at', '>=', date('Y-m-d'))
            ->groupBy('dietitian_id')
            ->pluck('count', 'dietitian_id')
            ->toArray();

        foreach ($list as $item) {
            $item->is_online = in_array($item->id, $online_users);
            $item->today_consult_count = $consultCount[$item->id] ?? 0;
        }
        return $list;
    }
}
