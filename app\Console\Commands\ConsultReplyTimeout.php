<?php

namespace App\Console\Commands;

use App\Events\ConsultReplyTimeoutEvent;
use App\Models\Consult;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ConsultReplyTimeout extends Command
{
    protected $signature = 'consult:reply-timeout';

    protected $description = '咨询回复超时提醒';

    public function handle()
    {
        Consult::query()
            ->select('id')
            ->where('status', Consult::CONSULT_STATUS_ACCEPTED)
            ->where('reply_notify', 0)
            ->whereNotNull('last_ask_time')
            ->whereRaw('last_answer_time+' . (Consult::CONSULT_REPLY_TIME_LIMIT * 60) . '<=last_ask_time')
            ->where('created_at', '>=', now()->subDays(30))  //只查询一个月内的
            ->chunk(100, function ($consults) {
                foreach ($consults as $consult) {
                    try {
                        event(new ConsultReplyTimeoutEvent($consult->id));
                        Consult::where('id', $consult->id)->update([
                            'reply_notify' => 1,
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Consult book reply timeout notification failed', [
                            'error'      => $e->getMessage(),
                            'consult_id' => $consult->id,
                        ]);
                    }
                }
            });
    }
}
