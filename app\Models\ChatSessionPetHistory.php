<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use DateTimeInterface;

class ChatSessionPetHistory extends Model
{
    use HasFactory;

    protected $table = 'chat_sessions_pet_history';

    protected $fillable = [
        'session_id',
        'user_id',
        'old_pet_ids',
        'new_pet_ids',
        'change_type',
        'change_reason'
    ];

    protected $casts = [
        'old_pet_ids' => 'array',
        'new_pet_ids' => 'array',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取关联的会话
     */
    public function session()
    {
        return $this->belongsTo(ChatSession::class, 'session_id', 'session_id');
    }

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 记录宠物列表变更
     *
     * @param string $sessionId 会话ID
     * @param int $userId 用户ID
     * @param array|null $oldPetIds 旧的宠物ID列表
     * @param array|null $newPetIds 新的宠物ID列表
     * @param string $reason 变更原因
     * @return self
     */
    public static function recordChange($sessionId, $userId, $oldPetIds, $newPetIds, $reason = 'API调用更新')
    {
        $changeType = self::determineChangeType($oldPetIds, $newPetIds);

        return self::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'old_pet_ids' => $oldPetIds,
            'new_pet_ids' => $newPetIds,
            'change_type' => $changeType,
            'change_reason' => $reason
        ]);
    }

    /**
     * 确定变更类型
     *
     * @param array|null $oldPetIds
     * @param array|null $newPetIds
     * @return string
     */
    protected static function determineChangeType($oldPetIds, $newPetIds)
    {
        // 如果旧列表为空，说明是创建
        if (empty($oldPetIds)) {
            return 'created';
        }

        // 如果新列表为空，说明是清空
        if (empty($newPetIds)) {
            return 'cleared';
        }

        $oldCount = count($oldPetIds);
        $newCount = count($newPetIds);

        if ($newCount > $oldCount) {
            return 'expanded';  // 扩大关注范围
        } elseif ($newCount < $oldCount) {
            return 'narrowed';  // 缩小关注范围
        } else {
            // 数量相同，检查是否有变化
            $oldSorted = $oldPetIds;
            $newSorted = $newPetIds;
            sort($oldSorted);
            sort($newSorted);

            if ($oldSorted === $newSorted) {
                return 'reordered'; // 重新排序
            } else {
                return 'changed';   // 替换宠物
            }
        }
    }

    /**
     * 获取会话的完整变更历史
     *
     * @param string $sessionId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getSessionHistory($sessionId)
    {
        return self::where('session_id', $sessionId)
            ->orderBy('created_at', 'asc')
            ->get();
    }

    /**
     * 获取用户的宠物关注模式分析
     *
     * @param int $userId
     * @param int $days 分析最近多少天的数据
     * @return array
     */
    public static function getUserPetFocusPattern($userId, $days = 30)
    {
        $history = self::where('user_id', $userId)
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'asc')
            ->get();

        $patterns = [
            'total_changes' => $history->count(),
            'change_types' => $history->groupBy('change_type')->map->count(),
            'most_common_change' => $history->groupBy('change_type')->sortByDesc->count()->keys()->first(),
            'average_pets_per_session' => 0,
            'pet_focus_trend' => []
        ];

        if ($history->isNotEmpty()) {
            $totalPets = $history->sum(function($item) {
                return count($item->new_pet_ids ?? []);
            });
            $patterns['average_pets_per_session'] = round($totalPets / $history->count(), 2);

            // 分析关注趋势
            foreach ($history as $change) {
                $patterns['pet_focus_trend'][] = [
                    'date' => $change->created_at->format('Y-m-d'),
                    'pet_count' => count($change->new_pet_ids ?? []),
                    'change_type' => $change->change_type
                ];
            }
        }

        return $patterns;
    }
}
