<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\AppUserFollow;
use App\Models\Pet;
use App\Models\PetMaster;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class AppUserController extends Controller
{

    /**
     * 客户端用户登录
     */
    public function mobileLogin(Request $request)
    {
        $request->validate([
            'phone'     => 'required|string',
            'code'      => 'required|string',
            'longitude' => 'nullable|numeric',
            'latitude'  => 'nullable|numeric',
        ]);

        $phoneNumber = $request->input('phone');
        $code = $request->input('code');
        $cacheKey = 'sms_' . $phoneNumber;

        // 白名单 测试自动推送
        $whitelist = ['85255784288', '85260615204', '85262354166', '8615802095857', '85255785389', '85266025544', '85255784368', '85251238765', '85260982341', '85271294856', '85280345678', '85295671234', '85244819920', '85252306789', '85278904321', '85261428375', '85287765543','85254008798'];

        // 如果用户在白名单中，跳过验证码验证
        if (!in_array($phoneNumber, $whitelist) && !app()->isLocal()) {
            // 验证验证码
            $cachedCode = redis()->get($cacheKey);
            if (!$cachedCode || $cachedCode != $code) {
                return ResponseHelper::error('驗證碼錯誤或已過期。');
            }
        }

        try {
            $area = substr($phoneNumber, 0, 3) === '852' ? 2 : 1; // 1-内地 2-香港

            // 查找用户
            $user = AppUser::where('phone', $phoneNumber)->first();

            if (!$user) {
                // 新用户，创建账号
                $user = AppUser::create([
                    'phone'     => $phoneNumber,
                    'username'  => substr_replace($phoneNumber, '****', 3, 4),
                    'area'      => $area,
                    'status'    => 1,
                    'uid'       => Str::uuid()->toString(),
                    'longitude' => $request->post('longitude'),
                    'latitude'  => $request->post('latitude'),
                ]);
            } else {
                // 已存在用户，检查状态
                if ($user->status == 0) {
                    return ResponseHelper::error('賬號已被禁用。');
                }
                if ($user->status == -1) {
                    return ResponseHelper::error('賬號已註銷。');
                }
            }
            AppUser::renderQrCode($user);

            $tokenResult = AppUser::generateToken($user);
            redis()->del($cacheKey); // 删除验证码

            return ResponseHelper::success([
                'access_token' => $tokenResult->accessToken,
                'user'         => $user,
                'is_new'       => $user->wasRecentlyCreated // 是否新创建的用户
            ]);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage() . '[' . $e->getFile() . '-' . $e->getLine() . ']');
        }
    }

    public function profile(Request $request)
    {
        $user = AppUser::getDetailById($request->user()->id);
        AppUser::renderQrCode($user);
        $user->family_count = count(PetMaster::getMyFamilyUserIds($user->id));
        return ResponseHelper::success($user);
    }


    public function updateUser(Request $request)
    {
        $rules = [
            'username'         => 'nullable|string|max:50',
            'avatar'           => 'nullable|url|pic|trust_host',
            'area'             => 'nullable|integer|in:1,2',
            'memo'             => 'nullable|string|max:255',
            'chinese_name'     => 'nullable|string|max:50',
            'english_name'     => 'nullable|string|max:50',
            'email'            => 'nullable|email|max:100',
            'gender'           => 'nullable|integer|in:0,1,2',
            'birthday'         => 'nullable|date',
            'background_image' => 'nullable|url|pic|trust_host',
            'longitude'        => 'nullable|numeric',
            'latitude'         => 'nullable|numeric',
        ];
        if ($request->user()->is_dietitian > 0) {
            $rules += [
                'certificates'     => 'nullable|string',
                'work_year'        => 'nullable|integer|gte:0',
                'work_clock_start' => 'nullable|clock',
                'work_clock_end'   => 'nullable|clock',
            ];
        }
        $request->validate($rules);
        $data = $request->only(array_keys($rules));
        $result = AppUser::updateUserInfo($request->user()->id, $data);
        return ResponseHelper::result(...$result);
    }


    /**
     * 用户注销账号（硬注销）
     */
    public function deleteAccount(Request $request)
    {
        $request->validate([
            'confirm' => 'required|boolean|accepted', // 用户确认注销
            'reason'  => 'nullable|string|max:255', // 注销原因（可选）
        ]);

        $reason = $request->input('reason', '用户主动注销');
        $user = $request->user();

        try {
            $result = AppUser::deleteUserAccount($user->id, $reason);

            if ($result[0]) {
                return ResponseHelper::success([
                    'message'    => '账号注销成功',
                    'deleted_at' => $result[2]['deleted_at']
                ]);
            } else {
                return ResponseHelper::error($result[1]);
            }
        } catch (\Exception $e) {
            return ResponseHelper::error('註銷失敗：' . $e->getMessage());
        }
    }

    /**
     * 搜索用户列表 - 通过手机号或用户名
     */
    public function searchUsers(Request $request)
    {
        $request->validate([
            'keyword'  => 'required|string|max:50', // 搜索关键词（手机号或用户名）
            'page'     => 'nullable|integer|gt:0',
            'per_page' => 'nullable|integer|gt:0|max:50',
        ]);

        $query = AppUser::query()
            ->where(function ($query) use ($request) {
                $keyword = $request->keyword;
                $query->where('username', 'like', "%{$keyword}%")
                    ->orWhere('phone', 'like', "%{$keyword}%");
            })
            ->where('id', '!=', $request->user()->id)  // 排除自己
            ->where('status', 1);  // 只查询正常状态用户

        $users = $query->paginate($request->input('per_page', 15));

        $currentUserId = $request->user()->id;
        $users->through(function ($user) use ($currentUserId, $request) {
            $data = $user->toArray();

            // 基础信息
            unset($data['phone']); // 默认移除手机号
            unset($data['password']);
            unset($data['deleted_at']);

            // 添加关注状态
            $data['is_following'] = AppUserFollow::isFollowing($currentUserId, $user->id);
            $data['is_mutual_following'] = AppUserFollow::isMutualFollowing($currentUserId, $user->id);

            return $data;
        });

        return ResponseHelper::success($users);
    }


    public function getAppUserInfo(Request $request)
    {
        $request->validate([
            'user_id' => 'nullable|integer|gt:0',
            'uid'     => 'nullable|string|max:50',
        ]);

        $details = $request->user_id
            ? AppUser::getDetailById($request->user_id)
            : AppUser::getDetailByUId($request->uid);

        if (!$details) {
            return ResponseHelper::error('User not found');
        }

        $currentUserId = $request->user()->id;
        $targetUserId = $details->id;

        $details = $details->toArray();
        unset($details['phone']);

        // 添加关系信息
        $details['relationship'] = $this->getUserRelationship($currentUserId, $targetUserId);

        // 保持原有的字段（向后兼容）
        $details['is_following'] = $details['relationship']['is_following'];
        $details['is_mutual_following'] = $details['relationship']['is_mutual_following'];

        return ResponseHelper::success($details);
    }

    /**
     * 获取用户关系信息
     *
     * @param int $currentUserId 当前用户ID
     * @param int $targetUserId  目标用户ID
     * @return array
     */
    private function getUserRelationship($currentUserId, $targetUserId)
    {
        // 如果是同一个用户
        if ($currentUserId == $targetUserId) {
            return [
                'is_self'             => true,
                'is_following'        => false,
                'is_followed_by'      => false,
                'is_mutual_following' => false,
                'is_family'           => false,
                'shared_pets'         => [],
                'follow_time'         => null,
                'followed_time'       => null
            ];
        }

        // 检查各种关系状态
        $isFollowing = AppUserFollow::isFollowing($currentUserId, $targetUserId);
        $isFollowedBy = AppUserFollow::isFollowing($targetUserId, $currentUserId);
        $isMutualFollowing = AppUserFollow::isMutualFollowing($currentUserId, $targetUserId);
        $isFamily = PetMaster::isFamily($currentUserId, $targetUserId);

        // 获取关注时间
        $followTime = $this->getFollowTime($currentUserId, $targetUserId);
        $followedTime = $this->getFollowTime($targetUserId, $currentUserId);

        // 获取共同宠物
        $sharedPets = $this->getSharedPets($currentUserId, $targetUserId);

        return [
            'is_self'             => false,
            'is_following'        => $isFollowing,
            'is_followed_by'      => $isFollowedBy,
            'is_mutual_following' => $isMutualFollowing,
            'is_family'           => $isFamily,
            'shared_pets'         => $sharedPets,
            'follow_time'         => $followTime,
            'followed_time'       => $followedTime
        ];
    }

    /**
     * 获取关注时间
     *
     * @param int $followerId 关注者ID
     * @param int $followedId 被关注者ID
     * @return string|null
     */
    private function getFollowTime($followerId, $followedId)
    {
        $follow = AppUserFollow::where('app_user_id', $followerId)
            ->where('follow_user_id', $followedId)
            ->whereNull('deleted_at')
            ->first();

        return $follow ? $follow->created_at->format('Y-m-d H:i:s') : null;
    }

    /**
     * 获取共同宠物信息
     *
     * @param int $userId1 用户1ID
     * @param int $userId2 用户2ID
     * @return array
     */
    private function getSharedPets($userId1, $userId2)
    {
        if (!$userId1 || !$userId2 || $userId1 == $userId2) {
            return [];
        }

        // 获取共同宠物ID
        $sharedPetIds = PetMaster::where('user_id', $userId1)
            ->whereIn('pet_id', function ($query) use ($userId2) {
                $query->select('pet_id')
                    ->from('pets_masters')
                    ->where('user_id', $userId2)
                    ->whereNull('deleted_at');
            })
            ->whereNull('deleted_at')
            ->pluck('pet_id')
            ->toArray();

        if (empty($sharedPetIds)) {
            return [];
        }

        // 获取宠物详细信息
        $pets = Pet::whereIn('id', $sharedPetIds)
            ->with(['petBreed:id,name,name_tc,name_en'])
            ->select('id', 'name', 'avatar', 'breed_id', 'sex')
            ->get()
            ->map(function ($pet) {
                return [
                    'id'     => $pet->id,
                    'name'   => $pet->name,
                    'avatar' => $pet->avatar,
                    'breed'  => $pet->petBreed ? $pet->petBreed->name : null,
                    'sex'    => $pet->sex
                ];
            })
            ->toArray();

        return $pets;
    }


}
