<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Friend extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_friends';


    protected $fillable = [
        'user_id',
        'friend_id',
        'remark',
        'status'
    ];


    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联好友用户信息
    public function friend()
    {
        return $this->belongsTo(AppUser::class, 'friend_id');
    }

    /**
     * 获取好友列表
     */
    public static function getList($search_data = [], $userId = null)
    {
        $page = $search_data['page'] ?? 1;
        $perPage = $search_data['per_page'] ?? 20;

        $list = self::with(['friend' => function($query) {
            $query->select('id', 'username', 'avatar', 'memo','uid');
        }])
            ->where('user_id', $userId)
            ->orderBy('id', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        return [true, $list, '获取成功'];
    }

}
