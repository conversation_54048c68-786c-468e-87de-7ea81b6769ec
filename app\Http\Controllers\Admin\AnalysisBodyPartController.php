<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AnalysisBodyPart;
use Illuminate\Http\Request;

class AnalysisBodyPartController extends Controller
{
    /**
     * 获取部位列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'   => 'nullable|in:asc,desc',
            'pet_type'  => 'nullable|in:general,dog,cat',
            'status'    => 'nullable|in:0,1',
        ]);

        $query = AnalysisBodyPart::query();

        // 关键词搜索
        if ($request->has('keyword') && $request->input('keyword')) {
            $keyword = $request->input('keyword');
            $query->where(function($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%");
            });
        }

        // 宠物类型筛选
        if ($request->has('pet_type') && $request->input('pet_type')) {
            $query->where('pet_type', $request->input('pet_type'));
        }

        // 状态筛选
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('status', $request->input('status'));
        }

        // 排序
        $sortName = $request->input('sort_name', 'sort_order');
        $sortBy = $request->input('sort_by', 'asc');
        $query->orderBy($sortName, $sortBy);

        // 使用标准分页
        $perPage = $request->input('per_page', 15);
        $records = $query->paginate($perPage);

        return ResponseHelper::success($records);
    }

    /**
     * 获取部位详情
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $detail = AnalysisBodyPart::with('symptoms')->find($id);

        if (!$detail) {
            return ResponseHelper::error('部位不存在');
        }

        return ResponseHelper::success($detail);
    }

    /**
     * 保存部位信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'          => 'sometimes|integer|gte:0',
            'code'        => 'required|string|max:50',
            'name'        => 'required|string|max:100',
            'description' => 'nullable|string',
            'icon'        => 'nullable|string|max:255',
            'pet_type'    => 'required|in:general,dog,cat',
            'sort_order'  => 'nullable|integer|gte:0',
            'status'      => 'required|in:0,1',
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));

        // 检查唯一性
        $query = AnalysisBodyPart::where('code', $formData['code'])
            ->where('pet_type', $formData['pet_type']);

        if (isset($formData['id']) && $formData['id'] > 0) {
            $query->where('id', '!=', $formData['id']);
        }

        $exists = $query->exists();
        if ($exists) {
            return ResponseHelper::error('该宠物类型下已存在相同代码的部位');
        }

        // 保存数据
        if (isset($formData['id']) && $formData['id'] > 0) {
            $model = AnalysisBodyPart::find($formData['id']);
            if (!$model) {
                return ResponseHelper::error('部位不存在');
            }
            $model->update($formData);
        } else {
            $model = AnalysisBodyPart::create($formData);
        }

        return ResponseHelper::success($model);
    }

    /**
     * 删除部位
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $model = AnalysisBodyPart::find($id);

        if (!$model) {
            return ResponseHelper::error('部位不存在');
        }

        // 检查是否有关联的症状
        $symptomsCount = $model->symptoms()->count();
        if ($symptomsCount > 0) {
            return ResponseHelper::error('该部位下有关联的症状，请先删除症状');
        }

        $model->delete();

        return ResponseHelper::success(true);
    }
}
