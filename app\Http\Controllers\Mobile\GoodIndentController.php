<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\GoodCart;
use App\Models\GoodIndent;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * GoodIndent
 * 商品订单
 * Class GoodIndentController
 * @package App\Http\Controllers\Mobile
 */
class GoodIndentController extends Controller
{
    /**
     * GoodIndentList
     * 商品订单列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'state'     => 'nullable|between:' . GoodIndent::GOOD_INDENT_STATE_PAY . ',' . GoodIndent::GOOD_INDENT_STATE_REFUND_PROCESSING,
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = GoodIndent::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(GoodIndent::getDetailForApp($id, $request->user()->id));
    }

    /**
     * GoodIndentList
     * 确认账单
     */
    public function confirm(Request $request)
    {
        $rules = [
            'shipping_id'             => 'required|integer|gte:0',
            'commodity'               => 'required|array',
            'commodity.*.good_id'     => 'required|integer|gt:0',
            'commodity.*.good_sku_id' => 'required|integer|gte:0',
            'commodity.*.number'      => 'required|integer|gt:0',
            'commodity.*.pet_ids'     => 'nullable|number_combine',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['commodity'] = $request->input('commodity');
        $formData['user_id'] = $request->user()->id;
        try {
            $result = GoodIndent::confirmGoodIndent($formData);
            return ResponseHelper::success($result);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage());
        }
    }

    /**
     * GoodIndentList
     * 创建商品订单
     */
    public function create(Request $request)
    {
        $rules = [
            'shipping_id'             => 'required|integer|gte:0',
            'is_cart'                 => 'required|in:0,1',
            'pay_method'              => 'required|in:1,2,3',  //支付接口:1=收银台,2=支付组件,3=Payment
            'pay_type'                => 'required_if:pay_method,2,3|integer|between:800008,806808',
            'commodity'               => 'required|array',
            'commodity.*.good_id'     => 'required|integer|gt:0',
            'commodity.*.good_sku_id' => 'required|integer|gte:0',
            'commodity.*.number'      => 'required|integer|gt:0',
            'commodity.*.remark'      => 'nullable|string|max:200',
            'commodity.*.pet_ids'     => 'nullable|number_combine',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['commodity'] = $request->input('commodity');
        $formData['user_id'] = $request->user()->id;
        $result = GoodIndent::createGoodIndent($formData);
        return ResponseHelper::result(...$result);
    }

    public function getShoppingCart(Request $request)
    {
        $records = GoodCart::getShoppingCart($request->user()->id);
        return ResponseHelper::success($records);
    }

    public function getCartCount(Request $request)
    {
        $records = GoodCart::getCartCount($request->user()->id);
        return ResponseHelper::success($records);
    }

    /**
     * 添加商品到购物车
     */
    public function addShoppingCart(Request $request)
    {
        $rules = [
            'good_id'     => 'required|integer|gt:0',
            'good_sku_id' => 'required|integer|gte:0',
            'number'      => 'required|integer|gt:0',
            'name'        => 'nullable|max:60',
            'name_tc'     => 'nullable|max:60',
            'name_en'     => 'required|max:255',
            'image'       => 'nullable|url|pic|trust_host',
            'price'       => 'required|numeric|gte:0',
            'pet_ids'     => 'nullable|number_combine',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $result = GoodCart::addShoppingCart($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * 修改宠物ID
     */
    public function updatePetIds(Request $request)
    {
        $rules = [
            'good_id'     => 'required|integer|gt:0',
            'good_sku_id' => 'required|integer|gte:0',
            'pet_ids'     => 'nullable|number_combine',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $result = GoodCart::updatePetIds($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * 减少商品到购物车
     */
    public function removeShoppingCart(Request $request)
    {
        $rules = [
            'good_id'     => 'required|integer|gt:0',
            'good_sku_id' => 'required|integer|gte:0',
            'number'      => 'required|integer|gt:0',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $result = GoodCart::removeShoppingCart($formData);
        return ResponseHelper::result(...$result);
    }

    /**
     * 清空购物车
     */
    public function clearShoppingCart(Request $request)
    {
        $result = GoodCart::clearShoppingCart($request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodIndentPay
     * 重新付款
     * @queryParam  id int 订单ID
     */
    public function repay(Request $request)
    {
        $rules = [
            'id' => 'required|integer|gt:0',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = GoodIndent::repay($formData, $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodIndentReceipt
     * 确认收货
     * @queryParam  id int 订单ID
     */
    public function receipt(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodIndent::receipt($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodIndentCancel
     * 取消订单
     * @param $id
     */
    public function cancel(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodIndent::cancel($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodIndentDestroy
     * 删除订单
     * @param $id
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodIndent::del($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    /**
     * GoodIndentQuantity
     * 订单数量统计
     */
    public function nums(Request $request)
    {
        $data['user_id'] = $request->user()->id;
        return ResponseHelper::success(GoodIndent::nums($data));
    }
}
