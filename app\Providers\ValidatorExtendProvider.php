<?php

namespace App\Providers;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class ValidatorExtendProvider extends ServiceProvider
{
    public function boot()
    {
        //判断是否经度
        Validator::extend('longitude', function ($attribute, $value, $parameters) {
            return preg_match('/^(-?(1[0-7][0-9]|[0-9]{1,2})(\.[0-9]+)?|180(\.0+)?)$/', $value);
        });
        //判断是否纬度
        Validator::extend('latitude', function ($attribute, $value, $parameters) {
            return preg_match('/^(-?[0-9]{1,2}(?:\.[0-9]+)?|([0-8][0-9])(?:\.[0-9]+)?|90(?:\.0+)?)$/', $value);
        });
        //判断是否手机号
        Validator::extend('mobile', function ($attribute, $value, $parameters) {
            return preg_match('/^[456789]\d{7}$|^1[3-9]\d{9}$/', $value);
        });
        //判断是否图片
        Validator::extend('pic', function ($attribute, $value, $parameters) {
            $extension = Str::lower(pathinfo($value, PATHINFO_EXTENSION));
            return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']);
        });
        //判断是否周期
        Validator::extend('weeks', function ($attribute, $value, $parameters) {
            $weeks = is_array($value) ? $value : explode(',', $value);
            $valid_weeks = [0, 1, 2, 3, 4, 5, 6];
            foreach ($weeks as $week) {
                if (!in_array($week, $valid_weeks)) {
                    return false;
                }
            }
            return true;
        });
        Validator::extend('special_weeks', function ($attribute, $value, $parameters) {
            $weeks = is_array($value) ? $value : explode(',', $value);
            $valid_weeks = [0, 1, 2, 3, 4, 5, 6, 99, 98]; //99=红色假期,98=红色假期前一天
            foreach ($weeks as $week) {
                if (!in_array($week, $valid_weeks)) {
                    return false;
                }
            }
            return true;
        });
        //判断是否时段
        Validator::extend('clock', function ($attribute, $value, $parameters) {
            $match = preg_match('/^\d{2}:\d{2}$/', $value);
            if (!$match) {
                return is_numeric($value) && $value >= 0 && $value < 24; //可以填写 9.5 代表 09:30
            }
            $carbon = Carbon::createFromFormat('H:i', $value);
            return $carbon !== false && $carbon->format('H:i') === $value;
        });
        //判断是否颜色
        Validator::extend('color', function ($attribute, $value, $parameters) {
            return preg_match('/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})$/', $value);
        });
        //判断是否信任域名
        Validator::extend('trust_host', function ($attribute, $value, $parameters) {
            //上传图片要求必须是受信任的域名，不然会有Bool型SSRF漏洞
            //域名白名单
            $whiteList = [
                config('services.alioss.bucket') . '.' . config('services.alioss.server'),
                config('services.alioss.bucket') . '.' . config('services.alioss_serverinternal'),
            ];
            $arr = parse_url($value);
            return in_array($arr['host'] ?? '', $whiteList);
        });
        //判断合法id组合
        Validator::extend('number_combine', function ($attribute, $value, $parameters) {
            $v = explode(',', $value);
            $arr = array_filter($v, function ($item) use ($parameters) {
                return is_numeric($item) && (empty($parameters) || in_array($item, $parameters));
            });
            return count($arr) == count($v);
        });
        //判断合法中国人姓名
        Validator::extend('chinese_name', function ($attribute, $value, $parameters) {
            return preg_match('/^[\x{4e00}-\x{9fa5}·•]+$/u', $value);
        });
        //判断合法外国人姓名
        Validator::extend('en_name', function ($attribute, $value, $parameters) {
            return preg_match('/^[a-zA-Z\'\- ]+$/', $value);
        });
        //判断不能是纯数字
        Validator::extend('not_pure_number', function ($attribute, $value, $parameters) {
            return !is_numeric(trim($value));
        });
    }
}
