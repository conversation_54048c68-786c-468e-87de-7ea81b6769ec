# 通知管理接口文档

本文档描述了后台通知管理系统的API接口，用于测试和管理通知功能。

## 基础信息

- 基础URL: `/api/admin/notification-management`
- 所有请求需要管理员身份验证

## 系统通知管理

### 获取系统通知列表

**请求方式**：GET

**接口地址**：`/system-notifications`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| page | int | 否 | 1 | 页码 |
| per_page | int | 否 | 20 | 每页数量，最大100 |
| type | string | 否 | - | 通知类型，如post_liked, parent_added等 |
| user_id | int | 否 | - | 用户ID，用于筛选特定用户的通知 |

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 100,
        "sender_id": 101,
        "type": "post_liked",
        "title": "帖子点赞通知",
        "content": "用户小明点赞了您的帖子",
        "related_id": 200,
        "related_type": "post",
        "read_at": null,
        "status": 1,
        "created_at": "2023-04-30 10:00:00",
        "updated_at": "2023-04-30 10:00:00",
        "user": {
          "id": 100,
          "username": "用户A",
          "avatar": "https://example.com/avatar.jpg"
        },
        "sender": {
          "id": 101,
          "username": "小明",
          "avatar": "https://example.com/avatar2.jpg"
        }
      }
    ],
    "first_page_url": "...",
    "from": 1,
    "last_page": 1,
    "last_page_url": "...",
    "next_page_url": null,
    "path": "...",
    "per_page": 20,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### 创建系统通知或系统公告

**请求方式**：POST

**接口地址**：`/system-notification`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| user_id | int | 条件必填 | - | 单个用户接收通知的用户ID，与receiver_ids至少选一 |
| sender_id | int | 否 | - | 发送者ID |
| type | string | 是 | - | 通知类型，系统公告使用system_announcement |
| title | string | 是 | - | 通知标题 |
| content | string | 是 | - | 通知内容 |
| related_id | int | 否 | - | 相关对象ID |
| related_type | string | 否 | - | 相关对象类型 |
| send_push | boolean | 否 | false | 是否发送推送通知 |
| receiver_ids | array | 条件必填 | - | 接收者ID数组，用于发送给多个用户，与user_id至少选一 |
| all_users | boolean | 否 | false | 是否发送给所有用户，如果为true，则忽略user_id和receiver_ids |

**请求示例1**：发送给单个用户

```json
{
  "user_id": 100,
  "sender_id": 101,
  "type": "post_liked",
  "title": "帖子点赞通知",
  "content": "用户小明点赞了您的帖子",
  "related_id": 200,
  "related_type": "post",
  "send_push": true
}
```

**请求示例2**：发送系统公告给指定用户

```json
{
  "receiver_ids": [100, 101, 102],
  "sender_id": 1,
  "type": "system_announcement",
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-23:00进行维护，请提前做好准备。",
  "send_push": true
}
```

**请求示例3**：发送系统公告给所有用户

```json
{
  "all_users": true,
  "sender_id": 1,
  "type": "system_announcement",
  "title": "系统维护通知",
  "content": "系统将于今晚22:00-23:00进行维护，请提前做好准备。",
  "send_push": true
}
```

**响应示例**：

```json
{
  "status": 200,
  "message": "创建成功",
  "data": {
    "count": 3,
    "notifications": [
      {
        "id": 1,
        "user_id": 100,
        "sender_id": 1,
        "type": "system_announcement",
        "title": "系统维护通知",
        "content": "系统将于今晚22:00-23:00进行维护，请提前做好准备。",
        "related_id": null,
        "related_type": null, 
        "read_at": null,
        "status": 1,
        "created_at": "2023-04-30 10:00:00",
        "updated_at": "2023-04-30 10:00:00"
      },
      // 其他通知对象...
    ]
  }
}
```

### 删除系统通知

**请求方式**：POST

**接口地址**：`/delete-system-notification`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| notification_id | int | 是 | - | 通知ID |

**请求示例**：

```json
{
  "notification_id": 1
}
```

**响应示例**：

```json
{
  "status": 200,
  "message": "删除成功",
  "data": null
}
```

## 宠物提醒管理

### 获取宠物提醒列表

**请求方式**：GET

**接口地址**：`/pet-reminders`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| page | int | 否 | 1 | 页码 |
| per_page | int | 否 | 20 | 每页数量，最大100 |
| type | int | 否 | - | 提醒类型，1=生日提醒，2=环境提示 |
| user_id | int | 否 | - | 用户ID，用于筛选特定用户的提醒 |
| pet_id | int | 否 | - | 宠物ID，用于筛选特定宠物的提醒 |

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "user_id": 100,
        "pet_id": 50,
        "type": 1,
        "title": "宠物生日提醒",
        "content": "您的宠物小白还有7天就要过2岁生日啦！",
        "trigger_date": "2023-05-07",
        "read_at": null,
        "status": 1,
        "created_at": "2023-04-30 08:00:00",
        "updated_at": "2023-04-30 08:00:00",
        "user": {
          "id": 100,
          "username": "用户A",
          "avatar": "https://example.com/avatar.jpg"
        },
        "pet": {
          "id": 50,
          "name": "小白",
          "avatar": "https://example.com/pet_avatar.jpg",
          "birthday": "2021-05-07"
        }
      }
    ],
    "first_page_url": "...",
    "from": 1,
    "last_page": 1,
    "last_page_url": "...",
    "next_page_url": null,
    "path": "...",
    "per_page": 20,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

### 创建宠物提醒

**请求方式**：POST

**接口地址**：`/pet-reminder`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| user_id | int | 是 | - | 接收提醒的用户ID |
| pet_id | int | 是 | - | 宠物ID |
| type | int | 是 | - | 提醒类型，1=生日提醒，2=环境提示 |
| title | string | 是 | - | 提醒标题 |
| content | string | 是 | - | 提醒内容 |
| trigger_date | date | 是 | - | 触发日期，格式：YYYY-MM-DD |

**请求示例**：

```json
{
  "user_id": 100,
  "pet_id": 50,
  "type": 1,
  "title": "宠物生日提醒",
  "content": "您的宠物小白还有7天就要过2岁生日啦！",
  "trigger_date": "2023-05-07"
}
```

**响应示例**：

```json
{
  "status": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "user_id": 100,
    "pet_id": 50,
    "type": 1,
    "title": "宠物生日提醒",
    "content": "您的宠物小白还有7天就要过2岁生日啦！",
    "trigger_date": "2023-05-07",
    "read_at": null,
    "status": 1,
    "created_at": "2023-04-30 08:00:00",
    "updated_at": "2023-04-30 08:00:00"
  }
}
```

### 删除宠物提醒

**请求方式**：POST

**接口地址**：`/delete-pet-reminder`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| reminder_id | int | 是 | - | 提醒ID |

**请求示例**：

```json
{
  "reminder_id": 1
}
```

**响应示例**：

```json
{
  "status": 200,
  "message": "删除成功",
  "data": null
}
```

## 辅助接口

### 获取用户列表

**请求方式**：GET

**接口地址**：`/users`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| keyword | string | 否 | - | 搜索关键词，可搜索用户名、邮箱或手机号 |

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 100,
      "username": "用户A",
      "avatar": "https://example.com/avatar.jpg",
      "email": "<EMAIL>",
      "phone": "13800138000"
    },
    {
      "id": 101,
      "username": "小明",
      "avatar": "https://example.com/avatar2.jpg",
      "email": "<EMAIL>",
      "phone": "13800138001"
    }
  ]
}
```

### 获取宠物列表

**请求方式**：GET

**接口地址**：`/pets`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| user_id | int | 否 | - | 用户ID，用于获取特定用户的宠物 |
| keyword | string | 否 | - | 搜索关键词，可搜索宠物名称 |

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 50,
      "user_id": 100,
      "name": "小白",
      "avatar": "https://example.com/pet_avatar.jpg",
      "birthday": "2021-05-07",
      "type": 1
    },
    {
      "id": 51,
      "user_id": 100,
      "name": "小黑",
      "avatar": "https://example.com/pet_avatar2.jpg",
      "birthday": "2020-10-15",
      "type": 2
    }
  ]
}
```

### 获取通知类型列表

**请求方式**：GET

**接口地址**：`/notification-types`

**请求参数**：无

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": [
    {
      "value": "parent_added",
      "label": "添加家长通知"
    },
    {
      "value": "parent_removed",
      "label": "删除家长通知"
    },
    {
      "value": "post_followed",
      "label": "帖子被关注通知"
    },
    {
      "value": "post_liked",
      "label": "帖子被点赞通知"
    },
    {
      "value": "post_commented",
      "label": "帖子被评论通知"
    },
    {
      "value": "system_announcement",
      "label": "系统公告"
    }
  ]
}
```

### 获取提醒类型列表

**请求方式**：GET

**接口地址**：`/reminder-types`

**请求参数**：无

**响应示例**：

```json
{
  "status": 200,
  "message": "操作成功",
  "data": [
    {
      "value": 1,
      "label": "生日提醒"
    },
    {
      "value": 2,
      "label": "环境提示"
    }
  ]
}
```

## 测试推送

### 测试推送通知

**请求方式**：POST

**接口地址**：`/test-push`

**请求参数**：

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
| ----- | ---- | ---- | ----- | ---- |
| user_id | int | 是 | - | 接收推送的用户ID |
| title | string | 是 | - | 推送标题 |
| content | string | 是 | - | 推送内容 |
| type | string | 是 | - | 通知类型 |

**请求示例**：

```json
{
  "user_id": 100,
  "title": "测试推送",
  "content": "这是一条测试推送消息",
  "type": "system_announcement"
}
```

**响应示例**：

```json
{
  "status": 200,
  "message": "推送测试成功",
  "data": null
}
```

## 错误码说明

| 状态码 | 说明 |
| ----- | ---- |
| 200 | 请求成功 |
| 201 | 请求失败 |
| 401 | 未授权 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 使用示例

### 创建系统通知并测试

1. 首先获取用户列表，选择一个用户：
   ```
   GET /api/admin/notification-management/users
   ```

2. 获取通知类型列表：
   ```
   GET /api/admin/notification-management/notification-types
   ```

3. 创建系统通知：
   ```
   POST /api/admin/notification-management/system-notification
   {
     "user_id": 100,
     "type": "system_announcement",
     "title": "系统公告",
     "content": "系统将于今晚22:00-23:00进行维护，请提前做好准备。",
     "send_push": true
   }
   ```

4. 查看通知列表：
   ```
   GET /api/admin/notification-management/system-notifications?user_id=100
   ```

### 创建宠物提醒并测试

1. 获取用户列表，选择一个用户：
   ```
   GET /api/admin/notification-management/users
   ```

2. 获取该用户的宠物列表：
   ```
   GET /api/admin/notification-management/pets?user_id=100
   ```

3. 获取提醒类型列表：
   ```
   GET /api/admin/notification-management/reminder-types
   ```

4. 创建宠物生日提醒：
   ```
   POST /api/admin/notification-management/pet-reminder
   {
     "user_id": 100,
     "pet_id": 50,
     "type": 1,
     "title": "宠物生日提醒",
     "content": "您的宠物小白还有7天就要过2岁生日啦！",
     "trigger_date": "2023-05-07"
   }
   ```

5. 查看提醒列表：
   ```
   GET /api/admin/notification-management/pet-reminders?user_id=100
   ```
