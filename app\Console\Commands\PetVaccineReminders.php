<?php

namespace App\Console\Commands;

use App\Models\PetVaccineRecord;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\Log;

class PetVaccineReminders extends Command
{
    protected $signature = 'pet:vaccine-reminders {days=7 : 提前几天生成疫苗即将开始提醒}';


    protected $description = '生成预约即将开始提醒';

    public function handle()
    {
        $days = (int)$this->argument('days');
        $this->info("开始生成疫苗即将开始提醒，提前{$days}天通知");

        $totalCount = 0;
        PetVaccineRecord::where('next_vaccine_date', '=', now()->addDays($days)->format('Y-m-d'))
            ->where('created_at', '>=', now()->subYears(2))  //只查询2年内的
            ->select('id', 'pet_id', 'next_vaccine_date')
            ->with([
                'pet' => function (Builder $q) {
                    $q->select('id', 'name');
                },
            ])
            ->chunkById(100, function ($records) use ($days, &$totalCount) {
                foreach ($records as $record) {
                    PetVaccineRecord::addReminder($record, $days);

                    $totalCount++;
                }
            });

        $this->info("共生成 {$totalCount} 条疫苗即将开始提醒");
        Log::info("Generated {$totalCount} pet vaccine will start reminders");

        return self::SUCCESS;
    }
}
