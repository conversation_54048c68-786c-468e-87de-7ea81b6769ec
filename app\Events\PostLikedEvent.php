<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostLikedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 帖子ID
     *
     * @var int
     */
    public $postId;

    /**
     * 帖子所有者ID
     *
     * @var int
     */
    public $postOwnerId;

    /**
     * 点赞者ID
     *
     * @var int
     */
    public $likerId;

    /**
     * Create a new event instance.
     *
     * @param int $postId 帖子ID
     * @param int $postOwnerId 帖子所有者ID
     * @param int $likerId 点赞者ID
     * @return void
     */
    public function __construct($postId, $postOwnerId, $likerId)
    {
        $this->postId = $postId;
        $this->postOwnerId = $postOwnerId;
        $this->likerId = $likerId;
    }
}
