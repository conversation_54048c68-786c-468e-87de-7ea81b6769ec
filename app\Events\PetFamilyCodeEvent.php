<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetFamilyCodeEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 宠物 ID
     *
     * @var int
     */
    public $petId;

    /**
     * 宠物所有者 ID
     *
     * @var int
     */
    public $ownerId;

    /**
     * 家庭码
     *
     * @var string
     */
    public $code;

    /**
     * Create a new event instance.
     *
     * @param int $petId 宠物 ID
     * @param int $ownerId 宠物所有者 ID
     * @param string $code 家庭码
     * @return void
     */
    public function __construct($petId, $ownerId, $code)
    {
        $this->petId = $petId;
        $this->ownerId = $ownerId;
        $this->code = $code;
    }
}
