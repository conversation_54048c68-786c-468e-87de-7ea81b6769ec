# 🐕 品种管理使用示例

## 📋 常见操作示例

### 1. 新增品种（包含别名）

```bash
curl -X POST "http://your-domain.com/api/admin/petBreed/save" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "柯基犬",
    "name_tc": "柯基犬",
    "name_en": "Welsh Corgi",
    "type_id": 1,
    "size": "small",
    "hair_type": "short",
    "weight_max_male": 14.0,
    "weight_max_female": 12.0,
    "alias_names": [
      "柯基",
      "威尔士柯基",
      "Welsh Corgi",
      "Corgi",
      "短腿犬"
    ],
    "description": "短腿、长身的小型犬种"
  }'
```

### 2. 编辑品种别名

```bash
curl -X POST "http://your-domain.com/api/admin/petBreed/updateAliases" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "breed_id": 1,
    "alias_names": [
      "金毛",
      "金毛犬",
      "金毛寻回犬",
      "Golden Retriever",
      "Golden",
      "黄金猎犬",
      "黄金寻回犬"
    ]
  }'
```

### 3. 测试别名匹配效果

```bash
# 测试精确匹配
curl -X POST "http://your-domain.com/api/admin/petBreed/testAliasMatch" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "breed_id": 1,
    "test_name": "金毛"
  }'

# 测试模糊匹配
curl -X POST "http://your-domain.com/api/admin/petBreed/testAliasMatch" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "breed_id": 1,
    "test_name": "Golden"
  }'
```

### 4. 查看别名统计

```bash
curl -X GET "http://your-domain.com/api/admin/petBreed/aliasStatistics" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 5. 搜索品种（支持别名搜索）

```bash
curl -X GET "http://your-domain.com/api/admin/petBreed/searchBreeds?keyword=金毛&limit=5" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## 🎯 前端集成示例

### 1. Vue.js 品种管理组件

```vue
<template>
  <div class="breed-management">
    <!-- 品种列表 -->
    <div class="breed-list">
      <el-table :data="breeds" style="width: 100%">
        <el-table-column prop="name" label="品种名称" width="150" />
        <el-table-column prop="name_en" label="英文名称" width="200" />
        <el-table-column prop="type_name" label="类型" width="80" />
        <el-table-column prop="size" label="尺寸" width="80" />
        <el-table-column label="别名" width="300">
          <template #default="scope">
            <el-tag 
              v-for="alias in scope.row.alias_names" 
              :key="alias"
              size="small"
              style="margin-right: 5px;"
            >
              {{ alias }}
            </el-tag>
            <span v-if="!scope.row.alias_names || scope.row.alias_names.length === 0" 
                  class="text-gray-400">
              未配置别名
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editBreed(scope.row)">编辑</el-button>
            <el-button size="small" @click="editAliases(scope.row)">别名</el-button>
            <el-button size="small" @click="testMatch(scope.row)">测试</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 别名编辑对话框 -->
    <el-dialog v-model="aliasDialogVisible" title="编辑品种别名" width="600px">
      <div class="alias-editor">
        <p><strong>品种：</strong>{{ currentBreed.name }}</p>
        <el-form>
          <el-form-item label="别名列表">
            <div class="alias-input-group">
              <el-input
                v-for="(alias, index) in editingAliases"
                :key="index"
                v-model="editingAliases[index]"
                placeholder="输入别名"
                style="margin-bottom: 10px;"
              >
                <template #append>
                  <el-button @click="removeAlias(index)">删除</el-button>
                </template>
              </el-input>
              <el-button @click="addAlias" type="primary" plain>添加别名</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="aliasDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAliases">保存</el-button>
      </template>
    </el-dialog>

    <!-- 匹配测试对话框 -->
    <el-dialog v-model="testDialogVisible" title="测试别名匹配" width="500px">
      <div class="test-match">
        <p><strong>品种：</strong>{{ currentBreed.name }}</p>
        <el-form>
          <el-form-item label="测试名称">
            <el-input v-model="testName" placeholder="输入要测试的名称" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="runTest">测试匹配</el-button>
          </el-form-item>
        </el-form>
        
        <div v-if="testResult" class="test-result">
          <h4>测试结果：</h4>
          <p><strong>匹配状态：</strong>
            <el-tag :type="testResult.matched ? 'success' : 'danger'">
              {{ testResult.matched ? '匹配成功' : '匹配失败' }}
            </el-tag>
          </p>
          <p v-if="testResult.matched">
            <strong>匹配类型：</strong>{{ testResult.match_detail }}
          </p>
          <p><strong>优先级：</strong>{{ testResult.match_priority }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BreedManagement',
  data() {
    return {
      breeds: [],
      currentBreed: {},
      aliasDialogVisible: false,
      testDialogVisible: false,
      editingAliases: [],
      testName: '',
      testResult: null,
      pagination: {
        current_page: 1,
        per_page: 20,
        total: 0
      }
    }
  },
  mounted() {
    this.loadBreeds();
  },
  methods: {
    // 加载品种列表
    async loadBreeds() {
      try {
        const response = await this.$api.post('/admin/petBreed/list', {
          page: this.pagination.current_page,
          per_page: this.pagination.per_page
        });
        
        this.breeds = response.data.list;
        this.pagination = response.data.pagination;
      } catch (error) {
        this.$message.error('加载品种列表失败');
      }
    },

    // 编辑品种
    editBreed(breed) {
      // 跳转到品种编辑页面或打开编辑对话框
      this.$router.push(`/admin/breed/edit/${breed.id}`);
    },

    // 编辑别名
    editAliases(breed) {
      this.currentBreed = breed;
      this.editingAliases = [...(breed.alias_names || []), ''];
      this.aliasDialogVisible = true;
    },

    // 添加别名输入框
    addAlias() {
      this.editingAliases.push('');
    },

    // 删除别名
    removeAlias(index) {
      this.editingAliases.splice(index, 1);
    },

    // 保存别名
    async saveAliases() {
      try {
        const aliases = this.editingAliases.filter(alias => alias.trim() !== '');
        
        await this.$api.post('/admin/petBreed/updateAliases', {
          breed_id: this.currentBreed.id,
          alias_names: aliases
        });

        this.$message.success('别名保存成功');
        this.aliasDialogVisible = false;
        this.loadBreeds(); // 重新加载列表
      } catch (error) {
        this.$message.error('保存别名失败');
      }
    },

    // 测试匹配
    testMatch(breed) {
      this.currentBreed = breed;
      this.testName = '';
      this.testResult = null;
      this.testDialogVisible = true;
    },

    // 运行匹配测试
    async runTest() {
      if (!this.testName.trim()) {
        this.$message.warning('请输入测试名称');
        return;
      }

      try {
        const response = await this.$api.post('/admin/petBreed/testAliasMatch', {
          breed_id: this.currentBreed.id,
          test_name: this.testName
        });

        this.testResult = response.data;
      } catch (error) {
        this.$message.error('测试失败');
      }
    }
  }
}
</script>

<style scoped>
.breed-management {
  padding: 20px;
}

.alias-input-group {
  max-height: 300px;
  overflow-y: auto;
}

.test-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.text-gray-400 {
  color: #9ca3af;
}
</style>
```

### 2. 别名统计仪表板

```vue
<template>
  <div class="alias-statistics">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>总品种数</h3>
            <div class="stat-number">{{ statistics.total_breeds }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>已配置别名</h3>
            <div class="stat-number">{{ statistics.breeds_with_aliases }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card>
          <div class="stat-item">
            <h3>覆盖率</h3>
            <div class="stat-number">{{ statistics.overall_coverage_rate }}%</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card style="margin-top: 20px;">
      <h3>按类型统计</h3>
      <el-table :data="statistics.by_type" style="width: 100%">
        <el-table-column prop="type_name" label="宠物类型" />
        <el-table-column prop="total_breeds" label="总品种数" />
        <el-table-column prop="with_aliases" label="已配置别名" />
        <el-table-column prop="coverage_rate" label="覆盖率">
          <template #default="scope">
            <el-progress 
              :percentage="scope.row.coverage_rate" 
              :color="getProgressColor(scope.row.coverage_rate)"
            />
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'AliasStatistics',
  data() {
    return {
      statistics: {
        total_breeds: 0,
        breeds_with_aliases: 0,
        overall_coverage_rate: 0,
        by_type: []
      }
    }
  },
  mounted() {
    this.loadStatistics();
  },
  methods: {
    async loadStatistics() {
      try {
        const response = await this.$api.get('/admin/petBreed/aliasStatistics');
        this.statistics = response.data;
      } catch (error) {
        this.$message.error('加载统计数据失败');
      }
    },

    getProgressColor(percentage) {
      if (percentage >= 80) return '#67c23a';
      if (percentage >= 50) return '#e6a23c';
      return '#f56c6c';
    }
  }
}
</script>

<style scoped>
.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2em;
  font-weight: bold;
  color: #409eff;
}
</style>
```

## 📊 数据导入脚本示例

### 批量导入别名

```javascript
// 批量导入品种别名的脚本
const breedAliases = [
  {
    name: '金毛寻回犬',
    aliases: ['金毛', '金毛犬', 'Golden Retriever', 'Golden', '黄金猎犬']
  },
  {
    name: '拉布拉多犬',
    aliases: ['拉布拉多', '拉拉', 'Labrador', 'Lab', '拉布拉多寻回犬']
  },
  {
    name: '西伯利亚雪橇犬',
    aliases: ['哈士奇', '二哈', 'Husky', 'Siberian Husky', '雪橇犬']
  }
];

async function importAliases() {
  for (const breedData of breedAliases) {
    try {
      // 先搜索品种ID
      const searchResponse = await api.get('/admin/petBreed/searchBreeds', {
        params: { keyword: breedData.name, limit: 1 }
      });
      
      if (searchResponse.data.length > 0) {
        const breedId = searchResponse.data[0].id;
        
        // 更新别名
        await api.post('/admin/petBreed/updateAliases', {
          breed_id: breedId,
          alias_names: breedData.aliases
        });
        
        console.log(`✅ ${breedData.name} 别名更新成功`);
      } else {
        console.log(`❌ 未找到品种: ${breedData.name}`);
      }
    } catch (error) {
      console.error(`❌ ${breedData.name} 更新失败:`, error.message);
    }
  }
}

// 执行导入
importAliases();
```

**完整的品种管理功能已就绪，支持别名配置和匹配测试！** 🎯
