-- 宠物品种数据插入SQL语句
-- 生成时间: 2025-07-18
-- 注意：假设 pets_types 表中 犬=1，如果不同请修改 type_id

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('比格犬', '比高犬', 'Beagle', 1, 'medium', 'short', 12.5, 12.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('比熊犬', '比熊犬', 'Bichon Frise', 1, 'small', 'long', 4.5, 4.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('哈士奇', '哈士奇', 'Husky', 1, 'medium', 'medium', 23.5, 19.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('西施犬', '西施犬', 'Shih Tzu', 1, 'small', 'long', 5.5, 5.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('西高地白梗', '西高地白爹利', 'West Highland White Terrier', 1, 'small', 'long', 8.5, 6.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('杜宾犬', '杜宾犬', 'Doberman', 1, 'large', 'short', 33.5, 32, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('柴犬(豆柴)', '柴犬(豆柴)', 'Mame Shibu Inu', 1, 'small', 'short', 5.5, 5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('柴犬', '柴犬', 'Shibu Inu', 1, 'small', 'short', 10.5, 7.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('拉布拉多', '拉布拉多', 'Labrador Retriever', 1, 'large', 'short', 33, 28.5, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('法国斗牛犬', '法国斗牛犬', 'French Bulldog', 1, 'small', 'short', 11, 8.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('吉娃娃(长毛)', '芝娃娃(长毛)', 'Chihuahua (long hair)', 1, 'small', 'long', 2.5, 2.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('吉娃娃(短毛)', '芝娃娃(短毛)', 'Chihuahua (short hair)', 1, 'small', 'short', 2.5, 2.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('秋田犬', '秋田犬', 'Akita', 1, 'large', 'medium', 44.5, 36.5, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('约克夏梗犬', '约瑟爹利', 'Yorkshire Terrier', 1, 'small', 'long', 2, 2, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('柯基', '哥基', 'Corgi', 1, 'small', 'medium', 15.5, 13.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('杜宾犬(迷你)', '杜宾犬(迷你)', 'Miniature Pinscher', 1, 'small', 'short', 4.5, 4, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('哈士奇(迷你)', '哈士奇(迷你)', 'Miniature Husky', 1, 'small', 'medium', 13.5, 11.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('雪纳瑞(迷你)', '史纳莎(迷你)', 'Schnauzer (Mini)', 1, 'small', 'medium', 6, 6, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('雪纳瑞(标准)', '史纳莎(标准)', 'Schnauzer (Standard)', 1, 'medium', 'medium', 17, 17, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('雪纳瑞(巨型)', '史纳莎(巨型)', 'Schnauzer (Giant)', 1, 'large', 'medium', 37.5, 34, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('博美', '博美', 'Pomeranian', 1, 'small', 'medium', 2.25, 2.25, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('斑点狗', '班点狗', 'Dalmatian', 1, 'medium', 'short', 24, 24, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('贵宾犬(茶杯)', '贵宾犬(茶杯)', 'Poodle (Teacup)', 1, 'small', 'long', 2.7, 2.7, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('贵宾犬(玩具)', '贵宾犬(玩具)', 'Poodle (Toy)', 1, 'small', 'medium', 3.25, 3.25, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('贵宾犬(迷你)', '贵宾犬(迷你)', 'Poodle (Mini)', 1, 'medium', 'medium', 7.5, 7.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('贵宾犬(标准)', '贵宾犬(标准)', 'Poodle (Standard)', 1, 'large', 'medium', 26, 23.5, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('金毛寻回犬', '金毛寻回犬', 'Golden Retriever', 1, 'large', 'long', 32, 27.5, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('马尔济斯犬', '魔天使', 'Maltese', 1, 'small', 'long', 2.5, 2.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('德国牧羊犬', '德国牧羊犬', 'German Shepherd', 1, 'large', 'short', 38.5, 38.5, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('蝴蝶犬', '蝴蝶犬', 'Papillon', 1, 'small', 'long', 4.25, 4.25, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('杰克罗素梗', '积罗素', 'Jack Russell Terrier', 1, 'small', 'short', 7, 7, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('松狮犬', '松狮犬', 'Chow Chow', 1, 'medium', 'medium', 26, 26, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('腊肠犬(长毛迷你)', '腊肠犬(长毛迷你)', 'Dachshund (Mini, long hair)', 1, 'small', 'long', 5, 5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('腊肠犬(短毛迷你)', '腊肠犬(短毛迷你)', 'Dachshund (Mini, short hair)', 1, 'small', 'short', 5, 5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('腊肠犬(长毛标准)', '腊肠犬(长毛标准)', 'Dachshund (long hair)', 1, 'medium', 'long', 10.5, 10.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('腊肠犬(短毛标准)', '腊肠犬(短毛标准)', 'Dachshund (short hair)', 1, 'medium', 'short', 10.5, 10.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('边境牧羊犬', '边境牧羊犬', 'Border Collie', 1, 'medium', 'medium', 17, 15.5, '{"juvenile":{"start":0,"end":12},"adult":{"start":13,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

INSERT INTO pets_breeds (name, name_tc, name_en, type_id, size, hair_type, weight_max_male, weight_max_female, month_ranges, created_at, updated_at) VALUES
('格力犬', '灵缇', 'Greyhound', 1, 'large', 'short', 30.5, 28, '{"juvenile":{"start":0,"end":24},"adult":{"start":25,"end":84},"senior":{"start":85,"end":120},"elderly":{"start":121,"end":null}}', NOW(), NOW());

-- 插入完成，共 38 个品种
