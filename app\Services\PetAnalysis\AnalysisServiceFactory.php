<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Exception;

class AnalysisServiceFactory
{
    /**
     * 创建分析服务实例
     *
     * @param string            $type 分析类型
     * @param AliBailianService $aliBailianService
     * @return AnalysisServiceInterface
     * @throws Exception
     */
    public static function create(string $type, AliBailianService $aliBailianService): AnalysisServiceInterface
    {
        switch ($type) {
            case 'food':
                return new FoodAnalysisService($aliBailianService);

            case 'stool':
                return new StoolAnalysisService($aliBailianService);

            case 'health':
                return new HealthAnalysisService($aliBailianService);

            case 'ingredient':
                return new IngredientAnalysisService($aliBailianService);

            default:
                throw new Exception("不支持的分析类型: $type");
        }
    }

    /**
     * 获取所有可用的分析类型
     *
     * @return array
     */
    public static function getAvailableTypes(): array
    {
        return [
            'food'       => '食物分析',
            'stool'      => '排泄物分析',
            'health'     => '健康状态分析',
            'ingredient' => '宠物粮成分表分析',
        ];
    }
}
