<?php

namespace App\Listeners;

use App\Events\PostFavoritedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPostFavoritedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PostFavoritedEvent  $event
     * @return void
     */
    public function handle(PostFavoritedEvent $event)
    {
        try {
            // 如果帖子所有者和收藏者是同一人，不发送通知
            if ($event->postOwnerId == $event->favoriterId) {
                return;
            }

            // 获取收藏者信息
            $favoriter = AppUser::find($event->favoriterId);

            if (!$favoriter) {
                Log::error('Post favorited notification failed: Favoriter not found', [
                    'favoriter_id' => $event->favoriterId,
                    'post_id' => $event->postId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->postOwnerId, // 接收通知的用户ID（帖子所有者）
                NotificationSetting::TYPE_POST_FAVORITED, // 通知类型
                '帖子收藏通知',
                "用户 {$favoriter->username} 收藏了您的帖子",
                $event->favoriterId, // 发送者ID（收藏者）
                $event->postId, // 相关ID（帖子ID）
                NotificationSetting::RELATION_TYPE_CONTENT // 相关类型
            );

            Log::info('Post favorited notification sent', [
                'post_id' => $event->postId,
                'owner_id' => $event->postOwnerId,
                'favoriter_id' => $event->favoriterId
            ]);
        } catch (\Exception $e) {
            Log::error('Post favorited notification failed', [
                'error' => $e->getMessage(),
                'post_id' => $event->postId,
                'owner_id' => $event->postOwnerId,
                'favoriter_id' => $event->favoriterId
            ]);
        }
    }
}
