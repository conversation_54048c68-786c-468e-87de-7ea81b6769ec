# 宠物营养需求分析API文档

基于AAFCO标准的宠物营养需求计算接口

## 接口概述

本系统提供基于AAFCO（美国饲料控制官员协会）标准的宠物营养需求计算功能，支持狗和猫的营养分析。

## API接口

### 1. 单只宠物营养需求分析

**接口地址：** `GET /api/mobile/pet/nutrition-requirements`

**请求参数：**
```json
{
    "pet_id": 123
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "basic_info": {
            "pet_name": "小金毛",
            "pet_type": "狗",
            "pet_breed": "金毛寻回犬",
            "age": 3,
            "age_stage": "adult",
            "weight": 25.5,
            "sex": "公",
            "neutered": "已绝育",
            "is_pregnant": "未怀孕",
            "is_ill": "健康",
            "weight_status": "理想",
            "active_status": "中等活动量"
        },
        "energy_info": {
            "daily_energy_demand": 1247.5,
            "unit": "kcal/天",
            "calculation_basis": "基于AAFCO标准，每1000kcal ME计算"
        },
        "macronutrients": {
            "protein": {
                "name": "蛋白质",
                "name_en": "Protein",
                "daily_requirement": {
                    "min": 56.14
                },
                "unit": "g",
                "function": "构建和修复肌肉组织，维持免疫系统功能，提供能量",
                "food_sources": ["鸡肉", "牛肉", "鱼肉", "鸡蛋", "豆类"]
            },
            "fat": {
                "name": "脂肪",
                "name_en": "Fat",
                "daily_requirement": {
                    "min": 17.22
                },
                "unit": "g",
                "function": "提供必需脂肪酸，促进脂溶性维生素吸收，维持皮肤和毛发健康",
                "food_sources": ["鱼油", "鸡脂", "植物油", "坚果"]
            }
        },
        "vitamins": {
            "vitamin_a": {
                "name": "维生素A",
                "name_en": "Vitamin A",
                "daily_requirement": {
                    "min": 1890.24
                },
                "unit": "IU",
                "function": "维持视力健康，支持免疫系统，促进细胞生长和分化",
                "food_sources": ["胡萝卜", "菠菜", "肝脏", "鸡蛋"]
            }
        },
        "minerals": {
            "calcium": {
                "name": "钙",
                "name_en": "Calcium",
                "daily_requirement": {
                    "min": 1.56,
                    "max": 5.61
                },
                "unit": "g",
                "function": "骨骼和牙齿发育，肌肉收缩，神经传导",
                "food_sources": ["骨粉", "奶制品", "绿叶蔬菜"]
            }
        },
        "amino_acids": {
            "arginine": {
                "name": "精氨酸",
                "name_en": "Arginine",
                "daily_requirement": {
                    "min": 1.60
                },
                "unit": "g",
                "function": "免疫功能，伤口愈合，蛋白质合成",
                "food_sources": ["肉类", "鱼类", "坚果"]
            }
        },
        "special_adjustments": [
            {
                "condition": "绝育状态",
                "adjustment": "代谢率可能降低，需要适当减少热量摄入",
                "note": "建议监测体重变化，调整食量"
            }
        ],
        "recommendations": {
            "feeding_guidelines": [
                "选择高质量的商业狗粮，确保符合AAFCO标准",
                "根据年龄、体重和活动量调整食量",
                "定时定量喂食，避免自由采食",
                "提供充足的清洁饮水"
            ],
            "foods_to_avoid": [
                "巧克力 - 含有可可碱，对狗有毒",
                "葡萄和葡萄干 - 可能导致肾衰竭",
                "洋葱和大蒜 - 可能导致贫血"
            ]
        },
        "calculated_at": "2024-01-15 10:30:00",
        "calculation_note": "基于AAFCO标准计算，仅供参考，具体营养需求请咨询兽医"
    }
}
```

### 2. 批量宠物营养需求分析

**接口地址：** `POST /api/mobile/pet/batch-nutrition-requirements`

**请求参数：**
```json
{
    "pet_ids": [123, 456, 789]
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "results": [
            {
                "pet_id": 123,
                "pet_name": "小金毛",
                "nutrition_requirements": {
                    // 完整的营养需求数据（同单只宠物接口）
                }
            },
            {
                "pet_id": 456,
                "pet_name": "小橘猫",
                "nutrition_requirements": {
                    // 完整的营养需求数据
                }
            }
        ],
        "calculated_at": "2024-01-15 10:30:00",
        "calculation_note": "基于AAFCO标准计算，仅供参考，具体营养需求请咨询兽医",
        "errors": [
            "宠物 小病猫: 生病状态下建议咨询兽医"
        ]
    }
}
```

## 营养标准说明

### AAFCO标准
- 基于每1000kcal代谢能(ME)的营养需求
- 分为不同年龄阶段：幼年期、成年期、老年期
- 包含最小需求量和最大安全量

### 年龄阶段划分
**狗：**
- 幼犬(puppy): 0-12个月
- 成犬(adult): 1-7岁
- 老犬(senior): 7岁以上

**猫：**
- 幼猫(kitten): 0-12个月
- 成猫(adult): 1-7岁
- 老猫(senior): 7岁以上

### 营养素分类
1. **宏量营养素：** 蛋白质、脂肪、碳水化合物、纤维
2. **维生素：** A、D、E、K、B族维生素、胆碱
3. **矿物质：** 钙、磷、钠、钾、镁、铁、锌、铜、锰、硒、碘
4. **氨基酸：** 必需氨基酸（猫还包括牛磺酸）

## 特殊情况调整

### 生理状态
- **怀孕期：** 蛋白质和钙需求增加25-50%
- **哺乳期：** 所有营养需求增加2-4倍
- **绝育状态：** 代谢率降低，需要调整热量摄入

### 体重状态
- **超重/肥胖：** 减少总热量，增加蛋白质比例
- **体重不足：** 增加总热量，确保营养均衡

### 活动水平
- **高活动量：** 增加能量和蛋白质需求
- **低活动量：** 适当减少热量摄入

## 使用注意事项

1. **仅供参考：** 计算结果基于AAFCO标准，实际营养需求可能因个体差异而不同
2. **兽医咨询：** 特殊情况（疾病、怀孕、哺乳等）建议咨询专业兽医
3. **定期监测：** 需要定期监测宠物体重和健康状况
4. **食物安全：** 注意避免对宠物有害的食物

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 没有查看权限
- `404`: 宠物不存在
- `500`: 服务器内部错误

## 技术实现

- 基于现有的能量需求计算系统
- 使用AAFCO官方营养标准数据
- 支持狗和猫的不同营养需求
- 考虑年龄、体重、活动状态等因素
- 提供完整的营养建议和食物来源信息
