<?php

namespace App\Helpers;

use Firebase\JWT\JWT;

/**
 * GoEasy工具类
 * <AUTHOR>
 */
class GoEasy
{
    private $url;
    private $restKey;
    private $secretKey;

    public function __construct($config)
    {
        $this->url = $config['url'] ?? 'rest-hz.goeasy.io';
        $this->restKey = $config['rest_key'];
        $this->secretKey = $config['secret_key'];
    }

    /**
     * GoEasy-OTP(One-time password)是GoEasy对app key的安全保护措施，安全可靠，简单易用！ 将会有效杜绝他人通过在页面上获取app key的方式，进行非法操作
     * https://docs.goeasy.io/2.x/im/shared-otp
     * @return false|string
     */
    public function getOTP()
    {
        list($t1, $t2) = explode(' ', microtime());
        $text = (float)sprintf('%.0f', (floatval($t1) + floatval($t2)) * 1000);
        $text = "000" . $text;
        return openssl_encrypt($text, 'AES-128-ECB', $this->secretKey, 2, '');
    }

    /**
     * IM授权与访问控制
     * @doc https://docs.goeasy.io/2.x/im/shared-security/authorization/im
     * @param string $id               必须与connect GoEasy时传入的id一致
     * @param string $protectedChannel 要授权的channel
     * @param int    $exp              token的有效时间，最长不能超过3小时，为了安全，GoEasy不接受有效时间大于3小时的access token
     * @return mixed
     */
    public function pubSubAccessToken(string $id, string $protectedChannel, int $exp)
    {
        $payload = array(
            "id"      => $id,
            "channel" => $protectedChannel,
            "w"       => true,//写权限，是否允许publish
            "r"       => true,//读全新，是否允许subscriber
            "exp"     => $exp,
        );
        return JWT::encode($payload, $this->secretKey, 'HS256');
    }

    /**
     * Webhook签名验证
     * @doc https://docs.goeasy.io/2.x/im/shared-webhook/webhook
     * @param string $content
     * @return string
     */
    public function getSignature(string $content)
    {
        return base64_encode(hash_hmac("sha1", $content, $this->secretKey, true));
    }

    /**
     * Websocket服务端发送 - PubSub（Websocket）
     * @doc https://docs.goeasy.io/2.x/pubsub/message/rest-publish
     * @param string $channel      渠道
     * @param string $content      内容
     * @param array  $notification 通知 ['title'=>'', 'body'=>'']
     * @param int    $qos          离线补发:为1启用补发，0无需补发，默认为0。SDK需升级至2.6.2以上
     * @return bool|string
     */
    public function publish(string $channel, string $content, array $notification = [], int $qos = 0)
    {
        $data = [
            'channel' => $channel,
            'content' => $content,
            'qos'     => $qos,
        ];
        if ($notification) {
            $data['notification_title'] = $notification['title'];
            $data['notification_body'] = $notification['body'];
        }
        return $this->sendRequest('/v2/pubsub/publish', $data);
    }

    /**
     * Rest接口发送私聊、群聊 - IM即时通讯
     * @doc https://docs.goeasy.io/2.x/im/rest-api/send-message
     * @param string $senderId     发送方的UserId
     * @param array  $senderData   发送方的user data,用于更新会话列表conversation.data {'avatar':'/www/xxx.png','nickname':'Neo'}
     * @param array  $to           发送对象 type='private/group', id='userId/groupId', data={avatar: '/images/Avatar-1.png', name: 'Mattie'}, receivers=[{id: 'userId/groupId', data: {avatar: '/images/Avatar-1.png', name: 'Mattie'}}]
     * @param string $type         消息类型：image/video/audio/file/自定义类型
     * @param string $payload      消息内容：若type为text，必须是字符串；若自定义类型，必须传入对象
     * @param array  $notification 通知：title,body
     * @return bool|string
     */
    public function sendMessage(string $senderId, array $senderData, array $to, string $type, $payload, $notification = null)
    {
        $data = [
            'sender_id'   => $senderId,
            'sender_data' => $senderData,
            'to'          => $to,
            'type'        => $type,
            'payload'     => $payload,
        ];
        if ($notification) {
            $data['notification'] = $notification;
        }
        return $this->sendRequest('/v2/im/message', $data);
    }

    /**
     * 群消息订阅和取消订阅
     * @param array $userIds  用户id列表，每次最多不超过10个
     * @param array $groupIds 群id列表，每次最多不超过10个
     * @return bool|string
     */
    public function subscribeGroups(array $userIds, array $groupIds)
    {
        $data = [
            'userIds'  => $userIds,
            'groupIds' => $groupIds,
        ];
        return $this->sendRequest('/v2/im/subscribe-groups', $data);
    }

    /**
     * 取消订阅群消息
     * @param array $userIds  用户id列表，每次最多不超过10个
     * @param array $groupIds 群id列表，每次最多不超过10个
     * @return bool|string
     */
    public function unsubscribeGroups(array $userIds, array $groupIds)
    {
        $data = [
            'userIds'  => $userIds,
            'groupIds' => $groupIds,
        ];
        return $this->sendRequest('/v2/im/unsubscribe-groups', $data);
    }

    /**
     * 管理员撤回消息
     * @doc https://docs.goeasy.io/2.x/im/rest-api/recall
     * @param array      $messages 要撤回消息的时间戳列表，每次最多撤回20条消息
     * @param string     $userAId  私聊双方其中一方的userId，必须与userBId成对传入，不区分发送方和接收方
     * @param string     $userBId  私聊双方其中一方的userId，必须与userAId成对传入，不区分发送方和接收方
     * @param array|null $recaller 撤回方 {"id": "user001", "data": {"avatar": "/static/images/Avatar-2.png", "name": "Wallace"}}
     * @return bool|string
     */
    public function recallUserMessage(array $messages, string $userAId, string $userBId, $recaller = null)
    {
        $data = [
            'messages' => $messages,
            'userAId'  => $userAId,
            'userBId'  => $userBId,
        ];
        if ($recaller) {
            $data['recaller'] = $recaller;
        }
        return $this->sendRequest('/v2/im/history/recall', $data);
    }


    /**
     * 管理员撤回消息
     * @doc https://docs.goeasy.io/2.x/im/rest-api/recall
     * @param array      $messages 要撤回消息的时间戳列表，每次最多撤回20条消息
     * @param string     $groupId  群聊的groupId
     * @param array|null $recaller 撤回方 {"id": "user001", "data": {"avatar": "/static/images/Avatar-2.png", "name": "Wallace"}}
     * @return bool|string
     */
    public function recallGroupMessage(array $messages, string $groupId, $recaller = null)
    {
        $data = [
            'messages' => $messages,
            'groupId'  => $groupId,
        ];
        if ($recaller) {
            $data['recaller'] = $recaller;
        }
        return $this->sendRequest('/v2/im/history/recall', $data);
    }

    private function sendRequest(string $endpoint, array $data)
    {
        $url = $this->url . $endpoint;
        $curl = curl_init($url);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        $headers = array(
            "Accept: application/json",
            "Content-Type: application/json",
        );
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        $data['appkey'] = $this->restKey;
        $params = json_encode($data, JSON_UNESCAPED_UNICODE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $params);
        $resp = curl_exec($curl);
        curl_close($curl);
        return json_decode($resp, true);
    }
}
