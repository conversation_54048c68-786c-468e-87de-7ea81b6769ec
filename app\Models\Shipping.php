<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Helpers\RedisLock;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Shipping extends Model
{
    const SHIPPING_DEFAULTS_YES = 1; //默认：是
    const SHIPPING_DEFAULTS_NO = 0; //默认：否
    const MAX_SHIPPING_COUNT = 1000;

    protected $fillable = [
        'user_id',
        'cellphone',
        'name',
        'location',
        'address',
        'latitude',
        'longitude',
        'house',
        'defaults',
    ];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function getList($search_data = array())
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";

        return self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('cellphone', 'like', "%$keyword%");
            });
        })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetailForApp($id, $userId)
    {
        if ($id > 0) {
            return self::where('id', $id)->where('user_id', $userId)->first();
        } else {
            return self::where('defaults', self::SHIPPING_DEFAULTS_YES)->where('user_id', $userId)->first();
        }
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $lock = RedisLock::lock($redis, 'shipping_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            $count = self::where('user_id', $userId)->count();
            if ($count == 0) {
                $data['defaults'] = self::SHIPPING_DEFAULTS_YES;
            } else {
                $count <= self::MAX_SHIPPING_COUNT or throw_if(true, 'RuntimeException', "最多只能保存" . self::MAX_SHIPPING_COUNT . "条地址");
            }
            $id = $data['id'] ?? 0;
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
            } else {
                self::getDetailForApp($data['id'], $userId) or throw_if(true, 'RuntimeException', "地址：{$id}不存在或已删除");
                $success = self::where('id', $id)->update($data);
            }
            if ($data['defaults'] == 1 && $count > 0) {
                self::where('user_id', $userId)->where('id', '<>', $id)->update(['defaults' => self::SHIPPING_DEFAULTS_NO]);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'shipping_' . $userId);
        }
        return [$success, $record, $message];
    }

    public static function del($id, $userId = null)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        $redis = redis();
        try {
            $lock = RedisLock::lock($redis, 'shipping_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            $lock = RedisLock::lock($redis, 'shipping_' . $userId);
            $info = self::getDetailForApp($id, $userId) or throw_if(true, 'RuntimeException', "地址：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'shipping_' . $userId);
        }
        return [$success, $record, $message];
    }

    public static function getCarriage($shipping, $carts)
    {
        $error = '';
        $carriage_amount = 0;
        $list = [];
        if ($shipping) {
            if (count(explode('省', $shipping['address'])) > 1) {
                $name = explode('省', $shipping['address'])[0] . '省';
            } else {
                if (count(explode('自治区', $shipping['address'])) > 1) {
                    $name = explode('自治区', $shipping['address'])[0] . '自治区';
                } else {
                    $name = explode('市', $shipping['address'])[0] . '市';
                }
            }
            $provinces = config('provinces');
            $value = '';
            foreach ($provinces as $p) {
                if ($p['label'] == $name) {
                    $value = $p['value'];
                    break;
                }
            }
            $goods = Good::withTrashed()->whereIn('id', array_column($carts, 'good_id'))
                ->select('id', 'name', 'is_show', 'freight_id', 'weight', 'deleted_at')
                ->get()->toArray();
            $goods = array_column($goods, null, 'id');
            $skuIds = array_filter(array_column($carts, 'good_sku_id'));
            if (!empty($skuIds)) {
                $goodSkus = GoodSku::withTrashed()
                    ->whereIn('id', $skuIds)->select('id', 'weight', 'deleted_at')
                    ->get()->toArray();
                $goodSkus = array_column($goodSkus, null, 'id');
            } else {
                $goodSkus = [];
            }
            foreach ($carts as $cart) {
                $Good = $goods[$cart['good_id']];
                if ($Good['is_show'] != Good::GOOD_SHOW_PUTAWAY || $Good['deleted_at']) {
                    $error = '商品' . $Good['name'] . '已失效';
                    break;
                }
                $goodWeight = $Good['weight'];
                if ($cart['good_sku_id'] > 0) {
                    if (!isset($goodSkus[$cart['good_sku_id']])) {
                        $error = '商品规格' . $Good['name'] . '不存在';
                        break;
                    }
                    $GoodSku = $goodSkus[$cart['good_sku_id']];
                    if ($GoodSku['deleted_at']) {
                        $error = '商品规格' . $Good['name'] . '已失效';
                        break;
                    }
                    $goodWeight = $GoodSku['weight'];
                }
                $weightTotal = +bcmul($cart['number'], $goodWeight, 6);
                $freightId = $Good['freight_id'];
                if (array_key_exists($freightId, $list)) {
                    $list[$freightId]['number'] += $cart['number'];
                    $list[$freightId]['weight'] = +bcadd($list[$freightId]['weight'], $weightTotal, 6);
                } else {
                    $list[$freightId] = [
                        'number'   => $cart['number'],
                        'weight'   => $weightTotal,
                        'carriage' => 0,
                    ];
                }
            }
            if (!$error) {
                if ($value) {
                    $freights = Freight::with(['FreightWay'])->whereIn('id', array_keys($list))->get()->toArray();
                    $freights = array_column($freights, null, 'id');
                    foreach ($list as $freight_id => $info) {
                        if (!isset($freights[$freight_id])) {
                            $error = '运费模板' . $freight_id . '不存在';
                            break;
                        }
                        $freight = $freights[$freight_id];
                        $pinkage = json_decode($freight['pinkage'], true);
                        $carriage_total = 0;
                        if (!in_array($value, $pinkage)) { //不包邮
                            foreach ($freight['freightWay'] as $way) {
                                $location = json_decode($way['location'], true);
                                if (in_array($value, $location)) { //获取不包邮实际运费
                                    $numVal = $freight['valuation'] == 1 ? $info['number'] : $info['weight'];
                                    $carriage_total = $way['first_cost'];
                                    $continue_num = ceil(($numVal - $way['first_piece']) / $way['add_piece']);
                                    if ($continue_num > 0) {
                                        $carriage_total = +bcadd($carriage_total, bcmul($continue_num, $way['add_cost'], 2), 2);
                                    }
                                    break;
                                }
                            }
                        }
                        $list[$freight_id]['freight'] = $freight;
                        $list[$freight_id]['carriage'] = $carriage_total; //记录每项运费模板对应的金额
                        $carriage_amount = +bcadd($carriage_amount, $carriage_total, 2);
                    }
                } else {
                    $error = '不支持的配送地址';
                }
            }
        } else {
            $error = '请选择配送地址';
        }
        return [
            'error'    => $error,
            'carriage' => $carriage_amount,
            'list'     => $list,
        ];
    }
}
