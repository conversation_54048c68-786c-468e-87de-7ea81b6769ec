<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class StreamResponseTest extends TestCase
{
    /**
     * 测试宠物分析流式响应
     *
     * @return void
     */
    public function testPetAnalysisStreamResponse()
    {
        $response = $this->post('/api/mobile/petChat/stool', [
            'message' => '我家狗狗的便便有点黑色',
            'stream' => true,
        ]);

        // 检查响应状态码
        $response->assertStatus(200);

        // 检查响应头
        $this->assertTrue(
            strpos($response->headers->get('Content-Type'), 'text/event-stream') !== false,
            'Content-Type header should contain text/event-stream'
        );
        $this->assertTrue(
            strpos($response->headers->get('Cache-Control'), 'no-cache') !== false,
            'Cache-Control header should contain no-cache'
        );
        $this->assertTrue(
            $response->headers->get('Connection') === 'keep-alive',
            'Connection header should be keep-alive'
        );
        $this->assertTrue(
            $response->headers->get('X-Accel-Buffering') === 'no',
            'X-Accel-Buffering header should be no'
        );

        // 输出响应内容以便检查
        echo "Response content: " . $response->getContent() . "\n";
    }
}
