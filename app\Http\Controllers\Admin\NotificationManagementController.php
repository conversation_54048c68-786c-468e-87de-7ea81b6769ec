<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Pet;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\PetReminder;
use App\Models\NotificationLog;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 通知提醒管理控制器
 * 用于后台管理和测试通知功能
 */
class NotificationManagementController extends Controller
{
    /**
     * 获取系统通知列表
     */
    public function getSystemNotifications(Request $request)
    {
        $validated = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
            'type'     => 'nullable|string',
            'user_id'  => 'nullable|integer'
        ]);

        $query = NotificationLog::with(['user', 'sender'])
            ->orderBy('id', 'desc');

        if (isset($validated['type'])) {
            $query->where('type', $validated['type']);
        }

        if (isset($validated['user_id'])) {
            $query->where('user_id', $validated['user_id']);
        }

        $notifications = $query->paginate(
            $validated['per_page'] ?? 20,
            ['*'],
            'page',
            $validated['page'] ?? 1
        );

        return ResponseHelper::success($notifications);
    }

    /**
     * 获取宠物提醒列表
     */
    public function getPetReminders(Request $request)
    {
        $validated = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
            'type'     => 'nullable|integer|in:1,2,3',
            'user_id'  => 'nullable|integer',
            'pet_id'   => 'nullable|integer'
        ]);

        $query = PetReminder::with(['user', 'pet'])
            ->orderBy('id', 'desc');

        if (isset($validated['type'])) {
            $query->where('type', $validated['type']);
        }

        if (isset($validated['user_id'])) {
            $query->where('user_id', $validated['user_id']);
        }

        if (isset($validated['pet_id'])) {
            $query->where('pet_id', $validated['pet_id']);
        }

        $reminders = $query->paginate(
            $validated['per_page'] ?? 20,
            ['*'],
            'page',
            $validated['page'] ?? 1
        );

        return ResponseHelper::success($reminders);
    }

    /**
     * 获取通知类型列表
     */
    public function getNotificationTypes()
    {
        $typeConstants = NotificationSetting::getConstantsWithComments();
        $types = [];
        foreach ($typeConstants as $k => $info) {
            if (str_starts_with($k, 'TYPE_')) {
                $types[] = [
                    'value' => $info['value'],
                    'label' => $info['comment'],
                ];
            }
        }
        return ResponseHelper::success($types);
    }

    /**
     * 获取提醒类型列表
     */
    public function getReminderTypes()
    {
        $typeConstants = PetReminder::getConstantsWithComments();
        $types = [];
        foreach ($typeConstants as $k => $info) {
            if (str_starts_with($k, 'TYPE_')) {
                $types[] = [
                    'value' => $info['value'],
                    'label' => $info['comment'],
                ];
            }
        }
        return ResponseHelper::success($types);
    }

    /**
     * 创建系统通知
     * 支持创建普通通知和系统公告
     * 系统公告可以发送给所有用户或指定用户
     */
    public function createSystemNotification(Request $request)
    {
        $validated = $request->validate([
            'user_id'        => 'required_without:receiver_ids|nullable|integer|exists:app_users,id',
            'sender_id'      => 'nullable|integer|exists:app_users,id',
            'type'           => 'required|string',
            'title'          => 'required|string|max:100',
            'content'        => 'required|string|max:500',
            'related_id'     => 'nullable|integer',
            'related_type'   => 'nullable|string|max:50',
            'send_push'      => 'nullable|boolean',
            'receiver_ids'   => 'required_without:user_id|nullable|array',
            'receiver_ids.*' => 'integer|gt:0',
            'all_users'      => 'nullable|boolean'
        ]);

        try {
            DB::beginTransaction();

            $createdNotifications = [];
            $userIds = [];

            // 确定接收用户
            if (isset($validated['all_users']) && $validated['all_users']) {
                // 发送给所有用户
                $userIds = AppUser::where('status', 1)->pluck('id')->toArray();
            } elseif (!empty($validated['receiver_ids'])) {
                // 发送给指定用户列表
                $userIds = $validated['receiver_ids'];
            } elseif (isset($validated['user_id'])) {
                // 发送给单个用户
                $userIds = [$validated['user_id']];
            }

            // 分批处理userIds，避免SQL查询过长
            $pushEnabledUserIds = collect([]);
            $validatedType = $validated['type'];

            collect($userIds)->chunk(1000)->each(function ($chunk) use (&$pushEnabledUserIds, $validatedType) {
                $batchResults = NotificationSetting::whereIn('user_id', $chunk)
                    ->select('enabled', 'push_enabled', 'user_id')
                    ->where('type', $validatedType)->get();

                $noSettingUserIds = array_diff($chunk->toArray(), array_column($batchResults->toArray(), 'user_id'));
                $keepUserIds = collect($noSettingUserIds);
                collect($batchResults)->each(function ($item) use ($keepUserIds) {
                    if ($item['enabled'] && $item['push_enabled']) {
                        $keepUserIds->push($item['user_id']);
                    }
                });

                $pushEnabledUserIds = $pushEnabledUserIds->merge($keepUserIds);
            });

            // 为每个用户创建通知
            foreach ($userIds as $userId) {
                $notification = NotificationLog::create([
                    'user_id'      => $userId,
                    'sender_id'    => $validated['sender_id'] ?? null,
                    'type'         => $validated['type'],
                    'title'        => $validated['title'],
                    'content'      => $validated['content'],
                    'related_id'   => $validated['related_id'] ?? null,
                    'related_type' => $validated['related_type'] ?? null,
                    'status'       => 1
                ]);

                $createdNotifications[] = $notification;

                // 如果需要发送推送
                if (isset($validated['send_push']) && $validated['send_push'] && $pushEnabledUserIds->contains($userId)) {
                    // 调用推送服务
                    NotificationService::sendPushNotification(
                        $userId,
                        $validated['title'],
                        $validated['content'],
                        $validated['type'],
                        $validated['related_id'] ?? null,
                        $validated['related_type'] ?? null
                    );
                }
            }

            DB::commit();

            $responseData = [
                'count'         => count($createdNotifications),
                'notifications' => $createdNotifications
            ];

            return ResponseHelper::success($responseData, '创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create system notification failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ResponseHelper::error('创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 创建宠物提醒（用于测试）
     */
    public function createPetReminder(Request $request)
    {
        $validated = $request->validate([
            'user_id'      => 'required|integer|exists:app_users,id',
            'pet_id'       => 'required|integer|exists:pets,id',
            'type'         => 'required|integer|in:1,2,3',
            'title'        => 'required|string|max:100',
            'content'      => 'required|string|max:500',
            'trigger_date' => 'required|date',
            'vaccine_name' => 'required_if:type,3|string|max:100'
        ]);

        try {
            DB::beginTransaction();

            // 创建提醒
            $reminder = PetReminder::create([
                'user_id'      => $validated['user_id'],
                'pet_id'       => $validated['pet_id'],
                'type'         => $validated['type'],
                'title'        => $validated['title'],
                'content'      => $validated['content'],
                'trigger_date' => $validated['trigger_date'],
            ]);

            DB::commit();

            return ResponseHelper::success($reminder, '创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create pet reminder failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return ResponseHelper::error('创建失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除系统通知
     */
    public function deleteSystemNotification(Request $request)
    {
        $validated = $request->validate([
            'notification_id' => 'required|integer|exists:system_notifications,id'
        ]);

        try {
            $notification = NotificationLog::find($validated['notification_id']);
            $notification->update(['status' => 0]);

            return ResponseHelper::success(null, '删除成功');
        } catch (\Exception $e) {
            Log::error('Delete system notification failed', [
                'error'           => $e->getMessage(),
                'notification_id' => $validated['notification_id']
            ]);
            return ResponseHelper::error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 删除宠物提醒
     */
    public function deletePetReminder(Request $request)
    {
        $validated = $request->validate([
            'reminder_id' => 'required|integer|exists:pet_reminders,id'
        ]);

        try {
            $reminder = PetReminder::find($validated['reminder_id']);
            $reminder->update(['status' => 0]);

            return ResponseHelper::success(null, '删除成功');
        } catch (\Exception $e) {
            Log::error('Delete pet reminder failed', [
                'error'       => $e->getMessage(),
                'reminder_id' => $validated['reminder_id']
            ]);
            return ResponseHelper::error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试推送通知
     */
    public function testPushNotification(Request $request)
    {
        $validated = $request->validate([
            'user_id' => 'required|integer|exists:app_users,id',
            'title'   => 'required|string|max:100',
            'content' => 'required|string|max:500',
            'type'    => 'required|string'
        ]);

        try {
            $result = NotificationService::sendPushNotification(
                $validated['user_id'],
                $validated['title'],
                $validated['content'],
                $validated['type']
            );

            if ($result) {
                return ResponseHelper::success(null, '推送测试成功');
            } else {
                return ResponseHelper::error('推送测试失败');
            }
        } catch (\Exception $e) {
            Log::error('Test push notification failed', [
                'error'   => $e->getMessage(),
                'user_id' => $validated['user_id']
            ]);
            return ResponseHelper::error('推送测试失败: ' . $e->getMessage());
        }
    }


}
