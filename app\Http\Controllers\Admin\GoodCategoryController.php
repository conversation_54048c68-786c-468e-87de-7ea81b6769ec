<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\GoodCategory;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * category
 * 分类管理
 * Class CategoryController
 * @package App\Http\Controllers\Admin
 */
class GoodCategoryController extends Controller
{

    //商品详情需要的树形列表
    public function getTree(Request $request)
    {
        //展示应用所在分类下的子类目
        $list = GoodCategory::select('id', 'id as value', 'name as label', 'pid')->orderBy('sort_order', 'ASC')->orderBy('id', 'ASC')->get()->toArray();
        $records = genTree($list);
        return ResponseHelper::success($records);
    }

    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $records = GoodCategory::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(GoodCategory::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'            => 'sometimes|integer|gte:0',
            'name'          => 'required|max:32',
            'name_tc'       => 'required|max:32',
            'name_en'       => 'required|max:255',
            'pid'           => 'required|integer|gte:0',
            'logo'          => 'nullable|url|pic|trust_host',
            'sort_order'    => 'nullable|integer',
            'state'         => 'required|in:0,1',
            'is_recommend'  => 'required|in:0,1',
            'specification' => 'nullable|array',
            'brand'         => 'nullable|array',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = GoodCategory::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = GoodCategory::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
