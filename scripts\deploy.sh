#!/bin/bash

# 项目目录
PROJECT_PATH="/home/<USER>"
LOG_FILE="$PROJECT_PATH/storage/logs/deploy.log"

# 记录时间
DATETIME=$(date '+%Y-%m-%d %H:%M:%S')

cd $PROJECT_PATH

# Git pull
GIT_OUTPUT=$(git pull 2>&1)
echo "[$DATETIME] $GIT_OUTPUT" >> $LOG_FILE

if [ $? -eq 0 ] && [[ $GIT_OUTPUT != *"Already up to date"* ]]; then
    echo "[$DATETIME] Changes detected, rebuilding..." >> $LOG_FILE

    # 清理并重建所有缓存
    php artisan route:clear && php artisan route:cache >> $LOG_FILE 2>&1
    php artisan config:clear && php artisan config:cache >> $LOG_FILE 2>&1
    php artisan view:clear && php artisan view:cache >> $LOG_FILE 2>&1

    echo "[$DATETIME] All caches cleared and rebuilt" >> $LOG_FILE
fi
