<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

//后台内容变更事件(多个)
class DataChangedPack
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private array $changedList;

    public function __construct(DataChanged ...$changedList)
    {
        $this->changedList = $changedList;
    }
}
