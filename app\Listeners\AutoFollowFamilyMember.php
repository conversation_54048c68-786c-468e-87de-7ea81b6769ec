<?php

namespace App\Listeners;

use App\Events\ParentAddedEvent;
use App\Models\AppUserFollow;
use App\Models\AppUser;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AutoFollowFamilyMember implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理家人添加事件 - 自动建立互关关系
     *
     * @param  \App\Events\ParentAddedEvent  $event
     * @return void
     */
    public function handle(ParentAddedEvent $event)
    {
        try {
            $ownerId = $event->ownerId;
            $parentId = $event->parentId;

            // 验证用户是否存在
            if (!AppUser::where('id', $ownerId)->exists() || !AppUser::where('id', $parentId)->exists()) {
                Log::warning('Auto follow family member failed: User not found', [
                    'owner_id' => $ownerId,
                    'parent_id' => $parentId,
                    'pet_id' => $event->petId
                ]);
                return;
            }

            // 宠物主人关注新家人
            if (!AppUserFollow::isFollowing($ownerId, $parentId)) {
                $result = AppUserFollow::setFollowing($ownerId, $parentId);
                if (!$result[0]) {
                    Log::warning('Auto follow family member failed (owner->parent)', [
                        'owner_id' => $ownerId,
                        'parent_id' => $parentId,
                        'error' => $result[2]
                    ]);
                }
            }

            // 新家人关注宠物主人
            if (!AppUserFollow::isFollowing($parentId, $ownerId)) {
                $result = AppUserFollow::setFollowing($parentId, $ownerId);
                if (!$result[0]) {
                    Log::warning('Auto follow family member failed (parent->owner)', [
                        'owner_id' => $ownerId,
                        'parent_id' => $parentId,
                        'error' => $result[2]
                    ]);
                }
            }

            Log::info('Auto follow family member completed', [
                'owner_id' => $ownerId,
                'parent_id' => $parentId,
                'pet_id' => $event->petId
            ]);

        } catch (\Exception $e) {
            Log::error('Auto follow family member failed', [
                'error' => $e->getMessage(),
                'owner_id' => $event->ownerId,
                'parent_id' => $event->parentId,
                'pet_id' => $event->petId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
