<?php

namespace App\Http\Controllers\Admin;

use App\Exports\PaymentLogExport;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\PaymentLog;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PaymentLogController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $records = PaymentLog::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(PaymentLog::getDetail($id));
    }

    public function exportList(Request $request)
    {
        $request->validate([
            'keyword'       => 'nullable|max:255',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
            'created_start' => 'required|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'required|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['per_page'] = -1;
        try {
            $startDate = date('Y-m-d', strtotime($request->created_start));
            $endDate = date('Y-m-d', strtotime($request->created_end));
            $filePath = app()->make('exportPath', ["资金記錄{$startDate}至{$endDate}"]);
            if (!Excel::store(new PaymentLogExport($formData), $filePath)) {
                throw new \Exception(__('common.file_write_error'));
            }
            $download_url = asset('storage/' . $filePath);
            return ResponseHelper::success(['download_url' => $download_url]);
        } catch (\Exception $e) {
            return ResponseHelper::error($e->getMessage() . '[' . $e->getFile() . '-' . $e->getLine() . ']');
        }
    }
}
