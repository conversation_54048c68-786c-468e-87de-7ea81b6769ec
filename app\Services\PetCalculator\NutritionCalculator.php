<?php

namespace App\Services\PetCalculator;

use Exception;

/**
 * 宠物营养计算基类
 * 基于AAFCO标准计算宠物营养需求
 */
abstract class NutritionCalculator
{
    protected $pet;
    protected $energyDemand; // 每日能量需求 (kcal)

    /**
     * 构造函数
     *
     * @param array $pet 宠物信息
     */
    public function __construct($pet)
    {
        $this->pet = $pet;
        $this->validatePetData();
        $this->calculateEnergyDemand();
    }

    /**
     * 验证宠物数据
     *
     * @throws Exception
     */
    protected function validatePetData()
    {
        if (empty($this->pet['weight']) || $this->pet['weight'] <= 0) {
            throw new Exception('未录入体重');
        }
        if (empty($this->pet['birthday'])) {
            throw new Exception('未录入生日');
        }
        if (empty($this->pet['pet_type']['name_en'])) {
            throw new Exception('未录入宠物类型');
        }
    }

    /**
     * 计算能量需求
     */
    protected function calculateEnergyDemand()
    {
        $petType = strtolower($this->pet['pet_type']['name_en']);
        $className = 'App\Services\PetCalculator\\' . ucfirst($petType) . 'EnergyDemand';
        
        if (!class_exists($className)) {
            throw new Exception('未知的动物类型');
        }

        $calculator = new $className($this->pet);
        $this->energyDemand = $calculator->getEnergyDemand();
    }

    /**
     * 获取营养需求分析
     *
     * @return array
     */
    public function getNutritionRequirements(): array
    {
        $petType = $this->getPetType();
        $ageStage = $this->getAgeStage();
        $standards = AAFCOStandards::getStandards($petType, $ageStage);

        if (!$standards) {
            throw new Exception('无法获取营养标准');
        }

        // 计算营养需求 (基于每1000kcal的标准)
        $multiplier = $this->energyDemand / 1000;

        $requirements = [
            'basic_info' => $this->getBasicInfo(),
            'energy_info' => [
                'daily_energy_demand' => round($this->energyDemand, 2),
                'unit' => 'kcal/天',
                'calculation_basis' => '基于AAFCO标准，每1000kcal ME计算'
            ],
            'macronutrients' => $this->calculateNutrientGroup($standards['macronutrients'], $multiplier),
            'vitamins' => $this->calculateNutrientGroup($standards['vitamins'], $multiplier),
            'minerals' => $this->calculateNutrientGroup($standards['minerals'], $multiplier),
            'amino_acids' => $this->calculateNutrientGroup($standards['amino_acids'], $multiplier),
            'special_adjustments' => $this->getSpecialAdjustments(),
            'recommendations' => $this->getRecommendations()
        ];

        return $requirements;
    }

    /**
     * 计算营养素组
     *
     * @param array $nutrients 营养素标准
     * @param float $multiplier 倍数
     * @return array
     */
    protected function calculateNutrientGroup(array $nutrients, float $multiplier): array
    {
        $result = [];

        foreach ($nutrients as $key => $nutrient) {
            $dailyRequirement = [];

            // 计算最小需求量
            if (isset($nutrient['min'])) {
                $dailyRequirement['min'] = round($nutrient['min'] * $multiplier, 4);
            }

            // 计算最大安全量
            if (isset($nutrient['max'])) {
                $dailyRequirement['max'] = round($nutrient['max'] * $multiplier, 4);
            }

            $result[$key] = [
                'name' => $nutrient['name'],
                'name_en' => $nutrient['name_en'],
                'daily_requirement' => $dailyRequirement,
                'unit' => $nutrient['unit'],
                'function' => $this->getNutrientFunction($key),
                'food_sources' => $this->getFoodSources($key)
            ];
        }

        return $result;
    }

    /**
     * 获取基本信息
     *
     * @return array
     */
    protected function getBasicInfo(): array
    {
        $age = $this->getAge();
        
        return [
            'pet_name' => $this->pet['name'] ?? '未知',
            'pet_type' => $this->pet['pet_type']['name'] ?? '未知',
            'pet_breed' => $this->pet['pet_breed']['name'] ?? '未知',
            'age' => $age,
            'age_stage' => $this->getAgeStage(),
            'weight' => $this->pet['weight'],
            'sex' => $this->pet['sex'] == 1 ? '公' : '母',
            'neutered' => $this->pet['neutered'] ? '已绝育' : '未绝育',
            'is_pregnant' => $this->pet['is_pregnant'] ? '怀孕' : '未怀孕',
            'is_ill' => $this->pet['is_ill'] ? '生病' : '健康',
            'weight_status' => $this->getWeightStatus(),
            'active_status' => $this->getActiveStatus()
        ];
    }

    /**
     * 获取宠物类型
     *
     * @return string
     */
    protected function getPetType(): string
    {
        $petTypeEn = strtolower($this->pet['pet_type']['name_en']);
        
        if (strpos($petTypeEn, 'dog') !== false) {
            return 'dog';
        } elseif (strpos($petTypeEn, 'cat') !== false) {
            return 'cat';
        }
        
        return 'dog'; // 默认
    }

    /**
     * 获取年龄阶段
     *
     * @return string
     */
    protected function getAgeStage(): string
    {
        $petType = $this->getPetType();
        $age = $this->getAge();
        
        return AAFCOStandards::determineAgeStage($petType, $age);
    }

    /**
     * 获取年龄
     *
     * @return int
     */
    protected function getAge(): int
    {
        $birth = new \DateTime($this->pet['birthday']);
        $now = new \DateTime();
        return $now->diff($birth)->y;
    }

    /**
     * 获取体重状态描述
     *
     * @return string
     */
    protected function getWeightStatus(): string
    {
        $status = $this->pet['weight_status'] ?? 5;
        
        if ($status < 3) {
            return '过瘦';
        } elseif ($status <= 5) {
            return '理想';
        } elseif ($status <= 7) {
            return '超重';
        } else {
            return '肥胖';
        }
    }

    /**
     * 获取活动状态描述
     *
     * @return string
     */
    protected function getActiveStatus(): string
    {
        $status = $this->pet['active_status'] ?? 1;
        
        switch ($status) {
            case 1:
                return '低活动量';
            case 2:
                return '高活动量';
            default:
                return '中等活动量';
        }
    }

    /**
     * 获取特殊调整建议
     *
     * @return array
     */
    protected function getSpecialAdjustments(): array
    {
        $adjustments = [];

        // 怀孕期调整
        if ($this->pet['is_pregnant']) {
            $adjustments[] = [
                'condition' => '怀孕期',
                'adjustment' => '蛋白质需求增加25-50%，钙和磷需求增加',
                'note' => '建议咨询兽医制定专门的营养计划'
            ];
        }

        // 哺乳期调整
        if (isset($this->pet['is_lactating']) && $this->pet['is_lactating']) {
            $adjustments[] = [
                'condition' => '哺乳期',
                'adjustment' => '所有营养需求增加2-4倍',
                'note' => '需要高质量、高能量密度的食物'
            ];
        }

        // 疾病状态调整
        if ($this->pet['is_ill']) {
            $adjustments[] = [
                'condition' => '疾病状态',
                'adjustment' => '可能需要特殊营养支持',
                'note' => '请咨询兽医获取专业营养建议'
            ];
        }

        // 体重状态调整
        $weightStatus = $this->pet['weight_status'] ?? 5;
        if ($weightStatus > 6) {
            $adjustments[] = [
                'condition' => '超重/肥胖',
                'adjustment' => '减少总热量摄入，增加蛋白质比例',
                'note' => '建议制定减重计划，增加运动量'
            ];
        } elseif ($weightStatus < 4) {
            $adjustments[] = [
                'condition' => '体重不足',
                'adjustment' => '增加总热量摄入，确保营养均衡',
                'note' => '建议少量多餐，选择高质量食物'
            ];
        }

        return $adjustments;
    }

    /**
     * 获取营养素功能说明
     *
     * @param string $nutrient
     * @return string
     */
    abstract protected function getNutrientFunction(string $nutrient): string;

    /**
     * 获取食物来源
     *
     * @param string $nutrient
     * @return array
     */
    abstract protected function getFoodSources(string $nutrient): array;

    /**
     * 获取推荐建议
     *
     * @return array
     */
    abstract protected function getRecommendations(): array;
}
