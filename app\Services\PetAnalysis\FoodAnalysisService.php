<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class FoodAnalysisService extends BaseAnalysisService
{
    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);

        $this->type = 'food';
        $this->name = '食物分析';
        $this->description = '分析食物对宠物的安全性和营养价值';
    }

    /**
     * 执行食物分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位（用于健康分析）
     * @param array|null $symptoms 症状列表（用于健康分析）
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        try {
            // 记录开始时间
            $startTime = microtime(true);
            // 获取宠物信息
            $petInfo = $this->getPetInfo($petId);

            // 记录提示词构建开始时间
            $promptStartTime = microtime(true);

            // 构建提示词，使用模板格式并传入多宠物信息
            // 始终使用模板格式，不考虑stream参数
            $prompt = $this->buildFoodAnalysisPrompt($petInfo, $petsInfo, $images);

            // 记录提示词构建耗时
            $promptBuildTime = microtime(true) - $promptStartTime;

            // 添加用户查询
            $finalPrompt = $prompt . "\n\n用户问题: " . $query;

            // 记录AI调用开始时间
            $aiCallStartTime = microtime(true);

            // 调用AI服务，根据参数决定是否使用流式响应
            // 不使用上下文，只传递提示词和图片，加快响应速度
            $response = $this->aiService->chat(
                $finalPrompt,
                $images,
                $sessionId,
                0.7,
                $stream // 根据参数决定是否使用流式响应
            );

            // 记录AI调用耗时
            $aiCallTime = microtime(true) - $aiCallStartTime;

            // 如果是流式响应，直接返回响应对象
            if ($stream) {
                return $response;
            }

            // 如果不是流式响应，处理响应内容
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return [
                    'status' => 'error',
                    'message' => '获取分析结果失败',
                    'analysis_type' => $this->getType()
                ];
            }

            // 记录结束时间和耗时
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            $promptBuildTime = round($promptBuildTime ?? 0, 2);
            $aiCallTime = round($aiCallTime, 2);

            \Illuminate\Support\Facades\Log::info("FoodAnalysisService 耗时分析", [
                'total_time' => $duration,
                'prompt_build_time' => $promptBuildTime,
                'ai_call_time' => $aiCallTime,
                'other_time' => round($duration - $promptBuildTime - $aiCallTime, 2),
                'has_images' => !empty($images),
                'image_count' => count($images ?? [])
            ]);

            // 返回格式化的响应，保持与流式响应相同的格式
            return [
                'status' => 'success',
                'message' => '分析成功',
                'data' => [
                    'content' => $content,
                    'session_id' => $response['session_id'] ?? null,
                    'is_new_session' => $response['is_new_session'] ?? false
                ],
                // 移除pet_info字段，由控制器统一添加
                'analysis_type' => $this->getType(),
                'performance' => [
                    'total_time' => $duration,
                    'prompt_build_time' => $promptBuildTime,
                    'ai_call_time' => $aiCallTime
                ]
            ];

        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常
            throw $e;
        } catch (\Exception $e) {
            Log::error('食物分析失败: ' . $e->getMessage());
            // 将异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '食物分析失败: ' . $e->getMessage(),
                'food_analysis_error',
                $sessionId ?? null,
                [
                    'original_error' => $e->getMessage(),
                    'analysis_type' => $this->getType()
                ]
            );
        }
    }

    /**
     * 构建食物分析提示词
     *
     * @param array|null $petInfo
     * @param array|null $petsInfo 多宠物信息数组
     * @return string
     */
    protected function buildFoodAnalysisPrompt(?array $petInfo, ?array $petsInfo = null, array $images = []): string
    {
        $prompt = "你是一个专业的宠物食物分析专家，请分析用户描述或上传图片中的食物是否适合宠物食用。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式，语气要亲切友好。\n\n如果用户提供了多只宠物的信息，请智能分析每只宠物的情况，并将有相同分析结果的宠物合并到一条回复中，避免重复内容。例如：如果3只宠物都可以食用同一食物，请写成'宠物A、宠物B、宠物C都可以食用'，而不是分别为每只宠物写重复的回复。\n\n";

        // 使用模板格式，添加颜色标注
        $prompt .= "请按照以下格式回复，使用纯正的香港粤语（广东话）：\n\n";

        $prompt .= "查詢對象: [全部/宠物名称列表]\n\n";

        // 根据是否有图片来决定提示词
        if (!empty($images)) {
            $prompt .= "圖片中既食物: [食物名称]\n\n";
        } else {
            $prompt .= "查詢既食物: [食物名称]\n\n";
        }

        $prompt .= "能否食用: \n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= "请根据每只宠物的情况分析，并将有相同结果的宠物合并到一条回复中：\n";
            $prompt .= " ● <span style=\"color: red;\">[宠物A、宠物B]不可食用！</span> [解释为什么这些宠物不能食用该食物，包括可能的危害]\n";
            $prompt .= " ● <span style=\"color: orange;\">[宠物C、宠物D]可以食用。</span> [解释为什么这些宠物可以食用该食物，可以提及相关建议]\n";
            $prompt .= " ● <span style=\"color: #FF8C00;\">[宠物E]需要谨慎食用。</span> [解释需要谨慎的原因和注意事项]\n";
            $prompt .= "注意：请将分析结果相同的宠物合并在一条回复中，用顿号（、）分隔宠物名称，避免重复的回复内容。\n\n";
        }
        // 如果是单只宠物
        else {
            $prompt .= " ● <span style=\"color: orange;\">[可以食用]</span>/<span style=\"color: red;\">[不可食用]</span>/<span style=\"color: #FF8C00;\">[谨慎食用]</span>\n";
            $prompt .= "   [详细解释原因]\n\n";
        }

        // 根据能否食用来决定是否显示建议食法和建议食量
        $prompt .= "<span style=\"color: #FF5733;\">注意：如果所有宠物都不可食用，请跳过建议食法和建议食量部分，直接显示注意事项。</span>\n\n";

        $prompt .= "<span style=\"color: #4CAF50;\">建議食法: </span>\n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= " ● [详细的食物准备方法1]\n";
            $prompt .= " ● [详细的食物准备方法2]\n";
            $prompt .= " ● [详细的食物准备方法3]\n\n";
        } else {
            $prompt .= " ● [具体的食用方式建议1]\n";
            $prompt .= " ● [具体的食用方式建议2]\n";
            $prompt .= " ● [具体的食用方式建议3]\n";
            $prompt .= " ● [其他食用方式建议]\n\n";
        }

        $prompt .= "<span style=\"color: #4CAF50;\">建議食量: </span>\n";
        // 如果是多只宠物
        if (!empty($petsInfo) && count($petsInfo) > 1) {
            $prompt .= "请根据宠物的体型、年龄等特征给出食量建议，相似体型和年龄的宠物可以合并建议：\n";
            $prompt .= " ● [宠物A、宠物B]--[根据这些宠物的共同特征给出的具体食量建议]\n";
            $prompt .= " ● [宠物C]--[根据该宠物特殊情况给出的个性化食量建议]\n";
            $prompt .= " ● [过敏相关建议]\n";
            $prompt .= " ● [观察相关建议]\n";
            $prompt .= "注意：体型、年龄相似的宠物可以合并食量建议，特殊情况的宠物需要单独说明。\n\n";
        } else {
            $petName = $petInfo ? $petInfo['name'] : '宠物';
            $prompt .= " ● [根据{$petName}的年龄、体重等特征给出的具体食量建议]\n";
            $prompt .= " ● [过敏相关建议]\n";
            $prompt .= " ● [观察相关建议]\n\n";
        }

        $prompt .= "<span style=\"color: #2196F3;\">營養價值: </span>\n";
        $prompt .= " ● [营养价值1]\n";
        $prompt .= " ● [营养价值2]\n\n";

        $prompt .= "<span style=\"color: #FF5733;\">注意事項: </span>\n";
        $prompt .= " ● [喂食时需要注意的事项1]\n";
        $prompt .= " ● [喂食时需要注意的事项2]\n\n";

        // 以下是给AI的指导，使用特殊标记包裹，确保AI不会直接返回这些内容
        $prompt .= "<instructions>\n";
        $prompt .= "请注意：\n";
        $prompt .= "1. 如果无法识别食物，请在图片中的食物部分回答\"无法识别食物: 原因\"。\n";
        $prompt .= "2. **多宠物统一回复规则（重要）：**\n";
        $prompt .= "   - 当所有宠物都可以食用某种食物时，统一回复：'所有宠物都可以食用'\n";
        $prompt .= "   - 当所有宠物都不能食用某种食物时，统一回复：'所有宠物都不建议食用'\n";
        $prompt .= "   - 当所有宠物都需要谨慎食用时，统一回复：'所有宠物都需要谨慎食用'\n";
        $prompt .= "   - 只有当宠物之间对同一食物有不同的分析结果时，才分别进行个别分析\n";
        $prompt .= "   - 例如：宠物A可以食用，宠物B不能食用，宠物C需要谨慎食用\n";
        $prompt .= "   - 在建议食量部分，体型、年龄相似的宠物也可以合并建议\n";
        $prompt .= "3. 请确保回复的格式与模板一致，使用纯正的香港粤语（广东话）回复。\n";
        $prompt .= "4. 对于不同宠物的情况，请根据宠物的品种、年龄、体重等特征给出个性化的建议。\n";
        $prompt .= "5. 如果食物对宠物有害，请明确指出并解释原因。\n";
        $prompt .= "6. 当食物对所有宠物都不可食用时，请跳过建议食法和建议食量部分，直接显示注意事项。\n";
        $prompt .= "7. 请使用项目符号（●）来增强可读性，并保持格式美观。\n";
        $prompt .= "8. 请使用亲切、关心的语气，就像在和好朋友聊天一样。\n";
        $prompt .= "9. 请确保回复中没有乱码或格式错误。\n";
        $prompt .= "10. 请为重要内容添加颜色标注：\n";
        $prompt .= "   - 可以食用：<span style=\"color: orange;\">橙色</span>\n";
        $prompt .= "   - 不可食用：<span style=\"color: red;\">红色</span>\n";
        $prompt .= "   - 谨慎食用：<span style=\"color: #FF8C00;\">深橙色</span>\n";
        $prompt .= "   - 建议食法和食量：<span style=\"color: #4CAF50;\">绿色</span>\n";
        $prompt .= "   - 营养价值：<span style=\"color: #2196F3;\">蓝色</span>\n";
        $prompt .= "   - 注意事项：<span style=\"color: #FF5733;\">红橙色</span>\n";
        $prompt .= "11. **重要：请使用简洁明了的文字表达，避免冗长的句子和重复的表述。每个要点都要言简意赅，直接说重点。**\n";
        $prompt .= "12. **文字风格：用词精练，表达直接，避免过多的修饰词和解释性语言。**\n";
        $prompt .= "仅回答关于食物分析的问题，不要回答与食物无关的内容。\n";
        $prompt .= "</instructions>\n\n";

        // 添加多宠物信息（如果有）
        if (!empty($petsInfo)) {
            $prompt .= "宠物信息:\n";
            foreach ($petsInfo as $index => $pet) {
                $prompt .= "宠物" . ($index + 1) . ":\n";
                $prompt .= "- 名称: " . $pet['name'] . "\n";
                $prompt .= "- 类型: " . $pet['type'] . "\n";
                $prompt .= "- 品种: " . $pet['breed'] . "\n";
                $prompt .= "- 性别: " . $pet['sex'] . "\n";

                if (isset($pet['age'])) {
                    $prompt .= "- 年龄: " . $pet['age'] . " 岁\n";
                }

                if (isset($pet['weight'])) {
                    $prompt .= "- 体重: " . $pet['weight'] . " 公斤\n";
                }

                // 添加特定宠物类型警告
                $petTypeEn = strtolower($pet['type_en'] ?? '');

                if (Str::contains($petTypeEn, 'dog')) {
                    $prompt .= "- 注意：狗不能食用巧克力、葡萄/葡萄干、洋葱、大豆、韭菜等\n";
                } elseif (Str::contains($petTypeEn, 'cat')) {
                    $prompt .= "- 注意：猫不能食用洋葱、大豆、韭菜、生鱼、酒精等\n";
                }

                $prompt .= "\n";
            }
        } elseif ($petInfo) {
            // 添加单个宠物信息（如果没有多宠物信息但有单个宠物信息）
            $prompt .= "宠物信息:\n";
            $prompt .= "- 名称: " . $petInfo['name'] . "\n";
            $prompt .= "- 类型: " . $petInfo['type'] . "\n";
            $prompt .= "- 品种: " . $petInfo['breed'] . "\n";
            $prompt .= "- 性别: " . $petInfo['sex'] . "\n";

            if (isset($petInfo['age'])) {
                $prompt .= "- 年龄: " . $petInfo['age'] . " 岁\n";
            }

            if (isset($petInfo['weight'])) {
                $prompt .= "- 体重: " . $petInfo['weight'] . " 公斤\n";
            }

            if (isset($petInfo['weight_status'])) {
                $prompt .= "- 体重状态: " . $petInfo['weight_status'] . "\n";
            } else {
                $prompt .= "- 体重状态: 正常\n";
            }

            $prompt .= "- 是否绝育: " . ($petInfo['neutered'] ? '是' : '否') . "\n";
            $prompt .= "- 是否怀孕: " . ($petInfo['is_pregnant'] ? '是' : '否') . "\n";
            $prompt .= "- 是否生病: " . ($petInfo['is_ill'] ? '是' : '否') . "\n";

            if (!empty($petInfo['special_conditions'])) {
                $prompt .= "- 特殊情况: " . implode(', ', $petInfo['special_conditions']) . "\n";
            }

            // 添加特定宠物类型警告
            $petTypeEn = strtolower($petInfo['type_en'] ?? '');

            if (Str::contains($petTypeEn, 'dog')) {
                $prompt .= "\n特别注意：对于狗，以下食物是有毒或有害的：巧克力、葡萄/葡萄干、洋葱、大蒜、韭菜、咖啡因、酒精、粗骨头、牛油果、人工甜味剂木糖醇。";
            } elseif (Str::contains($petTypeEn, 'cat')) {
                $prompt .= "\n特别注意：对于猫，以下食物是有毒或有害的：洋葱、大蒜、韭菜、生鱼、酒精、巧克力、咖啡因、葡萄/葡萄干、牛奶/乳制品、生面团、人工甜味剂木糖醇。";
            }
        } else {
            $prompt .= "用户没有指定特定宠物，请提供适用于一般猫和狗的食物分析。";
        }

        return $prompt;
    }
}
