<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\AnalysisQuickQuestion;
use App\Helpers\ResponseHelper;
use Illuminate\Http\Request;

class AnalysisQuickQuestionController extends Controller
{
    /**
     * 获取快捷提问列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuickQuestions(Request $request)
    {
        try {
            $request->validate([
                'type' => 'nullable|in:food,health,stool', // 分析类型筛选
                'pet_type' => 'nullable|in:general,dog,cat', // 宠物类型筛选
                'status' => 'nullable|in:0,1', // 状态筛选：0=禁用，1=启用
            ]);

            // 根据状态筛选，默认只显示启用的
            $status = $request->input('status', 1);
            $type = $request->input('type');
            $petType = $request->input('pet_type');

            $query = AnalysisQuickQuestion::where('status', $status);

            // 根据分析类型筛选
            if ($type) {
                $query->where('analysis_type', $type);
            }

            // 根据宠物类型筛选
            if ($petType) {
                $query->where(function ($q) use ($petType) {
                    $q->where('pet_type', $petType)
                        ->orWhere('pet_type', 'general'); // 通用类型对所有宠物都适用
                });
            }

            // 按排序字段和创建时间排序
            $questions = $query->orderBy('sort_order', 'asc')
                ->orderBy('created_at', 'desc')
                ->get();

            // 格式化返回数据
            $result = $questions->map(function ($question) {
                return [
                    'id' => $question->id,
                    'type' => $question->analysis_type,
                    'type_name' => $this->getTypeName($question->analysis_type),
                    'question' => $question->question,
                    'pet_type' => $question->pet_type,
                    'icon' => $question->icon,
                    'sort' => $question->sort_order,
                ];
            });

            return ResponseHelper::success($result);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return ResponseHelper::error('参数验证失败', 422, $e->errors());
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('获取快捷提问列表失败: ' . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);
            return ResponseHelper::error('获取快捷提问列表失败');
        }
    }


    /**
     * 获取类型名称
     *
     * @param string $type
     * @return string
     */
    private function getTypeName(string $type): string
    {
        $typeNames = [
            'food' => '食物分析',
            'health' => '健康分析',
            'stool' => '排泄物分析'
        ];

        return $typeNames[$type] ?? '未知类型';
    }
}

