<?php

namespace App\Http\Middleware;

use App\Exceptions\NoPermissionException;
use App\Models\Role;
use App\Models\SystemMenu;
use Closure;
use Illuminate\Http\Request;

class CheckAdminPermission
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        $roleId = $user->role_id;
        if ($roleId != 1) {
            $api = str_replace('api/admin', '', $request->route()->uri);
            $menuId = SystemMenu::where('api', $api)->value('id');
            if (!$menuId) {
                throw new NoPermissionException('Unknown permission: ' . $api); //未录入菜单权限（不用授权的需要添加：withoutMiddleware('permission.admin')）
            }
            $systemMenuIds = explode(',', Role::where('id', $roleId)->where('status', 1)->value('system_menu_ids'));
            if (!in_array($menuId, $systemMenuIds)) {
                throw new NoPermissionException(); //没有菜单权限
            }
        }
        return $next($request);
    }
}
