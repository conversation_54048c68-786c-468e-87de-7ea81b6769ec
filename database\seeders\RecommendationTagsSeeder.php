<?php

namespace Database\Seeders;

use App\Models\RecommendationTag;
use Illuminate\Database\Seeder;

class RecommendationTagsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $tags = [
            // 年龄段标签
            [
                'name' => '幼年期',
                'name_tc' => '幼年期',
                'name_en' => 'Puppy/Kitten',
                'category' => 'age',
                'sort_order' => 10,
            ],
            [
                'name' => '成年期',
                'name_tc' => '成年期',
                'name_en' => 'Adult',
                'category' => 'age',
                'sort_order' => 20,
            ],
            [
                'name' => '老年期',
                'name_tc' => '老年期',
                'name_en' => 'Senior',
                'category' => 'age',
                'sort_order' => 30,
            ],
            
            // 体型标签
            [
                'name' => '小型犬',
                'name_tc' => '小型犬',
                'name_en' => 'Small Dog',
                'category' => 'size',
                'sort_order' => 10,
            ],
            [
                'name' => '中型犬',
                'name_tc' => '中型犬',
                'name_en' => 'Medium Dog',
                'category' => 'size',
                'sort_order' => 20,
            ],
            [
                'name' => '大型犬',
                'name_tc' => '大型犬',
                'name_en' => 'Large Dog',
                'category' => 'size',
                'sort_order' => 30,
            ],
            [
                'name' => '小型猫',
                'name_tc' => '小型貓',
                'name_en' => 'Small Cat',
                'category' => 'size',
                'sort_order' => 40,
            ],
            [
                'name' => '大型猫',
                'name_tc' => '大型貓',
                'name_en' => 'Large Cat',
                'category' => 'size',
                'sort_order' => 50,
            ],
            
            // 健康状况标签
            [
                'name' => '已绝育',
                'name_tc' => '已絕育',
                'name_en' => 'Neutered',
                'category' => 'health',
                'sort_order' => 10,
            ],
            [
                'name' => '怀孕中',
                'name_tc' => '懷孕中',
                'name_en' => 'Pregnant',
                'category' => 'health',
                'sort_order' => 20,
            ],
            [
                'name' => '哺乳期',
                'name_tc' => '哺乳期',
                'name_en' => 'Nursing',
                'category' => 'health',
                'sort_order' => 30,
            ],
            [
                'name' => '肠胃敏感',
                'name_tc' => '腸胃敏感',
                'name_en' => 'Sensitive Stomach',
                'category' => 'health',
                'sort_order' => 40,
            ],
            [
                'name' => '皮肤敏感',
                'name_tc' => '皮膚敏感',
                'name_en' => 'Sensitive Skin',
                'category' => 'health',
                'sort_order' => 50,
            ],
            [
                'name' => '关节问题',
                'name_tc' => '關節問題',
                'name_en' => 'Joint Issues',
                'category' => 'health',
                'sort_order' => 60,
            ],
            
            // 生活方式标签
            [
                'name' => '室内饲养',
                'name_tc' => '室內飼養',
                'name_en' => 'Indoor',
                'category' => 'lifestyle',
                'sort_order' => 10,
            ],
            [
                'name' => '户外活跃',
                'name_tc' => '戶外活躍',
                'name_en' => 'Outdoor Active',
                'category' => 'lifestyle',
                'sort_order' => 20,
            ],
            [
                'name' => '多宠家庭',
                'name_tc' => '多寵家庭',
                'name_en' => 'Multi-pet Household',
                'category' => 'lifestyle',
                'sort_order' => 30,
            ],
        ];

        foreach ($tags as $tag) {
            RecommendationTag::updateOrCreate(
                ['name' => $tag['name'], 'category' => $tag['category']],
                $tag
            );
        }
    }
}
