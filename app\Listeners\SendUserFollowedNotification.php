<?php

namespace App\Listeners;

use App\Events\UserFollowedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendUserFollowedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\UserFollowedEvent  $event
     * @return void
     */
    public function handle(UserFollowedEvent $event)
    {
        try {
            // 获取关注者信息
            $follower = AppUser::find($event->followerId);

            if (!$follower) {
                Log::error('User followed notification failed: Follower not found', [
                    'follower_id' => $event->followerId,
                    'followed_user_id' => $event->followedUserId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->followedUserId, // 接收通知的用户ID（被关注的用户）
                NotificationSetting::TYPE_USER_FOLLOWED, // 通知类型
                '用户关注通知',
                "用户 {$follower->username} 关注了您",
                $event->followerId, // 发送者ID（关注者）
                $event->followedUserId, // 相关ID（被关注的用户ID）
                NotificationSetting::RELATION_TYPE_USER // 相关类型
            );

            Log::info('User followed notification sent', [
                'followed_user_id' => $event->followedUserId,
                'follower_id' => $event->followerId
            ]);
        } catch (\Exception $e) {
            Log::error('User followed notification failed', [
                'error' => $e->getMessage(),
                'followed_user_id' => $event->followedUserId,
                'follower_id' => $event->followerId
            ]);
        }
    }
}
