<?php

namespace App\Listeners;

use App\Events\FriendHandledEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendFriendHandledNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\FriendHandledEvent $event
     * @return void
     */
    public function handle(FriendHandledEvent $event)
    {
        try {
            // 获取关注者信息
            $friend = AppUser::find($event->toUserId);

            if (!$friend) {
                Log::error('Friend handled notification failed: Friend not found', [
                    'toUserId'   => $event->toUserId,
                    'fromUserId' => $event->fromUserId,
                    'action'     => $event->action,
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->fromUserId, // 接收通知的用户ID
                NotificationSetting::TYPE_FRIEND_REQUEST, // 通知类型
                '好友申请已通过',
                "用户 {$friend->username} 已同意您的好友申请",
                $event->toUserId, // 发送者ID（关注者）
                $event->fromUserId, // 相关ID
                NotificationSetting::RELATION_TYPE_USER // 相关类型
            );

            Log::info('Friend handled notification sent', [
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId,
                'action'     => $event->action,
            ]);
        } catch (\Exception $e) {
            Log::error('Friend handled notification failed', [
                'error'      => $e->getMessage(),
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId,
                'action'     => $event->action,
            ]);
        }
    }
}
