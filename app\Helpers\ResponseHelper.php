<?php

namespace App\Helpers;

class ResponseHelper
{
    const STATUS_OK = 200;
    const STATUS_ERR = 201;
    const STATUS_NO_AUTH = 401;
    const STATUS_NO_PERMISSION = 403;
    const STATUS_SERVER_ERROR = 500;

    public static function success($data = [], $message = null, $status = self::STATUS_OK)
    {
        is_null($message) and $message = __('common.success');
        return response()->json([
            'status'  => $status,
            'message' => $message,
            'data'    => $data ?? (object)[],
        ], $status, [], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    public static function error($message = null, $status = self::STATUS_ERR, $data = null)
    {
        is_null($message) and $message = __('common.error');
        $response = [
            'status'  => $status,
            'message' => $message,
        ];

        // 如果提供了数据，添加到响应中
        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $status, [], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    public static function result(bool $res, $data = [], $message = null, $status = null)
    {
        if (is_null($message)) {
            $message = $res ? __('common.success') : __('common.error');
        }
        if (is_null($status)) {
            $status = $res ? self::STATUS_OK : self::STATUS_ERR;
        }
        return $res ? self::success($data, $message, $status) : self::error($message, $status);
    }
}
