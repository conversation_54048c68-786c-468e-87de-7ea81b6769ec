<?php

namespace App\Http\Controllers;

use App\Helpers\ResponseHelper;
use App\Jobs\TestJob;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class CommonController extends Controller
{
    public function testEmail(Request $request)
    {
        try {
            $recipientEmail = '<EMAIL>';
            $restaurantName = 'XX餐厅';
            $month = 'XX月份';

            // 获取附件路径和文件名
            $attachmentPath = $request->file('attachment') ? $request->file('attachment')->getRealPath() : null;
            $attachmentName = $request->file('attachment') ? $request->file('attachment')->getClientOriginalName() : null;

            $details = [
                'restaurantName' => $restaurantName,
                'month'          => $month
            ];

            // 发送邮件
            Mail::to($recipientEmail)->send(new \App\Mail\MonthlyDetailsMail($details, $attachmentPath, $attachmentName));
            return response()->json(['message' => 'Email sent successfully']);
        } catch (Exception $e) {
            return response()->json(['message' => 'Failed to send email', 'error' => $e->getMessage()], 500);
        }
    }

    public function testJob(Request $request)
    {
        $rand = rand(0, 9999);
        $this->dispatch(new TestJob($rand));
        return ResponseHelper::success(['rand' => $rand]);
    }

    public function testServerError(Request $request)
    {
        $arr = [];
        echo $arr[0];  //故意读取不存在的index
    }
}
