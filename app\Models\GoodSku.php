<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin Builder
 */
class GoodSku extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'good_id',
        'image',
        'market_price',
        'cost_price',
        'price',
        'inventory',
        'weight',
        'product_sku',
        'pet_filter_tags',
        'matching_pets_count',
    ];

    protected $casts = [
        'pet_filter_tags' => 'array',
    ];

    /**
     * 获取格式化的筛选标签信息
     */
    public function getFormattedFilterTagsAttribute()
    {
        if (empty($this->pet_filter_tags)) {
            return null;
        }

        $service = new \App\Services\PetMatchingService();
        $result = $service->calculateMatchingPets($this->pet_filter_tags);

        return [
            'tags' => $this->pet_filter_tags,
            'tags_count' => count($this->pet_filter_tags),
            'filter_summary' => $result['filter_summary'],
            'matching_pets_count' => $this->matching_pets_count,
            'has_tags' => true
        ];
    }

    /**
     * 检查是否有筛选标签
     */
    public function getHasFilterTagsAttribute()
    {
        return !empty($this->pet_filter_tags);
    }

    protected $table = 'goods_skus';

    /**
     * 关联商品
     */
    public function good()
    {
        return $this->belongsTo(\App\Models\Good::class, 'good_id');
    }

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
