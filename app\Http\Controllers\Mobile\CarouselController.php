<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Carousel;
use Illuminate\Http\Request;

class CarouselController extends Controller
{
    /**
     * 获取轮播图列表
     */
    public function getCarousels(Request $request)
    {
        $request->validate([
            'position' => 'required|string|max:50',
        ]);

        $position = $request->input('position', 'home');
        $carousels = Carousel::getActiveCarousels($position);

        return ResponseHelper::success($carousels);
    }
}
