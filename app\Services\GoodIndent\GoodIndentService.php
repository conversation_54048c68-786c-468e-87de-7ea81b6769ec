<?php

namespace App\Services\GoodIndent;

use App\Helpers\RedisLock;
use App\Models\AppUser;
use App\Models\GoodIndent;
use App\Models\GoodIndentCommodity;
use App\Models\GoodIndentLog;
use App\Models\GoodIndentRefund;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;

class GoodIndentService
{
    private $id;
    private $user;
    private GoodIndent $goodIndent;
    private array $operator;
    private string $operateName;
    private string $operateType;
    private string $operateContent;
    private array $data;

    private string $message = '';

    public function __construct($id, $user = null)
    {
        $this->id = $id;
        $this->user = $user;
        $this->initOperator();
        $this->data = ['id' => $id];
    }

    private function initOperator()
    {
        if ($this->user instanceof User) {
            $this->operator = ['id' => $this->user->id, 'nickname' => $this->user->name];
        } elseif ($this->user instanceof AppUser) {
            $this->operator = ['id' => 0, 'nickname' => '用户'];
        } else {
            $this->operator = ['id' => 0, 'nickname' => '系统'];
        }
    }

    public function repay(array $data): bool
    {
        $this->operateName = '';
        $this->operateType = '';
        $this->operateContent = '';
        return $this->run(function (GoodIndentService $self): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_PAY) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self) use ($data): array {
            return [];  //无需更新
        });
    }

    public function paid(array $data): bool
    {
        $this->operateName = '设为已支付';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_PAID;
        $this->operateContent = '设为已支付';
        return $this->run(function (GoodIndentService $self): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_PAY) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self) use ($data): array {
            return GoodIndent::setPaid($self->getGoodIndent());
        });
    }

    public function shipment(array $data): bool
    {
        $this->operateName = '发货';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_SHIPMENT;
        $this->operateContent = '发货';
        return $this->run(function (GoodIndentService $self): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_DELIVER) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self) use ($data): array {
            $is_automatic_receiving = GoodIndent::AUTOMATIC_RECEIVING_STATE ? 1 : 0;
            $receiving_time = $is_automatic_receiving ? date('Y-m-d 00:00:00', strtotime('+' . GoodIndent::AUTOMATIC_RECEIVING_DAY . ' day')) : null;
            return [
                'state'                  => GoodIndent::GOOD_INDENT_STATE_TAKE,
                'shipping_time'          => now()->toDateTimeString(),
                'dhl_id'                 => $data['dhl_id'],
                'odd'                    => $data['odd'],
                'is_automatic_receiving' => $is_automatic_receiving,
                'receiving_time'         => $receiving_time,
            ];
        });
    }

    public function receiving(array $data): bool
    {
        $this->operateName = '延长收货时间';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_RECEIVING;
        $this->operateContent = '延长收货时间';
        return $this->run(function (GoodIndentService $self) use ($data): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_TAKE) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            if (strtotime($data['receiving_time']) < strtotime($self->getGoodIndent()->receiving_time)) {
                return "延长收货时间不能小于当前收货时间";
            }
            return '';
        }, function (GoodIndentService $self) use ($data): array {
            return [
                'is_automatic_receiving' => GoodIndent::GOOD_INDENT_IS_AUTOMATIC_RECEIVING_YES,
                'receiving_time'         => $data['receiving_time'],
            ];
        });
    }

    public function receipt(): bool
    {
        $this->operateName = '收货';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_RECEIPT;
        $this->operateContent = '收货';
        return $this->run(function (GoodIndentService $self): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_TAKE) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self): array {
            return [
                'state'          => GoodIndent::GOOD_INDENT_STATE_ACCOMPLISH,
                'confirm_time'   => now()->toDateTimeString(),
                'receiving_time' => now()->toDateTimeString(),
            ];
        });
    }

    public function cancel(): bool
    {
        $this->operateName = '取消';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_CANCEL;
        $this->operateContent = '取消订单';
        return $this->run(function (GoodIndentService $self): string {
            if ($self->getGoodIndent()->state != GoodIndent::GOOD_INDENT_STATE_PAY) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self): array {
            GoodIndentCommodity::saveByCancel($self->getGoodIndent());
            return [
                'state' => GoodIndent::GOOD_INDENT_STATE_CANCEL,
            ];
        });
    }

    public function del(): bool
    {
        $this->operateName = '删除';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_DELETE;
        $this->operateContent = '删除订单';
        return $this->run(function (GoodIndentService $self): string {
            static $canDeleteStates = [
                GoodIndent::GOOD_INDENT_STATE_FAILURE,
                GoodIndent::GOOD_INDENT_STATE_CANCEL,
                GoodIndent::GOOD_INDENT_STATE_REFUND,
            ];
            if (!in_array($self->getGoodIndent()->state, $canDeleteStates)) {
                return "订单：{$self->getGoodIndent()->id}状态不正确";
            }
            return '';
        }, function (GoodIndentService $self): array {
            GoodIndentCommodity::saveByCancel($self->getGoodIndent());
            return [
                'deleted_at' => date('Y-m-d H:i:s'),
            ];
        });
    }

    /**
     * @param GoodIndentRefund $GoodIndentRefund
     * @return bool
     */
    public function refund($GoodIndentRefund): bool
    {
        $this->operateName = '退款';
        $this->operateType = GoodIndentLog::OPERATE_TYPE_REFUND;
        $this->operateContent = $GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_AUDIT ? '申请退款' : '审核退款';
        return $this->run(function (GoodIndentService $self): string {
            return '';
        }, function (GoodIndentService $self) use ($GoodIndentRefund): array {
            GoodIndentCommodity::saveByRefund($GoodIndentRefund);
            $refund_time = $self->getGoodIndent()->refund_time;
            if ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_AUDIT) {
                //发起退款
                $state = GoodIndent::GOOD_INDENT_STATE_REFUND_PROCESSING;
            } elseif ($GoodIndentRefund->state == GoodIndentRefund::GOOD_INDENT_REFUND_STATE_PASS) {
                //退款成功
                $state = GoodIndent::GOOD_INDENT_STATE_REFUND;
                $refund_time = now()->toDateTimeString();
            } else {
                //退款失败：状态恢复
                if (!$self->getGoodIndent()->shipping_time) {
                    $state = GoodIndent::GOOD_INDENT_STATE_DELIVER;
                } elseif (!$self->getGoodIndent()->confirm_time) {
                    $state = GoodIndent::GOOD_INDENT_STATE_TAKE;
                } else {
                    $state = GoodIndent::GOOD_INDENT_STATE_ACCOMPLISH;
                }
            }
            $refund_amount = GoodIndentCommodity::withTrashed()->where('good_indent_id', $self->getGoodIndent()->id)->sum('refund_total');
            return [
                'state'         => $state,
                'refund_time'   => $refund_time,
                'refund_amount' => $refund_amount,
            ];
        });
    }

    private function run(\Closure $validator, \Closure $update, \Closure $event = null): bool
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $id = $this->id;
            if ($this->user instanceof AppUser) {
                //避免用户非法操作并锁定其他人订单
                $id = GoodIndent::where('id', $this->id)->where('user_id', $this->user->id)->value('id');
                throw_if(!$id, 'RuntimeException', "订单：{$this->id}不存在或已删除");
            }

            //锁单
            $lock = RedisLock::lock($redis, 'UpdateGoodIndent_' . $id);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }

            /**
             * 初始化订单详情
             * @var GoodIndent $goodIndent
             */
            $goodIndent = GoodIndent::getDetail($id) or throw_if(true, 'RuntimeException', "订单：{$id}不存在或已删除");
            $this->goodIndent = $goodIndent;

            //触发验证器
            $message = call_user_func_array($validator, [$this]);
            throw_if($message, 'RuntimeException', $message);

            //获取更新内容
            $updateData = call_user_func_array($update, [$this]);
            $success = empty($updateData) || GoodIndent::withTrashed()->where('id', $goodIndent->id)->update($updateData);
            foreach ($updateData as $k => $v) {
                $this->goodIndent->$k = $v;
            }

            //记录操作日志
            $this->operateType and GoodIndentLog::saveByGoodIndent($goodIndent, $this->operator, $this->operateType, $this->operateContent);

            if ($success) {
                $success = true;
                $this->message = $this->operateName . '成功';
                if ($event) {
                    $eventObj = call_user_func_array('event', [$this]);
                    $eventObj and event($eventObj);
                }
            } else {
                throw new Exception($this->operateName . '失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $this->message = $e->getMessage();
            logErr($id . '订单' . $this->operateName . '失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'UpdateGoodIndent_' . $id);
        }
        return $success;
    }

    /**
     * @return GoodIndent
     */
    public function getGoodIndent()
    {
        return $this->goodIndent;
    }

    public function getData(): array
    {
        return $this->data;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->message;
    }
}
