<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppUserFollow;
use Illuminate\Http\Request;

class AppUserFollowController extends Controller
{
    public function setFollowing(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|gt:0',
        ]);
        $result = AppUserFollow::setFollowing($request->user()->id, $request->user_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelFollowing(Request $request)
    {
        $request->validate([
            'user_id' => 'required|integer|gt:0',
        ]);
        $result = AppUserFollow::cancelFollowing($request->user()->id, $request->user_id);
        return ResponseHelper::result(...$result);
    }

    public function getFollowingList(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
            'user_id'  => 'required|integer|gt:0',
        ]);
        $formData = $request->all();
        // 传递当前登录用户的ID
        $currentUserId = $request->user() ? $request->user()->id : 0;
        $result = AppUserFollow::getFollowingList($formData, $currentUserId);
        return ResponseHelper::result(...$result);
    }

    public function getFansList(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
            'user_id'  => 'required|integer|gt:0',
        ]);
        $formData = $request->all();
        // 传递当前登录用户的ID
        $currentUserId = $request->user() ? $request->user()->id : 0;
        $result = AppUserFollow::getFansList($formData, $currentUserId);
        return ResponseHelper::result(...$result);
    }
}
