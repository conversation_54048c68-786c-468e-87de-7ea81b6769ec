<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 宠物疫苗记录模型
 */
class PetVaccineRecord extends Model
{
    use HasFactory;

    protected $table = 'pets_vaccine_records';

    protected $fillable = [
        'pet_id',
        'vaccine_name',
        'vaccine_date',
        'vaccine_batch',
        'hospital_name',
        'doctor_name',
        'next_vaccine_date',
        'remark',
        'created_by',
        'admin_id'
    ];

    protected $casts = [
        'vaccine_date' => 'date',
        'next_vaccine_date' => 'date'
    ];

    // 创建人类型常量
    const CREATED_BY_ADMIN = 'admin';
    const CREATED_BY_SYSTEM = 'system';

    /**
     * 关联宠物
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * 关联创建管理员
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * 获取创建人类型名称
     */
    public function getCreatedByNameAttribute()
    {
        $types = [
            self::CREATED_BY_ADMIN => '管理员',
            self::CREATED_BY_SYSTEM => '系统'
        ];

        return $types[$this->created_by] ?? '未知';
    }

    /**
     * 获取所有创建人类型
     */
    public static function getCreatedByTypes()
    {
        return [
            self::CREATED_BY_ADMIN => '管理员',
            self::CREATED_BY_SYSTEM => '系统'
        ];
    }

    /**
     * 作用域：按疫苗名称筛选
     */
    public function scopeOfVaccine($query, $vaccineName)
    {
        return $query->where('vaccine_name', 'like', "%{$vaccineName}%");
    }

    /**
     * 作用域：按接种日期范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('vaccine_date', [$startDate, $endDate]);
    }

    /**
     * 作用域：即将到期的疫苗（30天内）
     */
    public function scopeUpcoming($query)
    {
        return $query->whereNotNull('next_vaccine_date')
                    ->whereBetween('next_vaccine_date', [now(), now()->addDays(30)]);
    }

    /**
     * 作用域：已过期的疫苗
     */
    public function scopeOverdue($query)
    {
        return $query->whereNotNull('next_vaccine_date')
                    ->where('next_vaccine_date', '<', now());
    }

    public static function addReminder($record, $days = 0)
    {
        if ($days > 0) {
            $content = "您的寵物 {$record->pet->name} 疫苗接種還有 {$days} 天就開始，請按時給其打針！";
        } else {
            $content = "您的寵物 {$record->pet->name} 疫苗接種今天開始，請按時給其打針！";
        }

        // 创建提醒
        $userIds = PetMaster::getPetFamilyUserIds($record->pet_id);
        foreach ($userIds as $userId) {
            //发送给所有家人
            $reminder = PetReminder::create([
                'user_id'      => $userId,
                'pet_id'       => $record->pet_id,
                'type'         => PetReminder::TYPE_PET_FILE_VACCINE,
                'title'        => PetReminder::MAPPING[PetReminder::TYPE_PET_FILE_VACCINE]['label'],
                'content'      => $content,
                'trigger_date' => now()->format('Y-m-d'),
            ]);

            // 创建备忘录
            PetReminderMemo::create([
                'user_id'     => $userId,
                'pet_id'      => $record->pet_id,
                'reminder_id' => $reminder->id,
            ]);
        }
    }
}
