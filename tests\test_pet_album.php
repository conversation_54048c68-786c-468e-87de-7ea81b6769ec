<?php

require_once 'vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== 测试宠物详情返回最新相册照片 ===\n";

try {
    // 测试获取宠物详情
    $pet = \App\Models\Pet::getDetail(1);

    if ($pet) {
        echo "宠物ID: " . $pet['id'] . "\n";
        echo "宠物名称: " . $pet['name'] . "\n";
        echo "最新相册照片: " . ($pet['latest_album_photo'] ? $pet['latest_album_photo']['image_url'] : '无') . "\n";

        if ($pet['latest_album_photo']) {
            echo "照片类型: " . $pet['latest_album_photo']['image_type'] . "\n";
            echo "上传时间: " . $pet['latest_album_photo']['created_at'] . "\n";
        }
    } else {
        echo "宠物不存在\n";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 测试宠物列表返回最新相册照片 ===\n";

try {
    // 测试获取宠物列表
    $pets = \App\Models\Pet::getList(['per_page' => 2]);

    foreach ($pets->items() as $pet) {
        echo "宠物ID: {$pet['id']}, 名称: {$pet['name']}\n";
        echo "  最新相册照片: " . ($pet['latest_album_photo'] ? $pet['latest_album_photo']['image_url'] : '无') . "\n";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 检查相册表数据 ===\n";

try {
    $albumCount = \App\Models\PetAlbum::count();
    echo "相册总数: $albumCount\n";

    $latestAlbum = \App\Models\PetAlbum::orderBy('created_at', 'desc')->first();
    if ($latestAlbum) {
        echo "最新相册照片: 宠物ID {$latestAlbum->pet_id}, 图片: {$latestAlbum->image_url}\n";
    }
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
