<?php

namespace App\Http\Controllers\Mobile;

use App\Http\Controllers\Controller;
use App\Models\PetMaster;
use App\Models\GoodRecommendation;
use App\Helpers\ResponseHelper;
use Illuminate\Http\Request;

/**
 * 商品推荐控制器 - 遵循MVC设计模式
 */
class GoodRecommendationController extends Controller
{
    /**
     * 为你推荐接口
     *
     * @param Request $request JSON请求参数
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRecommendationsForPets(Request $request)
    {
        // 参数验证
        $request->validate([
            'pet_ids' => 'required|array|min:1|max:10',
            'pet_ids.*' => 'integer|exists:pets,id',
            'page' => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,50',
        ]);

        $userId = $request->user()->id;
        $petIds = $request->input('pet_ids');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);

        // 验证用户对所有宠物的权限
        foreach ($petIds as $petId) {
            if (!PetMaster::isMaster($petId, $userId)) {
                return ResponseHelper::error("您没有权限访问宠物ID {$petId} 的推荐信息");
            }
        }

        // 生成缓存键
        $sortedPetIds = $petIds;
        sort($sortedPetIds);
        $petIdsStr = implode(',', $sortedPetIds);
        $cacheKey = "pets_recommendations_v3:{$petIdsStr}:{$page}:{$perPage}";

        // 尝试从缓存获取
        $redis = redis();
        $cachedResult = $redis->get($cacheKey);
        if ($cachedResult) {
            $result = json_decode($cachedResult, true);
            return ResponseHelper::success($result);
        }

        // 调用Model层获取推荐结果
        $result = GoodRecommendation::getRecommendationsForUser($userId, $petIds, $page, $perPage);

        // 缓存结果到 Redis，设置过期时间为 1 小时
        $redis->setex($cacheKey, 3600, json_encode($result));

        return ResponseHelper::success($result);
    }



    /**
     * 清除推荐缓存
     *
     * @param array|null $petIds 宠物ID数组
     */
    public static function clearCache($petIds = null)
    {
        GoodRecommendation::clearCache($petIds);
    }

    /**
     * 测试筛选逻辑 - 用于验证SKU匹配是否正确
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function testFilterLogic(Request $request)
    {
        // 参数验证
        $request->validate([
            'pet_ids' => 'required|array|min:1|max:10',
            'pet_ids.*' => 'integer|exists:pets,id',
        ]);

        $userId = $request->user()->id;
        $petIds = $request->input('pet_ids');

        // 验证用户对所有宠物的权限
        foreach ($petIds as $petId) {
            if (!PetMaster::isMaster($petId, $userId)) {
                return ResponseHelper::error("您没有权限访问宠物ID {$petId} 的推荐信息");
            }
        }

        // 调用Model层测试方法
        $result = GoodRecommendation::testFilterLogic($userId, $petIds);

        return ResponseHelper::success($result);
    }

}
