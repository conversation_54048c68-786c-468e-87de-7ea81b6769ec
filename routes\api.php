<?php

use App\Http\Controllers\Mobile\PetAnalysisController;
use App\Http\Controllers\Api\SimpleAIRecommendationController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| 用户端API
|--------------------------------------------------------------------------
*/
Route::prefix('/mobile')->middleware(['auth:api', 'app.user.log'])->group(function () {
    //AI商品推荐 (放在最前面避免路由冲突)
    Route::prefix('aiRecommendation')->group(function () {
        Route::post('/bySession', [SimpleAIRecommendationController::class, 'getRecommendationsBySession']);
    });

    Route::post('/sendSms', [\App\Http\Controllers\SmsController::class, 'sendSms'])->withoutMiddleware('auth:api');
    Route::post('/mobileLogin', [\App\Http\Controllers\Mobile\AppUserController::class, 'mobileLogin'])->withoutMiddleware('auth:api');
    Route::post('/updateUser', [\App\Http\Controllers\Mobile\AppUserController::class, 'updateUser']);
    Route::get('/profile', [\App\Http\Controllers\Mobile\AppUserController::class, 'profile']);
    Route::post('/deleteAccount', [\App\Http\Controllers\Mobile\AppUserController::class, 'deleteAccount']);

    //宠物
    Route::prefix('/pet')->group(function () {
        Route::get('/petTypeBreed', [\App\Http\Controllers\Mobile\PetController::class, 'petTypeBreed']);
        Route::post('/save', [\App\Http\Controllers\Mobile\PetController::class, 'saveDetail']);
        Route::get('/list', [\App\Http\Controllers\Mobile\PetController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\PetController::class, 'getDetail']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\PetController::class, 'delete']);

        Route::post('/sendFamilyRequest', [\App\Http\Controllers\Mobile\PetController::class, 'sendFamilyRequest']);
        Route::get('/getFamilyRequest', [\App\Http\Controllers\Mobile\PetController::class, 'getFamilyRequest']);
        Route::post('/handleFamilyRequest', [\App\Http\Controllers\Mobile\PetController::class, 'handleFamilyRequest']);

        Route::post('/createCode', [\App\Http\Controllers\Mobile\PetController::class, 'createCode']);
        Route::get('/getCode', [\App\Http\Controllers\Mobile\PetController::class, 'getCode']);
        Route::post('/cancelCode', [\App\Http\Controllers\Mobile\PetController::class, 'cancelCode']);
        Route::post('/receivePet', [\App\Http\Controllers\Mobile\PetController::class, 'receivePet']);

        Route::get('/getPetFamily', [\App\Http\Controllers\Mobile\PetController::class, 'getPetFamily']);
        Route::post('/removeFamilyMember', [\App\Http\Controllers\Mobile\PetController::class, 'removeFamilyMember']);
        Route::post('/transferOwner', [\App\Http\Controllers\Mobile\PetController::class, 'transferOwner']);
        Route::post('/updatePetSort', [\App\Http\Controllers\Mobile\PetController::class, 'updatePetSort']);

        // 营养需求分析接口
        Route::get('/nutrition-requirements', [\App\Http\Controllers\Mobile\PetController::class, 'nutritionRequirements']);
        Route::post('/batch-nutrition-requirements', [\App\Http\Controllers\Mobile\PetController::class, 'batchNutritionRequirements']);
    });

    // 疫苗记录 / 医疗档案
    Route::prefix('/petRecord')->group(function () {
        Route::get('/vaccine/list', [\App\Http\Controllers\Mobile\PetRecordController::class, 'getVaccineList']);
        Route::post('/vaccine/delete', [\App\Http\Controllers\Mobile\PetRecordController::class, 'deleteVaccineCard']);

        Route::get('/medical/list', [\App\Http\Controllers\Mobile\PetRecordController::class, 'getMedicalList']);
        Route::post('/medical/delete', [\App\Http\Controllers\Mobile\PetRecordController::class, 'deleteMedicalRecord']);

        Route::post('/birthCertificate/delete', [\App\Http\Controllers\Mobile\PetRecordController::class, 'deleteBirthCertificate']);
        Route::post('/chipCode/delete', [\App\Http\Controllers\Mobile\PetRecordController::class, 'deleteChipCode']);
    });


    //宠物相册
    Route::prefix('petAlbum')->group(function () {
        Route::post('/save', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'save']);
        Route::get('/list', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'getList']);
        Route::post('/edit', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'edit']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'delete']);
        Route::post('/likeAlbum', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'likeAlbum']);
        Route::post('/cancelLikeAlbum', [\App\Http\Controllers\Mobile\PetAlbumController::class, 'cancelLikeAlbum']);
    });

    //社交
    Route::get('/getAppUserInfo', [\App\Http\Controllers\Mobile\AppUserController::class, 'getAppUserInfo']);
    Route::get('/searchUsers', [\App\Http\Controllers\Mobile\AppUserController::class, 'searchUsers']);

    //关注
    Route::prefix('/appUserFollow')->group(function () {
        Route::post('/setFollowing', [\App\Http\Controllers\Mobile\AppUserFollowController::class, 'setFollowing']);
        Route::post('/cancelFollowing', [\App\Http\Controllers\Mobile\AppUserFollowController::class, 'cancelFollowing']);
        Route::get('/getFollowingList', [\App\Http\Controllers\Mobile\AppUserFollowController::class, 'getFollowingList']);
        Route::get('/getFansList', [\App\Http\Controllers\Mobile\AppUserFollowController::class, 'getFansList']);
    });

    //轮播图
    Route::prefix('/carousel')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\CarouselController::class, 'getCarousels']);
    });

    //内容相关
    Route::prefix('/appContent')->group(function () {
        Route::get('/index', [\App\Http\Controllers\Mobile\AppContentController::class, 'index'])->withoutMiddleware('auth:api');
        Route::get('/stranger', [\App\Http\Controllers\Mobile\AppContentController::class, 'stranger']);
        Route::get('/nearby', [\App\Http\Controllers\Mobile\AppContentController::class, 'nearby']);
        Route::get('/list', [\App\Http\Controllers\Mobile\AppContentController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\AppContentController::class, 'detail'])->withoutMiddleware('auth:api');
        Route::post('/save', [\App\Http\Controllers\Mobile\AppContentController::class, 'save']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\AppContentController::class, 'delete']);
        Route::post('/likeContent', [\App\Http\Controllers\Mobile\AppContentController::class, 'likeContent']);
        Route::post('/cancelLikeContent', [\App\Http\Controllers\Mobile\AppContentController::class, 'cancelLikeContent']);

        // 新版接口
        Route::get('/unifiedFeed', [\App\Http\Controllers\Mobile\AppContentController::class, 'unifiedFeed'])->withoutMiddleware('auth:api');
        Route::post('/saveWithType', [\App\Http\Controllers\Mobile\AppContentController::class, 'saveWithType']);

        // 标签相关
        Route::get('/tags', [\App\Http\Controllers\Mobile\AppContentController::class, 'getTags'])->withoutMiddleware('auth:api');
        Route::get('/related-by-tags', [\App\Http\Controllers\Mobile\AppContentController::class, 'getRelatedContentsByTags'])->withoutMiddleware('auth:api');

        // @提及相关
        Route::get('/mentionable-users', [\App\Http\Controllers\Mobile\AppContentController::class, 'getMentionableUsers']);
    });

    //内容收藏
    Route::prefix('/appContentFavorite')->group(function () {
        Route::post('/setFavorite', [\App\Http\Controllers\Mobile\AppContentFavoriteController::class, 'setFavorite']);
        Route::post('/cancelFavorite', [\App\Http\Controllers\Mobile\AppContentFavoriteController::class, 'cancelFavorite']);
        Route::post('/setIsPrivate', [\App\Http\Controllers\Mobile\AppContentFavoriteController::class, 'setIsPrivate']);
        Route::get('/getFavoriteList', [\App\Http\Controllers\Mobile\AppContentFavoriteController::class, 'getFavoriteList']);
    });

    //内容评论
    Route::prefix('/appContentComment')->group(function () {
        Route::post('/addComment', [\App\Http\Controllers\Mobile\AppContentCommentController::class, 'addComment']);
        Route::post('/delComment', [\App\Http\Controllers\Mobile\AppContentCommentController::class, 'delComment']);
        Route::get('/getCommentList', [\App\Http\Controllers\Mobile\AppContentCommentController::class, 'getCommentList']);
        Route::post('/likeComment', [\App\Http\Controllers\Mobile\AppContentCommentController::class, 'likeComment']);
        Route::post('/cancelLikeComment', [\App\Http\Controllers\Mobile\AppContentCommentController::class, 'cancelLikeComment']);
    });

    //好友
    Route::prefix('/friends')->group(function () {
        Route::post('/sendRequest', [\App\Http\Controllers\Mobile\FriendController::class, 'sendRequest']);
        Route::post('/handleRequest', [\App\Http\Controllers\Mobile\FriendController::class, 'handleRequest']);
        Route::get('/list', [\App\Http\Controllers\Mobile\FriendController::class, 'getFriendList']);
    });

    //首页提醒
    Route::prefix('/pet-reminder')->group(function () {
        Route::get('/getOne', [\App\Http\Controllers\Mobile\PetReminderController::class, 'getOne']);
        Route::get('/list', [\App\Http\Controllers\Mobile\PetReminderController::class, 'getList']);
        Route::get('/unread-count', [\App\Http\Controllers\Mobile\PetReminderController::class, 'getUnreadCount']);
        Route::post('/mark-read', [\App\Http\Controllers\Mobile\PetReminderController::class, 'markRead']);
    });

    //备忘录
    Route::prefix('/pet-reminder-memo')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\PetReminderMemoController::class, 'getList']);
        Route::get('/unread-count', [\App\Http\Controllers\Mobile\PetReminderMemoController::class, 'getUnreadCount']);
        Route::post('/mark-read', [\App\Http\Controllers\Mobile\PetReminderMemoController::class, 'markRead']);
    });

    //系统通知
    Route::prefix('/system-notification')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\NotificationLogController::class, 'getList'])->name('system-notifications.list');
        Route::get('/detail', [\App\Http\Controllers\Mobile\NotificationLogController::class, 'getDetail'])->name('system-notifications.detail');
        Route::get('/unread-count', [\App\Http\Controllers\Mobile\NotificationLogController::class, 'getUnreadCount']);
        Route::post('/mark-read', [\App\Http\Controllers\Mobile\NotificationLogController::class, 'markRead']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\NotificationLogController::class, 'delete']);
    });

    //通知设置
    Route::prefix('/notification-settings')->group(function () {
        Route::get('/', [\App\Http\Controllers\Mobile\NotificationSettingController::class, 'getSettings']);
        Route::post('/', [\App\Http\Controllers\Mobile\NotificationSettingController::class, 'updateSettings']);
    });

    // 系统公告通过系统通知实现，直接使用系统通知接口，指定 type=system_announcement 即可
    // 例如：/mobile/system-notification/list?type=system_announcement

    Route::prefix('/version')->group(function () {
        Route::prefix('/v1')->group(function () {
            Route::get('/info', [\App\Http\Controllers\Mobile\VersionController::class, 'info'])->withoutMiddleware('auth:api');
        });
    });

    Route::prefix('petChat')->group(function () {
        // 获取所有分析类型
        Route::get('/types', [PetAnalysisController::class, 'getAnalysisTypes']);

        // 带参数路径的分析接口
        Route::post('/{type}', [PetAnalysisController::class, 'analyze'])
            ->where('type', 'food|stool|health|ingredient');

        // 保存AI回复消息（用于流式响应后由前端调用）
        Route::post('/saveAIMessage', [PetAnalysisController::class, 'saveAIMessage']);

        // 快捷提问接口（统一接口）
        Route::get('/getQuickQuestions', [\App\Http\Controllers\Mobile\AnalysisQuickQuestionController::class, 'getQuickQuestions']);
    });

    // 宠物证件上传识别接口
    Route::prefix('petFile')->group(function () {
        Route::post('/uploadCertificate', [PetAnalysisController::class, 'uploadCertificate']);
        Route::post('/bindCertificate', [PetAnalysisController::class, 'bindCertificate']);
        Route::get('/getUserCertificateList', [PetAnalysisController::class, 'getUserCertificateList']);
        Route::get('/getPetCertificateOverview', [PetAnalysisController::class, 'getPetCertificateOverview']);

        // 实验室报告（血液/尿液）分析
        Route::prefix('labReport')->group(function () {
            Route::post('/analyze', [PetAnalysisController::class, 'analyzeLabReport']);
        });

    });

    // 聊天会话管理
    Route::prefix('chatSession')->group(function () {
        // 获取会话列表
        Route::get('/list', [\App\Http\Controllers\Mobile\ChatSessionController::class, 'getSessions']);
        // 获取会话详情及消息
        Route::get('/detail', [\App\Http\Controllers\Mobile\ChatSessionController::class, 'getSessionDetail']);
        // 删除会话
        Route::post('/delete', [\App\Http\Controllers\Mobile\ChatSessionController::class, 'deleteSession']);
    });

    //商品
    Route::prefix('good')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\GoodController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\GoodController::class, 'detail']);
        Route::get('/category', [\App\Http\Controllers\Mobile\GoodController::class, 'category']);
    });

    //商城订单
    Route::prefix('goodIndent')->group(function () {
        Route::get('/getShoppingCart', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'getShoppingCart']);
        Route::get('/getCartCount', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'getCartCount']);
        Route::post('/addShoppingCart', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'addShoppingCart']);
        Route::post('/updatePetIds', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'updatePetIds']);
        Route::post('/removeShoppingCart', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'removeShoppingCart']);
        Route::post('/clearShoppingCart', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'clearShoppingCart']);
        Route::get('/list', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'getList']);
        Route::get('/nums', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'nums']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'getDetail']);
        Route::post('/confirm', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'confirm']);
        Route::post('/create', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'create']);
        Route::post('/repay', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'repay']);
        Route::post('/cancel', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'cancel']);
        Route::post('/receipt', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'receipt']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\GoodIndentController::class, 'delete']);
    });

    //商城订单退款
    Route::prefix('goodIndentRefund')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'getList']);
        Route::get('/nums', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'nums']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'getDetail']);
        Route::post('/create', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'create']);
        Route::post('/cancel', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'cancel']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\GoodIndentRefundController::class, 'delete']);
    });

    //浏览记录
    Route::prefix('browse')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\BrowseController::class, 'getList']);
        Route::post('/save', [\App\Http\Controllers\Mobile\BrowseController::class, 'save']);
    });

    //配送地址管理
    Route::prefix('shipping')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\ShippingController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\ShippingController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Mobile\ShippingController::class, 'save']);
        Route::post('/freight', [\App\Http\Controllers\Mobile\ShippingController::class, 'freight']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\ShippingController::class, 'delete']);
        Route::post('/defaultSet', [\App\Http\Controllers\Mobile\ShippingController::class, 'defaultSet']);
    });

    //商品收藏
    Route::prefix('goodCollect')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\GoodCollectController::class, 'getList']);
        Route::post('/collect', [\App\Http\Controllers\Mobile\GoodCollectController::class, 'collect']);
        Route::post('/cancelCollect', [\App\Http\Controllers\Mobile\GoodCollectController::class, 'cancelCollect']);
    });

    //商品推荐 (统一接口)
    Route::prefix('good-recommendation')->group(function () {
        Route::post('/pets', [\App\Http\Controllers\Mobile\GoodRecommendationController::class, 'getRecommendationsForPets']);
        Route::post('/test-filter', [\App\Http\Controllers\Mobile\GoodRecommendationController::class, 'testFilterLogic']);
    });

    //文章
    Route::prefix('article')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Mobile\ArticleController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\ArticleController::class, 'getDetail']);
    });

    //咨询
    Route::prefix('consult')->group(function () {
        Route::get('/waiting', [\App\Http\Controllers\Mobile\ConsultController::class, 'waitingList']);
        Route::get('/list', [\App\Http\Controllers\Mobile\ConsultController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Mobile\ConsultController::class, 'getDetail']);
        Route::post('/check', [\App\Http\Controllers\Mobile\ConsultController::class, 'submitCheck']);
        Route::post('/create', [\App\Http\Controllers\Mobile\ConsultController::class, 'create']);
        Route::get('/lineupCount', [\App\Http\Controllers\Mobile\ConsultController::class, 'lineupCount']);
        Route::post('/cancel', [\App\Http\Controllers\Mobile\ConsultController::class, 'cancel']);
        Route::post('/delete', [\App\Http\Controllers\Mobile\ConsultController::class, 'delete']);
        Route::post('/getOTP', [\App\Http\Controllers\Mobile\ConsultController::class, 'getOTP']);
        Route::post('/getAccessToken', [\App\Http\Controllers\Mobile\ConsultController::class, 'getAccessToken']);
        Route::post('/end', [\App\Http\Controllers\Mobile\ConsultController::class, 'end']);
    });

    //营养师
    Route::prefix('dietitian')->middleware('permission.dietitian')->group(function () {
        Route::get('/getDietitianList', [\App\Http\Controllers\Mobile\DietitianController::class, 'getDietitianList']);
        Route::get('/waitingConsultList', [\App\Http\Controllers\Mobile\DietitianController::class, 'waitingConsultList']);
        Route::get('/bookingConsultList', [\App\Http\Controllers\Mobile\DietitianController::class, 'bookingConsultList']);
        Route::get('/getConsultList', [\App\Http\Controllers\Mobile\DietitianController::class, 'getConsultList']);
        Route::get('/getConsultDetail', [\App\Http\Controllers\Mobile\DietitianController::class, 'getConsultDetail']);
        Route::post('/getOTP', [\App\Http\Controllers\Mobile\DietitianController::class, 'getOTP']);
        Route::post('/getAccessToken', [\App\Http\Controllers\Mobile\DietitianController::class, 'getAccessToken']);
        Route::post('/setOnlineStatus', [\App\Http\Controllers\Mobile\DietitianController::class, 'setOnlineStatus']);
        Route::post('/acceptBook', [\App\Http\Controllers\Mobile\DietitianController::class, 'acceptBook']);
        Route::post('/acceptConsult', [\App\Http\Controllers\Mobile\DietitianController::class, 'acceptConsult']);
        Route::post('/saveMessage', [\App\Http\Controllers\Mobile\DietitianController::class, 'saveMessage']);
        Route::post('/endConsult', [\App\Http\Controllers\Mobile\DietitianController::class, 'endConsult']);
        Route::post('/setSummary', [\App\Http\Controllers\Mobile\DietitianController::class, 'setSummary']);
    });
});

Route::prefix('merchants')->middleware('auth:merchants')->group(function () {
    Route::post('/sendSms', [App\Http\Controllers\SmsController::class, 'sendSms'])->withoutMiddleware('auth:merchants');
    Route::post('/mobileLogin', [\App\Http\Controllers\Merchants\MerchantAuthController::class, 'mobileLogin'])->withoutMiddleware('auth:merchants');
});


/*
|--------------------------------------------------------------------------
| 公共API
|--------------------------------------------------------------------------
*/
Route::prefix('/common')->group(function () {
    Route::post('/testEmail', [\App\Http\Controllers\CommonController::class, 'testEmail']);
    Route::post('/testJob', [\App\Http\Controllers\CommonController::class, 'testJob'])->middleware(['log.response:simple']);
    Route::post('/testServerError', [\App\Http\Controllers\CommonController::class, 'testServerError']);
});
//oss
Route::prefix('/oss')->group(function () {
    Route::post('/upload', [\App\Http\Controllers\UploadOSSController::class, 'upload']);
    Route::post('/uploadPackage', [\App\Http\Controllers\UploadOSSController::class, 'uploadPackage']);
});
//webhook
Route::prefix('/webHooks')->group(function () {
    Route::post('/handle', [\App\Http\Controllers\WebhookController::class, 'github']);
    Route::post('/goEasy', [\App\Http\Controllers\WebhookController::class, 'goEasy']);
});
//支付
Route::prefix('/payment')->group(function () {
    Route::post('/qfpay_notify', [\App\Http\Controllers\PaymentController::class, 'qfpay_notify'])->middleware(['log.response:simple']);
});


/*
|--------------------------------------------------------------------------
| 管理员API
|--------------------------------------------------------------------------
*/
Route::prefix('admin')->middleware(['auth:admin', 'permission.admin', 'online:admin'])->group(function () {
    Route::prefix('/systemMenu')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\SystemMenuController::class, 'getList']);
        Route::post('/save', [\App\Http\Controllers\Admin\SystemMenuController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\SystemMenuController::class, 'delete']);
    });

    Route::prefix('/user')->group(function () {
        Route::get('/profile', [\App\Http\Controllers\Admin\AuthController::class, 'profile'])->withoutMiddleware(['permission.admin']);
        Route::get('/getUsers', [\App\Http\Controllers\Admin\AuthController::class, 'getUsers']);
        Route::post('/addUser', [\App\Http\Controllers\Admin\AuthController::class, 'addUser']);
        Route::post('/updateUser', [\App\Http\Controllers\Admin\AuthController::class, 'updateUser']);
        Route::post('/deleteUser', [\App\Http\Controllers\Admin\AuthController::class, 'deleteUser']);
        Route::get('/userDetail', [\App\Http\Controllers\Admin\AuthController::class, 'userDetail']);
        Route::get('/getOnlineUsers', [\App\Http\Controllers\Admin\AuthController::class, 'getOnlineUsers'])->withoutMiddleware(['online:admin', 'permission.admin']);
    });

    Route::prefix('/operateLog')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\OperateLogController::class, 'getList']);
        Route::get('/export', [\App\Http\Controllers\Admin\OperateLogController::class, 'exportList'])->middleware('export');
    });

    Route::post('/login', [\App\Http\Controllers\Admin\AuthController::class, 'loginUser'])->withoutMiddleware(['auth:admin', 'permission.admin']);
    Route::post('/logout', [\App\Http\Controllers\Admin\AuthController::class, 'logout'])->withoutMiddleware(['permission.admin']);

    //角色管理
    Route::prefix('/role')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\RoleController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\RoleController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\RoleController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\RoleController::class, 'delete']);
    });

    //APP版本管理
    Route::prefix('/version')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\VersionController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\VersionController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\VersionController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\VersionController::class, 'delete']);
    });

    //日志管理
    Route::prefix('/log')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\LogController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\LogController::class, 'getDetail']);
    });

    //轮播图管理
    Route::prefix('/carousel')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\CarouselController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\CarouselController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\CarouselController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\CarouselController::class, 'delete']);
    });

    Route::prefix('/appUser')->group(function () {
        Route::get('/getAppUsers', [\App\Http\Controllers\Admin\AppUserController::class, 'getAppUsers'])->withoutMiddleware(['permission.admin']);
        Route::get('/getUserDetail', [\App\Http\Controllers\Admin\AppUserController::class, 'getUserDetail']);
        Route::post('/updateUserStatus', [\App\Http\Controllers\Admin\AppUserController::class, 'updateUserStatus']);
        Route::post('/updateUserIsAdmin', [\App\Http\Controllers\Admin\AppUserController::class, 'updateUserIsAdmin']);
        Route::post('/updateUserIsDietitian', [\App\Http\Controllers\Admin\AppUserController::class, 'updateUserIsDietitian']);
        Route::get('/getUserFollowingList', [\App\Http\Controllers\Admin\AppUserController::class, 'getUserFollowingList']);
    });

    //宠物种类
    Route::prefix('/petType')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\PetTypeController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\PetTypeController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\PetTypeController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\PetTypeController::class, 'delete']);
    });

    //宠物身体部位
    Route::prefix('/analysisBodyPart')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\AnalysisBodyPartController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\AnalysisBodyPartController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\AnalysisBodyPartController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\AnalysisBodyPartController::class, 'delete']);
    });

    //宠物部位症状
    Route::prefix('/bodyPartSymptom')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\BodyPartSymptomController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\BodyPartSymptomController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\BodyPartSymptomController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\BodyPartSymptomController::class, 'delete']);
    });

    //分析快捷问题
    Route::prefix('/analysisQuickQuestion')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\AnalysisQuickQuestionController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\AnalysisQuickQuestionController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\AnalysisQuickQuestionController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\AnalysisQuickQuestionController::class, 'delete']);
    });

    //宠物品种
    Route::prefix('/petBreed')->group(function () {
        Route::match(['GET', 'POST'], '/list', [\App\Http\Controllers\Admin\PetBreedController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\PetBreedController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\PetBreedController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\PetBreedController::class, 'delete']);
        Route::get('/availableSizes', [\App\Http\Controllers\Admin\PetBreedController::class, 'getAvailableSizes']);
        // 品种别称管理
        Route::post('/updateAliases', [\App\Http\Controllers\Admin\PetBreedController::class, 'updateAliases']);
        Route::get('/searchBreeds', [\App\Http\Controllers\Admin\PetBreedController::class, 'searchBreeds']);
    });

    //宠物
    Route::prefix('/pet')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\PetController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\PetController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\PetController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\PetController::class, 'delete']);
    });

    // 宠物证件统一管理（整合证件审核、疫苗记录、医疗档案）
    Route::prefix('/petCertificateManage')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\PetCertificateManageController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\PetCertificateManageController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\PetCertificateManageController::class, 'save']);
        Route::post('/delete', [\App\Http\Controllers\Admin\PetCertificateManageController::class, 'delete']);
    });

    // 分享管理
    Route::prefix('/appContent')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\AppContentController::class, 'getList']);
        Route::post('/delete', [\App\Http\Controllers\Admin\AppContentController::class, 'delete']);
    });

    // 应用分享标签管理
    Route::prefix('/shareTag')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\AppShareTagController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\AppShareTagController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\AppShareTagController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\AppShareTagController::class, 'delete']);
        Route::post('/batch-update-status', [\App\Http\Controllers\Admin\AppShareTagController::class, 'batchUpdateStatus']);
    });

    // 评论管理
    Route::prefix('/appContentComment')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\AppContentCommentController::class, 'getList']);
        Route::post('/delete', [\App\Http\Controllers\Admin\AppContentCommentController::class, 'delete']);
    });

    // 品牌管理
    Route::prefix('/brand')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\BrandController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\BrandController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\BrandController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\BrandController::class, 'delete']);
    });

    // 分类管理
    Route::prefix('/goodCategory')->group(function () {
        Route::get('/tree', [\App\Http\Controllers\Admin\GoodCategoryController::class, 'getTree'])->withoutMiddleware(['permission.admin']);
        Route::get('/list', [\App\Http\Controllers\Admin\GoodCategoryController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\GoodCategoryController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\GoodCategoryController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\GoodCategoryController::class, 'delete']);
    });

    // 运费模板
    Route::prefix('/freight')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\FreightController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\FreightController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\FreightController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\FreightController::class, 'delete']);
    });

    // 快递公司管理
    Route::prefix('/dhl')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\DhlController::class, 'getList']);
        Route::post('/detail', [\App\Http\Controllers\Admin\DhlController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\DhlController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\DhlController::class, 'delete']);
    });

    // 规格组管理
    Route::prefix('/specificationGroup')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\SpecificationGroupController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\SpecificationGroupController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\SpecificationGroupController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\SpecificationGroupController::class, 'delete']);
    });

    // 系统公告通过系统通知实现，使用通知管理接口发送系统公告
    // 例如：/api/admin/notification-management/system-notification 并设置 type=system_announcement

    // 规格管理
    Route::prefix('/specification')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\SpecificationController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\SpecificationController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\SpecificationController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\SpecificationController::class, 'delete']);
    });

    // 商品管理
    Route::prefix('/good')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\GoodController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\GoodController::class, 'getDetail']);
        Route::get('/specification', [\App\Http\Controllers\Admin\GoodController::class, 'specification']);
        Route::post('/save', [\App\Http\Controllers\Admin\GoodController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\GoodController::class, 'delete']);
        Route::post('/state', [\App\Http\Controllers\Admin\GoodController::class, 'state']);
        Route::post('/test-sku-filter-integration', [\App\Http\Controllers\Admin\GoodController::class, 'testSkuFilterIntegration']);
    });

    // SKU宠物筛选标签管理
    Route::prefix('/sku-pet-filter')->group(function () {
        Route::get('/params', [\App\Http\Controllers\Admin\SkuPetFilterController::class, 'getParamInfo']);
        Route::post('/save', [\App\Http\Controllers\Admin\SkuPetFilterController::class, 'saveSkuFilterTags']);
        Route::post('/validate', [\App\Http\Controllers\Admin\SkuPetFilterController::class, 'validateFilterTags']);
    });

    // SKU宠物匹配测试接口
    Route::prefix('/sku-pet-matching-test')->group(function () {
        Route::post('/user-pet-matching', [\App\Http\Controllers\Admin\SkuPetMatchingTestController::class, 'testUserPetMatching']);
        Route::post('/good-list-matching', [\App\Http\Controllers\Admin\SkuPetMatchingTestController::class, 'testGoodListMatching']);
        Route::get('/user-pets', [\App\Http\Controllers\Admin\SkuPetMatchingTestController::class, 'getUserPets']);
    });


    // 商品订单管理
    Route::prefix('/goodIndent')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\GoodIndentController::class, 'getList']);
        Route::get('/export', [\App\Http\Controllers\Admin\GoodIndentController::class, 'exportList'])->middleware('export');
        Route::get('nums', [\App\Http\Controllers\Admin\GoodIndentController::class, 'nums']);
        Route::get('/detail', [\App\Http\Controllers\Admin\GoodIndentController::class, 'getDetail']);
        Route::post('/create', [\App\Http\Controllers\Admin\GoodIndentController::class, 'create']);
        Route::post('/paid', [\App\Http\Controllers\Admin\GoodIndentController::class, 'paid']);
        Route::post('/shipment', [\App\Http\Controllers\Admin\GoodIndentController::class, 'shipment']);
        Route::post('/receiving', [\App\Http\Controllers\Admin\GoodIndentController::class, 'receiving']);
    });

    // 商品订单退款管理
    Route::prefix('/goodIndentRefund')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\GoodIndentRefundController::class, 'getList']);
        Route::get('nums', [\App\Http\Controllers\Admin\GoodIndentRefundController::class, 'nums']);
        Route::get('/detail', [\App\Http\Controllers\Admin\GoodIndentRefundController::class, 'getDetail']);
        Route::post('/audit', [\App\Http\Controllers\Admin\GoodIndentRefundController::class, 'audit']);
        Route::post('/refund', [\App\Http\Controllers\Admin\GoodIndentRefundController::class, 'refund']);
    });

    Route::prefix('/paymentLog')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\PaymentLogController::class, 'getList']);
        Route::get('/export', [\App\Http\Controllers\Admin\PaymentLogController::class, 'exportList'])->middleware('export');
    });

    // 通知管理
    Route::prefix('/notification-management')->group(function () {
        // 系统通知管理
        Route::get('/system-notifications', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'getSystemNotifications']);
        Route::post('/system-notification', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'createSystemNotification']);
        Route::post('/delete-system-notification', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'deleteSystemNotification']);
        Route::get('/notification-types', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'getNotificationTypes']);

        // 宠物提醒管理
        Route::get('/pet-reminders', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'getPetReminders']);
        Route::post('/pet-reminder', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'createPetReminder']);
        Route::post('/delete-pet-reminder', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'deletePetReminder']);
        Route::get('/reminder-types', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'getReminderTypes']);

        // 测试推送
        Route::post('/test-push', [\App\Http\Controllers\Admin\NotificationManagementController::class, 'testPushNotification']);
    });

    //宠物提醒配置
    Route::prefix('/petReminderConfig')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\PetReminderConfigController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\PetReminderConfigController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\PetReminderConfigController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\PetReminderConfigController::class, 'delete']);
    });

    // 推荐算法配置管理
    Route::prefix('/recommendationAlgorithmConfig')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\RecommendationAlgorithmConfigController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\RecommendationAlgorithmConfigController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\RecommendationAlgorithmConfigController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\RecommendationAlgorithmConfigController::class, 'delete']);
    });

    //文章分类
    Route::prefix('/articleCategory')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\ArticleCategoryController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\ArticleCategoryController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\ArticleCategoryController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\ArticleCategoryController::class, 'delete']);
    });

    //文章
    Route::prefix('/article')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\ArticleController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\ArticleController::class, 'getDetail']);
        Route::post('/save', [\App\Http\Controllers\Admin\ArticleController::class, 'saveDetail']);
        Route::post('/delete', [\App\Http\Controllers\Admin\ArticleController::class, 'delete']);
    });

    //咨询
    Route::prefix('/consult')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\ConsultController::class, 'getList']);
        Route::get('/detail', [\App\Http\Controllers\Admin\ConsultController::class, 'getDetail']);
    });

    Route::prefix('/consultMessage')->group(function () {
        Route::get('/list', [\App\Http\Controllers\Admin\ConsultMessageController::class, 'getList']);
    });
});
