<?php

namespace App\Services\SearchEngine;

use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\LazyCollection;
use <PERSON><PERSON>\Scout\Builder;
use Laravel\Scout\Engines\Engine;

class XunSearchEngine extends Engine
{
    /**
     * @var \XS
     */
    protected $xs;

    public function __construct(\XS $xs)
    {
        $this->xs = $xs;
    }

    /**
     * 更新给定模型索引
     *
     * @param \Illuminate\Database\Eloquent\Collection $models
     * @return void
     */
    public function update($models)
    {
        if ($models->isEmpty()) {
            return;
        }

        if ($this->usesSoftDelete($models->first()) && config('scout.soft_delete', false)) {
            $models->each->pushSoftDeleteMetadata();
        }

        $index = $this->xs->index;
        $models->map(function ($model) use ($index) {
            $array = $model->toSearchableArray();
            if (empty($array)) {
                return;
            }
            $doc = new \XSDocument;
            $doc->setFields($array);
            $index->update($doc);
        });
        $index->flushIndex();
    }

    /**
     * 从索引中移除给定模型
     *
     * @param \Illuminate\Database\Eloquent\Collection $models
     * @return void
     */
    public function delete($models)
    {
        $index = $this->xs->index;
        $models->map(function ($model) use ($index) {
            $index->del($model->getKey());
        });
        $index->flushIndex();
    }

    /**
     * 通过迅搜引擎执行搜索
     *
     * @param \Laravel\Scout\Builder $builder
     * @return mixed
     */
    public function search(Builder $builder)
    {
        return $this->performSearch($builder, array_filter(['hitsPerPage' => $builder->limit]));
    }

    /**
     * 分页实现
     *
     * @param \Laravel\Scout\Builder $builder
     * @param int                    $perPage
     * @param int                    $page
     * @return mixed
     */
    public function paginate(Builder $builder, $perPage, $page)
    {
        return $this->performSearch($builder, [
            'hitsPerPage' => $perPage,
            'page'        => $page - 1,
        ]);
    }

    /**
     * 返回给定搜索结果的主键
     *
     * @param mixed $results
     * @return \Illuminate\Support\Collection
     */
    public function mapIds($results)
    {
        return collect($results)->pluck('id')->values();
    }

    /**
     * 将搜索结果和模型实例映射起来
     */
    public function map(Builder $builder, $results, $model)
    {
        if (count($results) === 0) {
            return Collection::make();
        }

        $objectIds = collect($results)->pluck('id')->values()->all();
        $objectIdPositions = array_flip($objectIds);

        return $model->getScoutModelsByIds(
            $builder, $objectIds
        )->filter(function ($model) use ($objectIds) {
            return in_array($model->getScoutKey(), $objectIds);
        })->sortBy(function ($model) use ($objectIdPositions) {
            return $objectIdPositions[$model->getScoutKey()];
        })->values();
    }

    /**
     * 返回搜索结果总数
     *
     * @param mixed $results
     * @return int
     */
    public function getTotalCount($results)
    {
        return $this->xs->search->getLastCount();
    }

    protected function usesSoftDelete($model)
    {
        return in_array(SoftDeletes::class, class_uses_recursive($model));
    }

    // 执行搜索功能
    protected function performSearch(Builder $builder, array $options = [])
    {
        $search = $this->xs->search;
        \Log::info('startSearch: ' . $builder->query);

        if ($builder->callback) {
            return call_user_func(
                $builder->callback,
                $search,
                $builder->query,
                $options
            );
        }

        $search->setFuzzy()->setQuery($builder->query);
        collect($builder->wheres)->map(function ($value, $key) use ($search) {
            $search->addRange($key, $value, $value);
        });

        $offset = 0;
        $perPage = $options['hitsPerPage'];

        if (!empty($options['page'])) {
            $offset = $perPage * $options['page'];
        }

        $start = microtime(true);
        $data = $search->setLimit($perPage, $offset)->search();
        $end = microtime(true);
        \Log::info('endSearch: ' . ($end - $start) . 's');
        return $data;
    }

    /**
     * 獲取中文分詞
     * @param $text
     * @return array
     */
    public function getScwsWords($text)
    {
        $tokenizer = new \XSTokenizerScws();
        return $tokenizer->getResult($text);
    }

    /**
     * 獲取其他相關關鍵字
     * @param $text
     * @return array
     */
    public function getExpandedQuery($text)
    {
        return $this->xs->search->getExpandedQuery($text);
    }

    public function lazyMap(Builder $builder, $results, $model)
    {
        if (count($results) === 0) {
            return LazyCollection::make();
        }

        $objectIds = collect($results)->pluck('id')->values()->all();
        $objectIdPositions = array_flip($objectIds);

        return $model->queryScoutModelsByIds(
            $builder, $objectIds
        )->cursor()->filter(function ($model) use ($objectIds) {
            return in_array($model->getScoutKey(), $objectIds);
        })->sortBy(function ($model) use ($objectIdPositions) {
            return $objectIdPositions[$model->getScoutKey()];
        })->values();
    }

    public function flush($model)
    {
        $index = $this->xs->index;
        $index->clean();
    }

    public function createIndex($name, array $options = [])
    {
        throw new Exception('Xunsearch indexes are created automatically upon adding objects.');
    }

    public function deleteIndex($name)
    {
        $index = $this->xs->index;
        $index->del($name);
    }
}
