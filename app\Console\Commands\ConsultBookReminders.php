<?php

namespace App\Console\Commands;

use App\Models\Consult;
use App\Models\PetReminder;
use App\Models\PetReminderMemo;
use Illuminate\Console\Command;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Support\Facades\Log;

class ConsultBookReminders extends Command
{
    protected $signature = 'consult:book-reminders {days=7 : 提前几天生成咨询预约即将开始提醒}';


    protected $description = '生成预约即将开始提醒';

    public function handle()
    {
        $days = (int)$this->argument('days');
        $this->info("开始生成咨询预约即将开始提醒，提前{$days}天通知");

        $totalCount = 0;
        Consult::where('is_book', 1)
            ->where('book_status', Consult::CONSULT_BOOK_STATUS_SUCCESS)
            ->where('status', Consult::CONSULT_STATUS_WAITING)
            ->whereDate('book_time', '=', now()->addDays($days)->format('Y-m-d'))
            ->where('created_at', '>=', now()->subDays(30))  //只查询一个月内的
            ->select('id', 'user_id', 'pet_id', 'book_time')
            ->with([
                'pet' => function (Builder $q) {
                    $q->select('id', 'name');
                },
            ])
            ->chunkById(100, function ($consults) use ($days, &$totalCount) {
                foreach ($consults as $consult) {
                    if ($days > 0) {
                        $content = "您的寵物 {$consult->pet->name} 營養咨詢還有 {$days} 天就開始，請及時進入聊天！";
                    } else {
                        $clock = date('H:i', strtotime($consult->book_time));
                        $content = "您的寵物 {$consult->pet->name} 營養咨詢今天 {$clock} 開始，請及時進入聊天！";
                    }

                    // 创建提醒
                    $reminder = PetReminder::create([
                        'user_id'      => $consult->user_id,
                        'pet_id'       => $consult->pet_id,
                        'type'         => PetReminder::TYPE_PET_CONSULT_REMINDER,
                        'title'        => PetReminder::MAPPING[PetReminder::TYPE_PET_CONSULT_REMINDER]['label'],
                        'content'      => $content,
                        'trigger_date' => now()->format('Y-m-d'),
                        'status'       => 1,
                        'created_at'   => now(),
                        'updated_at'   => now(),
                    ]);

                    // 创建备忘录
                    PetReminderMemo::create([
                        'user_id'     => $consult->user_id,
                        'pet_id'      => $consult->pet_id,
                        'reminder_id' => $reminder->id,
                    ]);

                    $totalCount++;
                }
            });

        $this->info("共生成 {$totalCount} 条咨询即将开始提醒");
        Log::info("Generated {$totalCount} consult book start reminders");

        return self::SUCCESS;
    }
}
