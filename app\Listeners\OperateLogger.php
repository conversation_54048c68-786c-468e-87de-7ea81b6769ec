<?php

namespace App\Listeners;

use App\Events\DataChanged;
use App\Events\DataChangedPack;
use App\Models\OperateLog as OperateLogModel;

//写入操作日志
class OperateLogger
{
    private int $user_id;
    private string $ip;

    public function __construct()
    {
        $user = \Request::instance()->user();
        $this->user_id = $user ? (int)$user->id : 0;
        $this->ip = \Request::instance()->getClientIp();
    }

    public function handle(DataChanged $event)
    {
        $log = $event->buildLog();
        $log['user_id'] = $this->user_id;
        $log['ip'] = $this->ip;
        $log['created_at'] = date('Y-m-d H:i:s');
        if (empty($log['link_type'])) {
            throw new \Exception('未定义日志关联类型，需到OperateLinkModelProvider补充');
        }
        OperateLogModel::insert($log);
    }

    public function handlePack(DataChangedPack $events)
    {
        $log = null;
        foreach ($events as $event) {
            /**
             * @var DataChanged $event
             */
            if (is_null($log)) {
                $log = $event->buildLog();
                $log['user_id'] = $this->user_id;
                $log['ip'] = $this->ip;
                $log['created_at'] = date('Y-m-d H:i:s');
                if (empty($log['link_type'])) {
                    throw new \Exception('未定义日志关联类型，需到OperateLinkModelProvider补充');
                }
            } else {
                $append = $event->buildLog();
                if ($append['content']) {
                    $log['content'] .= PHP_EOL . '------------------' . PHP_EOL;
                    $log['content'] .= $append['content'];
                }
            }
        }
        $log['content'] = rtrim($log['content'], PHP_EOL);
        OperateLogModel::insert($log);
    }
}
