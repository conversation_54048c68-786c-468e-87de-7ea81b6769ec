<?php

namespace App\Console\Commands;

use App\Models\GoodIndent;
use Illuminate\Console\Command;

class OrderInvalidationHandling extends Command
{
    protected $signature = 'order:invalidation';

    protected $description = '商品订单失效处理';

    public function handle()
    {
        GoodIndent::where('state', GoodIndent::GOOD_INDENT_STATE_PAY)
            ->where('overtime', '<=', date('Y-m-d H:i:s'))
            ->select('id')
            ->get()
            ->each(function ($goodIndent) {
                GoodIndent::cancel($goodIndent->id);
            });
    }
}
