<?php

namespace App\Http\Controllers;

use App\Helpers\GoEasy;
use App\Providers\GoEasyServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function github(Request $request)
    {
        // 验证签名
        $signature = $request->header('X-Hub-Signature-256');
        if (!$signature) {
            logErr('Webhook: No signature');
            return response('No signature', 400);
        }

        // 使用紧凑的JSON计算签名
        list($algo, $hash) = explode('=', $signature, 2) + ['', ''];

        // GitHub webhook密钥
        $secret = 'pet@123456';

        // 获取原始内容
        $payload = $request->getContent();

        // 尝试多种验证方式，避免过于武断的拒绝
        $isValidSignature = $this->verifyGithubSignature($payload, $hash, $secret);
        //测试githubwebhook
        if (!$isValidSignature) {
            // 记录详细的调试信息
            Log::warning('Github Webhook: Signature verification failed', [
                'received_signature' => $hash,
                'payload_length' => strlen($payload),
                'payload_preview' => substr($payload, 0, 200),
                'headers' => $request->headers->all(),
            ]);

            // 在开发环境下可能允许通过，生产环境严格验证
            if (app()->environment('production')) {
                logErr('Github Webhook: Invalid signature in production');
                return response('Invalid signature', 400);
            } else {
                Log::warning('Github Webhook: Signature verification failed in non-production environment, allowing through');
            }
        }

        // 检查是否是push事件
        if ($request->header('X-GitHub-Event') === 'push') {
            $branch = $data->ref ?? '';
            Log::info("Push event received on branch: $branch");

            // 执行部署脚本
            $output = [];
            $resultCode = 0;

            // 脚本目录
            $deployScript = base_path('scripts/deploy.sh');
            exec("bash $deployScript 2>&1", $output, $resultCode);

            // 记录执行结果
            Log::info("Deploy script output:\n" . implode("\n", $output));

            if ($resultCode === 0) {
                return response('Deploy script executed successfully');
            } else {
                return response('Deploy script execution failed', 500);
            }
        }

        return response('Unsupported event type', 400);
    }

    public function goEasy(Request $request)
    {
        // 验证签名
        $signature = $request->header('x-goeasy-signature');
        if (!$signature) {
            logErr('Webhook: No signature');
            return response('No signature', 400);
        }
        // 获取原始内容并转换为紧凑的JSON
        $payload = $request->post('content', '[]');
        Log::channel('im')->info('', $request->post());

        /**
         * @var GoEasy $goEasy
         */
        $goEasy = app()->make('goEasy');
        $payloadHash = $goEasy->getSignature($payload);

        Log::info('Hash Comparison:', [
            'from'       => 'goEasy',
            'received'   => $signature,
            'calculated' => $payloadHash
        ]);

        if (!hash_equals($signature, $payloadHash)) {
            logErr('GoEasy Webhook: Invalid signature');
            return response('Invalid signature', 400);
        }

        GoEasyServiceProvider::handleMessage($payload);

        return response('{"code":200,"content":"success"}');
    }

    /**
     * 验证 GitHub Webhook 签名
     * 使用多种方法尝试验证，避免过于武断的拒绝
     */
    private function verifyGithubSignature($payload, $receivedHash, $secret)
    {
        // 方法1: 直接使用原始 payload
        $directHash = hash_hmac('sha256', $payload, $secret);
        if (hash_equals($receivedHash, $directHash)) {
            Log::info('Github Webhook: Signature verified using direct payload');
            return true;
        }

        // 方法2: 尝试解析JSON并重新编码（紧凑格式）
        $data = json_decode($payload);
        if ($data) {
            $compactPayload = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            $compactHash = hash_hmac('sha256', $compactPayload, $secret);
            if (hash_equals($receivedHash, $compactHash)) {
                Log::info('Github Webhook: Signature verified using compact JSON');
                return true;
            }

            // 方法3: 尝试标准JSON编码
            $standardPayload = json_encode($data);
            $standardHash = hash_hmac('sha256', $standardPayload, $secret);
            if (hash_equals($receivedHash, $standardHash)) {
                Log::info('Github Webhook: Signature verified using standard JSON');
                return true;
            }

            // 方法4: 尝试美化的JSON编码
            $prettyPayload = json_encode($data, JSON_PRETTY_PRINT);
            $prettyHash = hash_hmac('sha256', $prettyPayload, $secret);
            if (hash_equals($receivedHash, $prettyHash)) {
                Log::info('Github Webhook: Signature verified using pretty JSON');
                return true;
            }
        }

        // 记录所有尝试的哈希值用于调试
        Log::debug('Github Webhook: All signature attempts failed', [
            'received' => $receivedHash,
            'direct' => $directHash,
            'compact' => $compactHash ?? 'N/A',
            'standard' => $standardHash ?? 'N/A',
            'pretty' => $prettyHash ?? 'N/A',
        ]);

        return false;
    }
}
