<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Models\PetType;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class PetBreed extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_breeds';

    protected $fillable = [
        'name',
        'name_tc',
        'name_en',
        'type_id',
        'size',
        'month_ranges',
        'hair_type',
        'weight_max_male',
        'weight_max_female',
        'alias_names', // 新增别称字段
    ];

    protected $casts = [
        'month_ranges' => 'array',
        'alias_names' => 'array', // 新增别称字段的类型转换
    ];

    public function petType()
    {
        return $this->belongsTo(PetType::class, 'type_id');
    }


    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getList($search_data = array())
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $typeId = $search_data['type_id'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "asc";

        // 检查是否有高级筛选条件
        $hasAdvancedFilters = !empty($search_data['size_categories']) ||
            !empty($search_data['hair_types']);

        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($typeId, function (Builder $query) use ($typeId) {
                $query->where('type_id', $typeId);
            });

        // 如果有高級篩選條件，需要特殊處理
        if ($hasAdvancedFilters) {
            return self::getListWithAdvancedFilters($search_data);
        }

        // 普通列表查詢 - 添加默認排序
        $paginatedResult = $query->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
            if ($sortName === 'name') {
                // 使用 utf8mb4_unicode_ci 排序規則進行中文排序
                $query->orderByRaw("name COLLATE utf8mb4_unicode_ci {$sortBy}");
            } else {
                $query->orderBy($sortName, $sortBy);
            }
        }, function (Builder $query) {
            // 默認按品種名稱排序（中文友好排序）
            $query->orderByRaw("name COLLATE utf8mb4_unicode_ci ASC");
        })
            ->with('petType', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en');
            })
            ->paginate($limit, array('*'), 'page', $page);

        // 为每个品种添加规格结构
        $paginatedResult->getCollection()->transform(function ($breed) {
            return $breed->toArrayWithSpecifications();
        });

        return $paginatedResult;
    }

    /**
     * 带高级筛选条件的列表查询
     */
    private static function getListWithAdvancedFilters($search_data)
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $typeId = $search_data['type_id'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "asc";

        // 使用filterBreeds方法获取符合条件的品种
        $filters = [
            'pet_type'        => $typeId ? (PetType::find($typeId)->name ?? null) : null,
            'size_categories' => $search_data['size_categories'] ?? null,
            'hair_types'      => $search_data['hair_types'] ?? null,
        ];

        $filterResults = self::filterBreeds($filters);
        $breedIds = array_column($filterResults['data'], 'breed_id');

        if (empty($breedIds)) {
            // 没有符合条件的品种，返回空分页结果
            return new \Illuminate\Pagination\LengthAwarePaginator(
                [],
                0,
                $limit,
                $page,
                ['path' => request()->url(), 'pageName' => 'page']
            );
        }

        // 查询符合条件的品种详情
        $query = self::whereIn('id', $breedIds)
            ->when($keyword, function (Builder $query) use ($keyword) {
                $query->where(function (Builder $query) use ($keyword) {
                    $query->where('name', 'like', "%$keyword%")
                        ->orWhere('name_tc', 'like', "%$keyword%")
                        ->orWhere('name_en', 'like', "%$keyword%");
                });
            })
            ->with('petType', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en');
            });

        // 添加排序邏輯
        $query->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
            if ($sortName === 'name') {
                // 使用 utf8mb4_unicode_ci 排序規則進行中文排序
                $query->orderByRaw("name COLLATE utf8mb4_unicode_ci {$sortBy}");
            } else {
                $query->orderBy($sortName, $sortBy);
            }
        }, function (Builder $query) {
            // 默認按品種名稱排序（中文友好排序）
            $query->orderByRaw("name COLLATE utf8mb4_unicode_ci ASC");
        });

        $paginatedResult = $query->paginate($limit, array('*'), 'page', $page);

        // 為每個品種添加規格結構
        $paginatedResult->getCollection()->transform(function ($breed) {
            return $breed->toArrayWithSpecifications();
        });

        return $paginatedResult;
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)
            ->with('petType', function (Builder $query) {
                $query->select('id', 'name');
            })->first();
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;

            // 检查名称重复
            if (self::where('name', $data['name'])->where('id', '!=', $id)->exists()) {
                throw new Exception('寵物品種已存在');
            }

            if (empty($id)) {
                // 新增记录
                $record = self::create($data);
                if ($record && isset($record->id) && $record->id) {
                    $success = true;
                    $message = '保存成功';
                    $oldData = null;
                    $newData = array_merge($data, ['id' => $record->id]);
                    event(new DataChanged(static::class, $newData, $oldData));
                } else {
                    $errorMsg = '创建记录失败';
                    if (!$record) {
                        $errorMsg .= ': create方法返回null';
                    } elseif (!isset($record->id)) {
                        $errorMsg .= ': 记录没有id属性';
                    } elseif (!$record->id) {
                        $errorMsg .= ': id为空';
                    }
                    throw new Exception($errorMsg);
                }
            } else {
                // 更新记录
                $oldRecord = self::where('id', $id)->first();
                if (!$oldRecord) {
                    throw new Exception("寵物品種：{$id}不存在或已删除");
                }

                $oldData = $oldRecord->toArray();
                $updateSuccess = self::where('id', $id)->update($data);

                if ($updateSuccess) {
                    $record = self::find($id);
                    $success = true;
                    $message = '保存成功';
                    $newData = array_merge($oldData, $data);
                    event(new DataChanged(static::class, $newData, $oldData));
                } else {
                    throw new Exception('更新失败');
                }
            }

            DB::commit();

        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = null;
            $message = $e->getMessage();
        }

        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "寵物品種：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }


    /**
     * 获取所有可用的体型分类
     *
     * @return array
     */
    public static function getAvailableSizes()
    {
        return self::whereNotNull('size')
            ->distinct()
            ->pluck('size')
            ->toArray();
    }

    /**
     * 灵活的品种筛选方法
     *
     * @param array $filters 筛选条件
     * @return array
     */
    public static function filterBreeds($filters)
    {
        $breeds = self::with('petType')->whereNotNull('size')->get();
        $results = [];
        $breedIds = [];
        $stageCount = 0;

        foreach ($breeds as $breed) {
            // 1. 基础筛选：宠物类型
            if (!empty($filters['pet_type']) && $breed->petType->name !== $filters['pet_type']) {
                continue;
            }

            // 2. 基础筛选：指定品种ID
            if (!empty($filters['breed_ids']) && !in_array($breed->id, $filters['breed_ids'])) {
                continue;
            }

            // 3. 毛发类型筛选
            if (!empty($filters['hair_types']) && !in_array($breed->hair_type, $filters['hair_types'])) {
                continue;
            }

            // 4. 体型筛选
            if (!empty($filters['size_categories']) && !in_array($breed->size, $filters['size_categories'])) {
                continue;
            }

            // 構建品種信息
            $breedIds[] = $breed->id;
            $results[] = [
                'breed_id'       => $breed->id,
                'breed_name'     => $breed->name,
                'breed_name_tc'  => $breed->name_tc,
                'breed_name_en'  => $breed->name_en,
                'pet_type'       => $breed->petType->name,
                'size'           => $breed->size,
                'size_name'      => self::getSizeDisplayName($breed->size),
                'hair_type'      => $breed->hair_type,
                'hair_type_name' => self::getHairTypeName($breed->hair_type)
            ];
        }

        return [
            'data'        => $results,
            'total_count' => count($results),
            'breed_count' => count(array_unique($breedIds))
        ];
    }


    /**
     * 獲取階段中文名稱
     */
    private static function getStageName($stage)
    {
        $stageNames = [
            'juvenile' => '幼年期',
            'adult'    => '成年期',
            'senior'   => '熟年期',
            'elderly'  => '老年期'
        ];

        return $stageNames[$stage] ?? $stage;
    }

    /**
     * 获取年龄阶段的月份范围
     */
    public static function getStageMonthRanges()
    {
        return [
            'juvenile' => ['min' => 0, 'max' => 12],
            'adult'    => ['min' => 12, 'max' => 84],
            'senior'   => ['min' => 84, 'max' => 120],
            'elderly'  => ['min' => 120, 'max' => null] // null表示无上限
        ];
    }

    /**
     * 获取体型的中文名称
     */
    public static function getSizeDisplayName($size)
    {
        $sizeNames = [
            'small'  => '小型',
            'medium' => '中型',
            'large'  => '大型'
        ];

        return $sizeNames[$size] ?? $size;
    }

    /**
     * 获取毛发类型的中文名称
     */
    public static function getHairTypeName($hairType)
    {
        $hairTypeNames = [
            'short'  => '短毛',
            'medium' => '中等毛',
            'long'   => '長毛'
        ];

        return $hairTypeNames[$hairType] ?? $hairType;
    }


    /**
     * 获取品种的规格结构（简化版本，只返回固定体型）
     */
    public function getSpecificationStructure()
    {
        if (!$this->size) {
            return [];
        }

        // 返回简化的规格结构，只包含固定体型
        return [
            [
                'size'      => $this->size,
                'size_name' => self::getSizeDisplayName($this->size)
            ]
        ];


    }

    /**
     * 为列表接口添加规格结构
     */
    public function toArrayWithSpecifications()
    {
        // 只返回基础字段和新的规格结构，不包含原始数据结构
        $result = $this->toArray();
        $result['specifications'] = $this->getSpecificationStructure();

        return $result;
    }

    /**
     * 检查品种名称是否匹配（包括别称）
     *
     * @param string $breedName 要匹配的品种名称
     * @return bool
     */
    public function matchesBreedName(string $breedName): bool
    {
        // 去除空格并转为小写进行比较
        $breedName = trim(strtolower($breedName));

        // 匹配标准名称
        if (strtolower($this->name) === $breedName) {
            return true;
        }

        // 匹配别称
        if (!empty($this->alias_names) && is_array($this->alias_names)) {
            foreach ($this->alias_names as $alias) {
                if (strtolower(trim($alias)) === $breedName) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 根据品种名称查找品种（包括别称匹配）
     *
     * @param string $breedName 品种名称
     * @param int|null $typeId 宠物类型ID（可选）
     * @return PetBreed|null
     */
    public static function findByBreedName(string $breedName, int $typeId = null): ?PetBreed
    {
        $query = self::query();

        if ($typeId) {
            $query->where('type_id', $typeId);
        }

        $breeds = $query->get();

        foreach ($breeds as $breed) {
            if ($breed->matchesBreedName($breedName)) {
                return $breed;
            }
        }

        return null;
    }

    /**
     * 获取所有别称（包括标准名称）
     *
     * @return array
     */
    public function getAllNames(): array
    {
        $names = [$this->name];

        if (!empty($this->alias_names) && is_array($this->alias_names)) {
            $names = array_merge($names, $this->alias_names);
        }

        return array_unique($names);
    }
}
