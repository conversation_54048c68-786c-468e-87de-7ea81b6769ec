<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Friend;
use App\Models\FriendRequest;
use Illuminate\Http\Request;


class FriendController extends Controller
{
    /**
     * 发送好友申请
     */
    public function sendRequest(Request $request)
    {
        $validated = $request->validate([
            'to_user_id' => 'required|integer|exists:app_users,id',
            'remark'     => 'nullable|string|max:255'
        ]);

        $result = FriendRequest::sendRequest(
            $request->user()->id,
            $validated['to_user_id'],
            $validated['remark'] ?? null
        );

        return ResponseHelper::result(...$result);
    }


    /**
     * 处理好友申请
     */
    public function handleRequest(Request $request)
    {
        $validated = $request->validate([
            'request_id' => 'required|integer|exists:app_friends_requests,id',
            'action'     => 'required|in:accept,reject'
        ]);

        $result = FriendRequest::handleRequest(
            $request->user()->id,
            $validated['request_id'],
            $validated['action']
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 获取好友列表
     */
    public function getFriendList(Request $request)
    {
        $searchData = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100'
        ]);

        $result = Friend::getList($searchData, $request->user()->id);

        return ResponseHelper::result(...$result);
    }


}
