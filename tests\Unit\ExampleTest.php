<?php

namespace Tests\Unit;

use App\Helpers\Tree;
use PHPUnit\Framework\TestCase;

class ExampleTest extends TestCase
{
    /**
     * A basic test example.
     *
     * @return void
     */
    public function test_that_true_is_true()
    {
        $this->assertTrue(true);
    }


    public function test_tree()
    {
        // id reply_id parent_id  reply_count
        // 1    0        0           5           删除1，同步删除2-6
        // 2    1        1           2           删除2，同步删除3、5；由于5有回复，同步删除6
        // 3    2        1           0           删除3
        // 4    1        1           0           删除4
        // 5    2        1           1           删除5，同步删除6
        // 6    5        1           0           删除6
        $comments = [
            ['id' => 1, 'reply_id' => 0, 'parent_id' => 0, 'content' => 'test 1'],
            ['id' => 2, 'reply_id' => 1, 'parent_id' => 1, 'content' => 'test 2'],
            ['id' => 3, 'reply_id' => 2, 'parent_id' => 1, 'content' => 'test 3'],
            ['id' => 4, 'reply_id' => 1, 'parent_id' => 1, 'content' => 'test 4'],
            ['id' => 5, 'reply_id' => 2, 'parent_id' => 1, 'content' => 'test 5'],
            ['id' => 6, 'reply_id' => 5, 'parent_id' => 1, 'content' => 'test 6'],
        ];
        $commentId = 1;
        $comments = array_column($comments, null, 'id');

        $childCommentTree = Tree::instance()->init($comments, 'reply_id');
        $childCommentIds = $childCommentTree->getChildrenIds($commentId);
        dump($childCommentIds);

        $data = $childCommentTree->getTreeArray(0);
        $list = $childCommentTree->getTreeList($data, 'content');
        dump($list);
    }
}
