<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\Good;
use App\Models\Shipping;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * shipping
 * 收货地址
 * Class ShippingController
 * @package App\Http\Controllers\Mobile
 */
class ShippingController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = Shipping::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Shipping::getDetailForApp($id, $request->user()->id));
    }

    public function save(Request $request)
    {
        $rules = [
            'id'        => 'sometimes|integer|gte:0',
            'cellphone' => 'required|numeric',
            'name'      => 'required|string|max:30',
            'location'  => 'required|string|max:255',
            'address'   => 'nullable|string|max:255',
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
            'house'     => 'required|string|max:255',
            'defaults'  => 'required|in:' . Shipping::SHIPPING_DEFAULTS_YES . ',' . Shipping::SHIPPING_DEFAULTS_NO,
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $result = Shipping::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Shipping::del($request->input('id'), $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * ShippingFreight
     * 获取运费
     * @return string
     */
    public function freight(Request $request)
    {
        $rules = [
            'shipping_id'             => 'required|integer|gte:0',
            'commodity'               => 'required|array',
            'commodity.*.good_id'     => 'required|integer|gt:0',
            'commodity.*.good_sku_id' => 'required|integer|gte:0',
            'commodity.*.number'      => 'required|integer|gt:0',
        ];
        $request->validate($rules);
        $good = $request->input('commodity');
        $return['shipping'] = Shipping::getDetailForApp($request->input('shipping_id'), $request->user()->id);
        $freight_ids = Good::select('id', 'freight_id')->whereIn('id', array_column($good, 'good_id'))->get()->toArray();
        $freight_ids = array_column($freight_ids, 'freight_id', 'id');
        foreach ($good as &$v) {
            $v['freight_id'] = $freight_ids[$v['good_id']] ?? 0;
            if (!$v['freight_id']) {
                return ResponseHelper::error('商品' . $v['good_id'] . '没有设置运费模板');
            }
        }
        $return['carriage'] = Shipping::getCarriage($return['shipping'], $good);
        return ResponseHelper::success($return);
    }

    public function defaultSet(Request $request)
    {
        $rules = [
            'id' => 'sometimes|integer|gte:0',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['defaults'] = Shipping::SHIPPING_DEFAULTS_YES;
        $formData['user_id'] = $request->user()->id;
        $result = Shipping::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }
}
