<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin \Illuminate\Contracts\Database\Query\Builder
 */
class Carousel extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'image_url',
        'link_type',
        'link_url',
        'position',
        'sort_order',
        'status',
        'start_time',
        'end_time',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取轮播图列表
     */
    public static function getList($params = [])
    {
        $query = self::query();

        // 关键词搜索
        if (!empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('title', 'like', "%$keyword%");
            });
        }

        // 位置筛选
        if (!empty($params['position'])) {
            $query->where('position', $params['position']);
        }

        // 状态筛选
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // 排序
        $sortName = $params['sort_name'] ?? 'sort_order';
        $sortBy = $params['sort_by'] ?? 'asc';
        $query->orderBy($sortName, $sortBy);

        // 分页
        $perPage = $params['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * 获取轮播图详情
     */
    public static function getDetail($id)
    {
        return self::findOrFail($id);
    }

    /**
     * 保存轮播图
     */
    public static function saveDetail($data)
    {
        $id = $data['id'] ?? 0;

        if ($id > 0) {
            $carousel = self::findOrFail($id);
            $carousel->update($data);
        } else {
            $carousel = self::create($data);
        }

        return [true, $carousel, '保存成功'];
    }

    /**
     * 获取前端可用的轮播图
     */
    public static function getActiveCarousels($position = 'home')
    {
        $now = now();

        return self::where('position', $position)
            ->where('status', 1)
            ->where(function ($query) use ($now) {
                $query->whereNull('start_time')
                    ->orWhere('start_time', '<=', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('end_time')
                    ->orWhere('end_time', '>=', $now);
            })
            ->orderBy('sort_order', 'asc')
            ->get();
    }
}
