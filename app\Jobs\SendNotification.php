<?php

namespace App\Jobs;

use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $type;
    protected $title;
    protected $content;
    protected $senderId;
    protected $relatedId;
    protected $relatedType;

    /**
     * 任务尝试次数
     */
    public $tries = 3;

    /**
     * 任务最大执行时间
     */
    public $timeout = 30;

    /**
     * 创建一个新的任务实例
     *
     * @param int $userId 接收通知的用户ID
     * @param string $type 通知类型
     * @param string $title 通知标题
     * @param string $content 通知内容
     * @param int|null $senderId 发送者ID
     * @param int|null $relatedId 相关对象ID
     * @param string|null $relatedType 相关对象类型
     */
    public function __construct($userId, $type, $title, $content, $senderId = null, $relatedId = null, $relatedType = null)
    {
        $this->userId = $userId;
        $this->type = $type;
        $this->title = $title;
        $this->content = $content;
        $this->senderId = $senderId;
        $this->relatedId = $relatedId;
        $this->relatedType = $relatedType;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        NotificationService::createNotification(
            $this->userId,
            $this->type,
            $this->title,
            $this->content,
            $this->senderId,
            $this->relatedId,
            $this->relatedType
        );
    }

    /**
     * 任务失败的处理
     */
    public function failed(\Exception $exception)
    {
        Log::error('Notification send failed:', [
            'user_id' => $this->userId,
            'type' => $this->type,
            'title' => $this->title,
            'error' => $exception->getMessage()
        ]);
    }
}
