<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->integer('user_sort_order')->default(0)->comment('用户自定义排序：数值越小越靠前')->after('is_dead');

            // 添加复合索引优化查询性能
            $table->index(['owner_id', 'user_sort_order'], 'idx_owner_sort');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->dropIndex('idx_owner_sort');
            $table->dropColumn('user_sort_order');
        });
    }
};
