<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Pet;
use App\Models\PetReminder;
use App\Services\PetReminder\PetReminderService;
use Illuminate\Http\Request;

class PetReminderController extends Controller
{
    /**
     * 获取一条提醒
     */
    public function getOne(Request $request)
    {
        $validated = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
            'pet_id'   => 'nullable|integer'
        ]);
        $user = $request->user();
        if (!empty($validated['pet_id']) && !Pet::hasEditPermission($validated['pet_id'], $user['id'])) {
            return ResponseHelper::error('没有查看权限');
        }
        $service = new PetReminderService($user['id'], $validated['pet_id'] ?? 0);
        $reminder = $service->getReminder();
        return ResponseHelper::success($reminder);
    }

    /**
     * 获取提醒列表
     */
    public function getList(Request $request)
    {
        $validated = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
            'type'     => 'nullable|in:' . implode(',', array_keys(PetReminder::MAPPING)),
        ]);

        $result = PetReminder::getList(
            $request->user()->id,
            $validated['type'] ?? null,
            $validated['page'] ?? 1,
            $validated['per_page'] ?? 20
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 获取未读提醒数量
     */
    public function getUnreadCount(Request $request)
    {
        $result = PetReminder::getUnreadCount($request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 标记提醒已读
     */
    public function markRead(Request $request)
    {
        $validated = $request->validate([
            'reminder_id' => 'nullable|integer',  // 为空则标记所有提醒已读
            'type'        => 'nullable|in:' . implode(',', array_keys(PetReminder::MAPPING)),  // 可选择按类型标记已读
        ]);

        $result = PetReminder::markRead(
            $request->user()->id,
            $validated['reminder_id'] ?? null,
            $validated['type'] ?? null
        );

        return ResponseHelper::result(...$result);
    }
}
