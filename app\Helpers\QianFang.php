<?php

namespace App\Helpers;

/**
 * 钱方支付SDK
 * @url https://sdk.qfapi.com/#environments
 */
class QianFang
{
    private $config;

    /**
     * @param array $config ['url', 'app_key', 'app_code', 'sign_type' => 'SHA256|MD5']
     */
    public function __construct($config = [])
    {
        $this->config = array_merge([
            'url'       => config('payment.qfpay.url', 'https://openapi-hk.qfapi.com'),
            'app_key'   => config('payment.qfpay.app_key', ''),
            'app_code'  => config('payment.qfpay.app_code', ''),
            'sign_type' => config('payment.qfpay.sign_type', 'MD5'),
            'mchid'     => config('payment.qfpay.mchid', ''),
        ], $config);
    }

    /**
     * 参数组合
     * @param array $params
     * @return bool|string
     */
    public function getFieldsString($params)
    {
        $fields_string = '';
        ksort($params);
        foreach ($params as $key => $value) {
            if ($key === 'lang') {
                continue;
            }
            $fields_string .= $key . '=' . $value . '&';
        }
        return substr($fields_string, 0, strlen($fields_string) - 1);
    }

    /**
     * 参数加签
     * @param array $params
     * @return string
     * @throws \Exception
     */
    public function getSign($params)
    {
        $sign_string = $this->getFieldsString($params) . $this->config['app_key'];
        return $this->sign($sign_string, $this->config['sign_type']);
    }

    /**
     * 字串加签
     * @param string $sign_string 待签字串
     * @param string $sign_type   签名方式
     * @return string
     */
    public function sign($sign_string, $sign_type)
    {
        switch (strtoupper($sign_type)) {
            case 'SHA256':
                $sign_string = hash('SHA256', $sign_string);
                break;
            case 'MD5':
                $sign_string = md5($sign_string);
                break;
            default:
                throw new \Exception('Unknown sign type');
        }
        return strtoupper($sign_string);
    }

    /**
     * 收银台
     */
    public function checkstand($body, $out_trade_no, $total, $notify_url, $expired_time, $return_url, $failed_url)
    {
        $params = [
            'appcode'               => $this->config['app_code'],
            'sign_type'             => strtolower($this->config['sign_type']),
            'paysource'             => 'remotepay_checkout',
            'txamt'                 => (int)bcmul($total, 100), // 单位：分 建议数值大于200，避免因支付金额过低而被交易风控。
            'txcurrcd'              => 'HKD',
            'out_trade_no'          => $out_trade_no,
            'txdtm'                 => date('Y-m-d H:i:s'),
            'return_url'            => $return_url,
            'failed_url'            => $failed_url,
            'notify_url'            => $notify_url,
            'goods_name'            => $body,
            'udid'                  => '0001',  //唯一設備ID
            'expired_time'          => strval($expired_time), //二维码过期时间单位为分钟，最短5分钟，最长120分钟
            'checkout_expired_time' => bcmul(time() + $expired_time * 60, 1000),  //客户端过期时间 单位为毫秒, e.g. 1715686118000，时间到了后，收银台页面会跳转至 支付失败后的重定向URL
            'limit_pay'             => 'no_credit',  //即禁止使用信用卡支付，仅微信支付支持此功能
            'lang'                  => 'zh-hk',
        ];
        ksort($params);
        $params['sign'] = $this->getSign($params);
        $url = $this->config['url'] . '/checkstand/#/?' . http_build_query($params);
        return ['url' => $url];
    }

    /**
     * WEB支付
     */
    public function payment($body, $out_trade_no, $total, $notify_url, $expired_time, $pay_type)
    {
        $params = [
            'txamt'        => (int)bcmul($total, 100), // 单位：分
            'txcurrcd'     => 'HKD',
            'pay_type'     => $pay_type,
            'out_trade_no' => $out_trade_no,
            'txdtm'        => date('Y-m-d H:i:s'),
            'expired_time' => $expired_time,
            'goods_name'   => $body,
            'notify_url'   => $notify_url,
        ];
        $result = $this->post('/trade/v1/payment', $params);
        if (!empty($result['pay_params'])) {
            return $result['pay_params'];
        } elseif (!empty($result['pay_url'])) {
            return $result['pay_url'];
        } elseif (!empty($result['qrcode'])) {
            return $result['qrcode'];
        } else {
            $msg = '';
            if (!empty($result)) {
                $msg = empty($msg) ? ($result['resperr'] ?? $msg) : $msg;
                $msg = empty($msg) ? ($result['respmsg'] ?? $msg) : $msg;
            }
            $msg = $msg ?: '钱方支付调用失败';
            throw new \Exception($msg);
        }
    }

    /**
     * 支付组件SDK
     */
    public function create_payment_intent($out_trade_no, $total, $notify_url, $pay_type)
    {
        $params = [
            'txamt'        => (int)bcmul($total, 100), // 单位：分
            'txcurrcd'     => 'HKD',
            'pay_type'     => $pay_type,
            'out_trade_no' => $out_trade_no,
            'notify_url'   => $notify_url,
        ];
        $result = $this->post('/payment_element/v1/create_payment_intent', $params);
        if (empty($result['resperr'])) {
            return $result;
        } else {
            $msg = '';
            if (!empty($result)) {
                $msg = empty($msg) ? ($result['resperr'] ?? $msg) : $msg;
                $msg = empty($msg) ? ($result['respmsg'] ?? $msg) : $msg;
            }
            $msg = $msg ?: '钱方支付调用失败';
            throw new \Exception($msg);
        }
    }

    /**
     * 微信服务商退款
     * @param string $syssn        钱方交易号
     * @param string $out_trade_no 商户订单号
     * @param float  $refund_fee   申请退款金额（元）
     * @return array 退款结果
     */
    public function refund($syssn, $out_trade_no, $refund_fee)
    {
        $params = [
            'syssn'        => $syssn,
            'out_trade_no' => $out_trade_no,
            'txamt'        => (int)bcmul($refund_fee, 100),
            'txdtm'        => date('Y-m-d H:i:s'),
        ];
        $result = $this->post('/trade/v1/refund', $params);
        if ($result['respcd'] == '0000') {
            return ['code' => 1, 'err_code_des' => ''];
        } else {
            $msg = '';
            if (!empty($result)) {
                $msg = empty($msg) ? ($result['resperr'] ?? $msg) : $msg;
                $msg = empty($msg) ? ($result['respmsg'] ?? $msg) : $msg;
            }
            $msg = $msg ?: '钱方退款调用失败';
            return ['code' => 0, 'err_code_des' => $msg];
        }
    }

    /**
     * 查询订单
     * @param string $out_trade_no
     * @return bool|array
     */
    public function queryByOutTradeNo($out_trade_no)
    {
        $params = [
            'out_trade_no' => $out_trade_no,
        ];
        $res = $this->post('/trade/v1/query', $params);
        if ($res['respcd'] == '0000' && !empty($res['data'])) {
            return $res['data'][0];
        }
        return false;
    }

    /**
     * 关闭订单
     * @param string $out_trade_no
     */
    public function closeOrder($out_trade_no, $total = '')
    {
        $params = [
            'out_trade_no' => $out_trade_no,
            'txamt'        => (int)bcmul($total, 100), // 单位：分
            'txdtm'        => date('Y-m-d H:i:s'),
        ];
        $res = $this->post('/trade/v1/close', $params);
        if ($res['respcd'] == '0000') {
            return ['code' => 1];
        }
        return ['code' => 0, 'err_code_des' => $res['resperr'] ?? ''];
    }

    /**
     * 异步通知验签
     * @param array  $headers request请求头
     * @param string $body    request请求体
     * @return bool
     */
    public function checkNotify($headers, $body)
    {
        return $headers['x-qf-sign'] == $this->sign($body . $this->config['app_key'], $headers['x-qf-signtype'] ?? 'md5');
    }

    public function post($url, $params)
    {
        $url = $this->config['url'] . $url;
        $header = array();
        $header[] = 'X-QF-APPCODE: ' . $this->config['app_code'];
        $header[] = 'X-QF-SIGN: ' . $this->getSign($params);
        $header[] = 'X-QF-SIGNTYPE: ' . strtoupper($this->config['sign_type']);

        //Post Data
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->getFieldsString($params));
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 20);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        $output = curl_exec($ch);
        curl_close($ch);
        if ($output) {
            return json_decode($output, true);
        }
        return false;
    }
}
