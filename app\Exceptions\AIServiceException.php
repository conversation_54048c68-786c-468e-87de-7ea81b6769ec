<?php

namespace App\Exceptions;

use Exception;

class AIServiceException extends Exception
{
    /**
     * 错误类型
     *
     * @var string
     */
    protected $errorType;

    /**
     * 会话ID
     *
     * @var string|null
     */
    protected $sessionId;

    /**
     * 额外数据
     *
     * @var array
     */
    protected $extraData;

    /**
     * 创建一个新的AI服务异常实例
     *
     * @param string $message 错误消息
     * @param string $errorType 错误类型
     * @param string|null $sessionId 会话ID
     * @param array $extraData 额外数据
     * @param int $code 错误代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(
        string $message = "AI服务错误",
        string $errorType = "general",
        ?string $sessionId = null,
        array $extraData = [],
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->errorType = $errorType;
        $this->sessionId = $sessionId;
        $this->extraData = $extraData;
    }

    /**
     * 获取错误类型
     *
     * @return string
     */
    public function getErrorType(): string
    {
        return $this->errorType;
    }

    /**
     * 获取会话ID
     *
     * @return string|null
     */
    public function getSessionId(): ?string
    {
        return $this->sessionId;
    }

    /**
     * 获取额外数据
     *
     * @return array
     */
    public function getExtraData(): array
    {
        return $this->extraData;
    }
}
