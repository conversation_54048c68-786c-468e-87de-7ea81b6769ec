<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class GoodCategory extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'name_tc',
        'name_en',
        'pid',
        'logo',
        'sort_order',
        'state',
        'is_recommend',
    ];

    const CATEGORY_STATE_YES = 0; //状态：正常
    const CATEGORY_STATE_NO = 1; //状态：隐藏
    const CATEGORY_IS_RECONMEND_NO = 0; //首页推荐：否
    const CATEGORY_IS_RECONMEND_YES = 1; //首页推荐：是
    protected $table = 'goods_categorys';

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取父分类
     */
    public function Category()
    {
        return $this->hasOne('App\Models\GoodCategory', 'id', 'pid');
    }

    /**
     * 已选的规格
     */
    public function SpecificationOn()
    {
        return $this->belongsToMany(Specification::class, 'goods_categorys_specifications', 'category_id', 'specification_id');
    }

    /**
     * 已选的品牌
     */
    public function BrandOn()
    {
        return $this->belongsToMany(Brand::class, 'goods_categorys_brands', 'category_id', 'brand_id');
    }

    /**
     * 获取所有的分类
     */
    public function getAllCategory()
    {
        $Category = static::get();
        $options = [];
        if ($Category) {
            foreach ($Category as $p) {
                $options[] = array(
                    'value' => $p['id'],
                    'label' => $p['name'],
                    'pid'   => $p['pid'],
                    'id'    => $p['id']
                );
            }
            return collect(genTree($options, 'pid'))->prepend(array(
                'value' => 0,
                'label' => '顶级分组'
            ));
        } else {
            return true;
        }
    }

    public static function getList($search_data = array())
    {
        // 遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $pid = $search_data['pid'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";

        $list = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($pid, function (Builder $query) use ($pid) {
                $query->where('pid', $pid);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->with('SpecificationOn', 'BrandOn')
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);

        $list->getCollection()->transform(function ($p) {
            $item = $p->toArray();
            $item['specification'] = $p->SpecificationOn->pluck('id');
            $item['brand'] = $p->BrandOn->pluck('id');
            unset($item['specification_on'], $item['brand_on']);
            return $item;
        });
        return $list;
    }

    public static function specification($id)
    {
        return self::where('state', self::CATEGORY_STATE_YES)
            ->with([
                'SpecificationOn' => function (Builder $q) {
                    $q->orderBy('sort_order', 'ASC');
                },
                'BrandOn'         => function (Builder $q) {
                    $q->orderBy('sort_order', 'ASC');
                }
            ])->find($id);
    }

    public static function getListForApp($formData)
    {
        $q = self::query();
        $q->where('state', GoodCategory::CATEGORY_STATE_YES);

        // 按分类ID筛选（支持父类和子类）
        if (isset($formData['category_id']) && is_numeric($formData['category_id'])) {
            $categoryId = $formData['category_id'];

            // 查询指定分类的信息
            $category = self::find($categoryId);
            if ($category) {
                if ($category->pid == 0) {
                    // 如果是父类，返回该父类下的所有子类
                    $q->where('pid', $categoryId);
                } else {
                    // 如果是子类，返回该子类本身
                    $q->where('id', $categoryId);
                }
            }
        }

        // 排序逻辑
        if (!empty($formData['sort_name']) && !empty($formData['sort_by'])) {
            $q->orderBy($formData['sort_name'], $formData['sort_by']);
        } else {
            $q->orderBy('sort_order', 'ASC')->orderBy('id', 'ASC');
        }

        $paginate = $q->get();

        // 树形结构处理
        if (!empty($formData['tree']) && $formData['tree'] == 1) {
            $paginate = genTree($paginate->toArray(), 'pid');
        }

        return $paginate;
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)
            ->with('SpecificationOn', 'BrandOn')
            ->first();
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('name', $data['name'])->where('id', '!=', $id)->exists()) {
                throw new Exception('商品分类已存在');
            }
            $specification = $data['specification'] ?? [];
            unset($data['specification']);
            $brand = $data['brand'] ?? [];
            unset($data['brand']);
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "商品分类：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            $oldData and DB::table('goods_categorys_specifications')->where('category_id', $id)->delete();
            if (!empty($specification)) {
                $insert = [];
                foreach ($specification as $specification_id) {
                    $insert[] = array(
                        'specification_id' => $specification_id,
                        'category_id'      => $id,
                    );
                }
                DB::table('goods_categorys_specifications')->insert($insert);
            }
            $oldData and DB::table('goods_categorys_brands')->where('category_id', $id)->delete();
            if (!empty($brand)) {
                $insert = [];
                foreach ($brand as $brand_id) {
                    $insert[] = array(
                        'brand_id'    => $brand_id,
                        'category_id' => $id,
                    );
                }
                DB::table('goods_categorys_brands')->insert($insert);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "商品分类：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
