<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AnalysisBodyPart extends Model
{
    use HasFactory;

    protected $fillable = [
        'code', 'name', 'description', 'icon', 'pet_type', 'sort_order', 'status'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取关联的症状
     */
    public function symptoms()
    {
        return $this->hasMany(BodyPartSymptom::class, 'body_part_id');
    }
}
