<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateAppUserLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('app_user_logs', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->index()->comment('用户ID');
            $table->string('path',255)->comment('路径');
            $table->string('method',10)->comment('请求方法');
            $table->ipAddress('ip',30)->comment('IP');
            $table->text('input')->nullable()->comment('提交的数据');
            $table->timestamps();
            $table->unique('id');
        });
        DB::statement("ALTER TABLE `app_user_logs` COMMENT='用户记录'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('app_user_logs');
    }
}
