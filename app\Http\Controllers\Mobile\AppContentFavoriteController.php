<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppContentFavorite;
use Illuminate\Http\Request;

class AppContentFavoriteController extends Controller
{
    public function setFavorite(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentFavorite::setFavorite($request->user()->id, $request->content_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelFavorite(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentFavorite::cancelFavorite($request->user()->id, $request->content_id);
        return ResponseHelper::result(...$result);
    }

    public function setIsPrivate(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'is_private' => 'required|integer|in:0,1',
        ]);
        $result = AppContentFavorite::setIsPrivate($request->user()->id, $request->content_id, $request->is_private);
        return ResponseHelper::result(...$result);
    }

    public function getFavoriteList(Request $request)
    {
        $request->validate([
            'page'       => 'sometimes|integer|gt:0',
            'per_page'   => 'sometimes|integer|between:1,200',
            'user_id'    => 'required|integer|gt:0',
            'is_private' => 'nullable|integer|in:0,1',
        ]);
        $formData = $request->all();
        $isPrivate = $request->input('is_private');
        if (is_numeric($isPrivate) && $request->user()->id != $request->user_id) {
            return ResponseHelper::error('Invalid request');
        }
        $result = AppContentFavorite::getFavoriteList($formData, $request->user()->id);
        return ResponseHelper::result(...$result);
    }
}
