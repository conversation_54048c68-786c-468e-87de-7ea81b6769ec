<?php

namespace App\Http\Controllers\Admin;


use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppUser;
use App\Models\AppUserFollow;
use App\Models\Pet;
use App\Models\PetMaster;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class AppUserController extends Controller
{

    /**
     * APP用户列表
     */
    public function getAppUsers(Request $request)
    {
        $query = AppUser::query();

        $page = $request->input('page', 1);
        $limit = $request->input('per_page', 15);
        $keyword = $request->input('keyword', '');
        $area = $request->input('area', '');
        $status = $request->input('status', '');
        $sortName = $request->input('sort_name', 'id');
        $sortBy = $request->input('sort_by', 'desc');
        // 允许排序的字段
        $allowSort = ['id', 'following_count', 'fans_count', 'hosting_count', 'created_at'];
        $sortName = in_array($sortName, $allowSort) ? $sortName : 'id';

        $query->when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('username', 'like', "%$keyword%")
                    ->orWhere('phone', 'like', "%$keyword%")
                    ->orWhere('memo', 'like', "%$keyword%");
            });
        })
            ->when($area !== '', function (Builder $query) use ($area) {
                $query->where('area', '=', $area);
            })
            ->when($status !== '', function (Builder $query) use ($status) {
                $query->where('status', '=', $status);
            })
            ->orderBy($sortName, $sortBy);

        $users = $query->paginate($limit, ['*'], 'page', $page);

        // 转换区域和状态显示
        $users->through(function ($user) {
            $user->area_text = $user->area == 1 ? '内地' : '香港';
            $user->status_text = $user->status == 1 ? '正常' : '禁用';
            $user->is_customer_service_text = $user->is_customer_service == 1 ? '是' : '否';

            // 获取用户的宠物列表
            $petIds = PetMaster::where('user_id', $user->id)->pluck('pet_id')->toArray();
            if (!empty($petIds)) {
                $pets = Pet::whereIn('id', $petIds)
                    ->select('id', 'name', 'avatar', 'sex', 'owner_id', 'type_id', 'breed_id')
                    ->with([
                        'petType' => function ($query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        }
                    ])
                    ->with([
                        'petBreed' => function ($query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        }
                    ])
                    ->get();

                // 标记宠物是否为用户所有
                $pets->transform(function ($pet) use ($user) {
                    $pet->is_owner = ($pet->owner_id == $user->id);
                    return $pet;
                });

                $user->pets = $pets;
            } else {
                $user->pets = [];
            }

            // 获取用户的家人关系
            $familyUserIds = PetMaster::getMyFamilyUserIds($user->id);
            if (!empty($familyUserIds)) {
                $familyUsers = AppUser::whereIn('id', $familyUserIds)
                    ->select('id', 'username', 'chinese_name', 'english_name', 'avatar', 'phone')
                    ->get();

                // 获取共同宠物
                $familyUsers->transform(function ($familyUser) use ($user) {
                    $petIds = PetMaster::where('user_id', $user->id)
                        ->whereIn('pet_id', function ($query) use ($familyUser) {
                            $query->select('pet_id')
                                ->from('pets_masters')
                                ->where('user_id', $familyUser->id);
                        })
                        ->pluck('pet_id')
                        ->toArray();

                    if (!empty($petIds)) {
                        $familyUser->shared_pets = Pet::whereIn('id', $petIds)
                            ->select('id', 'name', 'avatar')
                            ->get();
                    } else {
                        $familyUser->shared_pets = [];
                    }

                    return $familyUser;
                });

                $user->family = $familyUsers;
            } else {
                $user->family = [];
            }

            return $user;
        });

        return ResponseHelper::success($users);
    }


    /**
     * 修改用户状态
     */
    public function updateUserStatus(Request $request)
    {
        $request->validate([
            'id'     => 'required|integer',
            'status' => 'required|in:0,1',
        ]);

        DB::beginTransaction();
        try {
            $user = AppUser::find($request->id);

            if (!$user) {
                DB::rollBack();
                return ResponseHelper::error('用户不存在');
            }

            $user->status = $request->status;
            $user->save();

            DB::commit();
            return ResponseHelper::success(null, '状态更新成功');

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("更新用户状态失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return ResponseHelper::error('更新状态失败');
        }
    }


    /**
     * 修改用户是否为管理员
     */
    public function updateUserIsAdmin(Request $request)
    {
        $request->validate([
            'id'       => 'required|integer',
            'is_admin' => 'required|in:0,1',
        ]);

        DB::beginTransaction();
        try {
            $user = AppUser::find($request->id);

            if (!$user) {
                DB::rollBack();
                return ResponseHelper::error('用户不存在');
            }

            $user->is_admin = $request->is_admin;
            $user->save();

            DB::commit();
            return ResponseHelper::success(null, '是否为管理员更新成功');

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("更新用户管理员失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return ResponseHelper::error('更新管理员失败');
        }
    }

    /**
     * 修改用户身份信息（营养师、客服等）
     */
    public function updateUserIsDietitian(Request $request)
    {
        $request->validate([
            'id'                  => 'required|integer',
            'is_dietitian'        => 'required|in:0,1,2',
            'is_customer_service' => 'nullable|in:0,1',
            'chinese_name'        => 'nullable|string|max:50',
            'english_name'        => 'nullable|string|max:50',
            'memo'                => 'nullable|string',
            'certificates'        => 'nullable|string',
            'work_year'           => 'nullable|integer|gte:0',
            'work_clock_start'    => 'nullable|clock',
            'work_clock_end'      => 'nullable|clock',
        ]);

        DB::beginTransaction();
        try {
            $user = AppUser::find($request->id);

            if (!$user) {
                DB::rollBack();
                return ResponseHelper::error('用户不存在');
            }

            $user->is_dietitian = $request->is_dietitian;

            // 如果传递了客服身份参数，则更新
            if ($request->has('is_customer_service')) {
                $user->is_customer_service = $request->is_customer_service;
            }

            $user->chinese_name = $request->chinese_name ?? $user->chinese_name;
            $user->english_name = $request->english_name ?? $user->english_name;
            $user->memo = $request->memo ?? $user->memo;
            $user->certificates = $request->certificates ?? $user->certificates;
            $user->work_year = $request->work_year ?? $user->work_year;
            $user->work_clock_start = $request->work_clock_start ?? $user->work_clock_start;
            $user->work_clock_end = $request->work_clock_end ?? $user->work_clock_end;
            $user->save();

            DB::commit();

            $message = '用户信息更新成功';
            if ($request->has('is_customer_service')) {
                $message = '营养师和客服身份更新成功';
            } else {
                $message = '营养师信息更新成功';
            }

            return ResponseHelper::success(null, $message);

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("更新用户营养师失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return ResponseHelper::error('更新营养师失败');
        }
    }



    /**
     * 获取用户关注的人列表
     */
    public function getUserFollowingList(Request $request)
    {
        $request->validate([
            'user_id'  => 'required|integer|gt:0',
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,100',
        ]);

        $formData = $request->all();
        list($success, $followingList) = AppUserFollow::getFollowingList($formData, 0);

        if ($success) {
            return ResponseHelper::success($followingList);
        } else {
            return ResponseHelper::error('获取关注列表失败');
        }
    }


    /**
     * 获取用户详情
     */
    public function getUserDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $userId = $request->input('id');
        $user = AppUser::find($userId);

        if (!$user) {
            return ResponseHelper::error('用户不存在');
        }

        // 转换区域和状态显示
        $user->area_text = $user->area == 1 ? '内地' : '香港';
        $user->status_text = $user->status == 1 ? '正常' : '禁用';

        // 获取用户关注的人列表
        list($success, $followingList) = AppUserFollow::getFollowingList(['user_id' => $user->id, 'per_page' => 100], 0);
        if ($success) {
            $user->following = $followingList['data'];
        } else {
            $user->following = [];
        }

        // 获取用户的粉丝列表
        list($success, $fansList) = AppUserFollow::getFansList(['user_id' => $user->id, 'per_page' => 100], 0);
        if ($success) {
            $user->fans = $fansList['data'];
        } else {
            $user->fans = [];
        }

        // 获取用户的宠物列表
        $petIds = PetMaster::where('user_id', $user->id)->pluck('pet_id')->toArray();
        if (!empty($petIds)) {
            $pets = Pet::whereIn('id', $petIds)
                ->select('id', 'name', 'avatar', 'sex', 'owner_id', 'type_id', 'breed_id', 'birthday', 'description')
                ->with([
                    'petType' => function ($query) {
                        $query->select('id', 'name', 'name_tc', 'name_en');
                    }
                ])
                ->with([
                    'petBreed' => function ($query) {
                        $query->select('id', 'name', 'name_tc', 'name_en');
                    }
                ])
                ->with([
                    'petMasters' => function ($query) {
                        $query->with([
                            'user' => function ($query) {
                                $query->select('id', 'username', 'chinese_name', 'english_name', 'avatar', 'phone');
                            }
                        ]);
                    }
                ])
                ->get();

            // 标记宠物是否为用户所有
            $pets->transform(function ($pet) use ($user) {
                $pet->is_owner = ($pet->owner_id == $user->id);

                // 处理宠物家人关系，标记主人
                if (!empty($pet->petMasters)) {
                    foreach ($pet->petMasters as &$master) {
                        if (isset($master->user)) {
                            $master->user->is_owner = ($master->user->id == $pet->owner_id);
                        }
                    }

                    // 将家人信息提取到单独的字段，方便前端展示
                    $pet->family_members = $pet->petMasters->map(function ($master) {
                        return $master->user;
                    })->filter()->values();
                } else {
                    $pet->family_members = collect([]);
                }

                return $pet;
            });

            $user->pets = $pets;
        } else {
            $user->pets = collect([]);
        }

        // 获取用户的家人关系
        $familyUserIds = PetMaster::getMyFamilyUserIds($user->id);
        if (!empty($familyUserIds)) {
            $familyUsers = AppUser::whereIn('id', $familyUserIds)
                ->select('id', 'username', 'chinese_name', 'english_name', 'avatar', 'phone')
                ->get();

            // 获取共同宠物
            $familyUsers->transform(function ($familyUser) use ($user) {
                $petIds = PetMaster::where('user_id', $user->id)
                    ->whereIn('pet_id', function ($query) use ($familyUser) {
                        $query->select('pet_id')
                            ->from('pets_masters')
                            ->where('user_id', $familyUser->id);
                    })
                    ->pluck('pet_id')
                    ->toArray();

                if (!empty($petIds)) {
                    $familyUser->shared_pets = Pet::whereIn('id', $petIds)
                        ->select('id', 'name', 'avatar')
                        ->get();
                } else {
                    $familyUser->shared_pets = collect([]);
                }

                return $familyUser;
            });

            $user->family = $familyUsers;
        } else {
            $user->family = collect([]);
        }

        return ResponseHelper::success($user);
    }
}
