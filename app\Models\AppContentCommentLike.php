<?php

namespace App\Models;

use App\Events\PostCommentLikedEvent;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class AppContentCommentLike extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_contents_comments_likes';

    protected $fillable = [
        'comment_id',
        'content_id',
        'content_user_id',
        'comment_user_id',
        'comment_like_user_id',
        'is_like',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($contentId, $commentId)
    {
        //活动评论点赞列表
        $cacheContentCommentLikeCount = "app_content:{$contentId}:comment_like_count:{$commentId}";
        redis()->del($cacheContentCommentLikeCount);
        if ($commentLikeList = self::where('comment_id', $commentId)->select('id', 'comment_like_user_id', 'created_at')->get()->toArray()) {
            redis()->incrBy($cacheContentCommentLikeCount, count($commentLikeList));
            //用户评论点赞-用于判断某评论是否已点赞
            foreach ($commentLikeList as $v) {
                $appUserId = $v['comment_like_user_id'];
                $cacheMyCommentLikeId = "app_user:{$appUserId}:content_comment_like_id:{$contentId}_{$commentId}";
                redis()->set($cacheMyCommentLikeId, $v['id']);
            }
        }
        //更新为初始化
        $cacheInitKey = "app_content:{$contentId}:init_comment_like";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($contentId, $commentId)
    {
        $cacheInitKey = "app_content:{$contentId}:init_comment_like";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($contentId, $commentId); //数据有问题时把key删除，重新初始化
    }

    public static function getCommentLikeCount($contentId, $commentId)
    {
        self::ensureInitRedis($contentId, $commentId);
        $cacheContentCommentLikeCount = "app_content:{$contentId}:comment_like_count:{$commentId}";
        return (int)redis()->get($cacheContentCommentLikeCount);
    }

    public static function isLike($appUserId, $contentId, $commentId)
    {
        self::ensureInitRedis($contentId, $commentId);
        $cacheMyCommentLikeId = "app_user:{$appUserId}:content_comment_like_id:{$contentId}_{$commentId}";
        return redis()->get($cacheMyCommentLikeId);
    }

    public static function likeComment($appUserId, $contentId, $commentId)
    {
        self::ensureInitRedis($contentId, $commentId);
        if (self::isLike($appUserId, $contentId, $commentId)) {
            return [false, [], '不能重複點贊該評論'];
        }
        $today = date('Y-m-d');
        $cacheCommentLikeCountToday = "app_user:{$appUserId}:content_comment_like_count:{$today}";
        if (!redis()->exists($cacheCommentLikeCountToday)) {
            redis()->setex($cacheCommentLikeCountToday, 86400, self::withTrashed()->where('comment_like_user_id', $appUserId)->where('created_at', '>=', $today)->count());
        }
        if (redis()->get($cacheCommentLikeCountToday) >= 1000) {
            logErr('评论点赞数量告警：' . $appUserId);
            return [false, [], '今日點贊次數已達上限'];
        }
        if (!$comment = AppContentComment::where('id', $commentId)->first()) {
            return [false, [], '評論不存在或已刪除'];
        }
        if ($contentId != $comment['content_id']) {
            return [false, [], '分享參數有誤'];
        }
        DB::beginTransaction();
        try {
            //用户今日评论次数
            redis()->incr($cacheCommentLikeCountToday);
            //更新到数据库
            $commentLike = new self();
            $commentLike->comment_id = $commentId;
            $commentLike->content_id = $comment->content_id;
            $commentLike->content_user_id = $comment->content_user_id;
            $commentLike->comment_user_id = $comment->comment_user_id;
            $commentLike->comment_like_user_id = $appUserId;
            $commentLike->is_like = 1;
            $commentLike->save();
            //评论点赞次数
            $cacheContentCommentLikeCount = "app_content:{$contentId}:comment_like_count:{$commentId}";
            redis()->incr($cacheContentCommentLikeCount);
            //用户评论点赞-用于判断某评论是否已点赞
            $cacheMyCommentLikeId = "app_user:{$appUserId}:content_comment_like_id:{$contentId}_{$commentId}";
            redis()->set($cacheMyCommentLikeId, $commentLike->id);
            //更新统计
            AppContentComment::incrementLike($commentId);
            //更新评论权重
            AppContentComment::refreshScore($comment);

            // 触发评论点赞事件（只有当点赞者和评论所有者不是同一人时才触发）
            if ($appUserId != $comment->comment_user_id) {
                event(new PostCommentLikedEvent($commentId, $contentId, $comment->comment_user_id, $appUserId));
            }

            DB::commit();
            return [true, ['comment_like_id' => $commentLike->id], '點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '點贊失敗'];
        }
    }

    public static function cancelLikeComment($appUserId, $contentId, $commentId)
    {
        if (!$commentLikeId = self::isLike($appUserId, $contentId, $commentId)) {
            return [false, [], '還沒點贊該評論'];
        }
        DB::beginTransaction();
        try {
            //评论点赞次数
            $cacheContentCommentLikeCount = "app_content:{$contentId}:comment_like_count:{$commentId}";
            redis()->decr($cacheContentCommentLikeCount);
            //用户评论点赞
            $cacheMyCommentLikeId = "app_user:{$appUserId}:content_comment_like_id:{$contentId}_{$commentId}";
            redis()->del($cacheMyCommentLikeId);
            //更新到数据库
            self::where('id', $commentLikeId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            //更新统计
            AppContentComment::decrementLike($commentId);
            //更新评论权重
            $comment = AppContentComment::where('id', $commentId)->first();
            AppContentComment::refreshScore($comment);
            DB::commit();
            return [true, [], '取消點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取消点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取消點贊失敗'];
        }
    }
}
