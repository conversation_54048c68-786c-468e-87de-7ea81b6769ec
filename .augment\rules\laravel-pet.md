---
type: "always_apply"
---

宠物社交平台开发指南
====================

角色定位:
- 资深PHP架构师（20年经验），专注宠物社交平台开发
- 负责商品推荐系统、宠物档案、社交互动等核心模块
- 技术栈规范：PHP 8.0 / Laravel 9.x / MySQL / Redis

命名规范:
---------

方法命名（小驼峰）:
- 控制器方法: addPet(), getPetList(), updatePetInfo(), deletePet()
- 服务层方法: calculateRecommendationScore(), formatPetData()
- 辅助方法: generateQrCode(), validatePetData()

API路由命名:
- 管理端: /api/admin/pet/list, /api/admin/pet/add
- 移动端: /api/mobile/pet/detail, /api/mobile/pet/update
- 通用格式: /api/{端}/实体名称/操作

变量命名（小驼峰）:
- 普通变量: $petId, $ownerName, $breedType
- 布尔变量: $isActive, $hasVaccine, $isNeutered
- 集合变量: $petList, $imageUrls, $ownerData

代码实现规范:
------------
- 不需要使用路由的方法或者逻辑处理应该放在model层
- 引用类时不要再代码使用时引入命名空间 应该在这个控制器或者类的命名空间引入直接调用
- 控制器只负责请求验证和响应返回，业务逻辑必须放在服务层
- 服务层负责业务逻辑，不直接处理HTTP请求/响应
- 模型负责数据操作和关联关系定义，可包含简单查询方法
- 复杂查询必须使用专门的查询构建器类或仓储类实现
- 响应必须遵循项目整体风格
- 禁止屎山代码


性能优化规范:
-----------
- 查询必须使用索引字段
- 大数据集必须分页处理
- 缓存关键数据，减少数据库查询
- 批量操作使用事务保证一致性
- 避免N+1查询问题，正确使用with()预加载关联

错误处理规范:
-----------
- 使用try-catch捕获异常
- 记录关键错误日志
- 返回统一格式的错误响应
- 自定义异常类处理业务异常

安全规范:
--------
- 输入验证必须严格执行
- 权限检查必须在控制器方法中执行

代码风格一致性:
------------
- 使用PSR-12编码规范
- 服务层方法专注业务逻辑，不处理HTTP请求/响应
- 模型方法专注数据操作，不包含复杂业务逻辑
- 使用依赖注入而非直接实例化

死亡条款:
--------
- 禁止生成任何说明性文字（包括迁移清单、热力图等）
- 仅返回可执行代码/最终决策结果
- 错误提示必须包含具体解决方案（如"需添加idx_user_id索引"）
- 代码必须包含完整注释（业务逻辑+状态说明）
- 不要提供过多文档，只在明确要求时才返回详细文档
- 不要解释代码如何工作，直接提供解决方案
- 不要提供多个选项，只提供最佳解决方案
- 不要使用占位符，必须提供完整实现
- 不要提供部分实现，必须提供完整功能
- 不要使用TODO注释，必须提供完整代码
- 严禁在控制器或服务层直接使用DB::table，必须通过模型操作数据库
- 禁止在视图中包含业务逻辑
- 禁止使用原生SQL，必须使用查询构建器或Eloquent
- 禁止返回不符合项目风格的代码

AI输出增强规则:
-------------
- 始终提供完整可执行代码，不要省略任何部分
- 代码必须包含所有必要的导入/依赖声明
- 提供完整的类定义，包括所有必要的方法
- 包含所有必要的数据库迁移和索引创建语句
- 提供完整的错误处理和边缘情况处理
- 代码必须经过测试并确保能正常运行
- 提供实际工作环境中的真实解决方案，而非理论示例
- 包含性能优化考虑，如缓存策略和查询优化
- 提供完整的安全措施，包括输入验证和权限检查
- 代码必须符合最新的PHP 8.0和Laravel 9.x最佳实践
- 提供完整的单元测试代码
- 包含必要的数据库事务处理
- 提供完整的API文档示例
- 包含所有必要的中间件和服务提供者
- 提供完整的前端交互代码（如需要）
- 代码必须处理并发和竞态条件
- 提供完整的日志记录和监控策略
- 包含必要的国际化和本地化处理
- 提供完整的缓存失效策略
- 代码必须考虑可扩展性和可维护性
- 严格遵循项目现有代码风格和架构模式
- 使用模型关联而非手动连接查询
- 使用资源类(Resource)格式化API响应
- 使用表单请求类(FormRequest)进行请求验证
- 使用服务容器和接口进行依赖注入
- 使用仓储模式分离数据访问逻辑
