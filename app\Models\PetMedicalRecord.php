<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 宠物医疗档案模型
 */
class PetMedicalRecord extends Model
{
    use HasFactory;

    protected $table = 'pets_medical_records';

    protected $fillable = [
        'pet_id',
        'record_title',
        'record_date',
        'image_url',
        'description',
        'hospital_name',
        'doctor_name',
        'diagnosis',
        'treatment',
        'remark',
        'status',
        'analyze_status',
        'analyze_result',
        'analyze_failure_reason',
        'analyze_failure_details',
        'admin_id',
        'audited_at'
    ];

    protected $casts = [
        'image_url' => 'array',
        'analyze_failure_details' => 'array',
        'record_date' => 'date',
        'audited_at' => 'datetime'
    ];

    // 审核状态常量
    const STATUS_PENDING = 1;  // 待审核
    const STATUS_APPROVED = 2; // 通过
    const STATUS_REJECTED = 3; // 拒绝

    // 分析状态常量
    const ANALYZE_STATUS_PENDING = 0;  // 待分析
    const ANALYZE_STATUS_SUCCESS = 1;  // 分析成功
    const ANALYZE_STATUS_FAILED = 2;   // 分析失败

    // 分析失败原因常量
    const FAILURE_REASON_JSON_PARSE = 'json_parse_failure';           // JSON解析失败
    const FAILURE_REASON_ORGANS_EMPTY = 'organs_empty';               // 器官数据为空
    const FAILURE_REASON_IMAGE_INCOMPLETE = 'image_incomplete';       // 图片识别不完整
    const FAILURE_REASON_AI_NO_CONTENT = 'ai_no_content_returned';    // AI未返回内容
    const FAILURE_REASON_AI_SERVICE_ERROR = 'ai_service_error';       // AI服务异常
    const FAILURE_REASON_NETWORK_ERROR = 'network_error';             // 网络错误
    const FAILURE_REASON_TIMEOUT = 'timeout';                         // 超时
    const FAILURE_REASON_UNKNOWN = 'unknown';                         // 未知错误

    /**
     * 关联宠物
     */
    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    /**
     * 关联审核管理员
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * 获取审核状态名称
     */
    public function getStatusNameAttribute()
    {
        $statuses = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '通过',
            self::STATUS_REJECTED => '拒绝'
        ];

        return $statuses[$this->status] ?? '未知状态';
    }

    /**
     * 获取分析状态名称
     */
    public function getAnalyzeStatusNameAttribute()
    {
        $statuses = [
            self::ANALYZE_STATUS_PENDING => '待分析',
            self::ANALYZE_STATUS_SUCCESS => '分析成功',
            self::ANALYZE_STATUS_FAILED => '分析失败'
        ];

        return $statuses[$this->analyze_status] ?? '未知状态';
    }

    /**
     * 获取失败原因名称
     */
    public function getFailureReasonNameAttribute()
    {
        if (empty($this->analyze_failure_reason)) {
            return null;
        }

        $reasons = [
            self::FAILURE_REASON_JSON_PARSE => 'JSON解析失败',
            self::FAILURE_REASON_ORGANS_EMPTY => '器官数据为空',
            self::FAILURE_REASON_IMAGE_INCOMPLETE => '图片识别不完整',
            self::FAILURE_REASON_AI_NO_CONTENT => 'AI未返回内容',
            self::FAILURE_REASON_AI_SERVICE_ERROR => 'AI服务异常',
            self::FAILURE_REASON_NETWORK_ERROR => '网络错误',
            self::FAILURE_REASON_TIMEOUT => '分析超时',
            self::FAILURE_REASON_UNKNOWN => '未知错误'
        ];

        return $reasons[$this->analyze_failure_reason] ?? $this->analyze_failure_reason;
    }

    /**
     * 获取所有审核状态
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '通过',
            self::STATUS_REJECTED => '拒绝'
        ];
    }

    /**
     * 作用域：按审核状态筛选
     */
    public function scopeOfStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待审核的档案
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已通过的档案
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * 作用域：已拒绝的档案
     */
    public function scopeRejected($query)
    {
        return $query->where('status', self::STATUS_REJECTED);
    }

    /**
     * 作用域：按档案日期范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('record_date', [$startDate, $endDate]);
    }

    /**
     * 作用域：按医院筛选
     */
    public function scopeOfHospital($query, $hospitalName)
    {
        return $query->where('hospital_name', 'like', "%{$hospitalName}%");
    }

    /**
     * 作用域：按医生筛选
     */
    public function scopeOfDoctor($query, $doctorName)
    {
        return $query->where('doctor_name', 'like', "%{$doctorName}%");
    }

    /**
     * 作用域：按分析状态筛选
     */
    public function scopeOfAnalyzeStatus($query, $analyzeStatus)
    {
        return $query->where('analyze_status', $analyzeStatus);
    }

    /**
     * 作用域：分析失败的档案
     */
    public function scopeAnalyzeFailed($query)
    {
        return $query->where('analyze_status', self::ANALYZE_STATUS_FAILED);
    }

    /**
     * 作用域：按失败原因筛选
     */
    public function scopeOfFailureReason($query, $reason)
    {
        return $query->where('analyze_failure_reason', $reason);
    }

    /**
     * 获取图片URL列表（支持向后兼容）
     */
    public function getImageUrlsAttribute()
    {
        $imageUrl = $this->attributes['image_url'] ?? null;

        if (empty($imageUrl)) {
            return [];
        }

        // 如果是JSON数组格式，直接返回
        if (is_array($imageUrl)) {
            return $imageUrl;
        }

        // 如果是字符串，尝试解析JSON
        if (is_string($imageUrl)) {
            $decoded = json_decode($imageUrl, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                return $decoded;
            }
            // 如果不是JSON，当作单个URL处理
            return [$imageUrl];
        }

        return [];
    }

    /**
     * 获取第一张图片URL（向后兼容）
     */
    public function getFirstImageUrlAttribute()
    {
        $urls = $this->image_urls;
        return !empty($urls) ? $urls[0] : null;
    }

    /**
     * 设置图片URL（支持字符串和数组）
     */
    public function setImageUrlAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['image_url'] = json_encode($value);
        } elseif (is_string($value)) {
            // 检查是否为JSON字符串
            $decoded = json_decode($value, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                $this->attributes['image_url'] = $value;
            } else {
                // 单个URL转为数组格式
                $this->attributes['image_url'] = json_encode([$value]);
            }
        } else {
            $this->attributes['image_url'] = json_encode([]);
        }
    }
}
