<?php
require_once 'vendor/autoload.php';

// 初始化Laravel应用
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\PetBreed;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

try {
    echo "=== 测试 month_ranges 字段保存 ===\n";
    
    // 你的完整数据
    $breedData = [
        "name" => "测试月份范围_" . time(),
        "name_tc" => "測試月份範圍_" . time(),
        "name_en" => "Test Month Ranges_" . time(),
        "type_id" => 1,
        "size" => "medium",
        "hair_type" => "medium",
        "weight_max_male" => 1,
        "weight_max_female" => 1,
        "month_ranges" => [
            "juvenile" => [
                "start" => 0,
                "end" => 12
            ],
            "adult" => [
                "start" => 12,
                "end" => 84
            ],
            "senior" => [
                "start" => 84,
                "end" => 120
            ],
            "elderly" => [
                "start" => 120,
                "end" => null
            ]
        ]
    ];
    
    echo "1. 测试控制器验证规则:\n";
    
    // 新的验证规则
    $rules = [
        'id'                => 'sometimes|integer|gte:0',
        'name'              => 'required|max:32',
        'name_tc'           => 'required|max:32',
        'name_en'           => 'required|max:255',
        'type_id'           => 'required|integer|gt:0|exists:pets_types,id',
        'size'              => 'required|string|in:small,medium,large',
        'hair_type'         => 'nullable|string|in:short,medium,long',
        'weight_max_male'   => 'nullable|numeric|gte:0',
        'weight_max_female' => 'nullable|numeric|gte:0',
        'month_ranges'      => 'nullable|array',
        'month_ranges.*.start' => 'nullable|integer|gte:0',
        'month_ranges.*.end'   => 'nullable|integer|gte:0',
    ];
    
    $validator = Validator::make($breedData, $rules);
    
    if ($validator->fails()) {
        echo "   ❌ 验证失败:\n";
        foreach ($validator->errors()->all() as $error) {
            echo "     - {$error}\n";
        }
    } else {
        echo "   ✅ 验证通过\n";
    }
    
    echo "\n2. 测试数据提取:\n";
    $formData = collect($breedData)->only(array_keys($rules))->toArray();
    echo "   提取的字段: " . implode(', ', array_keys($formData)) . "\n";
    echo "   month_ranges 是否包含: " . (isset($formData['month_ranges']) ? '✅ 是' : '❌ 否') . "\n";
    
    if (isset($formData['month_ranges'])) {
        echo "   month_ranges 内容: " . json_encode($formData['month_ranges'], JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "\n3. 测试模型保存:\n";
    try {
        $result = PetBreed::saveDetail($formData);
        if ($result[0]) {
            echo "   ✅ 保存成功! ID: {$result[1]['id']}\n";
            
            // 查询保存的数据
            $savedBreed = PetBreed::find($result[1]['id']);
            echo "   保存后的 month_ranges: " . json_encode($savedBreed->month_ranges, JSON_UNESCAPED_UNICODE) . "\n";
            
            // 验证数据完整性
            if ($savedBreed->month_ranges && is_array($savedBreed->month_ranges)) {
                echo "   ✅ month_ranges 保存成功，类型正确\n";
                
                // 检查具体内容
                $expectedStages = ['juvenile', 'adult', 'senior', 'elderly'];
                $savedStages = array_keys($savedBreed->month_ranges);
                
                if (array_diff($expectedStages, $savedStages) === [] && array_diff($savedStages, $expectedStages) === []) {
                    echo "   ✅ 所有生命阶段都已保存\n";
                } else {
                    echo "   ⚠️  生命阶段不完整\n";
                    echo "     期望: " . implode(', ', $expectedStages) . "\n";
                    echo "     实际: " . implode(', ', $savedStages) . "\n";
                }
            } else {
                echo "   ❌ month_ranges 保存失败或类型错误\n";
            }
            
            // 清理测试数据
            PetBreed::where('id', $result[1]['id'])->delete();
            echo "   ✅ 测试数据已清理\n";
            
        } else {
            echo "   ❌ 保存失败: {$result[1]}\n";
        }
    } catch (Exception $e) {
        echo "   ❌ 异常: " . $e->getMessage() . "\n";
    }
    
    echo "\n4. 测试不同的 month_ranges 格式:\n";
    
    // 测试空值
    $testData1 = $breedData;
    $testData1['name'] = "测试空值_" . time();
    $testData1['month_ranges'] = null;
    
    $result1 = PetBreed::saveDetail($testData1);
    if ($result1[0]) {
        $saved1 = PetBreed::find($result1[1]['id']);
        echo "   ✅ null 值保存成功: " . json_encode($saved1->month_ranges) . "\n";
        PetBreed::where('id', $result1[1]['id'])->delete();
    }
    
    // 测试部分数据
    $testData2 = $breedData;
    $testData2['name'] = "测试部分数据_" . time();
    $testData2['month_ranges'] = [
        "juvenile" => ["start" => 0, "end" => 12],
        "adult" => ["start" => 12, "end" => 84]
    ];
    
    $result2 = PetBreed::saveDetail($testData2);
    if ($result2[0]) {
        $saved2 = PetBreed::find($result2[1]['id']);
        echo "   ✅ 部分数据保存成功: " . json_encode($saved2->month_ranges, JSON_UNESCAPED_UNICODE) . "\n";
        PetBreed::where('id', $result2[1]['id'])->delete();
    }
    
    echo "\n=== 测试完成 ===\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
