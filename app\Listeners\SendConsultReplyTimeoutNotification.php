<?php

namespace App\Listeners;

use App\Events\ConsultReplyTimeoutEvent;
use App\Helpers\GoEasy;
use App\Models\AppUser;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultReplyTimeoutNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultReplyTimeoutEvent $event
     * @return void
     */
    public function handle(ConsultReplyTimeoutEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_CONSULT_REPLY_TIMEOUT,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定营养师
            $channel = GoEasyServiceProvider::getChannelForDietitian($consult->dietitian_id);
            $notification = ConsultNotificationService::getConsultReplyTimeoutMessage();
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            //推送给上级营养师
            $superDietitians = AppUser::where('is_dietitian', 2)->select('id')->get()->toArray();
            foreach ($superDietitians as $superDietitian) {
                $channel = GoEasyServiceProvider::getChannelForDietitian($superDietitian['id']);
                $notification['body'] = '【' . $consult->dietitian->chinese_name . '】' . $notification['body'];
                $res = $goEasy->publish($channel, $json, $notification);
                if (!$res || $res['code'] != 200) {
                    throw new \Exception('GoEasy publish to super dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
                }
            }

            Log::info('Consult reply timeout notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult reply timeout notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
