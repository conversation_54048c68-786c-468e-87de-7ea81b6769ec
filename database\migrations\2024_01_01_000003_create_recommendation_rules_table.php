<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRecommendationRulesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 推荐规则表
        Schema::create('recommendation_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->comment('规则名称');
            $table->string('description', 500)->nullable()->comment('规则描述');
            $table->json('pet_types')->nullable()->comment('适用宠物类型ID数组');
            $table->json('pet_breeds')->nullable()->comment('适用宠物品种ID数组');
            $table->integer('age_range_min')->nullable()->comment('适用最小年龄(月)');
            $table->integer('age_range_max')->nullable()->comment('适用最大年龄(月)');
            $table->decimal('weight_range_min', 5, 2)->nullable()->comment('适用最小体重(kg)');
            $table->decimal('weight_range_max', 5, 2)->nullable()->comment('适用最大体重(kg)');
            $table->tinyInteger('recommendation_priority')->default(1)->comment('推荐优先级:1=低,2=中,3=高');
            $table->tinyInteger('is_active')->default(1)->comment('是否启用:0=禁用,1=启用');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->timestamps();
            
            $table->index('is_active');
            $table->index('sort_order');
        });

        // 为商品表添加推荐规则字段
        Schema::table('goods', function (Blueprint $table) {
            $table->bigInteger('recommendation_rule_id')->nullable()->comment('关联的推荐规则ID');
            $table->index('recommendation_rule_id');
        });

        // 插入预设规则
        $this->insertDefaultRules();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods', function (Blueprint $table) {
            $table->dropColumn('recommendation_rule_id');
        });
        
        Schema::dropIfExists('recommendation_rules');
    }

    /**
     * 插入预设规则
     */
    private function insertDefaultRules()
    {
        $rules = [
            [
                'name' => '幼犬专用规则',
                'description' => '适用于2-12个月的幼犬商品，高推荐优先级',
                'pet_types' => json_encode([1]), // 狗
                'pet_breeds' => null,
                'age_range_min' => 2,
                'age_range_max' => 12,
                'weight_range_min' => 1.0,
                'weight_range_max' => 15.0,
                'recommendation_priority' => 3,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '成犬通用规则',
                'description' => '适用于12个月以上成犬商品，中等推荐优先级',
                'pet_types' => json_encode([1]), // 狗
                'pet_breeds' => null,
                'age_range_min' => 12,
                'age_range_max' => 84,
                'weight_range_min' => 5.0,
                'weight_range_max' => 50.0,
                'recommendation_priority' => 2,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '猫咪通用规则',
                'description' => '适用于所有猫咪商品',
                'pet_types' => json_encode([2]), // 猫
                'pet_breeds' => null,
                'age_range_min' => 3,
                'age_range_max' => 180,
                'weight_range_min' => 1.0,
                'weight_range_max' => 15.0,
                'recommendation_priority' => 2,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '小型犬规则',
                'description' => '适用于小型犬品种商品',
                'pet_types' => json_encode([1]), // 狗
                'pet_breeds' => json_encode([1, 2, 3]), // 小型犬品种ID (需要根据实际数据调整)
                'age_range_min' => 6,
                'age_range_max' => 120,
                'weight_range_min' => 1.0,
                'weight_range_max' => 10.0,
                'recommendation_priority' => 2,
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '大型犬规则',
                'description' => '适用于大型犬品种商品',
                'pet_types' => json_encode([1]), // 狗
                'pet_breeds' => json_encode([4, 5, 6]), // 大型犬品种ID (需要根据实际数据调整)
                'age_range_min' => 6,
                'age_range_max' => 120,
                'weight_range_min' => 20.0,
                'weight_range_max' => 80.0,
                'recommendation_priority' => 2,
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => '通用商品规则',
                'description' => '适用于所有宠物的通用商品，无特殊限制',
                'pet_types' => json_encode([1, 2]), // 狗和猫
                'pet_breeds' => null,
                'age_range_min' => null,
                'age_range_max' => null,
                'weight_range_min' => null,
                'weight_range_max' => null,
                'recommendation_priority' => 1,
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('recommendation_rules')->insert($rules);
    }
}
