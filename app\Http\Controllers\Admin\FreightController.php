<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\Freight;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * @group   freight
 * 运费模板管理
 * Class FreightController
 * @package App\Http\Controllers\Admin
 */
class FreightController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $records = Freight::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Freight::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                        => 'sometimes|integer|gte:0',
            'name'                      => 'required|max:60',
            'name_tc'                   => 'required|max:60',
            'name_en'                   => 'required|max:255',
            'location'                  => 'required|array',
            'pinkage'                   => 'nullable|array',
            'valuation'                 => 'required|in:1,2',
            'freight_way'               => 'nullable|array',
            'freight_way.*.first_piece' => 'required|numeric|gte:1',
            'freight_way.*.first_cost'  => 'required|numeric',
            'freight_way.*.add_piece'   => 'required|numeric|gte:1',
            'freight_way.*.add_cost'    => 'required|numeric',
            'freight_way.*.location'    => 'required|array',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['freight_way'] = $request->post('freight_way');
        $result = Freight::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Freight::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
