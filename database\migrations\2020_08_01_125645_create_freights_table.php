<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateFreightsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('freights', function (Blueprint $table) {
            $table->id();
            $table->string('name', 60)->comment('模板名称');
            $table->text('location')->comment('宝贝地址json');
            $table->text('pinkage')->comment('包邮地区JSON');
            $table->tinyInteger('valuation')->comment('计价方式');
            $table->timestamps();
            $table->unique('id');
        });
        DB::statement("ALTER TABLE `freights` COMMENT='运费模板'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('freights');
    }
}
