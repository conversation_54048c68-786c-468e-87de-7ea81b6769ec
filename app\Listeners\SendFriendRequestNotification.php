<?php

namespace App\Listeners;

use App\Events\FriendRequestEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendFriendRequestNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param \App\Events\FriendRequestEvent $event
     * @return void
     */
    public function handle(FriendRequestEvent $event)
    {
        try {
            // 获取关注者信息
            $follower = AppUser::find($event->toUserId);

            if (!$follower) {
                Log::error('Friend request notification failed: Follower not found', [
                    'toUserId'   => $event->toUserId,
                    'fromUserId' => $event->fromUserId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->toUserId, // 接收通知的用户ID（被关注的用户）
                NotificationSetting::TYPE_FRIEND_REQUEST, // 通知类型
                '新的好友申请',
                "用户 {$follower->username} 申请添加您为好友",
                $event->fromUserId, // 发送者ID（关注者）
                $event->toUserId, // 相关ID（被关注的用户ID）
                NotificationSetting::RELATION_TYPE_USER // 相关类型
            );

            Log::info('Friend request notification sent', [
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId
            ]);
        } catch (\Exception $e) {
            Log::error('Friend request notification failed', [
                'error'      => $e->getMessage(),
                'toUserId'   => $event->toUserId,
                'fromUserId' => $event->fromUserId
            ]);
        }
    }
}
