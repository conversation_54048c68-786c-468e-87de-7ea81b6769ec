<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetAlbumLikedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 相册ID
     *
     * @var int
     */
    public $albumId;

    /**
     * 相册所有者ID
     *
     * @var int
     */
    public $albumOwnerId;

    /**
     * 点赞者ID
     *
     * @var int
     */
    public $likerId;

    /**
     * Create a new event instance.
     *
     * @param int $albumId 相册ID
     * @param int $albumOwnerId 相册所有者ID
     * @param int $likerId 点赞者ID
     * @return void
     */
    public function __construct($albumId, $albumOwnerId, $likerId)
    {
        $this->albumId = $albumId;
        $this->albumOwnerId = $albumOwnerId;
        $this->likerId = $likerId;
    }
}
