<?php

namespace App\Http\Controllers\Mobile;

use App\Events\PostCommentedEvent;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppContent;
use App\Models\AppContentComment;
use App\Models\AppContentCommentLike;
use Illuminate\Http\Request;

class AppContentCommentController extends Controller
{
    public function addComment(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'comment_id' => 'sometimes|integer|gt:0', // 回复某评论
            'content'    => 'required',
        ]);
        $commentId = $request->input('comment_id', 0);
        $userId = $request->user()->id;
        $contentId = $request->content_id;

        $commentContent = $request->input('content');
        $result = AppContentComment::addComment($userId, $contentId, $commentContent, $commentId);

        // 如果评论成功，触发事件
        if ($result[0] && isset($result[1]['comment_id'])) {
            $newCommentId = $result[1]['comment_id'];
            $contentUserId = AppContent::where('id', $contentId)->value('user_id');

            // 只有当评论者和帖子所有者不是同一人时才触发
            if ($userId != $contentUserId) {
                event(new PostCommentedEvent($contentId, $contentUserId, $newCommentId, $userId));
            }
        }

        return ResponseHelper::result(...$result);
    }

    public function delComment(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'comment_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentComment::delComment($request->user()->id, $request->content_id, $request->comment_id);
        return ResponseHelper::result(...$result);
    }

    public function getCommentList(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'sort_type'  => 'nullable|integer|in:0,1',  //0=按时间排序(默认)，1=按点赞数排序
        ]);
        $formData = $request->all();
        $result = AppContentComment::getCommentList($formData, $request->user()->id);
        return ResponseHelper::result(...$result);
    }

    public function likeComment(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'comment_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentCommentLike::likeComment($request->user()->id, $request->content_id, $request->comment_id);
        return ResponseHelper::result(...$result);
    }

    public function cancelLikeComment(Request $request)
    {
        $request->validate([
            'content_id' => 'required|integer|gt:0',
            'comment_id' => 'required|integer|gt:0',
        ]);
        $result = AppContentCommentLike::cancelLikeComment($request->user()->id, $request->content_id, $request->comment_id);
        return ResponseHelper::result(...$result);
    }






}
