<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class OperateLinkModelProvider extends ServiceProvider
{
    const LINK_TYPES = [
        'App\Models\SystemMenu'         => 1,
        'App\Models\Role'               => 2,
        'App\Models\User'               => 3,
        'App\Models\AppUser'            => 4,
        'App\Models\Version'            => 5,
        'App\Models\AppContentComment'  => 6,
        'App\Models\AppContent'         => 7,
        'App\Models\PetType'            => 8,
        'App\Models\PetBreed'           => 9,
        'App\Models\Pet'                => 10,
        'App\Models\Brand'              => 11,
        'App\Models\GoodCategory'       => 12,
        'App\Models\Freight'            => 13,
        'App\Models\Dhl'                => 14,
        'App\Models\SpecificationGroup' => 15,
        'App\Models\Specification'      => 16,
        'App\Models\Good'               => 17,
        'App\Models\ArticleCategory'    => 18,
        'App\Models\Article'            => 19,
        'App\Models\PetReminderConfig'  => 20,
    ];

    const LINK_TYPE_NAMES = [
        'App\Models\SystemMenu'         => '系統菜單',
        'App\Models\Role'               => '系統角色',
        'App\Models\User'               => '管理員',
        'App\Models\AppUser'            => 'APP用戶',
        'App\Models\Version'            => '版本',
        'App\Models\AppContentComment'  => '評論',
        'App\Models\AppContent'         => '分享',
        'App\Models\PetType'            => '寵物種類',
        'App\Models\PetBreed'           => '寵物品種',
        'App\Models\Pet'                => '寵物',
        'App\Models\Brand'              => '品牌',
        'App\Models\GoodCategory'       => '商品分類',
        'App\Models\Freight'            => '運費模板',
        'App\Models\Dhl'                => '快遞公司',
        'App\Models\SpecificationGroup' => '規格組',
        'App\Models\Specification'      => '規格值',
        'App\Models\Good'               => '商品',
        'App\Models\ArticleCategory'    => '文章分類',
        'App\Models\Article'            => '文章',
        'App\Models\PetReminderConfig'  => '寵物提醒配置',
    ];

    public static function getLinkType($modelClass)
    {
        static $pass = null;
        if (is_null($pass)) {
            $uniqueLinkTypes = array_unique(self::LINK_TYPES);
            if (count($uniqueLinkTypes) != count(self::LINK_TYPES)) {
                $diff = array_diff_key(self::LINK_TYPES, $uniqueLinkTypes);
                throw new \Exception('关联类型定义有误，需检查重复項：' . implode('，', array_keys($diff)));
            }
            if (count(self::LINK_TYPES) != count(self::LINK_TYPE_NAMES)) {
                $diff = array_diff_key(self::LINK_TYPES, self::LINK_TYPE_NAMES);
                throw new \Exception('关联类型名稱有误，需检查缺少項：' . implode('，', array_keys($diff)));
            }
            $pass = true;
        }
        return self::LINK_TYPES[$modelClass] ?? null;
    }

    public static function getLinkName($link_type)
    {
        $modelClass = array_search($link_type, self::LINK_TYPES);
        return self::LINK_TYPE_NAMES[$modelClass] ?? null;
    }
}
