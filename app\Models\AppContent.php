<?php

namespace App\Models;

use App\Events\DataChanged;
use Carbon\Carbon;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


/**
 * 内容模型
 * @package App\Models
 */
class AppContent extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'content',
        'images',
        'status',
        'visibility',
        'post_type',
        'longitude',
        'latitude',
    ];

    /**
     * 日期格式化
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public function pets()
    {
        return $this->belongsToMany(Pet::class, 'app_contents_pets', 'app_content_id', 'pet_id');
    }

    public function Commodities()
    {
        return $this->belongsToMany(GoodIndentCommodity::class, 'app_contents_indents_commoditys', 'app_content_id', 'good_indent_commodity_id');
    }

    /**
     * 与标签的多对多关系
     */
    public function tags()
    {
        return $this->belongsToMany(AppShareTag::class, 'app_contents_tags', 'app_content_id', 'app_share_tag_id');
    }

    public static function getList($search_data = array(), $requestAppUserId = null)
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "id";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $appUserId = $search_data['user_id'] ?? ""; //篩選指定用戶發佈的文章
        $petId = $search_data['pet_id'] ?? "";
        $goodId = $search_data['good_id'] ?? "";
        $type = $search_data['type'] ?? "";
        $status = $search_data['status'] ?? "";
        $longitude = $search_data['longitude'] ?? "";
        $latitude = $search_data['latitude'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $tagIds = $search_data['tag_ids'] ?? ""; // 標籤篩選
        $petTypeFilter = $search_data['_pet_type_filter'] ?? ""; // 宠物种类筛选

        $query = new self;
        if (!empty($search_data['is_deleted'])) {
            $query = $query->onlyTrashed();
        }


        $query->when(isset($search_data['ids']), function (Builder $query) use ($search_data) {
            $query->whereIn('app_contents.id', $search_data['ids']);
        });

        $query = $query->select([
            'app_contents.id',
            'app_contents.user_id',
            'app_contents.title',
            'app_contents.content',
            'app_contents.images',
            'app_contents.visibility',
            'app_contents.status',
            'app_contents.post_type',
            'app_contents.longitude',
            'app_contents.latitude',
            'app_contents.view_count',
            'app_contents.like_count',
            'app_contents.favorite_count',
            'app_contents.comment_count',
            'app_contents.created_at',
            'app_contents.updated_at',
            'app_contents.deleted_at',
        ])
            ->with([
                'user'        => function (Builder $query) {
                    $query->select('id', 'username', 'phone', 'avatar');
                },
                'pets'        => function (Builder $query) {
                    $query->select('pets.id', 'pets.name', 'pets.sex', 'pets.avatar');
                },
                'Commodities' => function (Builder $q) {
                    $q->select('goods_indents_commoditys.id', 'goods_indents_commoditys.good_id', 'goods_indents_commoditys.image', 'goods_indents_commoditys.name', 'goods_indents_commoditys.name_tc', 'goods_indents_commoditys.name_en');
                },
                'tags'        => function (Builder $query) {
                    $query->select('app_share_tags.id', 'app_share_tags.name', 'app_share_tags.color');
                },
            ])
            ->when($keyword, function (Builder $query) use ($keyword) {
                $query->where(function (Builder $query) use ($keyword) {
                    $query->where('app_contents.content', 'like', "%$keyword%");
                });
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('app_contents.created_at', [$createdStart, $createdEnd]);
            })
            ->when($appUserId !== "", function (Builder $query) use ($appUserId, $requestAppUserId) {
                $query->where('app_contents.user_id', $appUserId);
                if ($requestAppUserId != 0 && $appUserId != $requestAppUserId) {
                    //查看其他人的内容列表，需要判断权限
                    $isFamily = PetMaster::isFamily($requestAppUserId, $appUserId);
                    $isFollowing = AppUserFollow::isFollowing($requestAppUserId, $appUserId);

                    // 基于visibility字段的权限控制
                    // 注意：private类型的内容只有作者自己能看到，这里不包含在任何权限组合中
                    if ($isFamily && $isFollowing) {
                        // 既是家人又是朋友，可以看family、friends、public
                        $visibilityTypes = ['family', 'friends', 'public'];
                    } elseif ($isFamily) {
                        // 只是家人，可以看family、public
                        $visibilityTypes = ['family', 'public'];
                    } elseif ($isFollowing) {
                        // 只是朋友，可以看friends、public
                        $visibilityTypes = ['friends', 'public'];
                    } else {
                        // 陌生人，只能看public
                        $visibilityTypes = ['public'];
                    }
                    $query->whereIn('app_contents.visibility', $visibilityTypes);
                }
            })
            ->when($petId !== "", function (Builder $query) use ($petId) {
                if ($petId == -1) {
                    //不查看寵物相關帖子
                    $query->whereDoesntHave('pets');
                } else {
                    //篩選寵物相關帖子
                    $query->join('app_contents_pets', 'app_contents.id', '=', 'app_contents_pets.app_content_id')
                        ->where('app_contents_pets.pet_id', $petId)
                        ->distinct();
                }
            })
            // 支持多寵物ID篩選（新增）
            ->when(!empty($search_data['_filtered_pet_ids']), function (Builder $query) use ($search_data) {
                $petIds = $search_data['_filtered_pet_ids'];
                // 所有模式：只返回關聯指定寵物的帖子
                $query->whereHas('pets', function (Builder $query) use ($petIds) {
                    $query->whereIn('pets.id', $petIds);
                });
            })
            ->when($goodId !== "", function (Builder $query) use ($goodId) {
                $query->whereHas('Commodities', function ($q) use ($goodId) {
                    $q->withTrashed()->where('good_id', $goodId);
                });
            })
            ->when($type !== "", function (Builder $query) use ($type) {
                $query->where('app_contents.type', $type);
            })
            ->when($status !== "", function (Builder $query) use ($status) {
                $query->where('app_contents.status', $status);
            })
            ->when($tagIds !== "", function (Builder $query) use ($tagIds) {
                $tagIdsArray = is_array($tagIds) ? $tagIds : explode(',', $tagIds);
                $tagIdsArray = array_filter($tagIdsArray);
                if (!empty($tagIdsArray)) {
                    $query->whereHas('tags', function ($q) use ($tagIdsArray) {
                        $q->whereIn('app_share_tags.id', $tagIdsArray);
                    });
                }
            })
            ->when(!empty($search_data['_index']), function (Builder $query) use ($requestAppUserId) {
                //首页 展示自己的所有内容（包括private） + 同宠其他家人的family和public内容 + 关注的人的friends和public内容
                $query->where(function (Builder $query) use ($requestAppUserId) {
                    // 自己的所有内容（包括private私人记录）
                    $query->where('app_contents.user_id', $requestAppUserId)
                        // 家人的family和public内容
                        ->orWhere(function (Builder $query) use ($requestAppUserId) {
                            $familyUserIds = PetMaster::getMyFamilyUserIds($requestAppUserId);
                            if ($familyUserIds) {
                                $query->whereIn('app_contents.user_id', $familyUserIds)
                                    ->whereIn('app_contents.visibility', ['family', 'public']);
                            }
                        })
                        // 关注的人的friends和public内容
                        ->orWhere(function (Builder $query) use ($requestAppUserId) {
                            $query->whereHas('user.following', function ($q) use ($requestAppUserId) {
                                $q->where('app_user_id', $requestAppUserId);
                            })->whereIn('app_contents.visibility', ['friends', 'public']);
                        });
                });
            })
            ->when(!empty($search_data['_stranger']), function (Builder $query) use ($requestAppUserId, $search_data) {
                //只看陌生人的公开帖子
                $query->where('app_contents.visibility', 'public')
                    ->where(function (Builder $query) use ($requestAppUserId) {
                        //排除自己和家人
                        $familyUserIds = PetMaster::getMyFamilyUserIds($requestAppUserId, true);
                        $query->whereNotIn('app_contents.user_id', $familyUserIds)
                            // 排除关注的人
                            ->whereDoesntHave('user.following', function ($q) use ($requestAppUserId) {
                                $q->where('app_user_id', $requestAppUserId);
                            });
                    });

                // 如果指定了宠物种类筛选，只显示相同种类宠物的帖子
                if (!empty($search_data['_pet_type_filter'])) {
                    $petTypeFilter = $search_data['_pet_type_filter'];

                    // 支持单个或多个宠物种类筛选
                    if (is_array($petTypeFilter)) {
                        $query->whereHas('pets', function ($q) use ($petTypeFilter) {
                            $q->whereIn('type_id', $petTypeFilter);
                        });
                    } else {
                        $query->whereHas('pets', function ($q) use ($petTypeFilter) {
                            $q->where('type_id', $petTypeFilter);
                        });
                    }
                }
            })
            ->when(!empty($search_data['_public_feed']), function (Builder $query) use ($requestAppUserId) {
                // 公共社群：只展示普通帖子（post_type=1），排除日常帖子和自己的帖子
                $query->where('app_contents.visibility', 'public')
                    ->where('app_contents.user_id', '!=', $requestAppUserId)
                    ->where('app_contents.post_type', 1); // 只显示普通帖子
            })
            ->when(!empty($search_data['_similar_feed']), function (Builder $query) use ($requestAppUserId, $search_data) {
                // 同种社群：基于宠物种类推荐相似宠物的帖子
                $query->where('app_contents.visibility', 'public')
                    ->where('app_contents.user_id', '!=', $requestAppUserId);

                // 如果有宠物种类筛选条件
                if (!empty($search_data['_pet_type_filter'])) {
                    $petTypeIds = $search_data['_pet_type_filter'];
                    $query->whereHas('pets', function ($q) use ($petTypeIds) {
                        $q->whereIn('type_id', $petTypeIds);
                    });
                }
            })
            ->when(!empty($search_data['_family_feed']), function (Builder $query) use ($requestAppUserId) {
                // 家人动态：展示家人的帖子（包含自己）
                $familyUserIds = PetMaster::getMyFamilyUserIds($requestAppUserId, true); // 包含自己
                if ($familyUserIds) {
                    $query->where(function (Builder $query) use ($familyUserIds, $requestAppUserId) {
                        // 自己的所有内容（包括private）
                        $query->where(function (Builder $query) use ($requestAppUserId) {
                            $query->where('app_contents.user_id', $requestAppUserId);
                        })
                            // 家人的family和public内容
                            ->orWhere(function (Builder $query) use ($familyUserIds, $requestAppUserId) {
                                $familyOnlyIds = array_diff($familyUserIds, [$requestAppUserId]); // 排除自己
                                if (!empty($familyOnlyIds)) {
                                    $query->whereIn('app_contents.user_id', $familyOnlyIds)
                                        ->whereIn('app_contents.visibility', ['family', 'public']);
                                }
                            });
                    });
                } else {
                    // 如果没有家人，只显示自己的内容
                    $query->where('app_contents.user_id', $requestAppUserId);
                }
            })
            ->when(!empty($search_data['_friends_feed']), function (Builder $query) use ($requestAppUserId) {
                // 朋友动态：仅展示关注用户的帖子（排除家人）
                $followingUserIds = AppUserFollow::getMyFollowingUserIdsExcludeFamily($requestAppUserId);
                if ($followingUserIds) {
                    $query->whereIn('app_contents.user_id', $followingUserIds)
                        ->whereIn('app_contents.visibility', ['friends', 'public']);
                } else {
                    // 如果没有非家人的关注用户，返回空结果
                    $query->whereRaw('1 = 0');
                }
            });

        if (!empty($search_data['_nearby'])) {
            $distance = 10; //公里
            $haversine = "(6371 * acos(cos(radians($latitude)) * cos(radians(app_contents.latitude)) * cos(radians(app_contents.longitude) - radians($longitude)) + sin(radians($latitude)) * sin(radians(app_contents.latitude))))";
            $query = $query
                ->selectRaw("{$haversine} AS distance")
                ->whereNotNull('app_contents.latitude')
                ->whereNotNull('app_contents.longitude')
                ->having('distance', '<=', $distance)
                ->where('app_contents.visibility', 'public') // 附近内容只显示公开的
                ->orderBy('distance', 'asc');
        }

        // 修复排序字段的表前缀
        $sortNameWithPrefix = $sortName === 'created_at' ? 'app_contents.created_at' : $sortName;
        $query = $query->orderBy($sortNameWithPrefix, $sortBy);

        if ($limit == -1) {
            return $query->get()->toArray();
        }

        $list = $query->paginate($limit, ['*'], 'page', $page);

        // 处理返回数据，整理封面图和轮播图
        $list->getCollection()->transform(function ($item) use ($requestAppUserId) {
            $item = $item->toArray();
            self::renderItem($item, $requestAppUserId);
            return $item;
        });

        return $list;
    }

    public static function renderItem(&$item, $requestAppUserId)
    {
        $item['images'] = array_filter(explode(',', $item['images']));

        //app用户访问，追加收藏状态
        if ($requestAppUserId) {
            self::renderTimeAgo($item);
            self::renderRealtimeDetail($item, $requestAppUserId);
        }
    }

    // 添加时间差处理
    public static function renderTimeAgo(&$item)
    {
        if (!$item['created_at']) {
            $item['time_ago'] = '';
            return;
        }
        $createTime = Carbon::parse($item['created_at']);
        $now = now();
        $diffInMinutes = $createTime->diffInMinutes($now);
        $diffInHours = $createTime->diffInHours($now);
        $diffInDays = $createTime->diffInDays($now);
        $diffInWeeks = $createTime->diffInWeeks($now);
        $diffInMonths = $createTime->diffInMonths($now);
        $diffInYears = $createTime->diffInYears($now);
        if ($diffInMinutes < 1) {
            $item['time_ago'] = '剛剛發佈';
        } elseif ($diffInMinutes < 60) {
            $item['time_ago'] = $diffInMinutes . '分鐘前發佈';
        } elseif ($diffInHours < 24) {
            $item['time_ago'] = $diffInHours . '小時前發佈';
        } elseif ($diffInDays < 7) {
            $item['time_ago'] = $diffInDays . '天前發佈';
        } elseif ($diffInWeeks < 4) {
            $item['time_ago'] = $diffInWeeks . '週前發佈';
        } elseif ($diffInMonths < 12) {
            $item['time_ago'] = $diffInMonths . '月前發佈';
        } elseif ($diffInYears >= 1) {
            $item['time_ago'] = $diffInYears . '年前發佈';
        }
    }

    public static function saveDetail($data, $userId)
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;

            $contentData = Arr::only($data, [
                'title',
                'content',
                'status',
                'images',
                'visibility',
                'post_type',
            ]);

            // 处理title和content字段：根据状态和可见性进行验证
            if (isset($contentData['status'])) {
                if ($contentData['status'] == 1) {
                    // 发布状态时的验证
                    $visibility = $contentData['visibility'] ?? 'public';

                    $postType = $contentData['post_type'] ?? 1; // 默认为正常帖子

                    if ($visibility === 'private') {
                        // 私人记录：允许title和content为空，但会在后续业务验证中检查
                        if (!isset($contentData['title'])) {
                            $contentData['title'] = null;
                        }
                        if (!isset($contentData['content'])) {
                            $contentData['content'] = null;
                        }
                    } elseif ($postType == 1) {
                        // 正常帖子：title和content都不能为空
                        if (empty($contentData['title'])) {
                            throw new Exception('发布内容时，标题不能为空');
                        }
                        if (empty($contentData['content'])) {
                            throw new Exception('发布内容时，内容不能为空');
                        }
                    } else {
                        // 日常贴文（post_type=2）：title和content可以为空
                        if (!isset($contentData['title'])) {
                            $contentData['title'] = null;
                        }
                        if (!isset($contentData['content'])) {
                            $contentData['content'] = null;
                        }
                    }
                } else {
                    // 草稿状态时，允许title和content为空，但确保字段存在
                    if (!isset($contentData['title'])) {
                        $contentData['title'] = null;
                    }
                    if (!isset($contentData['content'])) {
                        $contentData['content'] = null;
                    }
                }
            }

            // 处理图片数据
            $images = [];
            if (!empty($data['images']) && is_array($data['images'])) {
                $images = $data['images'];
                $contentData['images'] = implode(',', $images);
            } else {
                $contentData['images'] = '';  // 如果没有图片就设为空字符串
            }

            // 执行特殊场景的业务逻辑验证
            self::validateBusinessRules($contentData, $images);

            // 处理宠物ID数据（从请求数据中获取，不是数据库字段）
            $petIds = [];
            if (isset($data['pet_ids'])) {
                if (is_array($data['pet_ids'])) {
                    $petIds = array_filter($data['pet_ids']);
                } else {
                    $petIds = array_filter(explode(',', $data['pet_ids']));
                }
            }

            foreach ($petIds as $petId) {
                if (!PetMaster::isMaster($petId, $userId)) {
                    $petName = Pet::where('id', $petId)->value('name');
                    throw new Exception($petName ? "{$petName}不是您的宠物" : "宠物ID非法：{$petId}");
                }
            }

            // 处理商品ID数据（从请求数据中获取，不是数据库字段）
            $indentCommodityIds = [];
            if (isset($data['good_indent_commodity_ids'])) {
                if (is_array($data['good_indent_commodity_ids'])) {
                    $indentCommodityIds = array_filter($data['good_indent_commodity_ids']);
                } else {
                    $indentCommodityIds = array_filter(explode(',', $data['good_indent_commodity_ids']));
                }
            }

            // 处理标签ID数据（从请求数据中获取，不是数据库字段）
            $tagIds = [];
            if (isset($data['tag_ids'])) {
                if (is_array($data['tag_ids'])) {
                    $tagIds = array_filter($data['tag_ids']);
                } else {
                    $tagIds = array_filter(explode(',', $data['tag_ids']));
                }
            }
            if ($indentCommodityIds) {
                //关联订单表的user_id校验
                $count = GoodIndentCommodity::withTrashed()
                    ->from('goods_indents_commoditys as gic')
                    ->join('goods_indents as gi', 'gic.good_indent_id', '=', 'gi.id')
                    ->whereIn('gic.id', $indentCommodityIds)
                    ->where('gi.user_id', $userId)
                    ->count();
                if ($count != count($indentCommodityIds)) {
                    throw new Exception('关联订单商品ID非法');
                }
            }

            if (empty($id)) {
                $contentData['user_id'] = $userId;
                $info = self::create($contentData);
                $id = $info->id;
                $success = true;
            } else {
                // 编辑
                $info = self::where('id', $id)->first();
                $info or throw_if(true, 'RuntimeException', "内容：{$id}不存在或已删除");
                if ($info['user_id'] != $userId) {
                    throw new Exception('您没有权限编辑该内容');
                }
                $success = $info->update($contentData);
                //删除旧的关联
                AppContentPet::where('app_content_id', $id)->delete();
                AppContentIndentCommodity::where('app_content_id', $id)->delete();
                AppContentTag::removeContentTags($id);
            }

            //添加提醒
            $add_reminder = $data['status'] == 1 && ($data['visibility'] == 'public' || $data['visibility'] == 'family');
            if ($add_reminder && $petIds) {
                $familyUserIds = PetMaster::whereIn('pet_id', $petIds)->where('user_id', '!=', $userId)->pluck('user_id')->toArray();
                $visitedUserIds = AppUserLog::getTodayVisitedUsers($familyUserIds);
                foreach ($visitedUserIds as $familyUserId) {
                    PetReminder::create([
                        'user_id'      => $familyUserId,
                        'pet_id'       => $petIds[0],
                        'type'         => PetReminder::TYPE_FAMILY_POST_CONTENT_REMINDER,
                        'title'        => PetReminder::MAPPING[PetReminder::TYPE_FAMILY_POST_CONTENT_REMINDER]['label'],
                        'content'      => "您的家人發佈了動態，请查看",
                        'trigger_date' => Carbon::today()->format('Y-m-d'),
                        'extra'        => json_encode(['id' => $id]),
                    ]);
                }
            }

            if ($petIds) {
                AppContentPet::insert(
                    array_map(function ($petId) use ($id) {
                        return [
                            'app_content_id' => $id,
                            'pet_id'         => $petId,
                        ];
                    }, $petIds)
                );
            }

            if ($indentCommodityIds) {
                AppContentIndentCommodity::insert(
                    array_map(function ($indentCommodityId) use ($id) {
                        return [
                            'app_content_id'           => $id,
                            'good_indent_commodity_id' => $indentCommodityId,
                        ];
                    }, $indentCommodityIds)
                );
            }

            // 处理标签关联
            if ($tagIds) {
                AppContentTag::addContentTags($id, $tagIds);
            }

            if ($success) {
                $success = true;
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new Exception('更新失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $record = array();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "内容：{$id}不存在或已删除");
            $success = $info->where('id', $id)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    /**
     * 获取内容详情
     */
    public static function getDetailForApp($id, $requestAppUserId)
    {
        $content = self::with([
            'user'        => function ($query) {
                $query->select('id', 'username', 'phone', 'avatar');
            },
            'pets'        => function (Builder $query) {
                $query->select('pets.id', 'pets.name', 'pets.sex', 'pets.avatar');
            },
            'Commodities' => function (Builder $q) {
                $q->select('goods_indents_commoditys.id', 'goods_indents_commoditys.good_id', 'goods_indents_commoditys.image', 'goods_indents_commoditys.name', 'goods_indents_commoditys.name_tc', 'goods_indents_commoditys.name_en');
            },
            'tags'        => function (Builder $query) {
                $query->select('app_share_tags.id', 'app_share_tags.name', 'app_share_tags.color');
            },
        ])->find($id);

        if (!$content) {
            return [false, [], '内容不存在或已下架'];
        }

        // 基于visibility字段的权限检查
        if ($content['user_id'] != $requestAppUserId) {
            $visibility = $content['visibility'];

            // private类型只有作者自己能看
            if ($visibility === 'private') {
                return [false, [], '没有查看该内容的权限'];
            }

            // 其他类型需要检查用户关系
            if ($visibility !== 'public') {
                $isFamily = $requestAppUserId > 0 && PetMaster::isFamily($requestAppUserId, $content['user_id']);
                $isFans = $requestAppUserId > 0 && AppUserFollow::isFollowing($requestAppUserId, $content['user_id']);

                if ($visibility === 'family' && !$isFamily) {
                    return [false, [], '没有查看该内容的权限'];
                }

                if ($visibility === 'friends' && !$isFans) {
                    return [false, [], '没有查看该内容的权限'];
                }
            }
        }

        $content = $content->toArray();

        AppContentView::addPvuv($requestAppUserId, $id);
        self::renderItem($content, $requestAppUserId);

        return [true, $content, '获取成功'];
    }

    //todo app内容列表应该需要缓存，缓存的view_count肯定是需要实时读取redis的
    public static function renderRealtimeDetail(&$content, $appUserId)
    {
        if (!$content['id']) {
            return;
        }
        $content['view_count'] = AppContentView::getViewCount($content['id']);
        $content['like_count'] = AppContentLike::getContentLikeCount($content['id']);
        $content['is_like'] = (bool)AppContentLike::isLike($appUserId, $content['id']);
        $content['comment_count'] = AppContentComment::getCommentCount($content['id']);
        $content['favorite_count'] = AppContentFavorite::getFavoriteCount($content['id']);
        $content['is_favorite'] = AppContentFavorite::isFavorite($appUserId, $content['id']);
        if (!empty($content['pets'])) {
            foreach ($content['pets'] as &$pet) {
                unset($pet['pivot']);
            }
        }
        if (!empty($content['commodities'])) {
            foreach ($content['commodities'] as &$pet) {
                unset($pet['pivot']);
            }
        }
        if (!empty($content['tags'])) {
            foreach ($content['tags'] as &$tag) {
                unset($tag['pivot']);
            }
        }
    }

    public static function delForApp($id, $user_id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "分享：{$id}不存在或已删除");
            $info['user_id'] == $user_id or throw_if(true, 'RuntimeException', "您没有权限删除该分享");
            $success = $info->where('id', $id)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if ($success) {
                $success = true;
                $message = '删除成功';
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    //增加访问数量
    public static function incrementView($contentId)
    {
        return self::where('id', $contentId)->increment('view_count', 1);
    }

    //增加点赞内容数量
    public static function incrementLike($contentId)
    {
        return self::where('id', $contentId)->increment('like_count', 1);
    }

    //减少点赞内容数量
    public static function decrementLike($contentId)
    {
        return self::withTrashed()->where('id', $contentId)->decrement('like_count', 1);
    }

    //增加收藏活动数量
    public static function incrementFavorite($contentId)
    {
        return self::where('id', $contentId)->increment('favorite_count', 1);
    }

    //减少收藏活动数量
    public static function decrementFavorite($contentId)
    {
        return self::withTrashed()->where('id', $contentId)->decrement('favorite_count', 1);
    }

    //增加评价内容数量
    public static function incrementComment($contentId)
    {
        return self::where('id', $contentId)->increment('comment_count', 1);
    }

    //减少评价内容数量
    public static function decrementComment($contentId, $commentCount)
    {
        return self::withTrashed()->where('id', $contentId)->decrement('comment_count', $commentCount);
    }

    /**
     * 验证特殊场景的业务规则
     *
     * @param array $contentData 内容数据
     * @param array $images      图片数组
     * @throws Exception
     */
    protected static function validateBusinessRules($contentData, $images)
    {
        $status = $contentData['status'] ?? null;
        $visibility = $contentData['visibility'] ?? null;
        $title = $contentData['title'] ?? null;
        $content = $contentData['content'] ?? null;

        // 场景1：私人记录场景（visibility=private）
        if ($visibility === 'private') {
            self::validatePrivateRecordBusiness($title, $content, $images);
        }

        // 场景2：草稿状态场景（status=0）
        if ($status == 0) {
            self::validateDraftContentBusiness($title, $content, $images);
        }
    }

    /**
     * 验证私人记录的业务规则
     * 当 visibility=private 时，允许用户只上传图片而不填写标题和内容
     * 但如果标题、内容、图片都为空，则抛出异常
     *
     * @param string|null $title
     * @param string|null $content
     * @param array       $images
     * @throws Exception
     */
    protected static function validatePrivateRecordBusiness($title, $content, $images)
    {
        // 检查是否所有内容都为空
        $titleEmpty = empty($title);
        $contentEmpty = empty($content);
        $imagesEmpty = empty($images) || count($images) == 0;

        // 如果标题、内容、图片都为空，则要求至少上传一张图片
        if ($titleEmpty && $contentEmpty && $imagesEmpty) {
            throw new Exception('私人记录至少需要上传一张图片');
        }
    }

    /**
     * 验证草稿内容的业务规则
     * 当 status=0 时，title、content、images 三个字段中至少需要填写/上传其中一个
     *
     * @param string|null $title
     * @param string|null $content
     * @param array       $images
     * @throws Exception
     */
    protected static function validateDraftContentBusiness($title, $content, $images)
    {
        // 检查是否所有内容都为空
        $titleEmpty = empty($title);
        $contentEmpty = empty($content);
        $imagesEmpty = empty($images) || count($images) == 0;

        // 如果三个字段都为空，返回错误
        if ($titleEmpty && $contentEmpty && $imagesEmpty) {
            throw new Exception('草稿至少需要填写标题、内容或上传图片中的一项');
        }
    }

    /**
     * 将帖子图片自动保存到宠物相册
     *
     * @param array $data      帖子数据
     * @param int   $contentId 帖子ID
     * @param int   $userId    用户ID
     * @return bool 是否保存成功
     */
    public static function saveImagesToAlbum($data, $contentId, $userId)
    {
        try {
            // 检查是否已存在该帖子的相册记录
            $existingAlbum = PetAlbum::where('source_type', 'post')
                ->where('source_id', $contentId)
                ->where('user_id', $userId)
                ->first();

            // 准备相册数据
            $albumData = [
                'user_id'     => $userId,
                'images'      => $data['images'],
                'description' => !empty($data['title']) ? $data['title'] : '来自帖子的图片',
                'source_type' => 'post',
                'source_id'   => $contentId,
                'status'      => 1,
            ];

            // 如果帖子关联了宠物，相册也关联相同的宠物
            if (!empty($data['pet_ids'])) {
                // pet_ids 格式转换：从逗号分隔的字符串转为字符串格式
                $albumData['pet_ids'] = $data['pet_ids'];
            }

            if ($existingAlbum) {
                // 编辑模式：更新现有相册
                $albumData['id'] = $existingAlbum->id;
                [$success, $album, $message] = PetAlbum::saveMultiAlbum($albumData);
                $operationType = '更新';
            } else {
                // 新建模式：创建新相册
                [$success, $album, $message] = PetAlbum::saveMultiAlbum($albumData);
                $operationType = '创建';
            }

            if ($success) {
                Log::info("帖子图片自动{$operationType}相册成功", [
                    'content_id'     => $contentId,
                    'album_id'       => $album->id,
                    'image_count'    => count($data['images']),
                    'pet_ids'        => $albumData['pet_ids'] ?? null,
                    'operation_type' => $operationType,
                    'existing_album' => $existingAlbum ? $existingAlbum->id : null,
                ]);
                return true;
            } else {
                Log::error("帖子图片自动{$operationType}相册失败", [
                    'content_id'     => $contentId,
                    'error_message'  => $message,
                    'album_data'     => $albumData,
                    'operation_type' => $operationType,
                ]);
                return false;
            }
        } catch (Exception $e) {
            Log::error('帖子图片自动保存到相册异常', [
                'content_id'    => $contentId,
                'error_message' => $e->getMessage(),
                'error_file'    => $e->getFile(),
                'error_line'    => $e->getLine(),
            ]);
            return false;
        }
    }

    //判断是否发布过内容
    public static function getPostedPetIds($userId, $petIds, $days = 0)
    {
        return self::where('user_id', $userId)
            ->when($petIds !== "", function (Builder $query) use ($petIds) {
                //筛选宠物相关帖子
                $query->join('app_contents_pets', 'app_contents.id', '=', 'app_contents_pets.app_content_id')
                    ->whereIn('app_contents_pets.pet_id', $petIds)
                    ->distinct();
            })
            ->when($days, function (Builder $query) use ($days) {
                $query->where('app_contents.created_at', '>', Carbon::today()->subDays($days));
            })
            ->pluck('pet_id')
            ->toArray();
    }
}
