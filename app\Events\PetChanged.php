<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

//宠物变更事件
class PetChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public int $userId;
    public array|null $newData;
    public array|null $oldData;

    public function __construct($userId, $newData, $oldData = null)
    {
        $this->userId = $userId;
        $this->newData = $newData;
        $this->oldData = $oldData;
    }
}
