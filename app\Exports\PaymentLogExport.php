<?php

namespace App\Exports;

use App\Models\PaymentLog;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class PaymentLogExport extends DefaultValueBinder implements FromCollection, WithHeadings, WithCustomValueBinder, WithColumnWidths
{
    private $formData;
    private static $widthMap = [
        '用户ID' => 10,
        '用户昵称' => 15,
        '资金内容' => 40,
        '订单标识' => 20,
        '交易单号' => 30,
        '交易金额' => 15,
        '交易时间' => 20,
    ];

    public function __construct(array $formData)
    {
        $this->formData = $formData;
    }

    public function collection()
    {
        $records = PaymentLog::getList($this->formData); //后台导出
        $cellData = array_map(function ($v) {
            return [
                $v['user_id'],
                $v['user']['username'],
                $v['name'],
                $v['trade_no'],
                $v['transaction_id'],
                stripos($v['type'], 'Refund') !== false ? -$v['money'] : +$v['money'],
                $v['time'],
            ];
        }, $records);
        return Collection::make($cellData);
    }

    public function bindValue(Cell $cell, $value)
    {
        $colIndex = Coordinate::columnIndexFromString($cell->getColumn());

        if ($colIndex === 4) {
            $cell->setValueExplicit((string)$value);
            return true; // 表示已处理该单元格
        }

        return parent::bindValue($cell, $value);
    }

    public function headings(): array
    {
        return array_keys(self::$widthMap);
    }

    public function columnWidths(): array
    {
        $result = [];
        $columns = array_keys(self::$widthMap);
        foreach ($columns as $index => $header) {
            $colLetter = Coordinate::stringFromColumnIndex($index + 1); // A=1, B=2...
            $result[$colLetter] = self::$widthMap[$header];
        }

        return $result;
    }
}
