<?php

namespace App\Listeners;

use App\Events\PostLikedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendPostLikedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\PostLikedEvent  $event
     * @return void
     */
    public function handle(PostLikedEvent $event)
    {
        try {
            // 获取点赞者信息
            $liker = AppUser::find($event->likerId);

            if (!$liker) {
                Log::error('Post liked notification failed: Liker not found', [
                    'liker_id' => $event->likerId,
                    'post_id' => $event->postId
                ]);
                return;
            }

            // 创建通知
            NotificationService::createNotification(
                $event->postOwnerId, // 接收通知的用户ID（帖子所有者）
                NotificationSetting::TYPE_POST_LIKED, // 通知类型
                '帖子点赞通知',
                "用户 {$liker->username} 点赞了您的帖子",
                $event->likerId, // 发送者ID（点赞者）
                $event->postId, // 相关ID（帖子ID）
                NotificationSetting::RELATION_TYPE_CONTENT // 相关类型
            );

            Log::info('Post liked notification sent', [
                'post_id' => $event->postId,
                'owner_id' => $event->postOwnerId,
                'liker_id' => $event->likerId
            ]);
        } catch (\Exception $e) {
            Log::error('Post liked notification failed', [
                'error' => $e->getMessage(),
                'post_id' => $event->postId,
                'owner_id' => $event->postOwnerId,
                'liker_id' => $event->likerId
            ]);
        }
    }
}
