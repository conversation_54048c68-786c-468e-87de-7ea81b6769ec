<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class NotificationLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'sender_id',
        'type',
        'title',
        'content',
        'related_id',
        'related_type',
        'read_at',
        'status'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    // 关联发送者
    public function sender()
    {
        return $this->belongsTo(AppUser::class, 'sender_id');
    }

    /**
     * 获取通知列表
     */
    public static function getList($userId, $type = null, $page = 1, $perPage = 20)
    {
        try {
            $query = self::with([
                'sender' => function ($query) {
                    $query->select('id', 'username', 'avatar');
                }
            ])
                ->where('user_id', $userId)
                ->where('status', 1);

            if ($type) {
                $query->where('type', $type);
            }

            $list = $query->orderBy('id', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return [true, $list, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get system notification list failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 获取未读消息数量 - 按类型分组
     */
    public static function getUnreadCount($userId)
    {
        try {
            $counts = self::where('user_id', $userId)
                ->where('status', 1)
                ->whereNull('read_at')
                ->selectRaw('type, count(*) as count')
                ->groupBy('type')
                ->get();

            $result = [
                'total'      => 0,
                'types'      => [],
                'categories' => []
            ];

            $constants = NotificationSetting::getConstantsByPrefix('CATEGORY_');
            foreach ($constants as $constantVal) {
                $result['categories'][$constantVal['key']] = [
                    'name'  => $constantVal['label'],
                    'count' => 0,
                    'types' => [],
                ];
            }

            // 处理每种类型的计数
            $typeMap = NotificationSetting::MAPPING;
            foreach ($counts as $item) {
                $type = $item->type;
                $count = $item->count;

                $result['total'] += $count;
                $result['types'][$type] = [
                    'type'  => $type,
                    'name'  => $typeMap[$type]['label'],
                    'count' => $count
                ];

                // 按分类归类
                $key = $typeMap[$type]['key'];
                $result['categories'][$key]['count'] += $count;
                $result['categories'][$key]['types'][$type] = $count;
            }

            $result['types'] = array_values($result['types']);
            return [true, $result, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get system notification unread count failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 标记消息已读
     */
    public static function markRead($userId, $notificationId = null, $type = null)
    {
        try {
            $query = self::where('user_id', $userId)
                ->where('status', 1)
                ->whereNull('read_at');

            if ($notificationId) {
                $query->where('id', $notificationId);
            }

            if ($type) {
                $query->where('type', $type);
            }

            $query->update(['read_at' => now()]);

            return [true, [], '标记成功'];
        } catch (\Exception $e) {
            logErr('Mark system notification read failed:', [
                'user_id'         => $userId,
                'notification_id' => $notificationId,
                'error'           => $e->getMessage()
            ]);
            return [false, [], '标记失败'];
        }
    }

    /**
     * 删除通知
     */
    public static function deleteOne($userId, $notificationId)
    {
        try {
            $notification = self::where('user_id', $userId)
                ->where('id', $notificationId)
                ->first();

            if (!$notification) {
                return [false, [], '通知不存在'];
            }

            $notification->update(['status' => 0]);

            return [true, [], '删除成功'];
        } catch (\Exception $e) {
            logErr('Delete system notification failed:', [
                'user_id'         => $userId,
                'notification_id' => $notificationId,
                'error'           => $e->getMessage()
            ]);
            return [false, [], '删除失败'];
        }
    }

    /**
     * 获取通知详情
     *
     * @param int $userId         用户ID
     * @param int $notificationId 通知ID
     * @return array
     */
    public static function getDetail($userId, $notificationId)
    {
        try {
            $notification = self::where('user_id', $userId)
                ->where('id', $notificationId)
                ->where('status', 1)
                ->first();

            if (!$notification) {
                return [false, [], '通知不存在或已删除'];
            }

            // 标记为已读
            if (!$notification->read_at) {
                $notification->update(['read_at' => now()]);
            }

            return [true, $notification, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get system notification detail failed:', [
                'user_id'         => $userId,
                'notification_id' => $notificationId,
                'error'           => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }
}
