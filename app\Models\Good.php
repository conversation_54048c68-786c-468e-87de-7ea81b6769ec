<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Good extends Model
{
    use SoftDeletes;

    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();

        // 当商品信息更新时，使缓存失效
        static::updated(function ($good) {
            // 清除推荐缓存（当商品信息变更时）
            \App\Models\GoodRecommendation::clearCache();
        });

        // 当商品被删除时，使缓存失效
        static::deleted(function ($good) {
            // 清除推荐缓存（当商品被删除时）
            \App\Models\GoodRecommendation::clearCache();
        });
    }

    protected $fillable = [
        'identification',
        'name',
        'name_tc',
        'name_en',
        'image',
        'images',
        'category_id',
        'product_code',
        'freight_id',
        'brand_id',
        'inventory',
        'market_price',
        'cost_price',
        'price',
        'sales_actual',
        'sales_initial',
        'order_price',
        'keywords',
        'short_description',
        'details',
        'weight',
        'is_show',
        'is_recommend',
        'is_new',
        'is_hot',
        'is_inventory',
        'is_refund',
        'refund_day',
        'sort_order',
        'sale_time',
        'sale_timing',
    ];

    const GOOD_SHOW_ENTREPOT = 0; //状态：仓库
    const GOOD_SHOW_PUTAWAY = 1; //状态：上架
    const GOOD_SHOW_TIMING = 2; //状态：定时
    const GOOD_RECOMMEND_NO = 0; //推荐：否
    const GOOD_RECOMMEND_YES = 1; //推荐：是
    const GOOD_NEW_NO = 0; //新品：否
    const GOOD_NEW_YES = 1; //推荐：是
    const GOOD_HOT_NO = 0; //热销：否
    const GOOD_HOT_YES = 1; //热销：是
    const GOOD_IS_INVENTORY_NO = 0; //减库存方式：拍下减库存
    const GOOD_IS_INVENTORY_FILM = 1; //减库存方式：付款减库存

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * 获取商品规格
     */
    public function goodSpecificationOld()
    {
        return $this->hasMany(GoodSpecification::class);
    }

    /**
     * 获取商品访问记录
     */
    public function browse()
    {
        return $this->hasMany(Browse::class);
    }

    /**
     * 获取商品收藏
     */
    public function collect()
    {
        return $this->hasMany(GoodCollect::class);
    }

    /**
     * 获取商品销量
     */
    public function goodIndentCommodity()
    {
        return $this->hasMany(GoodIndentCommodity::class);
    }

    /**
     * 获取商品SKU
     */
    public function goodSku()
    {
        return $this->hasMany(GoodSku::class);
    }

    /**
     * 获取品牌
     */
    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * 分类
     */
    public function category()
    {
        return $this->belongsTo(GoodCategory::class);
    }

    /**
     * 运费模板
     */
    public function freight()
    {
        return $this->belongsTo(Freight::class);
    }

    /**
     * 商品展示价格
     *
     * @param $row
     * @return array
     */
    public static function getPriceShow($row)
    {
        $return = [];

        if (is_countable($row->goodSku) && count($row->goodSku) > 0) {
            $return[] = $row->goodSku->min('price');
            $return[] = $row->goodSku->max('price');
            if ($return[0] == $return[1]) {
                $return = [$return[0]];
            }
        } else {
            $return[] = $row->price;
        }
        return $return;
    }

    /**
     * 商品展示市场价
     *
     * @param $row
     * @return array
     */
    public static function getMarketPriceShow($row)
    {
        $return = [];

        if (is_countable($row->goodSku) && count($row->goodSku) > 0) {
            $return[] = $row->goodSku->min('market_price');
            $return[] = $row->goodSku->max('market_price');
            if ($return[0] == $return[1]) {
                $return = [$return[0]];
            }
        } else {
            $return[] = $row->market_price;
        }
        return $return;
    }

    /**
     * 商品展示库存
     *
     * @param $row
     * @return int
     */
    public static function getInventoryShow($row)
    {
        if (is_countable($row->goodSku) && count($row->goodSku) > 0) {
            $return = $row->goodSku->sum('inventory');
        } else {
            $return = $row->inventory;
        }
        return $return;
    }

    public static function getList($search_data = array(), $requestAppUserId = null)
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $isShow = $search_data['is_show'] ?? "";
        $isRecommend = $search_data['is_recommend'] ?? "";
        $isNew = $search_data['is_new'] ?? "";
        $isHot = $search_data['is_hot'] ?? "";
        $categoryId = $search_data['category_id'] ?? "";
        $freightId = $search_data['freight_id'] ?? "";
        $brandId = $search_data['brand_id'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";

        $list = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($isShow, function (Builder $query) use ($isShow) {
                $query->where('is_show', $isShow);
            })
            ->when($isRecommend, function (Builder $query) use ($isRecommend) {
                $query->where('is_recommend', $isRecommend);
            })
            ->when($isNew, function (Builder $query) use ($isNew) {
                $query->where('is_new', $isNew);
            })
            ->when($isHot, function (Builder $query) use ($isHot) {
                $query->where('is_hot', $isHot);
            })
            ->when($categoryId && !$requestAppUserId, function (Builder $query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->when($freightId, function (Builder $query) use ($freightId) {
                $query->where('freight_id', $freightId);
            })
            ->when($brandId, function (Builder $query) use ($brandId) {
                $query->where('brand_id', $brandId);
            })
            ->when($categoryId && $requestAppUserId, function (Builder $query) use ($categoryId) {
                $Category = GoodCategory::where('state', GoodCategory::CATEGORY_STATE_YES)->select('id', 'pid')->get();
                $allSublevel = allSublevel($Category->toArray(), [$categoryId]);
                if (count($allSublevel) > 0) {
                    $query->whereIn('category_id', $allSublevel);
                }
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->when(!$sortName && $requestAppUserId, function (Builder $query) {
                //默认排序
                $query->orderBy('is_recommend', 'DESC')
                    ->orderBy('is_hot', 'DESC')
                    ->orderBy('is_new', 'DESC')
                    ->orderBy('sort_order', 'ASC')
                    ->orderBy('sale_time', 'DESC');
            })
            ->with([
                'goodSku'            => function (Builder $q) {
                    $q->select('id', 'good_id', 'image', 'price', 'inventory', 'product_sku', 'pet_filter_tags', 'matching_pets_count');
                },
                'brand'              => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en', 'logo');
                },
                'category'           => function (Builder $q) {
                    $q->select('id', 'pid', 'name', 'name_tc', 'name_en', 'logo');
                },
                'freight'            => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en');
                },
            ])
            ->select([
                'id',
                'identification',
                'name',
                'name_tc',
                'name_en',
                'image',
                'category_id',
                'product_code',
                'freight_id',
                'brand_id',
                'inventory',
                'market_price',
                'cost_price',
                'price',
                'sales_actual',
                'sales_initial',
                'order_price',
                'is_show',
                'is_recommend',
                'is_new',
                'is_hot',
                'is_inventory',
                'is_refund',
                'refund_day',
                'sort_order',
                'sale_time',
                'sale_timing',
                'created_at',
                'updated_at'
            ])
            ->selectRaw('IFNULL(sales_actual,0) + IFNULL(sales_initial,0) as sales_nums')
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);

        // 批量处理用户宠物匹配（避免N+1查询）
        $skuMatchingData = [];
        if ($requestAppUserId > 0) {
            $allSkus = [];
            foreach ($list->items() as $good) {
                foreach ($good->goodSku as $sku) {
                    // 处理所有SKU，包括空标签的SKU（空标签表示适合所有宠物）
                    $allSkus[] = [
                        'id'              => $sku->id,
                        'pet_filter_tags' => $sku->pet_filter_tags ?? []
                    ];
                }
            }

            if (!empty($allSkus)) {
                $petMatchingService = new \App\Services\PetMatchingService();
                $skuMatchingData = $petMatchingService->batchMatchUserPetsForSkus($allSkus, $requestAppUserId);
            }
        }

        $list->getCollection()->transform(function ($p) use ($requestAppUserId, $skuMatchingData) {
            $item = $p->toArray();
            $item['price_show'] = self::getPriceShow($p);
            $item['inventory_show'] = self::getInventoryShow($p);

            // 为每个SKU添加匹配的用户宠物信息
            if ($requestAppUserId > 0 && isset($item['good_sku'])) {
                foreach ($item['good_sku'] as &$sku) {
                    $sku['matched_pets'] = $skuMatchingData[$sku['id']] ?? [];
                }
                unset($sku); // 解除引用

                self::renderRealtimeDetail($item, $requestAppUserId);
            }
            return $item;
        });

        return $list;
    }

    public static function getDetail(int $id, $requestAppUserId = null)
    {
        $info = self::where('id', $id)->with([
            'goodSpecificationOld', //仅用于前端判断使用
            'goodSku'            => function (Builder $q) {
                $q->select('id', 'good_id', 'image', 'market_price', 'cost_price', 'price', 'inventory', 'weight', 'product_sku', 'pet_filter_tags', 'matching_pets_count', 'created_at', 'updated_at');
            },
            'brand'              => function (Builder $q) {
                $q->select('id', 'name', 'name_tc', 'name_en', 'logo');
            },
            'category'           => function (Builder $q) {
                $q->select('id', 'pid', 'name', 'name_tc', 'name_en', 'logo');
            },
            'freight'            => function (Builder $q) {
                $q->select('id', 'name', 'name_tc', 'name_en');
            },
        ])->first();
        if ($info) {
            $info['price_show'] = self::getPriceShow($info);
            $info['market_price_show'] = self::getMarketPriceShow($info);
            $info['inventory_show'] = self::getInventoryShow($info);
            $info['sales_nums'] = $info['sales_num'] + $info['sales_initial'];
            $info['images'] = explode(',', $info['images']);
            if ($requestAppUserId > 0) {
                if ($info['is_show'] != Good::GOOD_SHOW_PUTAWAY) {
                    $info = null;
                } else {
                    $info->setHidden(['goodSpecificationOld', 'created_at', 'updated_at', 'deleted_at', 'sales_actual', 'sales_initial', 'cost_price']);
                    if ($info->goodSku) {
                        // 为商品详情的SKU添加用户宠物匹配信息
                        $skus = $info->goodSku->map(function ($sku) {
                            return [
                                'id'              => $sku->id,
                                'pet_filter_tags' => $sku->pet_filter_tags ?? []
                            ];
                        })->toArray();

                        $petMatchingService = new \App\Services\PetMatchingService();
                        $skuMatchingData = $petMatchingService->batchMatchUserPetsForSkus($skus, $requestAppUserId);

                        $info->goodSku->transform(function ($p) use ($skuMatchingData) {
                            $p->setHidden(['cost_price', 'created_at', 'updated_at', 'deleted_at']);
                            // 添加匹配的用户宠物信息
                            $p->matched_pets = $skuMatchingData[$p->id] ?? [];
                            return $p;
                        });
                    }
                    self::renderRealtimeDetail($info, $requestAppUserId);
                }
            }
        }
        return $info;
    }

    //todo 商品列表应该需要缓存，缓存的view_count肯定是需要实时读取redis的
    public static function renderRealtimeDetail(&$good, $appUserId)
    {
        unset($good['sales_actual'], $good['sales_initial']);
        $good['is_collect'] = (bool)GoodCollect::isCollected($appUserId, $good['id']);
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('product_code', $data['product_code'])->where('id', '!=', $id)->exists()) {
                throw new Exception('商品货号已存在');
            }
            if (!empty($data['images']) && is_array($data['images'])) {
                $data['images'] = implode(',', $data['images']);
            } else {
                $data['images'] = '';  // 如果没有图片就设为空字符串
            }
            $data['brand_id'] = $data['brand_id'] ?? 0;
            $data['sale_time'] = $data['is_show'] == 1 ? now()->toDateTimeString() : null;
            $good_specification = $data['good_specification'] ?? [];
            unset($data['good_specification']);
            $good_sku = $data['good_sku'] ?? [];
            unset($data['good_sku']);

            // 新的推荐字段已在fillable中，无需特殊处理
            if (empty($id)) {
                $data['identification'] = orderNumber();
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
                // 商品规格处理
                if (is_array($good_specification) && count($good_specification) > 0) {
                    foreach ($good_specification as $specification) {
                        $specification['good_id'] = $id;
                        GoodSpecification::create($data);
                    }
                }
                // sku处理
                if (is_array($good_sku) && count($good_sku) > 0) {
                    $order_price = 0;
                    foreach ($good_sku as $sku) {
                        //获取最低售价
                        if ($order_price == 0 || $order_price > $sku['price']) {
                            $order_price = $sku['price'];
                        }
                        $sku['good_id'] = $id;
                        GoodSku::create($sku);
                    }
                    $result->order_price = $order_price;
                    $result->save();
                    $newData['order_price'] = $order_price;
                }
            } else {
                $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "商品：{$id}不存在或已删除");
                $oldData = $info->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
                // 商品规格处理
                if (is_array($good_specification) && count($good_specification) > 0) {
                    foreach ($good_specification as $specification) {
                        if (array_key_exists('id', $specification)) {
                            $GoodSpecification = GoodSpecification::find($specification['id']);
                            if (!$GoodSpecification) {
                                // 如果找不到指定的规格，创建新的
                                $GoodSpecification = new GoodSpecification();
                                $GoodSpecification->good_id = $id;
                                $GoodSpecification->specification_id = $specification['specification_id'];
                            }
                        } else {
                            $GoodSpecification = new GoodSpecification();
                            $GoodSpecification->good_id = $id;
                            $GoodSpecification->specification_id = $specification['specification_id'];
                        }
                        $GoodSpecification->data = $specification['data'];
                        $GoodSpecification->save();
                    }
                }
                // sku处理
                if (is_array($good_sku) && count($good_sku) > 0) {
                    $GoodSkuAll = [];
                    $order_price = 0;
                    foreach ($good_sku as $sku) {
                        //获取最低售价
                        if ($order_price == 0 || $order_price > $sku['price']) {
                            $order_price = $sku['price'];
                        }
                        if (array_key_exists('id', $sku)) {
                            $GoodSku = GoodSku::find($sku['id']);
                            if (!$GoodSku) {
                                // 如果找不到指定的SKU，创建新的
                                $GoodSku = new GoodSku();
                                $GoodSku->good_id = $id;
                            }
                        } else {
                            $GoodSku = new GoodSku();
                            $GoodSku->good_id = $id;
                        }
                        $GoodSku->market_price = $sku['market_price'];
                        $GoodSku->cost_price = $sku['cost_price'];
                        $GoodSku->price = $sku['price'];
                        $GoodSku->inventory = $sku['inventory'];
                        $GoodSku->product_sku = $sku['product_sku'];

                        // 处理宠物筛选标签
                        if (isset($sku['pet_filter_tags'])) {
                            $filterTags = $sku['pet_filter_tags'];

                            // 如果是空数组或null，表示适合所有宠物
                            if (empty($filterTags)) {
                                $GoodSku->pet_filter_tags = [];
                                $GoodSku->matching_pets_count = 0; // 0表示通用，适合所有宠物
                            } else {
                                // 验证并计算匹配的宠物数量
                                try {
                                    $petMatchingService = new \App\Services\PetMatchingService();
                                    $matchingResult = $petMatchingService->calculateMatchingPets($filterTags);

                                    $GoodSku->pet_filter_tags = $filterTags;
                                    $GoodSku->matching_pets_count = $matchingResult['count'];
                                } catch (\Exception $e) {
                                    // 如果匹配计算失败，记录日志但不影响保存
                                    \Log::warning('SKU宠物筛选标签匹配计算失败', [
                                        'sku_data' => $sku,
                                        'filter_tags' => $filterTags,
                                        'error' => $e->getMessage()
                                    ]);

                                    $GoodSku->pet_filter_tags = $filterTags;
                                    $GoodSku->matching_pets_count = null; // null表示计算失败
                                }
                            }
                        } else {
                            // 如果没有提供筛选标签，保持原有值或设为空
                            if (!$GoodSku->exists) {
                                $GoodSku->pet_filter_tags = [];
                                $GoodSku->matching_pets_count = 0;
                            }
                        }

                        $GoodSku->save();
                        $GoodSkuAll[] = $GoodSku->id;
                    }
                    //删除去除的SKU
                    GoodSku::where('good_id', $id)->whereNotIn('id', $GoodSkuAll)->update(['deleted_at' => now()->toDateTimeString()]);
                    $info->order_price = $order_price;
                    $info->save();
                    $newData['order_price'] = $order_price;
                } else {
                    //删除所有SKU
                    GoodSku::where('good_id', $id)->update(['deleted_at' => now()->toDateTimeString()]);
                }
            }
            // 清除推荐缓存（当商品信息变更时）
            if ($success) {
                \App\Models\GoodRecommendation::clearCache();
            }

            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function state($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = Good::find($id) or throw_if(true, 'RuntimeException', "商品：{$id}不存在或已删除");
            $oldData = $info->toArray();
            if ($info->is_show == Good::GOOD_SHOW_PUTAWAY) {
                $info->is_show = Good::GOOD_SHOW_ENTREPOT;
            } else {
                $info->is_show = Good::GOOD_SHOW_PUTAWAY;
            }
            $success = $info->save();
            $newData = array_merge($oldData, $info->toArray());
            if ($success) {
                $success = true;
                $message = '更新成功';
                event(new DataChanged(static::class, $oldData, $newData));
            } else {
                throw new Exception('更新失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "商品：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
