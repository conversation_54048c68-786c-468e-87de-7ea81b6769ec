<?php

namespace App\Http\Controllers;

use App\Helpers\QianFang;
use App\Models\PaymentLog;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function return_page()
    {
        return '支付成功';
    }

    public function failed_page()
    {
        return '支付失败';
    }

    public function qfpay_notify(Request $request)
    {
        try {
            /** @var array $header ['x-qf-sign' => '703AFC8E55A637B75C9D26CFF4EC6942']; */
            $headers = $request->header();
            Log::info('请求头: ' . json_encode($headers, 256));
            /** @var string $body '{"cash_fee_type": "CNY", "exchange_rate": "82145380", "cancel": "0", "pay_type": "800213", "txdtm": "2021-10-22 16:54:18", "out_trade_no": "20211022165418405918", "syssn": "20211022154200020007231226", "status": "1", "sysdtm": "2021-10-22 16:54:19", "paydtm": "2021-10-22 16:54:32", "goods_name": "/api/shop.order/pay_res_3_1", "txcurrcd": "HKD", "mchid": "ZrzJ9ZwTP4k3Wd", "cash_fee": "82", "chnlsn2": "", "cardcd": "o0qfh4vmakzdEn7RznQ_ax3VYbDs", "txamt": "100", "outcardnm": "", "respcd": "0000", "goods_info": "", "notify_type": "payment", "chnlsn": "4200001143202110229735928666"}'; */
            $body = file_get_contents('php://input');
            Log::info('请求体: ' . var_export($body, true));
            $header = ['x-qf-sign' => $request->header('x-qf-sign'), 'x-qf-signtype' => $request->header('x-qf-signtype')];
            if (empty($header['x-qf-sign'])) {
                return 'HEADER_CAN_NOT_BE_EMPTY';
            }
            $qianfang = new QianFang();
            if (!$qianfang->checkNotify($header, $body)) {
                throw new Exception('签名错误');
            }
            $res = json_decode($body, true);
            if (empty($res) || $res['respcd'] !== '0000') {
                throw new Exception('交易状态错误');
            }
            list($success, , $message) = PaymentLog::notify($res['notify_type'], $res['out_trade_no'], $res['chnlsn'], $res['paydtm'], $res['syssn']);
            if (!$success) {
                throw new Exception($message);
            }
            return 'SUCCESS';
        } catch (Exception $e) {
            return 'FAIL';
        }
    }
}
