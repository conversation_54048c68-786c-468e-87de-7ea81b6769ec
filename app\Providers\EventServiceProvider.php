<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        'App\Events\Registered' => [
            'App\Listeners\SendEmailVerificationNotification',
        ],
        'App\Events\DataChanged' => [
            'App\Listeners\OperateLogger',
        ],
        'App\Events\DataChangedPack' => [
            'App\Listeners\OperateLogger',
        ],
        'Illuminate\Mail\Events\MessageSending' => [
            'App\Listeners\LogSendingMessage',
        ],
        'Illuminate\Mail\Events\MessageSent' => [
            'App\Listeners\LogSentMessage',
        ],
        // 帖子相关通知事件
        'App\Events\PostLikedEvent' => [
            'App\Listeners\SendPostLikedNotification',
        ],
        'App\Events\PostCommentedEvent' => [
            'App\Listeners\SendPostCommentedNotification',
        ],
        'App\Events\PostFavoritedEvent' => [
            'App\Listeners\SendPostFavoritedNotification',
        ],

        // 评论相关通知事件
        'App\Events\PostCommentLikedEvent' => [
            'App\Listeners\SendPostCommentLikedNotification',
        ],

        // 用户关注通知事件
        'App\Events\UserFollowedEvent' => [
            'App\Listeners\SendUserFollowedNotification',
        ],

        // 好友通知事件
        'App\Events\FriendRequestEvent' => [
            'App\Listeners\SendFriendRequestNotification',
        ],
        'App\Events\FriendHandledEvent' => [
            'App\Listeners\SendFriendHandledNotification',
        ],

        // 宠物相关通知事件
        'App\Events\PetFamilyRequestEvent' => [
            'App\Listeners\SendPetFamilyRequestNotification',
        ],
        'App\Events\PetTransferCodeEvent' => [
            'App\Listeners\SendPetTransferCodeNotification',
        ],
        'App\Events\PetFamilyCodeEvent' => [
            'App\Listeners\SendPetFamilyCodeNotification',
        ],
        'App\Events\PetReceivedEvent' => [
            'App\Listeners\SendPetReceivedNotification',
        ],
        'App\Events\ParentAddedEvent' => [
            'App\Listeners\SendParentAddedNotification',
            'App\Listeners\AutoFollowFamilyMember',
        ],
        'App\Events\ParentRemovedEvent' => [
            'App\Listeners\SendParentRemovedNotification',
            'App\Listeners\AutoUnfollowFamilyMember',
        ],
        'App\Events\OwnerTransferEvent' => [
            'App\Listeners\SendOwnerTransferNotification',
        ],
        'App\Events\PetAlbumLikedEvent' => [
            'App\Listeners\SendPetAlbumLikedNotification',
        ],
        // 系统公告通过系统通知直接创建，不再使用事件


        //宠物资料更新
        'App\Events\PetChanged' => [
            'App\Listeners\PetLogger',
            'App\Listeners\PetBirthdayReminder',
        ],

        //宠物咨询
        'App\Events\ConsultCreatedEvent' => [
            'App\Listeners\SendConsultCreatedNotification',
        ],
        'App\Events\ConsultCancelledEvent' => [
            'App\Listeners\SendConsultCancelledNotification',
        ],
        'App\Events\ConsultBookAcceptedEvent' => [
            'App\Listeners\SendConsultBookAcceptedNotification',
            'App\Listeners\ConsultBookStartTodayReminder',
        ],
        'App\Events\ConsultBookWillStartEvent' => [
            'App\Listeners\SendConsultBookWillStartNotification',
        ],
        'App\Events\ConsultBookStartedEvent' => [
            'App\Listeners\SendConsultBookStartedNotification',
        ],
        'App\Events\ConsultAcceptedEvent' => [
            'App\Listeners\SendConsultAcceptedNotification',
        ],
        'App\Events\ConsultBookNoEnterEvent' => [
            'App\Listeners\SendConsultBookNoEnterNotification',
        ],
        'App\Events\ConsultReplyTimeoutEvent' => [
            'App\Listeners\SendConsultReplyTimeoutNotification',
        ],
        'App\Events\ConsultEndedEvent' => [
            'App\Listeners\SendConsultEndedNotification',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }

}
