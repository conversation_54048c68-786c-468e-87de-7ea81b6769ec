<?php

namespace App\Models;

use App\Helpers\RedisLock;
use App\Services\GoodIndent\GoodIndentService;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class GoodIndentRefund extends Model
{
    use SoftDeletes;

    protected $table = 'goods_indents_refunds';

    protected $fillable = [
        'good_indent_id',
        'good_indent_commodity_id',
        'identification',
        'user_id',
        'number',
        'total',
        'reason',
        'remark',
        'apply_id',
        'apply_name',
        'refund_id',
        'refund_name',
        'state',
        'refund_time',
    ];

    const GOOD_INDENT_REFUND_STATE_AUDIT = 1;   //待审核
    const GOOD_INDENT_REFUND_STATE_PASS = 2;  //已退款
    const GOOD_INDENT_REFUND_STATE_REJECT = 3;  //已驳回
    const GOOD_INDENT_REFUND_STATE_CANCEL = 4;  //已取消

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function Commodity()
    {
        return $this->hasOne(GoodIndentCommodity::class, 'id', 'good_indent_commodity_id');
    }

    public function Indent()
    {
        return $this->hasOne(GoodIndent::class, 'id', 'good_indent_id');
    }

    public static function getListQuery($search_data = array())
    {
        // 遍历筛选条件
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";
        $state = $search_data['state'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $auditTimeStart = $search_data['audit_time_start'] ?? "";
        $auditTimeEnd = $search_data['audit_time_end'] ?? "";
        $refundTimeStart = $search_data['refund_time_start'] ?? "";
        $refundTimeEnd = $search_data['refund_time_end'] ?? "";

        if (!empty($search_data['with_trashed'])) {
            $query = self::withTrashed();
        } else {
            $query = self::whereNull('deleted_at');
        }

        return $query->when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $q) use ($keyword) {
                $q->where(function (Builder $q1) use ($keyword) {
                    $q1->Where('identification', $keyword);
                });
                $q->orWhereHas('Indent', function ($query) use ($keyword) {
                    $query->withTrashed()->where('identification', $keyword);
                });
                $q->orWhereHas('Commodity', function ($query) use ($keyword) {
                    $query->withTrashed()->where('name', 'like', "%$keyword%");
                });
            });
        })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($state, function (Builder $query) use ($state) {
                $query->where('state', $state);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($auditTimeStart && $auditTimeEnd, function (Builder $query) use ($auditTimeStart, $auditTimeEnd) {
                $query->whereBetween('audit_time', [$auditTimeStart, $auditTimeEnd]);
            })
            ->when($refundTimeStart && $refundTimeEnd, function (Builder $query) use ($refundTimeStart, $refundTimeEnd) {
                $query->whereBetween('refund_time', [$refundTimeStart, $refundTimeEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            });
    }

    public static function getList($search_data = array())
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        return self::getListQuery($search_data)
            ->with([
                'Commodity' => function (Builder $q) {
                    $q->select([
                        'id',
                        'good_indent_id',
                        'good_id',
                        'good_sku_id',
                        'image',
                        'name',
                        'name_tc',
                        'name_en',
                        'product_code',
                        'product_sku',
                        'price',
                        'number',
                        'weight',
                        'remark',
                        'good_total',
                        'carriage_total',
                        'coupon_total',
                        'pay_total',
                        'refund_number',
                        'refund_total',
                    ]);
                },
                'Indent'    => function (Builder $q) {
                    $q->select([
                        'id',
                        'user_id',
                        'consignee',
                        'state',
                        'good_amount',
                        'carriage_amount',
                        'coupon_amount',
                        'pay_amount',
                        'identification',
                        'refund_amount',
                        'pay_time',
                    ]);
                },
            ])
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail($id)
    {
        return self::withTrashed()
            ->where('id', $id)
            ->with([
                'Commodity',
                'Indent',
            ])->first();
    }

    public static function getDetailForApp($id, $userId)
    {
        return self::where('id', $id)->where('user_id', $userId)
            ->with([
                'Commodity',
                'Indent'
            ])->first();
    }

    public static function confirmGoodIndentRefund($Commodity, $refundInfo)
    {
        if (!$Commodity) {
            throw new \Exception('商品不存在');
        }
        if (!$Commodity['pay_time']) {
            throw new \Exception('商品未付款');
        }
        if (!$Commodity['is_refund']) {
            throw new \Exception('商品不支持售后');
        }
        if (strtotime("+{$Commodity['refund_day']} days", strtotime($Commodity['pay_time'])) < time()) {
            throw new \Exception('商品已超过售后时间');
        }
        if ($Commodity['refund_mark'] == GoodIndentCommodity::REFUND_MARK_REFUNDING) {
            throw new \Exception('商品正在售后，请等待审核');
        }
        self::checkRefundInfo($Commodity, $refundInfo);
        $GoodIndentRefund = new self();
        $GoodIndentRefund->good_indent_id = $Commodity['good_indent_id'];
        $GoodIndentRefund->good_indent_commodity_id = $Commodity['id'];
        $GoodIndentRefund->identification = orderNumber();
        $GoodIndentRefund->number = $refundInfo['number'];
        $GoodIndentRefund->total = $refundInfo['total'];
        $GoodIndentRefund->reason = $refundInfo['reason'] ?? '';
        return $GoodIndentRefund;
    }

    public static function checkRefundInfo($Commodity, $refundInfo)
    {
        if ($refundInfo['number'] + $Commodity['refund_number'] > $Commodity['number']) {
            throw new \Exception('退款数量超出可退数量');
        }
        $maxRefundTotal = bcmul($Commodity['price'], $refundInfo['number'], 2);
        if ($refundInfo['total'] > $maxRefundTotal) {
            throw new \Exception('退款金额超出可退金额');
        }
    }

    public static function createGoodIndentRefund($data = array(), $user = null)
    {
        DB::beginTransaction();
        $redis = redis();
        $locks = [];
        try {
            $userId = $user->id;
            $refundCommodities = $data['commodity'];
            $Commodities = GoodIndentCommodity::getListForRefund($refundCommodities);
            $Commodities = array_filter($Commodities, function ($item) use ($userId) {
                return $item['user_id'] == $userId;
            });
            $Commodities = array_column($Commodities, null, 'id');
            if (count($Commodities) != count($refundCommodities)) {
                throw new \Exception('非法请求');
            }
            foreach ($Commodities as $commodity) {
                $lock = RedisLock::lock($redis, 'RefundGoodIndentCommodity_' . $commodity['id']);
                if (!$lock) {
                    throw new Exception('业务繁忙，请稍后再试');
                }
                $locks[$lock] = 'RefundGoodIndentCommodity_' . $commodity['id'];
            }
            $ids = [];
            foreach ($refundCommodities as $refundInfo) {
                //生成退款单
                $Commodity = $Commodities[$refundInfo['good_indent_commodity_id']] ?? null;
                $GoodIndentRefund = self::confirmGoodIndentRefund($Commodity, $refundInfo);
                $GoodIndentRefund->user_id = $user->id;
                $GoodIndentRefund->identification = orderNumber();
                $GoodIndentRefund->apply_id = 0;
                $GoodIndentRefund->apply_name = '用户';
                $GoodIndentRefund->state = self::GOOD_INDENT_REFUND_STATE_AUDIT;
                $GoodIndentRefund->id = self::create($GoodIndentRefund->toArray())->id;
                $success = $GoodIndentRefund->id > 0;
                $ids[] = $GoodIndentRefund->id;
                if (!$success) {
                    throw new Exception('生成退款单失败');
                }
                //关联更新订单
                $service = new GoodIndentService($Commodity['good_indent_id'], $user);
                $success = $service->refund($GoodIndentRefund);
                if (!$success) {
                    throw new Exception($service->getMessage());
                }
            }
            $success = true;
            $record = ['ids' => $ids];
            $message = '发起退款成功';
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($userId . '用户发起退款失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($locks) or array_map(function ($lock_key) use ($redis) {
                RedisLock::unlock($redis, $lock_key);
            }, $locks);
        }
        return [$success, $record, $message];
    }

    public static function cancel($id, $user = null)
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $GoodIndentRefund = self::getDetailForApp($id, $user->id);
            $lock = RedisLock::lock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            throw_if($GoodIndentRefund->state != self::GOOD_INDENT_REFUND_STATE_AUDIT, '当前退款单状态不允许取消');
            $GoodIndentRefund->state = self::GOOD_INDENT_REFUND_STATE_CANCEL;
            $success = $GoodIndentRefund->save();
            if (!$success) {
                throw new Exception('生成退款单失败');
            }
            //关联更新订单
            $service = new GoodIndentService($GoodIndentRefund->good_indent_id, $user);
            $success = $service->refund($GoodIndentRefund);
            if (!$success) {
                throw new Exception($service->getMessage());
            }
            $record = [];
            $message = '取消成功';
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = '取消失敗';
            logErr($GoodIndentRefund['id'] . '取消退款失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);;
        }
        return [$success, $record, $message];
    }

    public static function del($id, $user = null)
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $GoodIndentRefund = self::getDetailForApp($id, $user->id);
            $lock = RedisLock::lock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            throw_if(in_array($GoodIndentRefund->state, [self::GOOD_INDENT_REFUND_STATE_CANCEL, self::GOOD_INDENT_REFUND_STATE_REJECT]), '当前退款单状态不允许删除');
            $success = $GoodIndentRefund->where('id', $id)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if (!$success) {
                throw new Exception('删除退款单失败');
            }
            $record = [];
            $message = '删除成功';
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = '删除失敗';
            logErr($GoodIndentRefund['id'] . '删除退款失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);;
        }
        return [$success, $record, $message];
    }

    private static function getListForRefund(array $ids)
    {
        $refunds = [];
        $arr = self::withTrashed()
            ->from('goods_indents_refunds as gir')
            ->join('goods_indents as gi', 'gir.good_indent_id', '=', 'gi.id')
            ->join('goods_indents_commoditys as gic', 'gir.good_indent_commodity_id', '=', 'gic.id')
            ->whereIn('gir.id', $ids)
            ->select([
                'gir.*',
                'gic.name',
                'gic.name_tc',
                'gic.name_en',
                'gi.pay_time',
                'gi.user_id',
                'gi.identification as good_indent_identification',
            ])
            ->get();
        if ($arr->count() != count($ids)) {
            throw new \Exception('非法请求');
        }
        foreach ($arr as $v) {
            if ($v->state != self::GOOD_INDENT_REFUND_STATE_AUDIT) {
                throw new \Exception("退款单：{$v->id}状态不正确");
            }
            $refunds[$v->id] = $v;
        }
        return $refunds;
    }

    public static function audit($data = array(), $user = null)
    {
        $refundCommodities = $data['refund'];
        try {
            $refunds = self::getListForRefund(array_column($refundCommodities, 'id'));
        } catch (\Exception $e) {
            return [false, [], $e->getMessage()];
        }
        $success = 0;
        $fail = 0;
        $record = [];
        foreach ($refundCommodities as $refundInfo) {
            DB::beginTransaction();
            $redis = redis();
            try {
                /**
                 * 更新退款单
                 * @var GoodIndentRefund $GoodIndentRefund
                 */
                $GoodIndentRefund = $refunds[$refundInfo['id']];
                $lock = RedisLock::lock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);
                if (!$lock) {
                    throw new Exception('业务繁忙，请稍后再试');
                }
                self::auditByGoodIndent($GoodIndentRefund, $refundInfo, $user);
                $success++;
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $fail++;
                $record[$refundInfo['id']] = $e->getMessage();
                logErr($refundInfo['id'] . '审核退款失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            } finally {
                empty($lock) or RedisLock::unlock($redis, 'RefundGoodIndentCommodity_' . $GoodIndentRefund->good_indent_commodity_id);;
            }
        }
        $message = '成功：' . $success . ($fail > 0 ? "，失败：{$fail}" : '');
        return [true, $record, $message];
    }

    private static function auditByGoodIndent($GoodIndentRefund, $refundInfo, $user)
    {
        $GoodIndentRefund->state = $refundInfo['state'];
        $GoodIndentRefund->audit_time = now()->toDateTimeString();
        $GoodIndentRefund->remark = $refundInfo['remark'] ?? '';
        if ($GoodIndentRefund->state == self::GOOD_INDENT_REFUND_STATE_PASS) {
            $GoodIndentRefund->refund_id = $user->id;
            $GoodIndentRefund->refund_name = $user->name;
            $GoodIndentRefund->refund_time = now()->toDateTimeString();
        }
        $bool = $GoodIndentRefund->save();
        if (!$bool) {
            throw new Exception('更新退款单失败');
        }
        //关联更新订单
        $service = new GoodIndentService($GoodIndentRefund->good_indent_id, $user);
        $bool = $service->refund($GoodIndentRefund);
        if (!$bool) {
            throw new Exception($service->getMessage());
        }
        //发起退款
        if ($GoodIndentRefund->total > 0) {
            PaymentLog::refundByGoodIndent($GoodIndentRefund);
        }
    }

    public static function refund($data = array(), $user = null)
    {
        DB::beginTransaction();
        $redis = redis();
        $locks = [];
        try {
            $userId = $user->id;
            $refundCommodities = $data['commodity'];
            $Commodities = GoodIndentCommodity::getListForRefund($refundCommodities);
            $Commodities = array_column($Commodities, null, 'id');
            if (count($Commodities) != count($refundCommodities)) {
                throw new \Exception('非法请求');
            }
            foreach ($Commodities as $commodity) {
                $lock = RedisLock::lock($redis, 'RefundGoodIndentCommodity_' . $commodity['id']);
                if (!$lock) {
                    throw new Exception('业务繁忙，请稍后再试');
                }
                $locks[$lock] = 'RefundGoodIndentCommodity_' . $commodity['id'];
            }
            $ids = [];
            foreach ($refundCommodities as $refundInfo) {
                //生成退款单
                $Commodity = $Commodities[$refundInfo['good_indent_commodity_id']] ?? null;
                $Commodity['is_refund'] = 1;     //管理员不用校验is_refund字段，默认设置可退款
                $Commodity['refund_day'] = 365;  //可退款时间也不用校验，1年内都可以退
                $GoodIndentRefund = self::confirmGoodIndentRefund($Commodity, $refundInfo);
                $GoodIndentRefund->user_id = $Commodity['user_id'];
                $GoodIndentRefund->identification = orderNumber();
                $GoodIndentRefund->apply_id = $user->id;
                $GoodIndentRefund->apply_name = $user->name;
                $GoodIndentRefund->state = self::GOOD_INDENT_REFUND_STATE_AUDIT;
                $GoodIndentRefund->id = self::create($GoodIndentRefund->toArray())->id;
                $success = $GoodIndentRefund->id > 0;
                $ids[] = $GoodIndentRefund->id;
                if (!$success) {
                    throw new Exception('生成退款单失败');
                }
                //关联更新订单
                $refunds = self::getListForRefund([$GoodIndentRefund->id]);
                $GoodIndentRefund = $refunds[$GoodIndentRefund->id];
                $service = new GoodIndentService($Commodity['good_indent_id'], $user);
                $success = $service->refund($GoodIndentRefund);
                if (!$success) {
                    throw new Exception($service->getMessage());
                }
                //自动审核
                $auditInfo = [
                    'id'     => $GoodIndentRefund->id,
                    'state'  => self::GOOD_INDENT_REFUND_STATE_PASS,
                    'remark' => $refundInfo['remark'] ?? '',
                ];
                self::auditByGoodIndent($GoodIndentRefund, $auditInfo, $user);
            }
            $success = true;
            $record = ['ids' => $ids];
            $message = '退款成功';
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($userId . '管理员退款失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($locks) or array_map(function ($lock_key) use ($redis) {
                RedisLock::unlock($redis, $lock_key);
            }, $locks);
        }
        return [$success, $record, $message];
    }

    public static function nums($data = array())
    {
        $query = self::getListQuery($data);

        $result = $query->selectRaw('
            COUNT(*) as `all`,
            SUM(CASE WHEN state = ? AND deleted_at IS NULL THEN 1 ELSE 0 END) as audit
        ', [
            self::GOOD_INDENT_REFUND_STATE_AUDIT,
        ])
            ->first();

        return [
            'all'   => $result->all,
            'audit' => (int)$result->audit,  //审核
        ];
    }
}
