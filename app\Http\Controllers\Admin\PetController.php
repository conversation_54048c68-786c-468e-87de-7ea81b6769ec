<?php

namespace App\Http\Controllers\Admin;


use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Pet;
use Illuminate\Http\Request;

class PetController extends Controller
{

    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
            'owner_id'  => 'nullable|integer|gt:0',
            'is_lost'   => 'nullable|in:0,1',
            'sex'       => 'nullable|in:1,2',
            'type_id'   => 'nullable|integer|gt:0|exists:pets_types,id',
            'breed_id'  => 'nullable|integer|gt:0|exists:pets_breeds,id',
        ]);
        $formData = $request->all();
        $records = Pet::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Pet::getDetail($id));
    }


    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:1',
        ]);
        $result = Pet::del($request->input('id'), $request->user()->id, 1);
        return ResponseHelper::result(...$result);
    }

    /**
     * 保存宠物信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'                    => 'sometimes|integer|gte:0',
            'name'                  => 'required|max:32',
            'description'           => 'nullable|string|max:200',
            'sex'                   => 'required|in:1,2',
            'avatar'                => 'required|url',
            'type_id'               => 'required|integer|gt:0|exists:pets_types,id',
            'breed_id'              => 'required|integer|gt:0|exists:pets_breeds,id',
            'birthday'              => 'required|date_format:Y-m-d',
            'owner_id'              => 'required|integer|gt:0|exists:app_users,id',
            'vaccinated'            => 'sometimes|in:0,1',
            'latitude'              => 'nullable|numeric',
            'longitude'             => 'nullable|numeric',
            'city_code'             => 'nullable|string|max:50',
            'location'              => 'nullable|string|max:255',
            'father_id'             => 'nullable|integer|gt:0',
            'mother_id'             => 'nullable|integer|gt:0',
            'birth_certificate'     => 'nullable|url',
            'bloodline_certificate' => 'nullable|url',
            'vaccine_certificate'   => 'nullable|url',
            'health_report'         => 'nullable|url',
            'weight'                => 'nullable|numeric|min:0',
            'weight_status'         => 'nullable|integer|between:1,9',
            'active_status'         => 'nullable|in:0,1,2',
            'is_pregnant'           => 'nullable|in:0,1',
            'is_ill'                => 'nullable|in:0,1',
            'is_dead'               => 'nullable|in:0,1',
            'family_members'        => 'nullable|array',
            'family_members.*.id'   => 'required|integer|gt:0|exists:app_users,id'
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['family_members'] = $request->input('family_members', []);
        $result = Pet::saveDetail($formData, $request->user()->id, 1);
        return ResponseHelper::result(...$result);
    }
}
