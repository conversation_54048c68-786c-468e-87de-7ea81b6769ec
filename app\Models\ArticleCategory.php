<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class ArticleCategory extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'articles_categorys';

    protected $fillable = [
        'name',
        'name_tc',
        'name_en',
        'status',
        'sort_order'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getList($search_data = array())
    {
        // 遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";

        return self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)->first();
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('name', $data['name'])->where('id', '!=', $id)->exists()) {
                throw new Exception('文章分類已存在');
            }
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "文章分類：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "文章分類：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
