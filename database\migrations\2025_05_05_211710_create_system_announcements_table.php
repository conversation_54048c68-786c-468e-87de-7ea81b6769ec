<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('system_announcements', function (Blueprint $table) {
            $table->id();
            $table->string('title', 255)->comment('公告标题');
            $table->text('content')->comment('公告内容');
            $table->unsignedBigInteger('publisher_id')->comment('发布者ID');
            $table->tinyInteger('status')->default(1)->comment('状态：0禁用，1启用');
            $table->timestamp('publish_at')->nullable()->comment('发布时间');
            $table->timestamp('expire_at')->nullable()->comment('过期时间');
            $table->json('receiver_ids')->nullable()->comment('接收者ID数组，为空表示发送给所有用户');
            $table->timestamps();
            $table->softDeletes();

            $table->index('publisher_id');
            $table->index('status');
            $table->index('publish_at');
            $table->index('expire_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_announcements');
    }
};
