<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateCategorySpecificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('goods_categorys_specifications', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('specification_id')->default(0)->index()->comment('规格ID');
            $table->bigInteger('category_id')->default(0)->index()->comment('分类ID');
            $table->unique('id');
        });
        DB::statement("ALTER TABLE `goods_categorys_specifications` COMMENT='分类规格中间表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods_categorys_specifications');
    }
}
