<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;

class HealthAnalysisService extends BaseAnalysisService
{
    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);

        $this->type = 'health';
        $this->name = '健康状态分析';
        $this->description = '分析宠物的综合健康状况';
    }

    /**
     * 执行健康分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位
     * @param array|null $symptoms 症状列表
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        try {
            // 获取宠物信息
            $petInfo = $this->getPetInfo($petId);

            // 如果没有指定部位，使用默认值
            $bodyPart = $bodyPart ?? 'general';

            // 构建提示词，始终使用模板格式，不考虑stream参数
            $prompt = $this->buildHealthAnalysisPrompt($petInfo, $bodyPart, $symptoms, $petsInfo, $images);

            // 添加用户查询
            $finalPrompt = $prompt . "\n\n用户问题: " . $query;

            // 调用AI服务，根据参数决定是否使用流式响应
            $response = $this->aiService->chat(
                $finalPrompt,
                $images,
                $sessionId,
                0.7,
                $stream // 根据参数决定是否使用流式响应
            );

            // 如果是流式响应，直接返回响应对象
            if ($stream) {
                return $response;
            }

            // 如果不是流式响应，处理响应内容
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return [
                    'status' => 'error',
                    'message' => '获取分析结果失败',
                    'analysis_type' => $this->getType()
                ];
            }

            // 返回格式化的响应，保持与流式响应相同的格式
            return [
                'status' => 'success',
                'message' => '分析成功',
                'data' => [
                    'content' => $content,
                    'session_id' => $response['session_id'] ?? null,
                    'is_new_session' => $response['is_new_session'] ?? false
                ],
                // 移除pet_info字段，由控制器统一添加
                'analysis_type' => $this->getType()
            ];

        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常
            throw $e;
        } catch (\Exception $e) {
            Log::error('健康状态分析失败: ' . $e->getMessage());
            // 将异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '健康状态分析失败: ' . $e->getMessage(),
                'health_analysis_error',
                $sessionId ?? null,
                [
                    'original_error' => $e->getMessage(),
                    'analysis_type' => $this->getType(),
                    'body_part' => $bodyPart,
                    'symptoms' => $symptoms
                ]
            );
        }
    }

    /**
     * 构建健康分析提示词
     *
     * @param array|null $petInfo
     * @param string|null $bodyPart 分析的身体部位
     * @param array|null $symptoms 症状列表
     * @param array|null $petsInfo 多宠物信息数组
     * @return string
     */
    protected function buildHealthAnalysisPrompt(?array $petInfo, ?string $bodyPart = 'general', ?array $symptoms = null, ?array $petsInfo = null, array $images = []): string
    {
        // 根据是否有图片来调整提示词
        if (!empty($images)) {
            $prompt = "你是一个专业的宠物健康顾问，请根据用户描述和上传的图片分析宠物的健康状况，并提供健康建议。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式。\n\n";
        } else {
            $prompt = "你是一个专业的宠物健康顾问，请根据用户的描述分析宠物的健康状况，并提供健康建议。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式。\n\n";
        }

        // 根据宠物类型确定分析的特征
        $petType = 'general';
        if ($petInfo && isset($petInfo['type_en'])) {
            if (stripos($petInfo['type_en'], 'dog') !== false) {
                $petType = 'dog';
            } elseif (stripos($petInfo['type_en'], 'cat') !== false) {
                $petType = 'cat';
            }
        }

        // 添加部位特定的提示
        $prompt .= $this->getBodyPartSpecificPrompt($bodyPart, $petType) . "\n\n";

        // 如果提供了症状，添加症状信息
        if ($symptoms && !empty($symptoms)) {
            $prompt .= "用户报告的症状：" . implode("、", $symptoms) . "\n\n";
            $prompt .= "请特别关注上述症状，并在分析中考虑这些症状可能指示的健康问题。\n\n";
        }



        // 使用与食物分析和粒便分析类似的带颜色标注的模板格式
        $prompt .= "请按照以下格式回复，使用纯正的香港粤语（广东话）：\n\n";

        $prompt .= "<span style=\"color: #2196F3;\">健康状况描述：</span>\n";
        $prompt .= " ● [对宠物健康状况的详细描述]\n\n";

        $prompt .= "<span style=\"color: #4CAF50;\">健康评估等级：</span>\n";
        $prompt .= " ● [宠物名称]--<span style=\"color: #4CAF50;\">[正常]</span>/<span style=\"color: #FFC107;\">[轻度异常]</span>/<span style=\"color: #FF9800;\">[中度异常]</span>/<span style=\"color: #F44336;\">[严重异常]</span>\n\n";

        $prompt .= "<span style=\"color: #FF9800;\">可能原因：</span>\n";
        $prompt .= " ● [可能原因1]\n";
        $prompt .= " ● [可能原因2]\n";
        $prompt .= " ● [可能原因3]\n\n";

        $prompt .= "<span style=\"color: #9C27B0;\">建议处理方式：</span>\n";
        $prompt .= " ● [建议处理方式1]\n";
        $prompt .= " ● [建议处理方式2]\n";
        $prompt .= " ● [建议处理方式3]\n\n";

        $prompt .= "<span style=\"color: #F44336;\">是否需要就医：</span>\n";
        $prompt .= " ● <span style=\"color: #4CAF50;\">[不需要]</span>/<span style=\"color: #FFC107;\">[观察几天]</span>/<span style=\"color: #FF9800;\">[尽快就医]</span>/<span style=\"color: #F44336;\">[立即就医]</span>\n\n";

        $prompt .= "<span style=\"color: #009688;\">日常护理建议：</span>\n";
        $prompt .= " ● [日常护理建议1]\n";
        $prompt .= " ● [日常护理建议2]\n\n";

        $prompt .= "<span style=\"color: #009688;\">饮食调整建议：</span>\n";
        $prompt .= " ● [饮食调整建议1]\n";
        $prompt .= " ● [饮食调整建议2]\n\n";

        $prompt .= "<span style=\"color: #9C27B0;\">预防措施：</span>\n";
        $prompt .= " ● [预防措施1]\n";
        $prompt .= " ● [预防措施2]\n\n";

        $prompt .= "<span style=\"color: #673AB7;\">总结：</span>\n";
        $prompt .= "[对所有宠物的健康状况的总体评估和建议]\n\n";

        // 以下是给AI的指导，使用特殊标记包裹，确保AI不会直接返回这些内容
        $prompt .= "<instructions>\n";
        $prompt .= "请注意：\n";
        $prompt .= "1. 请为重要内容添加颜色标注。\n";
        $prompt .= "2. 健康状况描述使用蓝色。\n";
        $prompt .= "3. 健康评估等级使用绿色。\n";
        $prompt .= "4. 可能的原因使用橙色。\n";
        $prompt .= "5. 建议处理方式使用紫色。\n";
        $prompt .= "6. 是否需要就医使用红色，尤其是当需要紧急就医时。\n";
        $prompt .= "7. 日常护理建议使用青绿色。\n";
        $prompt .= "8. 如果无法识别宠物健康状况，请在第一项回答\"无法评估健康状况: 原因\"，其中原因可能是\"图片不清晰\"、\"没有提供图片\"、\"描述信息不足\"或\"需要更多症状信息\"等。\n";
        $prompt .= "9. 健康评估等级请使用：正常/轻度异常/中度异常/严重异常 四个等级之一。\n";
        $prompt .= "10. 是否需要就医请使用：不需要/观察几天/尽快就医/立即就医 四个选项之一。\n";
        $prompt .= "11. 如果可以识别一些症状但无法评估某些具体方面，请在相应项目中注明\"无法评估: 原因\"。\n";
        $prompt .= "12. 请确保在无法识别或评估时提供清晰的标题和原因，以便前端可以正确展示。\n";
        $prompt .= "13. 明确声明你不能替代兽医的专业诊断，严重症状应立即就医。\n";
        $prompt .= "14. **多宠物统一回复规则：**\n";
        $prompt .= "    - 当所有宠物健康状况相同时，统一回复：'所有宠物--[健康状态]'\n";
        $prompt .= "    - 当所有宠物就医建议相同时，统一回复：'所有宠物都[不需要/需要观察/需要就医]'\n";
        $prompt .= "    - 只有在宠物之间有差异时才分别说明个别情况\n";
        $prompt .= "15. **重要：请使用简洁明了的文字表达，避免冗长的句子和重复的表述。每个要点都要言简意赅，直接说重点。**\n";
        $prompt .= "16. **文字风格：用词精练，表达直接，避免过多的修饰词和解释性语言。**\n";
        $prompt .= "</instructions>\n\n";

        // 添加多宠物信息（如果有）
        if (!empty($petsInfo)) {
            $prompt .= "宠物信息:\n";
            foreach ($petsInfo as $index => $pet) {
                $prompt .= "宠物" . ($index + 1) . ":\n";
                $prompt .= "- 名称: " . $pet['name'] . "\n";
                $prompt .= "- 类型: " . $pet['type'] . "\n";
                $prompt .= "- 品种: " . $pet['breed'] . "\n";
                $prompt .= "- 性别: " . $pet['sex'] . "\n";

                if (isset($pet['age'])) {
                    $prompt .= "- 年龄: " . $pet['age'] . " 岁\n";
                }

                if (isset($pet['weight'])) {
                    $prompt .= "- 体重: " . $pet['weight'] . " 公斤\n";
                }

                $prompt .= "\n";
            }
        } elseif ($petInfo) {
            // 添加单个宠物信息（如果没有多宠物信息但有单个宠物信息）
            $prompt .= "宠物信息:\n";
            $prompt .= "- 名称: " . $petInfo['name'] . "\n";
            $prompt .= "- 类型: " . $petInfo['type'] . "\n";
            $prompt .= "- 品种: " . $petInfo['breed'] . "\n";
            $prompt .= "- 性别: " . $petInfo['sex'] . "\n";

            if (isset($petInfo['age'])) {
                $prompt .= "- 年龄: " . $petInfo['age'] . " 岁\n";
            }

            if (isset($petInfo['weight'])) {
                $prompt .= "- 体重: " . $petInfo['weight'] . " 公斤\n";
            }

            if (isset($petInfo['weight_status'])) {
                $prompt .= "- 体重状态: " . $petInfo['weight_status'] . "\n";
            } else {
                $prompt .= "- 体重状态: 正常\n";
            }

            $prompt .= "- 是否绝育: " . ($petInfo['neutered'] ? '是' : '否') . "\n";
            $prompt .= "- 是否怀孕: " . ($petInfo['is_pregnant'] ? '是' : '否') . "\n";
            $prompt .= "- 是否生病: " . ($petInfo['is_ill'] ? '是' : '否') . "\n";

            if (!empty($petInfo['special_conditions'])) {
                $prompt .= "- 特殊情况: " . implode(', ', $petInfo['special_conditions']) . "\n";
            }
        } else {
            $prompt .= "用户没有指定特定宠物，请提供适用于一般猫和狗的健康建议。";
        }

        return $prompt;
    }

    /**
     * 获取部位特定的提示词
     *
     * @param string $bodyPart 身体部位
     * @param string $petType 宠物类型
     * @return string
     */
    protected function getBodyPartSpecificPrompt(string $bodyPart, string $petType = 'general'): string
    {
        // 通用部位提示
        $generalPrompts = [
            'head' => "请特别关注宠物头部的健康状况，包括头部形状、对称性、有无肿块、头部姿势等。",
            'eyes' => "请特别关注宠物眼睛的健康状况，包括眼睛清澈度、分泌物、瞳孔反应、眼睝状态、结膜颜色等。",
            'ears' => "请特别关注宠物耳朵的健康状况，包括耳道清洁度、有无异味、分泌物、发红、肿胀等。",
            'mouth' => "请特别关注宠物口腔的健康状况，包括牙齿、牙龍、舌头、口腔黏膜、口臭等。",
            'nose' => "请特别关注宠物鼻子的健康状况，包括鼻子湿润度、分泌物、呼吸声、打喷嚏频率等。",
            'skin' => "请特别关注宠物皮肤和被毛的健康状况，包括皮肤弹性、有无红肿、痧痒、脱毛、寄生虫等。",
            'paws' => "请特别关注宠物爪子和足垫的健康状况，包括爪子状态、足垫完整性、有无伤口或异物等。",
            'limbs' => "请特别关注宠物四肢和关节的健康状况，包括行走姿势、关节活动、肌肉状态、有无跌行等。",
            'back' => "请特别关注宠物背部和脊椎的健康状况，包括脊椎形状、背部肌肉、有无疼痛反应等。",
            'tail' => "请特别关注宠物尾巴的健康状况，包括尾巴姿势、活动度、被毛状态、有无伤口等。",
            'digestive' => "请特别关注宠物消化系统的健康状况，包括食欲、吧喘、腹部触诊、排便情况等。",
            'respiratory' => "请特别关注宠物呼吸系统的健康状况，包括呼吸频率、呼吸声、咳嗽、打喷嚏等。",
            'urinary' => "请特别关注宠物泌尿系统的健康状况，包括排尿频率、尿液颜色、排尿姿势、饮水量等。",
            'reproductive' => "请特别关注宠物生殖系统的健康状况，包括外生殖器状态、分泌物、发情行为等。",
            'behavior' => "请特别关注宠物行为的健康状况，包括活动水平、睡眠模式、社交行为、异常行为等。",
            'general' => "请全面评估宠物的整体健康状况，包括各个系统和身体部位。"
        ];

        // 狗特有的部位提示
        $dogPrompts = [
            'head' => "请特别关注狗的头部健康，包括头部形状、对称性、有无肿块等。注意犬类特有的问题如犬窝炎、牙周疾病等。",
            'skin' => "请特别关注狗的皮肤和被毛健康，包括是否有皮屑、红肿、痧痒等。犬类常见皮肤问题包括过敏性皮炎、疮蟒、真菌感染等。",
            'digestive' => "请特别关注狗的消化系统健康，包括食欲、腹部触诊等。犬类常见消化问题包括胆腺炎、肠胃炎、异物摄入等。",
            'anal' => "请特别关注狗的肛门和肛门腺健康，包括肛门腺是否充血、有无分泌物、狗是否经常蜡蜡肛门区域等。犬类常需要定期挤压肛门腺。",
            'limbs' => "请特别关注狗的四肢和关节健康，包括行走姿势、关节活动等。特别注意大型狗常见的骷关节发育不良和关节炎等问题。"
        ];

        // 猫特有的部位提示
        $catPrompts = [
            'head' => "请特别关注猫的头部健康，包括头部形状、对称性、有无肿块等。注意猫科特有的问题如上呼吸道感染、牙龍炎等。",
            'skin' => "请特别关注猫的皮肤和被毛健康，包括是否有过度舔舔、脱毛、皮屑等。猫科常见皮肤问题包括跳蜴过敏性皮炎、猫癖、心理性脱毛等。",
            'urinary' => "请特别关注猫的泌尿系统健康，这是猫的常见问题区域。注意是否有排尿困难、频繁排尿、尿血等症状，这可能与猫下泌尿道疾病(FLUTD)相关。",
            'grooming' => "请特别关注猫的清洁行为，包括舔毛频率、方式和舔毛区域。猫的清洁行为是健康的重要指标，过度或不足的清洁行为可能表明健康问题。",
            'whiskers' => "请特别关注猫的胡须和触须状态，包括是否完整、对称、有无断裂。猫的触须对感知环境非常重要，触须损伤可能影响猫的行为和平衡感。"
        ];

        // 根据宠物类型选择适当的提示
        if ($petType === 'dog' && isset($dogPrompts[$bodyPart])) {
            return $dogPrompts[$bodyPart];
        } elseif ($petType === 'cat' && isset($catPrompts[$bodyPart])) {
            return $catPrompts[$bodyPart];
        } else {
            return $generalPrompts[$bodyPart] ?? $generalPrompts['general'];
        }
    }
}
