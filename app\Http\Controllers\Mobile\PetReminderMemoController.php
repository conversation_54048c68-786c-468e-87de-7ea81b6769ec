<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\PetReminderMemo;
use Illuminate\Http\Request;

class PetReminderMemoController extends Controller
{
    /**
     * 获取提醒列表
     */
    public function getList(Request $request)
    {
        $validated = $request->validate([
            'page'     => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,100',
        ]);

        $result = PetReminderMemo::getList(
            $request->user()->id,
            $validated['page'] ?? 1,
            $validated['per_page'] ?? 20
        );

        return ResponseHelper::result(...$result);
    }

    /**
     * 获取未读提醒数量
     */
    public function getUnreadCount(Request $request)
    {
        $result = PetReminderMemo::getUnreadCount($request->user()->id);
        return ResponseHelper::result(...$result);
    }

    /**
     * 标记提醒已读
     */
    public function markRead(Request $request)
    {
        $validated = $request->validate([
            'memo_id' => 'nullable|integer',  // 为空则标记所有提醒已读
        ]);

        $result = PetReminderMemo::markRead(
            $request->user()->id,
            $validated['memo_id'] ?? null,
        );

        return ResponseHelper::result(...$result);
    }
}
