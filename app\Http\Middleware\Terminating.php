<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class Terminating
{
    public function handle(Request $request, Closure $next, string $guard = '')
    {
        $request->guard = $guard;
        return $next($request);
    }

    /**
     * 在響應發送到瀏覽器後處理任務。
     */
    public function terminate(Request $request, $response)
    {
        $url = $request->fullUrl();
        $user = $request->user();
        $requestData = $request->all();
        $responseData = $response->getContent();

        Log::info('請求地址: ' . $url . '，用戶ID：' . $user?->id);
        Log::info('請求參數: ' . json_encode($requestData, 256));
        if (empty($request->guard)) {
            Log::info('響應內容: ' . $responseData);
        }
        Log::info('響應耗時：' . (microtime(true) - LARAVEL_START) . 's');
        Log::info(LOG_SPLIT_LINE);
        if (isset($GLOBALS['sqlTimes'])) {
            Log::channel('sql')->info('SQL耗時：' . $GLOBALS['sqlTimes'] . 's');
            Log::channel('sql')->info(LOG_SPLIT_LINE);
        }

        return $response;
    }
}
