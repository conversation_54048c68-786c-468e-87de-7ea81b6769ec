<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Helpers\Tree;
use \DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin \Illuminate\Database\Eloquent\Builder
 */
class AppContentComment extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_contents_comments';

    protected $fillable = [
        'content_id',
        'content_user_id',
        'comment_user_id',
        'comment_content',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function content()
    {
        return $this->belongsTo(AppContent::class);
    }

    public function commentUser()
    {
        return $this->belongsTo(AppUser::class, 'comment_user_id');
    }

    public static function getList($search_data = array())
    {
        // 遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $contentId = $search_data['content_id'] ?? "";


        $query = new self;
        if (!empty($search_data['is_deleted'])) {
            $query = $query->onlyTrashed();
        }

        return $query->when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('comment_content', 'like', "%$keyword%");
            });
        })
            ->when($contentId, function (Builder $query) use ($contentId) {
                $query->where('content_id', $contentId);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->with('content', function (Builder $query) use ($sortName, $sortBy) {
                $query->select('id', 'title');
            })
            ->with('commentUser', function (Builder $query) use ($sortName, $sortBy) {
                $query->select('id', 'username', 'phone', 'avatar');
            })
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "分享评论：{$id}不存在或已删除");
            list($success, , $msg) = self::delComment($info->comment_user_id, $info->content_id, $info->id);
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception($msg);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    //将数据库中的数据同步到redis
    public static function initRedis($contentId)
    {
        //分享评论列表
        $cacheContentComments = "app_content:{$contentId}:comments";
        $commentList = self::where('content_id', $contentId)->select('id', 'comment_user_id', 'like_count', 'created_at')->get()->toArray();
        redis()->del($cacheContentComments);
        foreach ($commentList as $v) {
            $time = strtotime($v['created_at']);
            redis()->zAdd($cacheContentComments, self::calcCommentScore($time, $v['like_count']), $v['id']);
            $appUserId = $v['comment_user_id'];
            $cacheMyComments = "app_user:{$appUserId}:content_comments:{$contentId}";
            redis()->zAdd($cacheMyComments, $time, $v['id']);
        }
        //更新为初始化
        $cacheInitKey = "app_content:{$contentId}:init_comment";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    //计算评论权重
    public static function calcCommentScore($time, $likeCount)
    {
        // 假设热评数量的权重为100（可根据实际情况调整）
        $score = $time + ($likeCount * 100);
        return $score;
    }

    public static function ensureInitRedis($contentId)
    {
        $cacheInitKey = "app_content:{$contentId}:init_comment";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($contentId); //数据有问题时把key删除，重新初始化
    }

    public static function getCommentCount($contentId)
    {
        self::ensureInitRedis($contentId);
        $cacheContentComments = "app_content:{$contentId}:comments";
        return (int)redis()->zCard($cacheContentComments);
    }

    public static function isComment($appUserId, $contentId, $commentId)
    {
        self::ensureInitRedis($contentId);
        $cacheMyComments = "app_user:{$appUserId}:content_comments:{$contentId}";
        return redis()->zRank($cacheMyComments, $commentId) !== false;
    }

    public static function addComment($appUserId, $contentId, $content, $replyId = 0)
    {
        self::ensureInitRedis($contentId);
        $today = date('Y-m-d');
        $cacheCommentCountToday = "app_user:{$appUserId}:content_comment_count:{$today}";
        if (!redis()->exists($cacheCommentCountToday)) {
            redis()->setex($cacheCommentCountToday, 86400, self::withTrashed()->where('comment_user_id', $appUserId)->where('created_at', '>=', $today)->count());
        }
        if (redis()->get($cacheCommentCountToday) >= 1000) {
            logErr('评论数量告警：' . $appUserId);
            return [false, [], '今日評論次數已達上限'];
        }
        if (!AppContent::where('id', $contentId)->value('id')) {
            return [false, [], '分享不存在或已刪除'];
        }
        if ($replyId && (!$replyUserId = self::where('id', $replyId)->value('comment_user_id'))) {
            return [false, [], '回復評論不存在或已刪除'];
        }
        $content = strip_tags($content);
        DB::beginTransaction();
        try {
            //用户今日评论次数
            redis()->incr($cacheCommentCountToday);
            //更新到数据库
            $comment = new self();
            $comment->content_id = $contentId;
            $comment->content_user_id = AppContent::where('id', $contentId)->value('user_id');
            $comment->comment_user_id = $appUserId;
            $comment->comment_content = $content;
            if ($replyId > 0) {
                $comment->reply_id = $replyId;
                $comment->reply_user_id = $replyUserId;
                $comment->parent_id = self::where('id', $replyId)->value('parent_id') ?: $replyId;
            }
            $comment->save();
            //分享评论列表
            $commentId = $comment->id;
            $time = time();
            $cacheContentComments = "app_content:{$contentId}:comments";
            redis()->zAdd($cacheContentComments, $time, $commentId);
            //用户评论列表-用于判断是否已评论
            $cacheMyComments = "app_user:{$appUserId}:content_comments:{$contentId}";
            redis()->zAdd($cacheMyComments, $time, $commentId);
            //更新统计
            AppContent::incrementComment($contentId);
            if (!empty($comment->parent_id)) {
                self::where('id', $comment->parent_id)->increment('reply_count');
            }
            DB::commit();
            return [true, ['comment_id' => $commentId], '評論成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('评论失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '評論失敗'];
        }
    }

    public static function delComment($appUserId, $contentId, $commentId)
    {
        if (!self::isComment($appUserId, $contentId, $commentId)) {
            return [false, [], '評論不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            //更新到数据库
            $comment = self::find($commentId);
            $deleteUserCommentIds = [];
            if ($comment->parent_id > 0) {
                // 1、查出所有子集评论
                $childComments = self::where('parent_id', $comment->parent_id)->select(['id', 'reply_id', 'comment_user_id'])->get()->toArray();
                // 2、构造子集树
                $childCommentTree = Tree::instance()->init($childComments, 'reply_id');
                // 3、获得当前评论ID的所有子集
                $deleteCommentIds = $childCommentTree->getChildrenIds($commentId, true);
                foreach ($childComments as $v) {
                    $deleteUserCommentIds[$v['comment_user_id']][] = $v['id'];
                }
            } else {
                $deleteCommentIds = [$commentId];
                $deleteUserCommentIds[$appUserId] = [$commentId];
            }
            //分享评论列表
            $cacheContentComments = "app_content:{$contentId}:comments";
            redis()->zRem($cacheContentComments, ...$deleteCommentIds);
            //用户评论列表
            foreach ($deleteUserCommentIds as $userId => $userCommentIds) {
                $cacheMyComments = "app_user:{$userId}:content_comments:{$contentId}";
                redis()->zRem($cacheMyComments, ...$userCommentIds);
            }

            //避免ID数量过多导致MySQL卡死，这里分块删除
            $chunkDeleteIds = array_chunk($deleteCommentIds, 100);
            foreach ($chunkDeleteIds as $chunkIds) {
                $comment->whereIn('id', $chunkIds)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            }

            //更新统计
            $decrReplyCount = count($deleteCommentIds);
            AppContent::decrementComment($contentId, $decrReplyCount);
            if ($comment->parent_id > 0) {
                self::where('id', $comment->parent_id)->decrement('reply_count', $decrReplyCount);
            }
            DB::commit();
            return [true, [], '刪除評論成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('刪除評論失敗：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '刪除評論失敗'];
        }
    }

    //前端評論組件 https://ext.dcloud.net.cn/plugin?id=16266
    //按照不支持分頁的做法去做，直接返回全部，暫時不考慮大體量評論的情況；需要了再重新設計數據結構和接口
    public static function getCommentList($search_data, $appUserId)
    {
        $contentId = $search_data['content_id'];
        self::ensureInitRedis($contentId);
        if (!AppContent::where('id', $contentId)->value('id')) {
            return [false, [], '分享不存在或已刪除'];
        }
        return [true, self::buildList($search_data, $appUserId), '獲取成功'];
    }

    protected static function buildList($search_data, $appUserId)
    {
        $contentId = $search_data['content_id'];
        $sortType = $search_data['sort_type'] ?? 0;

        $comments = self::select(['id', 'parent_id', 'reply_id', 'reply_user_id', 'comment_user_id', 'comment_content', 'created_at'])->where('content_id', $contentId)->get()->toArray();
        $comments = array_column($comments, null, 'id');

        //避免ID数量过多导致MySQL卡死，这里分块查询
        $commentUserIds = array_unique(array_column($comments, 'comment_user_id'));
        $replyUserIds = array_unique(array_filter(array_column($comments, 'reply_user_id')));
        $chunkUserIds = array_chunk(array_unique(array_merge($commentUserIds, $replyUserIds)), 100);
        $commentUsers = [];
        foreach ($chunkUserIds as $userIds) {
            $users = AppUser::select(['id', 'username', 'avatar'])->whereIn('id', $userIds)->get()->toArray();
            $users = array_column($users, null, 'id');
            $commentUsers = $commentUsers + $users; //数组合并，保留key
        }

        $tree = Tree::instance()->init($comments, 'reply_id');
        $treeArray = $tree->getTreeArray(0);
        foreach ($treeArray as $k => $v) {
            //追加分值
            $cacheContentComments = "app_content:{$contentId}:comments";
            if ($sortType == 0) {
                $treeArray[$k]['score'] = strtotime($v['created_at']);  //默认按照评论时间最新的排最前面
            } else {
                $treeArray[$k]['score'] = redis()->zScore($cacheContentComments, $v['id']);   //点赞高的排前面、其次就是评论时间新的排前面
            }
        }
        $sortByScore = array_column($treeArray, 'score');
        array_multisort($sortByScore, SORT_DESC, $treeArray);  //仅针对顶级评论进行排序
        $treeList = $tree->getTreeList($treeArray, 'content');

        $list = [];
        foreach ($treeList as $v) {
            $id = $v['id'];
            $commentInfo = $comments[$id] ?? [];
            if ($commentInfo) {
                $commentInfo['user_name'] = $commentUsers[$commentInfo['comment_user_id']]['username'] ?? '已注销';
                $commentInfo['user_avatar'] = $commentUsers[$commentInfo['comment_user_id']]['avatar'] ?? null;
                $commentInfo['reply_name'] = $commentInfo['reply_user_id'] > 0 ? ($commentUsers[$commentInfo['reply_user_id']]['username'] ?? '已注销') : null;
                $commentInfo['reply_avatar'] = $commentInfo['reply_user_id'] > 0 ? ($commentUsers[$commentInfo['reply_user_id']]['avatar'] ?? null) : null;
                $commentInfo['like_count'] = AppContentCommentLike::getCommentLikeCount($contentId, $commentInfo['id']);
                $commentInfo['is_like'] = (bool)AppContentCommentLike::isLike($appUserId, $contentId, $commentInfo['id']);
                $list[] = $commentInfo;
            }
        }

        return $list;
    }

    //增加点赞数量
    public static function incrementLike($commentId)
    {
        return self::where('id', $commentId)->increment('like_count', 1);
    }

    //减少点赞数量
    public static function decrementLike($commentId)
    {
        return self::withTrashed()->where('id', $commentId)->decrement('like_count', 1);
    }

    //刷新评论权重
    public static function refreshScore($comment)
    {
        $likeCount = AppContentCommentLike::getCommentLikeCount($comment->content_id, $comment->id);
        $newScore = self::calcCommentScore(strtotime($comment->created_at), $likeCount);
        $contentId = $comment->content_id;
        $cacheContentComments = "app_content:{$contentId}:comments";
        return redis()->zAdd($cacheContentComments, $newScore, $comment->id);
    }
}
