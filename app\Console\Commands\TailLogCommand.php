<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class TailLogCommand extends Command
{
    protected $signature = 'log:tail {--lines=50 : Number of lines to show}';

    protected $description = '实时查看日志文件';

    public function handle()
    {
        $logPath = storage_path('logs/laravel.log');

        if (!file_exists($logPath)) {
            $this->error("日志文件不存在: $logPath");
            return 1;
        }

        $lines = $this->option('lines');

        // 显示最后N行
        $this->info("显示最后 $lines 行日志:");
        $this->line(shell_exec("tail -n $lines $logPath"));

        // 实时跟踪日志
        $this->info("开始实时跟踪日志 (按 Ctrl+C 退出):");
        $this->line('');

        // 使用 passthru 来保持颜色输出
        passthru("tail -f $logPath");

        return 0;
    }
}
