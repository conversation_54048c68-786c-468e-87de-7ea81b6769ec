<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppContent;
use Illuminate\Http\Request;


class AppContentController extends Controller
{
    /**
     * 获取分享列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'            => 'sometimes|integer|gt:0',
            'per_page'        => 'sometimes|integer|between:1,200',
            'keyword'         => 'nullable|max:255',
            'created_start'   => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'     => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'sort_name'       => 'nullable|in:id,created_at,updated_at,view_count,like_count,comment_count',
            'sort_by'         => 'nullable|in:asc,desc',
            'pet_id'          => 'nullable|integer|exists:pets,id',
            'good_id'         => 'nullable|integer|exists:goods,id',
            'user_id'         => 'nullable|integer|exists:app_users,id',
            'visibility'      => 'nullable|in:public,friends,family,private',
            'tag_ids'         => 'nullable|string',
        ]);

        $records = AppContent::getList($request->all());
        return ResponseHelper::success($records);
    }

    /**
     * 删除分享
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = AppContent::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }


}
