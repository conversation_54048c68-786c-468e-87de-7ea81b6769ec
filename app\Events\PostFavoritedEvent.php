<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostFavoritedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 帖子 ID
     *
     * @var int
     */
    public $postId;

    /**
     * 帖子所有者 ID
     *
     * @var int
     */
    public $postOwnerId;

    /**
     * 收藏者 ID
     *
     * @var int
     */
    public $favoriterId;

    /**
     * Create a new event instance.
     *
     * @param int $postId 帖子 ID
     * @param int $postOwnerId 帖子所有者 ID
     * @param int $favoriterId 收藏者 ID
     * @return void
     */
    public function __construct($postId, $postOwnerId, $favoriterId)
    {
        $this->postId = $postId;
        $this->postOwnerId = $postOwnerId;
        $this->favoriterId = $favoriterId;
    }
}
