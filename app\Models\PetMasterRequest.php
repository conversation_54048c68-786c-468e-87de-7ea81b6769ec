<?php

namespace App\Models;

use App\Events\ParentAddedEvent;
use App\Events\PetFamilyRequestEvent;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;


/**
 * @mixin Builder
 */
class PetMasterRequest extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'pets_masters_requests';

    protected $fillable = [
        'from_user_id',
        'to_user_id',
        'pet_id',
        'status'
    ];


    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function user()
    {
        return $this->belongsTo(AppUser::class, 'from_user_id');
    }

    public function pet()
    {
        return $this->belongsTo(Pet::class, 'pet_id');
    }

    /**
     * 发送家人申请
     */
    public static function sendRequest($fromUserId, $toUserId, $petId)
    {
        if ($fromUserId == $toUserId) {
            return [false, [], '不能添加自己为家人'];
        }

        DB::beginTransaction();

        try {
            if (!PetMaster::isMaster($petId, $fromUserId)) {
                DB::rollBack();
                return [false, [], '你不是该宠物的家人'];
            }

            if (PetMaster::isMaster($petId, $toUserId)) {
                DB::rollBack();
                return [false, [], '该用户已经是该宠物的家人'];
            }

            // 检查是否有待处理的申请
            $pendingRequest = self::where('from_user_id', $fromUserId)
                ->where('to_user_id', $toUserId)
                ->where('status', 0)
                ->exists();

            if ($pendingRequest) {
                DB::rollBack();
                return [false, [], '已发送过家人申请,请等待对方处理'];
            }

            // 创建家人申请
            $familyRequest = self::create([
                'from_user_id' => $fromUserId,
                'to_user_id'   => $toUserId,
                'pet_id'       => $petId,
                'status'       => 0
            ]);
            $id = $familyRequest->id;

            if ($id > 0) {
                $success = true;
                $record = [];
                $record['id'] = $id;
                $message = '保存成功';
            } else {
                throw new \Exception('保存失败');
            }

            event(new PetFamilyRequestEvent($id, $fromUserId, $toUserId, $petId));

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function getRequest($userId, $requestId)
    {
        $familyRequest = self::where('id', $requestId)
            ->where('to_user_id', $userId)
            ->where('status', 0)
            ->with('user', function (Builder $query) {
                $query->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
            })
            ->with('pet', function (Builder $query) {
                $query->select([
                    'id',
                    'name',
                    'sex',
                    'avatar',
                    'type_id',
                    'breed_id',
                    'birthday',
                    'owner_id',
                    'neutered', // 新增：绝育状态字段
                    'vaccinated',
                    'birth_certificate',
                    'bloodline_certificate',
                    'vaccine_certificate',
                    'health_report',
                    'weight',
                    'weight_status',
                    'active_status',
                    'is_pregnant',
                    'is_ill',
                    'is_dead',
                    'chip_code',
                    'chip_implant_date',
                ])
                    ->with('petType', function (Builder $query) {
                        $query->select('id', 'name', 'name_tc', 'name_en');
                    })
                    ->with('petBreed', function (Builder $query) {
                        $query->select('id', 'name', 'name_tc', 'name_en');
                    });
            })
            ->first();
        if (!$familyRequest) {
            return [false, [], '申请不存在或已处理'];
        }
        return [true, $familyRequest, '成功'];
    }

    /**
     * 处理家人申请
     */
    public static function handleRequest($userId, $requestId, $action)
    {
        DB::beginTransaction();

        try {
            $familyRequest = self::where('id', $requestId)
                ->where('to_user_id', $userId)
                ->where('status', 0)
                ->first();
            if (!$familyRequest) {
                DB::rollBack();
                return [false, [], '申请不存在或已处理'];
            }

            if ($action == 'accept') {
                $fromUserId = $familyRequest->from_user_id;
                $petId = $familyRequest->pet_id;

                if (!PetMaster::isMaster($petId, $userId)) {
                    PetMaster::addMaster($petId, $userId);

                    event(new ParentAddedEvent($petId, $fromUserId, $userId));
                }

                $familyRequest->update(['status' => 1]);
                $message = '已添加家人';
            } else {
                $familyRequest->update(['status' => 2]);
                $message = '已拒绝申请';
            }

            $success = true;
            $record = array();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

}
