<?php

namespace App\Services\PetCalculator;

class CatEnergyDemand extends EnergyDemand
{
    //计算基础能量需求
    protected function getBaseEnergyDemand(): int
    {
        $diff = $this->getBirthDiff();
        // 修正年龄判断：只有12个月以内才是幼年期
        if ($diff->y == 0) {
            $result = $this->getInfancy();
        } elseif ($diff->y <= 7) {
            $result = $this->getAdulthood();
        } elseif ($diff->y <= 10) {
            $result = $this->getMaturityPeriod();
        } else {
            $result = $this->getOldAge();
        }
        return intval($result);
    }

    //幼年期（0-12 個月）
    protected function getInfancy()
    {
        /**
         * 1 個月：0.36 × 240
         * 2 個月：BW × 210
         * 3 個月：BW × 200
         * 4 個月：BW × 175
         * 5 個月：1.96 × 145
         * 6 個月：BW × 135
         * 7 個月：BW × 120
         * 8 個月：4.8 × 110
         * 9 個月：BW × 100
         * 10 個月：6 × 95
         * 11 個月：6 × 90
         * 12 個月：6 × 85
         */
        $diff = $this->getBirthDiff();
        switch ($diff->m) {
            case 1:
                $result = 0.36 * 240;
                break;
            case 2:
                $result = $this->pet['weight'] * 210;
                break;
            case 3:
                $result = $this->pet['weight'] * 200;
                break;
            case 4:
                $result = $this->pet['weight'] * 175;
                break;
            case 5:
                $result = 1.96 * 145;
                break;
            case 6:
                $result = $this->pet['weight'] * 135;
                break;
            case 7:
                $result = $this->pet['weight'] * 120;
                break;
            case 8:
                $result = 4.8 * 110;
                break;
            case 9:
                $result = $this->pet['weight'] * 100;
                break;
            default:
                $result = $this->getWeightMax() * 95;
                break;
        }
        return $result;
    }

    //成年期（1-7 歲）
    protected function getAdulthood()
    {
        //公式 = 1.0-1.6 × 70 × BW^0.75
        return 1.6 * $this->getRestingEnergyRequirement();
    }

    //熟年期（7-10 歲）
    protected function getMaturityPeriod()
    {
        //公式 = 1.1-1.4 × 70 × BW^0.75
        return 1.4 * $this->getRestingEnergyRequirement();
    }

    //老齡期（11-14 歲）及高齡期（15 歲以上）
    protected function getOldAge()
    {
        //公式 = 1.1-1.6 × 70 × BW^0.75
        return 1.1 * $this->getRestingEnergyRequirement();
    }
}
