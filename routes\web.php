<?php

use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/delay/{seconds}', function ($seconds) {
    sleep(intval($seconds));
    return "Delayed response by {$seconds} seconds";
});

//支付
Route::prefix('/payment')
    ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class])
    ->group(function () {
        Route::get('/return_page', [\App\Http\Controllers\PaymentController::class, 'return_page'])->middleware(['log.response:simple']);
        Route::get('/failed_page', [\App\Http\Controllers\PaymentController::class, 'failed_page'])->middleware(['log.response:simple']);
    });
