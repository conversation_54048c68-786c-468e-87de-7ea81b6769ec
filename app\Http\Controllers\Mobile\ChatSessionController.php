<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\Pet;
use App\Services\AliBailian\AliBailianService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ChatSessionController extends Controller
{
    /**
     * AI服务
     *
     * @var AliBailianService
     */
    protected $aiService;

    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        $this->aiService = $aliBailianService;
    }

    /**
     * 获取用户的聊天会话列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSessions(Request $request)
    {
        $request->validate([
            'page' => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,100',
            'analysis_type' => 'sometimes|integer|in:0,1,2,3,4', // 添加分析类型筛选参数
        ]);

        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        $userId = Auth::id();

        // 构建查询
        $query = ChatSession::where('user_id', $userId)
            ->where('status', 1);

        // 如果提供了分析类型参数，添加筛选条件
        if ($request->has('analysis_type')) {
            $analysisType = $request->input('analysis_type');
            $query->where('analysis_type', $analysisType);
        }

        // 如果提供了宠物ID参数，添加筛选条件
        if ($request->has('pet_id')) {
            $petId = $request->input('pet_id');
            // 使用 JSON_CONTAINS 查询宠物ID数组
            $query->whereJsonContains('pet_ids', (int)$petId);
        }

        // 获取会话列表
        $sessions = $query->orderBy('last_message_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // 获取每个会话的最后一条消息和宠物信息
        $sessions->getCollection()->transform(function ($session) {
            $lastMessage = ChatMessage::where('session_id', $session->session_id)
                ->where('status', 1)
                ->orderBy('created_at', 'desc')
                ->first();

            $session = $session->toArray();
            $session['last_message'] = $lastMessage ? [
                'content' => $lastMessage->content,
                'role' => $lastMessage->role,
                'created_at' => $lastMessage->created_at
            ] : null;

            // 添加宠物信息（保证数据完整性，不过滤owner_id）
            $petIds = $session['pet_ids'] ?? [];
            if (!empty($petIds)) {
                $pets = Pet::whereIn('id', $petIds)
                    ->with(['petType', 'petBreed'])
                    ->select('id', 'name', 'avatar', 'type_id', 'breed_id')
                    ->get();

                // 转换为标准格式
                $petsArray = [];
                foreach ($pets as $pet) {
                    $petsArray[] = [
                        'id' => $pet->id,
                        'name' => $pet->name,
                        'avatar' => $pet->avatar,
                        'type' => $pet->petType?->name,
                        'type_en' => $pet->petType?->name_en,
                        'breed' => $pet->petBreed?->name,
                    ];
                }
                $session['pets'] = $petsArray;
            } else {
                $session['pets'] = [];
            }

            return $session;
        });

        return ResponseHelper::success($sessions);
    }

    /**
     * 创建新的聊天会话
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createSession(Request $request)
    {
        $request->validate([
            'pet_ids' => 'nullable|array',
            'pet_ids.*' => 'integer|exists:pets,id',
            'analysis_type' => 'nullable|integer|in:0,1,2,3,4',
            'title' => 'nullable|string|max:255',
        ]);

        $userId = Auth::id();
        $petIds = $request->input('pet_ids');
        $analysisType = $request->input('analysis_type', 0);
        $title = $request->input('title');

        $session = ChatSession::createSession($userId, $petIds, $analysisType, $title);

        return ResponseHelper::success([
            'session_id' => $session->session_id,
            'created_at' => $session->created_at
        ]);
    }

    /**
     * 获取会话详情及消息列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSessionDetail(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'page' => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,100',
        ]);

        $sessionId = $request->input('session_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 50);
        $userId = Auth::id();

        // 获取会话信息
        $session = ChatSession::where('session_id', $sessionId)
            ->where('user_id', $userId)
            ->where('status', 1)
            ->first();

        if (!$session) {
            return ResponseHelper::error('会话不存在或已删除');
        }

        // 获取消息列表
        $messages = ChatMessage::where('session_id', $sessionId)
            ->where('status', 1)
            ->orderBy('created_at', 'asc')
            ->paginate($perPage, ['*'], 'page', $page);

        // 获取会话中涉及的所有宠物ID（保证数据一致性）
        $allSessionPetIds = collect();
        $sessionPetIds = $session->pet_ids ?? [];
        if (!empty($sessionPetIds)) {
            $allSessionPetIds = $allSessionPetIds->merge($sessionPetIds);
        }

        // 从消息中收集所有涉及的宠物ID
        $messagesPetIds = ChatMessage::where('session_id', $sessionId)
            ->where('status', 1)
            ->whereNotNull('session_pet_ids')
            ->pluck('session_pet_ids')
            ->flatten()
            ->unique()
            ->filter();

        $allSessionPetIds = $allSessionPetIds->merge($messagesPetIds)->unique();

        // 获取所有相关宠物信息（不限制owner_id，保证完整性）
        $allPets = collect();
        if ($allSessionPetIds->isNotEmpty()) {
            $allPets = Pet::whereIn('id', $allSessionPetIds->toArray())
                ->with(['petType', 'petBreed'])
                ->get()
                ->keyBy('id');
        }

        // 为所有消息添加宠物信息（前端可选择是否展示）
        $messages->getCollection()->transform(function ($message) use ($allPets) {
            $messageArray = $message->toArray();

            // 为所有消息添加宠物信息（使用完整的宠物数据）
            $messagePets = $this->buildPetDetails($message->session_pet_ids ?? [], $allPets);
            $messageArray['pets'] = $messagePets;

            return $messageArray;
        });

        // 获取会话关联的宠物信息（创建时指定的）
        $sessionPetsInfo = [];
        if (!empty($session->pet_ids)) {
            $pets = Pet::whereIn('id', $session->pet_ids)
                ->with(['petType', 'petBreed'])
                ->get();

            foreach ($pets as $pet) {
                $petData = [
                    'id' => $pet->id,
                    'name' => $pet->name,
                    'type' => $pet->petType ? $pet->petType->name : null,
                    'type_en' => $pet->petType ? $pet->petType->name_en : null,
                    'breed' => $pet->petBreed ? $pet->petBreed->name : null,
                    'avatar' => $pet->avatar,
                    'sex' => $pet->sex == 1 ? '公' : '母',
                    'age' => $pet->birthday ? now()->diffInYears($pet->birthday) : null,
                    'weight' => $pet->weight
                ];
                $sessionPetsInfo[] = $petData;
            }
        }

        return ResponseHelper::success([
            'session' => $session,
            'messages' => $messages,
            'pets_info' => $sessionPetsInfo // 会话创建时指定的宠物（保持向后兼容）
        ]);
    }

    /**
     * 删除聊天会话
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteSession(Request $request)
    {
        $request->validate([
            'session_id' => 'required|string|exists:chat_sessions,session_id',
        ]);

        $sessionId = $request->input('session_id');
        $userId = Auth::id();

        // 检查会话是否存在且属于当前用户
        $session = ChatSession::where('session_id', $sessionId)
            ->where('user_id', $userId)
            ->where('status', 1)
            ->first();

        if (!$session) {
            return ResponseHelper::error('会话不存在或已删除');
        }

        // 设置会话状态为已删除
        $session->status = 0;
        $session->save();

        return ResponseHelper::success(true, '删除成功');
    }



    /**
     * 获取会话上下文（最近10条消息）
     *
     * @param string $sessionId
     * @return array
     */
    protected function getSessionContext($sessionId)
    {
        $messages = ChatMessage::where('session_id', $sessionId)
            ->where('status', 1)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->sortBy('created_at');

        $context = [];

        foreach ($messages as $message) {
            $role = $message->role == 1 ? 'user' : 'assistant';
            $context[] = [
                'role' => $role,
                'content' => $message->content
            ];
        }

        return $context;
    }


    /**
     * 根据宠物ID列表构建宠物详情
     *
     * @param array $petIds 宠物ID列表
     * @param \Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection $pets 宠物集合
     * @return array
     */
    protected function buildPetDetails($petIds, $pets)
    {
        if (empty($petIds)) {
            return [];
        }

        $result = [];
        foreach ($petIds as $petId) {
            if (isset($pets[$petId])) {
                $pet = $pets[$petId];
                $result[] = [
                    'id' => $pet->id,
                    'name' => $pet->name,
                    'type' => $pet->petType?->name,
                    'type_en' => $pet->petType?->name_en,
                    'breed' => $pet->petBreed?->name,
                    'avatar' => $pet->avatar,
                    'sex' => $pet->sex == 1 ? '公' : '母',
                    'age' => $pet->birthday ? now()->diffInYears($pet->birthday) : null,
                    'weight' => $pet->weight
                ];
            }
        }

        return $result;
    }
}
