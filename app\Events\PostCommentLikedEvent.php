<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostCommentLikedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 评论 ID
     *
     * @var int
     */
    public $commentId;

    /**
     * 内容 ID
     *
     * @var int
     */
    public $contentId;

    /**
     * 评论所有者 ID
     *
     * @var int
     */
    public $commentOwnerId;

    /**
     * 点赞者 ID
     *
     * @var int
     */
    public $likerId;

    /**
     * Create a new event instance.
     *
     * @param int $commentId 评论 ID
     * @param int $contentId 内容 ID
     * @param int $commentOwnerId 评论所有者 ID
     * @param int $likerId 点赞者 ID
     * @return void
     */
    public function __construct($commentId, $contentId, $commentOwnerId, $likerId)
    {
        $this->commentId = $commentId;
        $this->contentId = $contentId;
        $this->commentOwnerId = $commentOwnerId;
        $this->likerId = $likerId;
    }
}
