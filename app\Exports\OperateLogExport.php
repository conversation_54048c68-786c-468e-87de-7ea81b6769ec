<?php

namespace App\Exports;

use App\Events\DataChanged;
use App\Models\OperateLog;
use App\Providers\OperateLinkModelProvider;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OperateLogExport implements FromCollection, WithHeadings
{
    private $formData;

    public function __construct(array $formData)
    {
        $this->formData = $formData;
    }

    public function collection()
    {
        $records = OperateLog::getList($this->formData); //后台导出
        $cellData = array_map(function ($v) {
            return [
                $v['supervisor']['name'],
                DataChanged::OPERATE_TYPE_TEXT_LIST[$v['operate_type']],
                $v['title'],
                OperateLinkModelProvider::getLinkName($v['link_type']),
                $v['created_at'],
                $v['content'],
            ];
        }, $records);
        return Collection::make($cellData);
    }

    public function headings(): array
    {
        return ['操作人', '操作類型', '操作標題', '關聯類型', '操作時間', '操作内容'];
    }
}
