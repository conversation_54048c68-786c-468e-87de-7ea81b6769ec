<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FriendHandledEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 发起人 ID
     *
     * @var int
     */
    public $fromUserId;

    /**
     * 关注者 ID
     *
     * @var int
     */
    public $toUserId;

    /**
     * @var string
     */
    public $action;

    /**
     * Create a new event instance.
     *
     * @param int    $fromUserId 被关注的用户 ID
     * @param int    $toUserId   关注者 ID
     * @param string $action     处理类型
     * @return void
     */
    public function __construct($fromUserId, $toUserId, $action)
    {
        $this->fromUserId = $fromUserId;
        $this->toUserId = $toUserId;
        $this->action = $action;
    }
}
