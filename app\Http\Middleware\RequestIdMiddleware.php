<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * 自动在所有请求加上 requestId 方便查看错误
 */
class RequestIdMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // 生成唯一的 requestId
        $requestId = Str::uuid()->toString();

        // 将 requestId 存储在请求属性中
        $request->requestId = $requestId;

        // 将 requestId 设置到响应头中，以便客户端获取
        $response = $next($request);
        $response->headers->set('X-Request-ID', $requestId);

        return $response;
    }
}
