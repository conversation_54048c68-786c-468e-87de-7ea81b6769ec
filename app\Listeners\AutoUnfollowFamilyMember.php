<?php

namespace App\Listeners;

use App\Events\ParentRemovedEvent;
use App\Models\AppUserFollow;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class AutoUnfollowFamilyMember
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\ParentRemovedEvent  $event
     * @return void
     */
    public function handle(ParentRemovedEvent $event)
    {
        $ownerId = $event->ownerId;
        $parentId = $event->parentId;

        // 检查两人是否还有其他家人关系（通过其他宠物）
        $stillFamily = \App\Models\PetMaster::isFamily($ownerId, $parentId);
        
        // 如果不再是家人关系，则自动取消关注
        if (!$stillFamily) {
            // 取消宠物主人对家人的关注
            if (AppUserFollow::isFollowing($ownerId, $parentId)) {
                AppUserFollow::cancelFollowing($ownerId, $parentId);
            }

            // 取消家人对宠物主人的关注
            if (AppUserFollow::isFollowing($parentId, $ownerId)) {
                AppUserFollow::cancelFollowing($parentId, $ownerId);
            }
        }
    }
}
