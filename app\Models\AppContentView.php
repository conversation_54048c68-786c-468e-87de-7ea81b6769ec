<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class AppContentView extends Model
{
    use HasFactory;

    protected $table = 'app_contents_views';

    protected $fillable = [
        'content_id',
        'content_user_id',
        'pvuv_user_id',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($contentId)
    {
        //分享PVUV列表
        $cachePvKey = "app_content:{$contentId}:pv";
        $cacheUvKey = "app_content:{$contentId}:uv";
        $pvuvList = self::where('content_id', $contentId)->select('pvuv_user_id', 'created_at')->get()->toArray();
        redis()->del($cachePvKey, $cacheUvKey);
        if ($pvuvList) {
            redis()->incrBy($cachePvKey, count($pvuvList));
            redis()->sAddArray($cacheUvKey, array_column($pvuvList, 'pvuv_user_id'));
        }
        //更新为初始化
        $cacheInitKey = "app_content:{$contentId}:init_view";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($contentId)
    {
        $cacheInitKey = "app_content:{$contentId}:init_view";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($contentId); //数据有问题时把key删除，重新初始化
    }

    public static function getViewCount($contentId)
    {
        self::ensureInitRedis($contentId);
        $cachePvKey = "app_content:{$contentId}:pv";
        return (int)redis()->get($cachePvKey);
    }

    public static function addPvuv($appUserId, $contentId)
    {
        self::ensureInitRedis($contentId);
        DB::beginTransaction();
        try {
            //pv
            $cachePvKey = "app_content:{$contentId}:pv";
            redis()->incr($cachePvKey);
            //uv
            $cacheUvKey = "app_content:{$contentId}:uv";
            redis()->sAdd($cacheUvKey, $appUserId);
            //更新到数据库
            $pvuv = new self();
            $pvuv->content_id = $contentId;
            $pvuv->content_user_id = AppContent::where('id', $contentId)->value('user_id');
            $pvuv->pvuv_user_id = $appUserId;
            $pvuv->save();
            //更新统计
            AppContent::incrementView($contentId);
            DB::commit();
            return [true, [], '操作成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('分享PVUV记录失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '操作失敗'];
        }
    }
}
