<?php

return [

    /*
    |--------------------------------------------------------------------------
    |驗證語言行
    |--------------------------------------------------------------------------
    |
    |以下語言行包含的默認錯誤消息由
    |驗證器類。 其中一些規則有多個版本,例如
    |作為大小規則。 請隨意調整此處的每條消息。
    |
    */

    'accepted'             => '必須接受:attribute。',
    'accepted_if'          => '當:other為:value時,必須接受:attribute。',
    'active_url'           => ':attribute不是有效的url。',
    'after'                => ':attribute必須是:date之後的日期。',
    'after_or_equal'       => ':attribute必須是在:date之後或等於:date的日期。',
    'alpha'                => ':attribute只能包含字母。',
    'alpha_dash'           => ':attribute只能包含字母、數位、短劃線和底線。',
    'alpha_num'            => ':attribute只能包含字母和數位。',
    'array'                => ':attribute必須是數組。',
    'ascii'                => ':attribute只能包含單位元組字母數位字元和符號。',
    'before'               => ':attribute必須是:date之前的日期。',
    'before_or_equal'      => ':attribute的日期必須早於或等於:date。',
    'between'              => [
        'array'   => ':attribute必須具有介於:min和:max之間的項。',
        'file'    => ':attribute必須介於:min和:max千位元組之間。',
        'numeric' => ':attribute必須介於:min和:max之間',
        'string'  => ':attribute必須介於:min和:max個字元之間。',
    ],
    'boolean'              => ':attribute欄位必須為true或false。',
    'confirmed'            => ':attribute確認不匹配。',
    'current_password'     => '密碼不正確。',
    'date'                 => ':attribute不是有效的日期。',
    'date_equals'          => ':attribute的日期必須等於:date。',
    'date_format'          => ':attribute格式與:format不匹配。',
    'decimal'              => ':attribute必須有:decimal小數位數。',
    'declined'             => '必須拒絕:attribute。',
    'declined_if'          => '當:other為:value時,必須拒絕:attribute。',
    'different'            => ':attribute和:other必須不同。',
    'digits'               => ':attribute必須是:digits格式。',
    'digits_between'       => ':attribute必須介於:min和:max數位之間。',
    'dimensions'           => ':attribute具有無效的影像維度。',
    'distinct'             => ':attribute欄位具有重複值。',
    'doesnt_end_with'      => ':attribute不能以以下值之一結尾。',
    'doesnt_start_with'    => ':attribute不能以以下值之一開頭。',
    'email'                => ':attribute必須是有效的電子郵寄地址。',
    'ends_with'            => ':attribute必須以以下值之一結尾。',
    'enum'                 => '所選的:attribute無效。',
    'exists'               => '選定的:attribute無效。',
    'file'                 => ':attribute必須是檔案。',
    'filled'               => ':attribute欄位必須有值。',
    'gt'                   => [
        'array'   => ':attribute必須具有多個:value項。',
        'file'    => ':attribute必須大於:value千位元組。',
        'numeric' => ':attribute必須大於:value。',
        'string'  => ':attribute必須大於:value個字元。',
    ],
    'gte'                  => [
        'array'   => ':attribute必須具有:value項或更多項。',
        'file'    => ':attribute必須大於或等於:value千位元組。',
        'numeric' => ':attribute必須大於或等於:value。',
        'string'  => ':attribute必須大於或等於:value個字元。',
    ],
    'image'                => ':attribute必須是影像。',
    'in'                   => '所選的:attribute無效。',
    'in_array'             => ':attribute欄位不存在於:other中。',
    'integer'              => ':attribute必須是整數。',
    'ip'                   => ':attribute必須是有效的ip地址。',
    'ipv4'                 => ':attribute必須是有效的ipv4地址。',
    'ipv6'                 => ':attribute必須是有效的ipv6地址。',
    'json'                 => ':attribute必須是有效的json字串。',
    'lowercase'            => ':attribute必須是小寫的。',
    'lt'                   => [
        'array'   => ':attribute的項數必須少於:value。',
        'file'    => ':attribute必須小於:value千位元組。',
        'numeric' => ':attribute必須小於:value。',
        'string'  => ':attribute必須少於:value個字元。',
    ],
    'lte'                  => [
        'array'   => ':attribute的項數不能超過:value。',
        'file'    => ':attribute必須小於或等於:value千位元組。',
        'numeric' => ':attribute必須小於或等於:value。',
        'string'  => ':attribute必須小於或等於:value個字元。',
    ],
    'mac_address'          => ':attribute必須是有效的mac地址。',
    'max'                  => [
        'array'   => ':attribute的項數不得超過:max。',
        'file'    => ':attribute不得大於:max KB。',
        'numeric' => ':attribute不能大於:max',
        'string'  => ':attribute不能大於:max個字元。',
    ],
    'max_digits'           => ':attribute的位數不能超過:max。',
    'mimes'                => ':attribute必須是類型為:values的檔案。',
    'mimetypes'            => ':attribute必須是類型為:values的檔案。',
    'min'                  => [
        'array'   => ':attribute必須至少有:min個項。',
        'file'    => ':attribute必須至少為:min KB。',
        'numeric' => ':attribute必須至少為:min.',
        'string'  => ':attribute必須至少包含:min個字元。',
    ],
    'min_digits'           => ':attribute必須至少有:min個數位。',
    'missing'              => '必須缺少:attribute欄位。',
    'missing_if'           => '當:other為:value時,必須缺少:attribute欄位。',
    'missing_unless'       => '必須缺少:attribute欄位,除非:other為:value。',
    'missing_with'         => '存在:values時,必須缺少:attribute欄位。',
    'missing_with_all'     => '存在:value時,必須缺少:attribute欄位。',
    'multiple_of'          => ':attribute必須是:value的倍數。',
    'not_in'               => '選定的:attribute無效。',
    'not_regex'            => ':attribute格式無效。',
    'numeric'              => ':attribute必須是數位。',
    'password'             => [
        'letters'       => ':attribute必須至少包含一個字母。',
        'mixed'         => ':attribute必須至少包含一個大寫字母和一個小寫字母。',
        'numbers'       => ':attribute必須至少包含一個數位。',
        'symbols'       => ':attribute必須至少包含一個符號。',
        'uncompromised' => '給定的:attribute出現在數據洩漏中。 請選擇其他:attribute。',
    ],
    'present'              => ':attribute欄位必須存在。',
    'prohibited'           => '禁止使用:attribute欄位。',
    'prohibited_if'        => '當:other為:value時,禁止使用:attribute欄位。',
    'prohibited_unless'    => '禁止使用:attribute欄位,除非:other在:values中。',
    'prohibits'            => ':attribute欄位禁止出現:other。',
    'regex'                => ':attribute格式無效。',
    'required'             => '需要:attribute欄位。',
    'required_array_keys'  => ':attribute欄位必須包含：:value的條目。',
    'required_if'          => '當:other為:value時,需要:attribute欄位。',
    'required_if_accepted' => '接受:other時,需要:attribute欄位。',
    'required_except'      => ':attribute欄位是必需的,除非:other在:values中。',
    'required_with'        => '存在:value時需要:attribute欄位。',
    'required_with_all'    => '當存在:value時,需要:attribute欄位。',
    'required_without'     => '當不存在:value時,需要:attribute欄位。',
    'required_without_all' => '當不存在:value時,需要:attribute欄位。',
    'same'                 => ':attribute和:other必須匹配。',
    'size'                 => [
        'array'   => ':attribute必須包含:size項。',
        'file'    => ':attribute必須為:size KB。',
        'numeric' => ':attribute必須是:size。',
        'string'  => ':attribute必須是:size字元。',
    ],
    'starts_with'          => ':attribute必須以以下值之一開頭。',
    'string'               => ':attribute必須是字串。',
    'timezone'             => ':attribute必須是有效的時區。',
    'unique'               => '已獲取:attribute。',
    'uploaded'             => '無法上載:attribute。',
    'capital'              => ':attribute必須是大寫的。',
    'url'                  => ':attribute必須是有效的url。',
    'ulid'                 => ':attribute必須是有效的ulid。',
    'uuid'                 => ':attribute必須是有效的uuid。',

    /*
    |--------------------------------------------------------------------------
    |自定義驗證語言行
    |--------------------------------------------------------------------------
    |
    |在這裡，您可以使用為内容指定自定義驗證消息
    |約定“attribute.rule”來命名行。 這使得它很快
    |為給定的内容規則指定特定的自定義語言行。
    |
    */
    'longitude'            => ':attribute必須是有效的經度。',
    'latitude'             => ':attribute必須是有效的緯度。',

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    |自定義驗證内容
    |--------------------------------------------------------------------------
    |
    |以下語言行用於交換我們的内容預留位置
    |使用更便於閱讀的內容，如“電子郵寄地址”
    |替换“email”。 這只是幫助我們使我們的資訊更具表達力。
    |
    */

    'attributes' => [],

];
