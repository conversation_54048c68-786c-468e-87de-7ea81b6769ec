<?php

namespace App\Models;

use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use DateTimeInterface;

/**
 * @mixin Builder
 */
class ChatMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'session_id',
        'user_id',
        'role',
        'content',
        'images',
        'metadata',
        'session_pet_ids',
        'status'
    ];

    protected $casts = [
        'images' => 'array',
        'metadata' => 'array',
        'session_pet_ids' => 'array',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 获取消息所属的会话
     */
    public function session()
    {
        return $this->belongsTo(ChatSession::class, 'session_id', 'session_id');
    }

    /**
     * 获取消息关联的用户
     */
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 添加用户消息
     */
    public static function addUserMessage($sessionId, $userId, $content, $images = null, $sessionPetIds = null)
    {
        // 更新会话最后消息时间
        ChatSession::where('session_id', $sessionId)->update([
            'last_message_at' => now()
        ]);

        return self::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'role' => 1, // 用户
            'content' => $content,
            'images' => $images,
            'session_pet_ids' => $sessionPetIds,
            'status' => 1
        ]);
    }

    /**
     * 添加AI消息
     *
     * @param string $sessionId 会话 ID
     * @param int $userId 用户 ID
     * @param string|null $content 消息内容，可以为空
     * @param array|null $metadata 元数据
     * @param array|null $sessionPetIds 消息发送时的宠物ID列表
     * @return ChatMessage 创建的消息对象
     */
    public static function addAIMessage($sessionId, $userId, $content = null, $metadata = null, $sessionPetIds = null)
    {
        // 更新会话最后消息时间
        ChatSession::where('session_id', $sessionId)->update([
            'last_message_at' => now()
        ]);

        // 如果内容为空，设置一个默认值
        if ($content === null || $content === '') {
            $content = '已收到您的消息，正在處理中...';  // "已收到您的消息，正在處理中..."
        }

        return self::create([
            'session_id' => $sessionId,
            'user_id' => $userId,
            'role' => 2, // AI
            'content' => $content,
            'metadata' => $metadata,
            'session_pet_ids' => $sessionPetIds,
            'status' => 1
        ]);
    }
}
