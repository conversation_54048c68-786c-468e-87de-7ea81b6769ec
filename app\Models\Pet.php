<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Events\PetChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Pet extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 模型启动方法
     */
    protected static function boot()
    {
        parent::boot();

        // 当宠物信息更新时，使缓存失效
        static::updated(function ($pet) {
            \App\Models\GoodRecommendation::clearCache([$pet->id]);
        });

        // 当宠物被删除时，使缓存失效
        static::deleted(function ($pet) {
            \App\Models\GoodRecommendation::clearCache([$pet->id]);
        });
    }

    protected $fillable = [
        'name',
        'description',
        'sex',
        'avatar',
        'type_id',
        'breed_id',
        'birthday',
        'user_id',
        'owner_id',
        'neutered',
        'vaccinated',
        'latitude',
        'longitude',
        'city_code',
        'location',
        'father_id',
        'mother_id',
        'birth_certificate',
        'bloodline_certificate',
        'vaccine_certificate',
        'health_report',
        'weight',
        'weight_status',
        'active_status',
        'is_pregnant',
        'is_ill',
        'is_dead',
        // 新增证件审核状态字段
        'birth_certificate_status',
        'vaccine_card_status',
        'chip_code_status',
        'medical_record_status',
        // 新增晶片编码相关字段
        'chip_code',
        'chip_implant_date',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function owner()
    {
        return $this->belongsTo(AppUser::class, 'owner_id');
    }

    public function petType()
    {
        return $this->belongsTo(PetType::class, 'type_id');
    }

    public function petBreed()
    {
        return $this->belongsTo(PetBreed::class, 'breed_id');
    }

    public function petFather()
    {
        return $this->belongsTo(Pet::class, 'father_id');
    }

    public function petMother()
    {
        return $this->belongsTo(Pet::class, 'mother_id');
    }

    public function petMasters()
    {
        return $this->hasMany(PetMaster::class, 'pet_id')->whereNull('deleted_at');
    }

    public function petLogs()
    {
        return $this->hasMany(PetLog::class, 'pet_id');
    }

    /**
     * 关联宠物证件
     */
    public function certificates()
    {
        return $this->hasMany(PetCertificate::class, 'pet_id');
    }

    /**
     * 关联疫苗记录
     */
    public function vaccineRecords()
    {
        return $this->hasMany(PetVaccineRecord::class, 'pet_id');
    }

    /**
     * 关联医疗档案
     */
    public function medicalRecords()
    {
        return $this->hasMany(PetMedicalRecord::class, 'pet_id');
    }

    /**
     * 获取出生证明
     */
    public function birthCertificate()
    {
        return $this->hasOne(PetCertificate::class, 'pet_id')
                    ->where('certificate_type', PetCertificate::TYPE_BIRTH_CERTIFICATE)
                    ->latest();
    }

    /**
     * 获取疫苗针卡
     */
    public function vaccineCard()
    {
        return $this->hasOne(PetCertificate::class, 'pet_id')
                    ->where('certificate_type', PetCertificate::TYPE_VACCINE_CARD)
                    ->latest();
    }

    /**
     * 获取晶片编码
     */
    public function chipCode()
    {
        return $this->hasOne(PetCertificate::class, 'pet_id')
                    ->where('certificate_type', PetCertificate::TYPE_CHIP_CODE)
                    ->latest();
    }

    public static function getList($search_data, $requestAppUserId = null)
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $appUserId = $search_data['user_id'] ?? "";
        $ownerId = $search_data['owner_id'] ?? "";
        $masterId = $search_data['master_id'] ?? "";
        $isLost = $search_data['is_lost'] ?? "";
        $sex = $search_data['sex'] ?? "";
        $typeId = $search_data['type_id'] ?? "";
        $breedId = $search_data['breed_id'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $masterPetIds = PetMaster::getPetIds($masterId, $sortBy);
        $list = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%");
            });
        })
            ->when($appUserId !== "", function (Builder $query) use ($appUserId) {
                $query->where('user_id', $appUserId);
            })
            ->when($ownerId !== "", function (Builder $query) use ($ownerId) {
                $query->where('owner_id', $ownerId);
            })
            ->when($isLost !== "", function (Builder $query) use ($isLost) {
                $query->where('is_lost', $isLost);
            })
            ->when($sex !== "", function (Builder $query) use ($sex) {
                $query->where('sex', $sex);
            })
            ->when($typeId, function (Builder $query) use ($typeId) {
                $query->where('type_id', $typeId);
            })
            ->when($breedId, function (Builder $query) use ($breedId) {
                $query->where('breed_id', $breedId);
            })
            ->when($masterId !== "", function (Builder $query) use ($masterPetIds) {
                $query->whereIn('id', array_keys($masterPetIds));
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName == 'sort_order' ? 'id' : $sortName, $sortBy);
            })
            ->with('owner', function (Builder $query) {
                $query->select('id', 'username', 'chinese_name', 'english_name');
            })
            ->with('petType', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en');
            })
            ->with('petBreed', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en', 'weight_max_male', 'weight_max_female');
            })
            ->with('petFather', function (Builder $query) {
                $query->select('id', 'name', 'avatar', 'type_id', 'breed_id');
            })
            ->with('petMother', function (Builder $query) {
                $query->select('id', 'name', 'avatar', 'type_id', 'breed_id');
            })
            ->with('petMasters', function (Builder $query) {
                $query->with([
                    'user' => function ($query) {
                        $query->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                    }
                ]);
            })
            ->select([
                'id',
                'name',
                'sex',
                'avatar',
                'type_id',
                'breed_id',
                'birthday',
                'owner_id',
                'neutered', // 新增：绝育状态字段
                'vaccinated',
                'birth_certificate',
                'bloodline_certificate',
                'vaccine_certificate',
                'health_report',
                'weight',
                'weight_status',
                'active_status',
                'is_pregnant',
                'is_ill',
                'is_dead',
                // 新增：四个证件审核状态字段
                'birth_certificate_status',
                'vaccine_card_status',
                'chip_code_status',
                'medical_record_status',
                // 新增：晶片编码相关字段
                'chip_code',
                'chip_implant_date',
            ])
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);

        // 批量获取所有宠物的最新相册照片，避免N+1查询
        $petIds = $list->pluck('id')->toArray();
        $latestAlbumPhotos = [];

        if (!empty($petIds)) {
            // 获取包含指定宠物的最新相册照片（适配合并表结构）
            $latestAlbumPhotos = [];

            foreach ($petIds as $petId) {
                $latestPhoto = \App\Models\PetAlbum::where('status', 1)
                    ->whereRaw("FIND_IN_SET(?, pet_ids)", [$petId])
                    ->orderBy('created_at', 'desc')
                    ->select('id', 'pet_ids', 'images', 'description', 'created_at')
                    ->first();

                if ($latestPhoto && !empty($latestPhoto->images)) {
                    // 取第一张图片作为封面
                    $latestAlbumPhotos[$petId] = [
                        'id'          => $latestPhoto->id,
                        'pet_id'      => $petId,
                        'image'       => $latestPhoto->images[0], // 取第一张图片
                        'description' => $latestPhoto->description,
                        'created_at'  => $latestPhoto->created_at,
                    ];
                }
            }
        }

        // 处理返回数据，整理封面图和轮播图
        $list->getCollection()->transform(function ($item) use ($requestAppUserId, $latestAlbumPhotos) {
            $item = $item->toArray();

            // 处理宠物家人关系，标记主人
            if (isset($item['pet_masters']) && !empty($item['pet_masters'])) {
                foreach ($item['pet_masters'] as &$master) {
                    if (isset($master['user'])) {
                        $master['user']['is_owner'] = ($master['user']['id'] == $item['owner_id']);
                    }
                }

                // 将家人信息提取到单独的字段，方便前端展示
                $item['family_members'] = array_map(function ($master) {
                    return $master['user'] ?? null;
                }, array_filter($item['pet_masters'], function ($master) {
                    return isset($master['user']);
                }));
            } else {
                $item['family_members'] = [];
            }
            unset($item['pet_masters']);

            // 从批量查询结果中获取最新相册照片
            $item['latest_album_photo'] = $latestAlbumPhotos[$item['id']] ?? null;

            //app用户访问
            if ($requestAppUserId) {
                self::renderDetail($item, $requestAppUserId);
            }

            return $item;
        });

        if ($masterId && $sortName == 'sort_order') {
            /**
             * 根据$masterPetIds排序
             * 保存时假设是：5,4,9
             * asc: 返回5,4,9
             * desc: 返回9,4,5
             */
            $sortedCollection = $list->getCollection()->sort(function ($a, $b) use ($masterPetIds, $sortBy) {
                $aSort = $masterPetIds[$a['id']] ?? 0;
                $bSort = $masterPetIds[$b['id']] ?? 0;
                if ($sortBy == 'desc') {
                    return $bSort <=> $aSort;
                } else {
                    return $aSort <=> $bSort;
                }
            });

            // 索引修复
            $sortedCollection = $sortedCollection->values();

            // 重新构建分页对象，保持分页结构
            $list = new \Illuminate\Pagination\LengthAwarePaginator(
                $sortedCollection,
                $list->total(),
                $list->perPage(),
                $list->currentPage(),
                [
                    'path'     => request()->url(),
                    'pageName' => 'page',
                ]
            );
        }

        return $list;
    }

    public static function getDetail(int $id)
    {
        $info = self::where('id', $id)
            ->with('owner', function (Builder $query) {
                $query->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
            })
            ->with('petType', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en');
            })
            ->with('petBreed', function (Builder $query) {
                $query->select('id', 'name', 'name_tc', 'name_en', 'weight_max_male', 'weight_max_female');
            })
            ->with('petFather', function (Builder $query) {
                $query->select('id', 'name', 'avatar', 'type_id', 'breed_id');
            })
            ->with('petMother', function (Builder $query) {
                $query->select('id', 'name', 'avatar', 'type_id', 'breed_id');
            })
            ->with('petMasters', function (Builder $query) {
                $query->with([
                    'user' => function ($query) {
                        $query->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                    }
                ]);
            })
            ->with('petLogs', function (Builder $query) {
                $query->select('id', 'pet_id', 'log_field', 'old_value', 'new_value', 'created_at');
            })
            ->first();

        if ($info) {
            $data = $info->toArray();

            // 处理宠物家人关系，标记主人
            if (isset($data['pet_masters']) && !empty($data['pet_masters'])) {
                foreach ($data['pet_masters'] as &$master) {
                    if (isset($master['user'])) {
                        $master['user']['is_owner'] = ($master['user']['id'] == $data['owner_id']);
                    }
                }

                // 将家人信息提取到单独的字段，方便前端展示
                $data['family_members'] = array_map(function ($master) {
                    return $master['user'] ?? null;
                }, array_filter($data['pet_masters'], function ($master) {
                    return isset($master['user']);
                }));
            } else {
                $data['family_members'] = [];
            }

            // 获取宠物相册最新添加的第一张照片（适配字符串格式）
            $latestAlbumPhoto = \App\Models\PetAlbum::where('status', 1)
                ->whereRaw("FIND_IN_SET(?, pet_ids)", [$id])
                ->orderBy('created_at', 'desc')
                ->select('id', 'pet_ids', 'images', 'description', 'created_at')
                ->first();

            // 转换为兼容格式
            if ($latestAlbumPhoto && !empty($latestAlbumPhoto->images)) {
                $latestAlbumPhoto = [
                    'id'          => $latestAlbumPhoto->id,
                    'image'       => $latestAlbumPhoto->images[0], // 取第一张图片
                    'description' => $latestAlbumPhoto->description,
                    'created_at'  => $latestAlbumPhoto->created_at,
                ];
            } else {
                $latestAlbumPhoto = null;
            }

            $data['latest_album_photo'] = $latestAlbumPhoto;

            return $data;
        }

        return false;
    }

    public static function saveDetail($data, $userId, $userType)
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (!empty($data['father_id'])) {
                $father = self::where('id', $data['father_id'])->first() or throw_if(true, 'RuntimeException', "父親：{$id}不存在或已刪除");
                $father['sex'] == 1 or throw_if(true, 'RuntimeException', "父親的性別必須為公");
                $father['birthday'] < $data['birthday'] or throw_if(true, 'RuntimeException', "父親的生日必須小於寵物的生日");
            }
            if (!empty($data['mother_id']) && self::where('id', $data['mother_id'])->value('sex') != 2) {
                $mother = self::where('id', $data['mother_id'])->first() or throw_if(true, 'RuntimeException', "母親：{$id}不存在或已刪除");
                $mother['sex'] == 2 or throw_if(true, 'RuntimeException', "母親的性別必須為母");
                $mother['birthday'] < $data['birthday'] or throw_if(true, 'RuntimeException', "母親的生日必須小於寵物的生日");
            }
            // 检查是否传递了 family_members 字段
            $shouldUpdateFamily = array_key_exists('family_members', $data);
            $family_members = $data['family_members'] ?? [];
            unset($data['family_members']);

            if (empty($id)) {
                if ($userType == 2) { // APP用户
                    $data['user_id'] = $userId;
                    $data['owner_id'] = $userId;
                } else { // 管理员
                    // 管理员添加宠物时，需要指定 owner_id
                    if (empty($data['owner_id'])) {
                        throw new \RuntimeException("管理員添加寵物時需要指定寵物主人");
                    }
                    $data['user_id'] = $data['owner_id']; // 设置 user_id 为 owner_id
                }

                $result = self::create($data);
                $id = $result->id;

                // 添加宠物主人关系
                PetMaster::addMaster($id, $data['owner_id']);

                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "寵物：{$id}不存在或已刪除");
                $oldData = $oldData->toArray();
                if ($userType == 2) { //app用户
                    self::hasEditPermission($oldData['id'], $userId) or throw_if(true, 'RuntimeException', "沒有修改權限");
                }
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }

            // 只有明确传递了 family_members 字段时才更新家人关系
            if ($success && $shouldUpdateFamily) {
                PetMaster::updatePetFamily($id, array_column($family_members, 'id'));
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                if ($userType == 1) {  //管理员
                    event(new DataChanged(static::class, $newData, $oldData));
                }
                if ($oldData) {
                    event(new PetChanged($userType == 1 ? 0 : $userId, $newData, $oldData));
                }
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function renderDetail(&$info, $user_id)
    {
        $info['is_owner'] = Pet::isOwner($info, $user_id);
        $info['is_master'] = (bool)PetMaster::isMaster($info['id'], $user_id);
        $info['age'] = getAgeByBirth($info['birthday']);
        $res = self::getEnergyDemand($info);
        $info['energy_demand'] = $res['energy_demand'];
        $info['weight_ideal'] = $res['weight_ideal'];
    }

    public static function getEnergyDemand($pet)
    {
        $petType = $pet['pet_type']['name_en'];
        $className = 'App\Services\PetCalculator\\' . ucfirst($petType) . 'EnergyDemand';
        $weight_ideal = '';
        if (!class_exists($className)) {
            $energy_demand = '未知的動物類型';
        } else {
            /**
             * 获得能量计算器实例
             * @var \App\Services\PetCalculator\EnergyDemand $calculator
             */
            $calculator = new $className($pet);
            try {
                $result = $calculator->getEnergyDemand();
                $energy_demand = round($result, 2) . 'kcal/天';
                $weight_ideal = $calculator->getWeightIdeal() . 'kg';
            } catch (\Exception $e) {
                $energy_demand = $e->getMessage();
            }
        }
        return [
            'energy_demand' => $energy_demand,
            'weight_ideal'  => $weight_ideal,
        ];
    }

    /**
     * 获取宠物营养需求分析
     *
     * @param array $pet 宠物信息
     * @return array
     */
    public static function getNutritionRequirements($pet)
    {
        try {
            // 确定营养计算器类名
            $petType = $pet['pet_type']['name_en'];
            $className = 'App\Services\PetCalculator\\' . ucfirst($petType) . 'NutritionCalculator';

            if (!class_exists($className)) {
                throw new \Exception('未知的動物類型');
            }

            /**
             * 获得营养计算器实例
             * @var \App\Services\PetCalculator\NutritionCalculator $calculator
             */
            $calculator = new $className($pet);
            return $calculator->getNutritionRequirements();

        } catch (\Exception $e) {
            return [
                'error'      => true,
                'message'    => $e->getMessage(),
                'basic_info' => [
                    'pet_name'      => $pet['name'] ?? '未知',
                    'error_details' => '無法計算營養需求：' . $e->getMessage()
                ]
            ];
        }
    }

    public static function isOwner($pet, $userId)
    {
        if (is_numeric($pet)) {
            return self::where('id', $pet)->value('owner_id') == $userId;
        }
        return $pet['owner_id'] == $userId;
    }

    public static function hasEditPermission($petId, $userId)
    {
        return self::isOwner($petId, $userId) || PetMaster::isMaster($petId, $userId);
    }

    public static function del($id, $userId, $userType)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "寵物：{$id}不存在或已刪除");
            if ($userType == 2) { //app用户
                if (self::isOwner($info, $userId)) {
                    $success = $info->delete();
                    $success && $success = PetMaster::resetMasters($id, 4); //删除宠物
                } elseif ($accessId = PetMaster::isMaster($id, $userId)) {
                    $success = PetMaster::delMaster($accessId, 2);  //主动退出
                } else {
                    throw new Exception('沒有刪除權限');
                }
            } else {
                $success = $info->delete();
                $success && $success = PetMaster::resetMasters($id, 4); //删除宠物
            }
            if ($success) {
                $success = true;
                $message = '刪除成功';
                if ($userType == 1) {
                    event(new DataChanged(static::class, null, $info->toArray()));
                }
            } else {
                throw new Exception('刪除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function transferOwner($petId, $newOwnerId, $user)
    {
        DB::beginTransaction();
        try {
            self::isOwner($petId, $user->id) or throw_if(true, 'RuntimeException', "沒有轉移權限");
            PetMaster::isMaster($petId, $newOwnerId) or throw_if(true, 'RuntimeException', "新主人不是家人");
            $newOwnerId == $user->id and throw_if(true, 'RuntimeException', "不能轉給自己");
            $success = self::where('id', $petId)->where('owner_id', $user->id)->update(['owner_id' => $newOwnerId]);
            if ($success) {
                $record = [];
                $message = '轉移成功';
            } else {
                throw new Exception('轉移失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    /**
     * 根据宠物ID数组获取排序后的宠物信息（保持原始顺序）
     * 用于历史数据查询，不受用户排序设置影响
     */
    public static function getPetsByIdsWithOriginalOrder($petIds, $userId = null)
    {
        if (empty($petIds)) {
            return collect();
        }

        $pets = self::whereIn('id', $petIds)
            ->with(['petType:id,name,name_tc,name_en', 'petBreed:id,name,name_tc,name_en'])
            ->select([
                'id',
                'name',
                'sex',
                'avatar',
                'type_id',
                'breed_id',
                'birthday',
                'owner_id',
                'created_at',
                'deleted_at',
            ])
            ->get()
            ->keyBy('id');

        // 按照原始ID数组的顺序重新排列
        $sortedPets = collect();
        foreach ($petIds as $petId) {
            if (isset($pets[$petId])) {
                $pet = $pets[$petId];
                if ($userId) {
                    $pet->is_owner = ($pet->owner_id == $userId);
                    $pet->is_master = !$pet->is_owner && PetMaster::isMaster($pet->id, $userId);
                    $pet->age = getAgeByBirth($pet->birthday);
                }
                $sortedPets->push($pet);
            }
        }

        return $sortedPets;
    }


}
