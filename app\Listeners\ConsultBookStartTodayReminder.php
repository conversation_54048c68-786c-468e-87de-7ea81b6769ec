<?php

namespace App\Listeners;

use App\Events\ConsultBookAcceptedEvent;
use App\Models\Consult;
use App\Models\PetReminder;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ConsultBookStartTodayReminder implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultBookAcceptedEvent $event
     * @return void
     */
    public function handle(ConsultBookAcceptedEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            if (!Carbon::parse($consult->book_time)->isSameDay(Carbon::today())) {
                Log::info('Consult book accepted reminder ignored', [
                    'consult_id' => $event->consultId,
                ]);
            }

            $clock = date('H:i', strtotime($consult->book_time));
            $content = "您的寵物 {$consult->pet->name} 營養咨詢今天 {$clock} 開始，請及時進入聊天！";
            // 创建提醒
            PetReminder::create([
                'user_id'      => $consult->user_id,
                'pet_id'       => $consult->pet_id,
                'type'         => PetReminder::TYPE_PET_CONSULT_REMINDER,
                'title'        => PetReminder::MAPPING[PetReminder::TYPE_PET_CONSULT_REMINDER]['label'],
                'content'      => $content,
                'trigger_date' => Carbon::today()->format('Y-m-d'),
            ]);

            Log::info('Consult book accepted reminder added', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult book accepted reminder failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
