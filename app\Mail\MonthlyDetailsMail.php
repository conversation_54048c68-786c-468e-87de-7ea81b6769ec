<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MonthlyDetailsMail extends Mailable
{
    use SerializesModels;

    public $details;
    public $attachmentPath;
    public $attachmentName;

    public function __construct($details, $attachmentPath = null, $attachmentName = null)
    {
        $this->details = $details;
        $this->attachmentPath = $attachmentPath;
        $this->attachmentName = $attachmentName;
    }

    public function build()
    {
        $email = $this->view('emails.monthly_details')
            ->with('details', $this->details)
            ->subject('请接收您 ' . $this->details['restaurantName'] . ' ' . $this->details['month'] . ' 月份的明细');

        if ($this->attachmentPath && $this->attachmentName) {
            $email->attach($this->attachmentPath, [
                'as' => $this->attachmentName,
                'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ]);
        }

        return $email;
    }
}

