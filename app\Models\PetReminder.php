<?php

namespace App\Models;

use App\Helpers\ConstantReader;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class PetReminder extends Model
{
    use HasFactory, ConstantReader;

    protected $table = 'pets_reminders';

    // 提醒类型常量 命名规则：前2位（分类，从11开始） 后4位（序号，同一分类下的提醒）
    /** 小P自我介绍 */
    const TYPE_SELF_INTRODUCTION = 110001;
    /** 介绍添加宠物 */
    const TYPE_TUTORIAL_ADD_PET = 110002;
    /** 介绍添加家长 */
    const TYPE_TUTORIAL_ADD_PARENT = 110003;
    /** 介绍添加日常照 */
    const TYPE_TUTORIAL_ADD_ALBUM = 110004;
    /** 介绍添加文件 */
    const TYPE_TUTORIAL_ADD_CONTENT = 110005;
    /** 介绍添加体重BCS */
    const TYPE_TUTORIAL_ADD_WEIGHT = 110006;
    /** 介绍功能：食物分析 */
    const TYPE_TUTORIAL_PET_FOOD_ANALYSIS = 110007;
    /** 介绍功能：状态分析 */
    const TYPE_TUTORIAL_PET_HEALTH_ANALYSIS = 110008;
    /** 介绍功能：营养咨询 */
    const TYPE_TUTORIAL_NUTRITION_CONSULTATION = 110009;
    /** 介绍功能：家庭圈 */
    const TYPE_TUTORIAL_FAMILY_CIRCLE = 110010;
    /** 介绍功能：宠友圈 */
    const TYPE_TUTORIAL_PET_FRIEND_CIRCLE = 110011;
    /** 介绍功能：发现页 */
    const TYPE_TUTORIAL_DISCOVERY_PAGE = 110012;
    /** 介绍功能：提醒备忘录 */
    const TYPE_TUTORIAL_REMINDER_MEMO = 110013;
    /** 介绍添加文件 */
    const TYPE_TUTORIAL_ADD_FILE = 1100014;
    /** 劝导添加日常照 */
    const TYPE_ADVICE_ADD_ALBUM = 120001;
    /** 劝导添加动态 */
    const TYPE_ADVICE_ADD_CONTENT = 120002;
    /** 劝导添加体重BCS */
    const TYPE_ADVICE_ADD_WEIGHT = 120003;
    /** 劝导添加文件 */
    const TYPE_ADVICE_ADD_FILE = 120004;
    /** 宠物常识-相关阶段行为 */
    const TYPE_PET_KNOWLEDGE_STAGE_BEHAVIOR = 130001;
    /** 宠物常识-相关阶段营养 */
    const TYPE_PET_KNOWLEDGE_STAGE_NUTRITION = 130002;
    /** 宠物常识-其他 */
    const TYPE_PET_KNOWLEDGE_OTHER = 130003;
    /** 闲话 */
    const TYPE_IDLE_TALK = 140001;
    /** 闲话结束 */
    const TYPE_IDLE_TALK_END = 140002;
    /** 生日提醒 */
    const TYPE_BIRTHDAY_REMINDER = 150001;
    /** 预约提醒-营养咨询 */
    const TYPE_PET_CONSULT_REMINDER = 160001;
    /** 家人上传日常照提醒 */
    const TYPE_FAMILY_UPLOAD_ALBUM_REMINDER = 170001;
    /** 家人动态提醒 */
    const TYPE_FAMILY_POST_CONTENT_REMINDER = 170002;
    /** 宠物上传文件提醒 */
    const TYPE_FAMILY_UPLOAD_FILE_REMINDER = 170003;
    /** 相关动态提醒 */
    const TYPE_RELATED_CONTENT_REMINDER = 180001;
    /** 热门动态提醒 */
    const TYPE_HOT_CONTENT_REMINDER = 180002;
    /** 相关发现提醒 */
    const TYPE_RELATED_ARTICLE_REMINDER = 190001;
    /** 热门发现提醒 */
    const TYPE_HOT_ARTICLE_REMINDER = 190002;
    /** 文件提醒-打针相关 */
    const TYPE_PET_FILE_VACCINE = 200001;
    /** 文件提醒-报告相关 */
    const TYPE_PET_FILE_REPORT = 200002;

    // 提醒分组常量
    const CATEGORY_TUTORIAL = ['key' => 'tutorial', 'label' => '备忘录'];
    const CATEGORY_ADVICE = ['key' => 'advice', 'label' => '使用建议'];
    const CATEGORY_KNOWLEDGE = ['key' => 'knowledge', 'label' => '宠物常识'];
    const CATEGORY_IDLE_TALK = ['key' => 'idle_talk', 'label' => '闲聊'];
    const CATEGORY_BIRTHDAY = ['key' => 'birthday', 'label' => '宠物生日提醒'];
    const CATEGORY_APPOINTMENT = ['key' => 'appointment', 'label' => '预约相关提醒'];
    const CATEGORY_FAMILY = ['key' => 'family', 'label' => '家人相关提醒'];
    const CATEGORY_CONTENT = ['key' => 'file_reminder', 'label' => '动态相关提醒'];
    const CATEGORY_ARTICLE = ['key' => 'article', 'label' => '发现相关提醒'];
    const CATEGORY_PET_FILE = ['key' => 'file', 'label' => '宠物档案提醒'];

    // 提醒类型映射表
    const MAPPING = [
        //APP教程（备忘录）
        self::TYPE_SELF_INTRODUCTION               => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_PET                => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_PARENT             => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_ALBUM              => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_CONTENT            => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_WEIGHT             => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_PET_FOOD_ANALYSIS      => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_PET_HEALTH_ANALYSIS    => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_NUTRITION_CONSULTATION => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_FAMILY_CIRCLE          => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_PET_FRIEND_CIRCLE      => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_DISCOVERY_PAGE         => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_REMINDER_MEMO          => self::CATEGORY_TUTORIAL,
        self::TYPE_TUTORIAL_ADD_FILE               => self::CATEGORY_TUTORIAL,
        //使用建议
        self::TYPE_ADVICE_ADD_ALBUM                => self::CATEGORY_ADVICE,
        self::TYPE_ADVICE_ADD_CONTENT              => self::CATEGORY_ADVICE,
        self::TYPE_ADVICE_ADD_WEIGHT               => self::CATEGORY_ADVICE,
        self::TYPE_ADVICE_ADD_FILE                 => self::CATEGORY_ADVICE,
        //宠物常识
        self::TYPE_PET_KNOWLEDGE_STAGE_BEHAVIOR    => self::CATEGORY_KNOWLEDGE,
        self::TYPE_PET_KNOWLEDGE_STAGE_NUTRITION   => self::CATEGORY_KNOWLEDGE,
        self::TYPE_PET_KNOWLEDGE_OTHER             => self::CATEGORY_KNOWLEDGE,
        //闲聊
        self::TYPE_IDLE_TALK                       => self::CATEGORY_IDLE_TALK,
        self::TYPE_IDLE_TALK_END                   => self::CATEGORY_IDLE_TALK,
        //宠物生日
        self::TYPE_BIRTHDAY_REMINDER               => self::CATEGORY_BIRTHDAY,
        //预约相关
        self::TYPE_PET_CONSULT_REMINDER            => self::CATEGORY_APPOINTMENT,
        //家人相关
        self::TYPE_FAMILY_UPLOAD_ALBUM_REMINDER    => self::CATEGORY_FAMILY,
        self::TYPE_FAMILY_POST_CONTENT_REMINDER    => self::CATEGORY_FAMILY,
        self::TYPE_FAMILY_UPLOAD_FILE_REMINDER     => self::CATEGORY_FAMILY,
        //动态相关
        self::TYPE_RELATED_CONTENT_REMINDER        => self::CATEGORY_CONTENT,
        self::TYPE_HOT_CONTENT_REMINDER            => self::CATEGORY_CONTENT,
        //发现相关
        self::TYPE_RELATED_ARTICLE_REMINDER        => self::CATEGORY_ARTICLE,
        self::TYPE_HOT_ARTICLE_REMINDER            => self::CATEGORY_ARTICLE,
        //宠物档案
        self::TYPE_PET_FILE_VACCINE                => self::CATEGORY_PET_FILE,
        self::TYPE_PET_FILE_REPORT                 => self::CATEGORY_PET_FILE,
    ];

    protected $fillable = [
        'user_id',
        'pet_id',
        'type',
        'title',
        'content',
        'trigger_date',
        'read_at',
        'status',
        'extra',
    ];

    protected $casts = [
        'trigger_date' => 'date',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    // 关联宠物
    public function pet()
    {
        return $this->belongsTo(Pet::class, 'pet_id');
    }

    /**
     * 获取提醒列表
     */
    public static function getList($userId, $type = null, $page = 1, $perPage = 20)
    {
        try {
            $query = self::with([
                'pet' => function ($query) {
                    $query->select('id', 'name', 'avatar', 'birthday');
                }
            ])
                ->where('user_id', $userId)
                ->where('status', 1);

            if ($type) {
                $query->where('type', $type);
            }

            $list = $query->orderBy('trigger_date', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            return [true, $list, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get pet reminder list failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 获取未读提醒数量
     */
    public static function getUnreadCount($userId)
    {
        try {
            $counts = self::where('user_id', $userId)
                ->where('status', 1)
                ->whereNull('read_at')
                ->selectRaw('type, count(*) as count')
                ->groupBy('type')
                ->get();

            $result = [
                'total'      => 0,
                'types'      => [],
                'categories' => []
            ];

            $constants = self::getConstantsByPrefix('CATEGORY_');
            foreach ($constants as $constantVal) {
                $result['categories'][$constantVal['key']] = [
                    'name'  => $constantVal['label'],
                    'count' => 0,
                    'types' => [],
                ];
            }

            $typeMap = self::MAPPING;
            foreach ($counts as $item) {
                $type = $item->type;
                $count = $item->count;

                $result['total'] += $count;
                $result['types'][$type] = [
                    'type'  => $type,
                    'name'  => $typeMap[$type]['label'],
                    'count' => $count
                ];

                // 按分类归类
                $key = $typeMap[$type]['key'];
                $result['categories'][$key]['count'] += $count;
                $result['categories'][$key]['types'][$type] = $count;
            }

            $result['types'] = array_values($result['types']);
            return [true, $result, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get pet reminder unread count failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 标记提醒已读
     */
    public static function markRead($userId, $reminderId = null, $type = null)
    {
        try {
            $query = self::where('user_id', $userId)
                ->where('status', 1)
                ->whereNull('read_at');

            if ($reminderId) {
                $query->where('id', $reminderId);
            }

            if ($type) {
                $query->where('type', $type);
            }

            $query->update(['read_at' => now()]);

            return [true, [], '标记成功'];
        } catch (\Exception $e) {
            logErr('Mark pet reminder read failed:', [
                'user_id'     => $userId,
                'reminder_id' => $reminderId,
                'error'       => $e->getMessage()
            ]);
            return [false, [], '标记失败'];
        }
    }

    /**
     * 获取每个type的最后一条数据（最新记录）
     */
    public static function getLastRecords($userId, $petId)
    {
        return self::query()
            // 子查询：获取每个type的最大created_at（或id）
            ->whereIn('id', function ($query) use ($userId, $petId) {
                $query->selectRaw('MAX(id)')
                    ->from((new self())->getTable()) // 引用当前表
                    ->when($userId, function ($query, $userId) {
                        $query->where('user_id', $userId);
                    })
                    ->when($petId, function ($query, $petId) {
                        $query->where('pet_id', $petId);
                    })
                    ->groupBy('type'); // 按type分组取每组最大id
            })
            ->when($userId, function ($query, $userId) {
                $query->where('user_id', $userId);
            })
            ->when($petId, function ($query, $petId) {
                $query->where('pet_id', $petId);
            })
            ->orderBy('type') // 按类型排序（可选）
            ->get()
            ->keyBy('type')
            ->toArray();
    }

    //备忘录自动标记为已读
    public static function markMemoRead($userId, $type)
    {
        PetReminderMemo::initList($userId);
        $configId = PetReminderConfig::where('type', $type)->value('id');
        $memoId = PetReminderMemo::where('user_id', $userId)->where('config_id', $configId)->whereNull('read_at')->value('id');
        $memoId and PetReminderMemo::markRead($userId, $memoId);
    }
}
