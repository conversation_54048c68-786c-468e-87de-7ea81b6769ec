<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->string('session_id', 64)->comment('关联的会话标识');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->tinyInteger('role')->default(1)->comment('消息发送者角色：1=用户，2=AI');
            $table->text('content')->comment('消息内容');
            $table->json('images')->nullable()->comment('图片数组，JSON格式');
            $table->json('metadata')->nullable()->comment('元数据，如AI分析结果等');
            $table->tinyInteger('status')->default(1)->comment('状态：0=已删除，1=正常');
            $table->timestamps();
            $table->softDeletes();

            $table->index('session_id');
            $table->index(['session_id', 'status']);
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_messages');
    }
};
