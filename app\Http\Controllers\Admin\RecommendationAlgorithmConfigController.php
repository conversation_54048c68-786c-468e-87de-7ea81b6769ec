<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RecommendationAlgorithmConfig;
use App\Helpers\ResponseHelper;
use Illuminate\Http\Request;

/**
 * 推荐算法配置管理控制器
 */
class RecommendationAlgorithmConfigController extends Controller
{
    /**
     * 获取推荐算法配置列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page' => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
            'keyword' => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by' => 'nullable|in:asc,desc',
            'is_active' => 'nullable|in:0,1',
        ]);
        $formData = $request->all();
        $records = RecommendationAlgorithmConfig::getList($formData);
        return ResponseHelper::success($records);
    }

    /**
     * 获取推荐算法配置详情
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(RecommendationAlgorithmConfig::getDetail($id));
    }

    /**
     * 保存推荐算法配置（包含启用/禁用功能）
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id' => 'sometimes|integer|gte:0',
            'name' => 'required|max:100',
            'description' => 'nullable|max:500',
            'enable_is_new' => 'required|in:0,1',
            'enable_is_hot' => 'required|in:0,1',
            'enable_sales_volume' => 'required|in:0,1',
            'enable_user_purchase' => 'required|in:0,1',
            'enable_same_pet_preference' => 'required|in:0,1',
            'weight_is_new' => 'required|numeric|between:0,10',
            'weight_is_hot' => 'required|numeric|between:0,10',
            'weight_sales_volume' => 'required|numeric|between:0,10',
            'weight_user_purchase' => 'required|numeric|between:0,10',
            'weight_same_pet_preference' => 'required|numeric|between:0,10',
            'random_factor_min' => 'required|numeric|between:0.1,2.0',
            'random_factor_max' => 'required|numeric|between:0.1,2.0',
            'hot_sales_threshold' => 'required|integer|min:1',
            'is_active' => 'nullable|in:0,1',           // 启用状态：0=禁用，1=启用
            'sort_order' => 'nullable|integer|min:0'
        ];
        $request->validate($rules);

        $formData = $request->only(array_keys($rules));

        // 验证随机因子范围
        if ($formData['random_factor_min'] >= $formData['random_factor_max']) {
            return ResponseHelper::error('随机因子最小值必须小于最大值');
        }

        $result = RecommendationAlgorithmConfig::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }



    /**
     * 删除推荐算法配置
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = RecommendationAlgorithmConfig::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }


}
