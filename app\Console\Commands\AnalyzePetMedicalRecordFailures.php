<?php

namespace App\Console\Commands;

use App\Models\PetMedicalRecord;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

/**
 * 分析医疗档案失败原因统计
 */
class AnalyzePetMedicalRecordFailures extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'medical-record:analyze-failures
                            {--days=7 : 分析最近N天的数据}
                            {--detailed : 显示详细失败信息}
                            {--reason= : 筛选特定失败原因}';

    /**
     * 命令描述
     */
    protected $description = '分析医疗档案分析失败原因统计';

    /**
     * 执行命令
     */
    public function handle()
    {
        $days = (int)$this->option('days');
        $detailed = $this->option('detailed');
        $reason = $this->option('reason');

        $this->info("📊 医疗档案分析失败统计 (最近{$days}天)");
        $this->info("=" . str_repeat("=", 50));

        // 基础统计
        $this->showBasicStats($days);

        // 失败原因统计
        $this->showFailureReasonStats($days, $reason);

        // 详细失败信息
        if ($detailed) {
            $this->showDetailedFailures($days, $reason);
        }

        // 趋势分析
        $this->showTrendAnalysis($days);
    }

    /**
     * 显示基础统计
     */
    protected function showBasicStats(int $days): void
    {
        $startDate = now()->subDays($days)->startOfDay();

        $totalRecords = PetMedicalRecord::where('created_at', '>=', $startDate)->count();
        $pendingRecords = PetMedicalRecord::where('created_at', '>=', $startDate)
            ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_PENDING)
            ->count();
        $successRecords = PetMedicalRecord::where('created_at', '>=', $startDate)
            ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_SUCCESS)
            ->count();
        $failedRecords = PetMedicalRecord::where('created_at', '>=', $startDate)
            ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_FAILED)
            ->count();

        $successRate = $totalRecords > 0 ? round(($successRecords / $totalRecords) * 100, 2) : 0;
        $failureRate = $totalRecords > 0 ? round(($failedRecords / $totalRecords) * 100, 2) : 0;

        $this->table([
            '指标', '数量', '比例'
        ], [
            ['总档案数', $totalRecords, '100%'],
            ['待分析', $pendingRecords, round(($pendingRecords / max($totalRecords, 1)) * 100, 2) . '%'],
            ['分析成功', $successRecords, $successRate . '%'],
            ['分析失败', $failedRecords, $failureRate . '%'],
        ]);
    }

    /**
     * 显示失败原因统计
     */
    protected function showFailureReasonStats(int $days, ?string $filterReason): void
    {
        $startDate = now()->subDays($days)->startOfDay();

        $query = PetMedicalRecord::where('created_at', '>=', $startDate)
            ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_FAILED);

        if ($filterReason) {
            $query->where('analyze_failure_reason', $filterReason);
        }

        $failureStats = $query
            ->select('analyze_failure_reason', DB::raw('COUNT(*) as count'))
            ->groupBy('analyze_failure_reason')
            ->orderBy('count', 'desc')
            ->get();

        if ($failureStats->isEmpty()) {
            $this->info("\n✅ 没有找到失败记录");
            return;
        }

        $this->info("\n🚨 失败原因统计:");

        $tableData = [];
        $totalFailures = $failureStats->sum('count');

        foreach ($failureStats as $stat) {
            $reason = $stat->analyze_failure_reason ?: '未知';
            $reasonName = $this->getFailureReasonName($reason);
            $count = $stat->count;
            $percentage = round(($count / $totalFailures) * 100, 2);

            $tableData[] = [$reason, $reasonName, $count, $percentage . '%'];
        }

        $this->table([
            '失败原因代码', '失败原因名称', '次数', '占比'
        ], $tableData);
    }

    /**
     * 显示详细失败信息
     */
    protected function showDetailedFailures(int $days, ?string $filterReason): void
    {
        $startDate = now()->subDays($days)->startOfDay();

        $query = PetMedicalRecord::with('pet')
            ->where('created_at', '>=', $startDate)
            ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_FAILED);

        if ($filterReason) {
            $query->where('analyze_failure_reason', $filterReason);
        }

        $failures = $query->orderBy('created_at', 'desc')->limit(10)->get();

        if ($failures->isEmpty()) {
            return;
        }

        $this->info("\n🔍 详细失败信息 (最近10条):");
        $this->info("-" . str_repeat("-", 80));

        foreach ($failures as $failure) {
            $this->warn("\n📅 时间: " . $failure->created_at->format('Y-m-d H:i:s'));
            $this->warn("🐾 宠物: " . ($failure->pet->name ?? '未知') . " (ID: {$failure->pet_id})");
            $this->warn("❌ 失败原因: " . $this->getFailureReasonName($failure->analyze_failure_reason));

            if ($failure->analyze_failure_details) {
                $details = $failure->analyze_failure_details;
                $this->info("📋 错误信息: " . ($details['error_message'] ?? '无'));

                if (isset($details['debug_info'])) {
                    $this->info("🔧 调试信息:");
                    foreach ($details['debug_info'] as $key => $value) {
                        if (is_array($value)) {
                            $this->info("  - {$key}: " . json_encode($value, JSON_UNESCAPED_UNICODE));
                        } else {
                            $this->info("  - {$key}: {$value}");
                        }
                    }
                }
            }
        }
    }

    /**
     * 显示趋势分析
     */
    protected function showTrendAnalysis(int $days): void
    {
        $this->info("\n📈 趋势分析 (按天统计):");

        $trends = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $startOfDay = now()->subDays($i)->startOfDay();
            $endOfDay = now()->subDays($i)->endOfDay();

            $total = PetMedicalRecord::whereBetween('created_at', [$startOfDay, $endOfDay])->count();
            $failed = PetMedicalRecord::whereBetween('created_at', [$startOfDay, $endOfDay])
                ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_FAILED)
                ->count();
            $success = PetMedicalRecord::whereBetween('created_at', [$startOfDay, $endOfDay])
                ->where('analyze_status', PetMedicalRecord::ANALYZE_STATUS_SUCCESS)
                ->count();

            $failureRate = $total > 0 ? round(($failed / $total) * 100, 1) : 0;

            $trends[] = [$date, $total, $success, $failed, $failureRate . '%'];
        }

        $this->table([
            '日期', '总数', '成功', '失败', '失败率'
        ], $trends);
    }

    /**
     * 获取失败原因名称
     */
    protected function getFailureReasonName(string $reason): string
    {
        $reasons = [
            PetMedicalRecord::FAILURE_REASON_JSON_PARSE => 'JSON解析失败',
            PetMedicalRecord::FAILURE_REASON_ORGANS_EMPTY => '器官数据为空',
            PetMedicalRecord::FAILURE_REASON_IMAGE_INCOMPLETE => '图片识别不完整',
            PetMedicalRecord::FAILURE_REASON_AI_NO_CONTENT => 'AI未返回内容',
            PetMedicalRecord::FAILURE_REASON_AI_SERVICE_ERROR => 'AI服务异常',
            PetMedicalRecord::FAILURE_REASON_NETWORK_ERROR => '网络错误',
            PetMedicalRecord::FAILURE_REASON_TIMEOUT => '分析超时',
            PetMedicalRecord::FAILURE_REASON_UNKNOWN => '未知错误'
        ];

        return $reasons[$reason] ?? $reason;
    }
}
