<?php

namespace App\Models;

use App\Exceptions\NoPermissionException;
use App\Http\Middleware\OnlineRefresh;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;

/**
 * @mixin Builder
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'status',
        'last_login_ip',
        'role_id',
        'avatar',
    ];

    protected $hidden = [
        'password',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public static function getDetail(int $id, $user = null)
    {
        $info = self::where('id', $id)->first();
        if ($user->role_id != 1 && $user?->id != $user->id) {  //校验权限
            $childrenIds = Role::getChildrenIds($user, false);
            if (!in_array($user?->role_id, $childrenIds)) {
                throw new NoPermissionException();
            }
        }
        return $info;
    }

    public static function getOnlineUsers($user_id)
    {
        $guard = 'admin';
        $online_users = OnlineRefresh::getOnlineMembers($guard);
        if (!in_array($user_id, $online_users)) {
            OnlineRefresh::hangUpMember($user_id, $guard);
        }
        $hangup_users = OnlineRefresh::getHangupMembers($guard);
        $all_users = array_merge($online_users, $hangup_users);
        $list = self::select(['id', 'name', 'avatar'])->whereIn('id', $all_users)->get();
        foreach ($list as $v) {
            $v->online_status = in_array($v->id, $online_users) ? 1 : 2; //1=在线,2=挂起
        }
        return $list;
    }
}
