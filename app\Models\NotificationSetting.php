<?php

namespace App\Models;

use App\Helpers\ConstantReader;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationSetting extends Model
{
    use HasFactory, ConstantReader;

    // 通知类型常量
    /** 邀请家长 */
    const TYPE_PARENT_INVITE = 'parent_invite';
    /** 添加家长 */
    const TYPE_PARENT_ADDED = 'parent_added';
    /** 删除家长 */
    const TYPE_PARENT_REMOVED = 'parent_removed';
    /** 帖子被点赞 */
    const TYPE_POST_LIKED = 'post_liked';
    /** 帖子被评论 */
    const TYPE_POST_COMMENTED = 'post_commented';
    /** 帖子被收藏 */
    const TYPE_POST_FAVORITED = 'post_favorited';
    /** 分享@提及 */
    const TYPE_POST_MENTION = 'post_mention';
    /** 评论被点赞 */
    const TYPE_COMMENT_LIKED = 'comment_liked';
    /** 用户被关注 */
    const TYPE_USER_FOLLOWED = 'user_followed';
    /** 宠物转让码 */
    const TYPE_PET_TRANSFER_CODE = 'pet_transfer_code';
    /** 宠物家庭码 */
    const TYPE_PET_FAMILY_CODE = 'pet_family_code';
    /** 宠物被接收 */
    const TYPE_PET_RECEIVED = 'pet_received';
    /** 系统公告 */
    const TYPE_SYSTEM_ANNOUNCEMENT = 'system_announcement';
    /** 相册被点赞 */
    const TYPE_ALBUM_LIKED = 'album_liked';
    /** 好友申请 */
    const TYPE_FRIEND_REQUEST = 'friend_request';

    // 通知分组常量
    const CATEGORY_INTERACTION = ['key' => 'interaction', 'label' => '互动消息'];
    const CATEGORY_PET = ['key' => 'pet', 'label' => '宠物管理'];
    const CATEGORY_SYSTEM = ['key' => 'system', 'label' => '系统消息'];
    const CATEGORY_FAMILY = ['key' => 'interaction', 'label' => '家长管理'];

    // 通知类型映射表
    const MAPPING = [
        NotificationSetting::TYPE_PARENT_INVITE       => self::CATEGORY_FAMILY,
        NotificationSetting::TYPE_PARENT_ADDED        => self::CATEGORY_FAMILY,
        NotificationSetting::TYPE_PARENT_REMOVED      => self::CATEGORY_FAMILY,
        NotificationSetting::TYPE_POST_LIKED          => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_POST_COMMENTED      => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_POST_FAVORITED      => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_POST_MENTION        => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_COMMENT_LIKED       => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_USER_FOLLOWED       => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_PET_TRANSFER_CODE   => self::CATEGORY_PET,
        NotificationSetting::TYPE_PET_FAMILY_CODE     => self::CATEGORY_PET,
        NotificationSetting::TYPE_PET_RECEIVED        => self::CATEGORY_PET,
        NotificationSetting::TYPE_SYSTEM_ANNOUNCEMENT => self::CATEGORY_SYSTEM,
        NotificationSetting::TYPE_ALBUM_LIKED         => self::CATEGORY_INTERACTION,
        NotificationSetting::TYPE_FRIEND_REQUEST      => self::CATEGORY_INTERACTION,
    ];

    // 关联类型常量
    const RELATION_TYPE_USER = 'user';
    const RELATION_TYPE_PET = 'pet';
    const RELATION_TYPE_COMMENT = 'comment';
    const RELATION_TYPE_CONTENT = 'content';
    const RELATION_TYPE_ALBUM = 'album';
    const RELATION_TYPE_FAMILY_REQUEST = 'family_request';

    protected $fillable = [
        'user_id',
        'notification_type',
        'is_enabled'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    /**
     * 获取用户的通知设置
     */
    public static function getUserSettings($userId)
    {
        try {
            $settings = self::where('user_id', $userId)->get()->keyBy('notification_type');

            // 默认类型列表
            $defaultTypes = [
                self::TYPE_PARENT_ADDED,
                self::TYPE_PARENT_REMOVED,
                self::TYPE_POST_LIKED,
                self::TYPE_POST_COMMENTED,
                self::TYPE_POST_MENTION,
                self::TYPE_SYSTEM_ANNOUNCEMENT,
            ];

            $result = [];

            // 确保所有类型都有设置
            foreach ($defaultTypes as $type) {
                if (isset($settings[$type])) {
                    $result[$type] = $settings[$type]->is_enabled;
                } else {
                    // 如果没有设置，默认为启用
                    $result[$type] = true;
                }
            }

            return [true, $result, '获取成功'];
        } catch (\Exception $e) {
            logErr('Get notification settings failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '获取失败'];
        }
    }

    /**
     * 更新用户的通知设置
     */
    public static function updateSettings($userId, $settings)
    {
        try {
            foreach ($settings as $type => $enabled) {
                self::updateOrCreate(
                    ['user_id' => $userId, 'notification_type' => $type],
                    ['is_enabled' => $enabled]
                );
            }

            return [true, [], '更新成功'];
        } catch (\Exception $e) {
            logErr('Update notification settings failed:', [
                'user_id' => $userId,
                'error'   => $e->getMessage()
            ]);
            return [false, [], '更新失败'];
        }
    }

    /**
     * 检查用户是否开启了指定类型的通知
     */
    public static function isEnabled($userId, $type)
    {
        $setting = self::where('user_id', $userId)
            ->where('notification_type', $type)
            ->first();

        // 如果没有设置，默认为启用
        return $setting ? $setting->is_enabled : true;
    }
}
