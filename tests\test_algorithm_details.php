<?php

/**
 * 测试算法详情功能
 */

// 模拟 calculateMatchScore 方法的返回结果
function simulateCalculateMatchScore($good, $pet) {
    // 模拟适用性检查
    if (!$good['applicable']) {
        return [
            'score' => 0,
            'details' => [
                'base_score' => 0,
                'random_factor' => 0,
                'reason' => '商品不适用于该宠物',
                'components' => [
                    'new_product' => 0,
                    'hot_product' => 0,
                    'priority' => 0,
                    'purchase_history' => 0,
                    'same_pet_preference' => 0
                ]
            ]
        ];
    }
    
    // a - 新品标识 (0或1)
    $a = $good['is_new'] == 1 ? 1 : 0;
    
    // b - 爆品标识 (0或1)
    $totalSales = $good['sales_total'] ?? 0;
    $b = ($good['is_hot'] == 1 || $totalSales > 100) ? 1 : 0;
    
    // z - 推荐值 (1-3)
    $z = $good['recommendation_priority'] ?? 1;
    
    // e - 购买记录得分 (0-1)
    $e = $pet['purchase_score'] ?? 0;
    
    // f - 同宠意向得分 (0-1)
    $f = $pet['same_pet_score'] ?? 0;
    
    // 计算基础权重得分
    $baseScore = 2*$a + 2*$b + 5*$z + 1.5*$e + 1.5*$f;
    
    // 添加随机因子 (0-1)
    $randomFactor = mt_rand(0, 100) / 100;
    
    // 最终得分
    $finalScore = $randomFactor * $baseScore;
    
    // 返回详细得分信息
    return [
        'score' => round($finalScore, 2),
        'details' => [
            'base_score' => round($baseScore, 2),
            'random_factor' => round($randomFactor, 2),
            'components' => [
                'new_product' => round($a * 2, 1),
                'hot_product' => round($b * 2, 1),
                'priority' => round($z * 5, 1),
                'purchase_history' => round($e * 1.5, 1),
                'same_pet_preference' => round($f * 1.5, 1)
            ],
            'raw_values' => [
                'a_new' => $a,
                'b_hot' => $b,
                'z_priority' => $z,
                'e_purchase' => round($e, 2),
                'f_same_pet' => round($f, 2)
            ]
        ]
    ];
}

// 测试数据
$goods = [
    [
        'id' => 1,
        'name' => '皇家金毛成犬粮',
        'applicable' => true,
        'is_new' => 1,
        'is_hot' => 0,
        'sales_total' => 50,
        'recommendation_priority' => 3,
    ],
    [
        'id' => 2,
        'name' => '通用狗粮',
        'applicable' => true,
        'is_new' => 0,
        'is_hot' => 1,
        'sales_total' => 200,
        'recommendation_priority' => 2,
    ],
    [
        'id' => 3,
        'name' => '猫粮',
        'applicable' => false, // 不适用于狗
        'is_new' => 1,
        'is_hot' => 1,
        'sales_total' => 300,
        'recommendation_priority' => 3,
    ]
];

$pets = [
    [
        'id' => 1,
        'name' => '金毛',
        'purchase_score' => 0.6,  // 购买记录得分
        'same_pet_score' => 0.4,  // 同宠意向得分
    ],
    [
        'id' => 2,
        'name' => '新用户的狗',
        'purchase_score' => 0,    // 无购买记录
        'same_pet_score' => 0.2,  // 较低同宠意向
    ]
];

echo "算法详情功能测试\n";
echo str_repeat("=", 80) . "\n";

foreach ($pets as $pet) {
    echo "\n宠物: {$pet['name']} (ID: {$pet['id']})\n";
    echo "购买得分: {$pet['purchase_score']}, 同宠得分: {$pet['same_pet_score']}\n";
    echo str_repeat("-", 60) . "\n";
    
    $recommendations = [];
    
    foreach ($goods as $good) {
        $result = simulateCalculateMatchScore($good, $pet);
        $recommendations[] = [
            'good' => $good,
            'score_data' => $result
        ];
    }
    
    // 按得分排序
    usort($recommendations, function($a, $b) {
        return $b['score_data']['score'] <=> $a['score_data']['score'];
    });
    
    // 显示推荐结果
    foreach ($recommendations as $index => $rec) {
        $good = $rec['good'];
        $scoreData = $rec['score_data'];
        
        echo sprintf("%d. %s (ID:%d)\n", $index + 1, $good['name'], $good['id']);
        echo sprintf("   推荐得分: %.2f\n", $scoreData['score']);
        
        if ($scoreData['score'] > 0) {
            $details = $scoreData['details'];
            echo sprintf("   基础得分: %.2f × 随机因子: %.2f\n", 
                $details['base_score'], $details['random_factor']);
            
            echo "   得分组成:\n";
            foreach ($details['components'] as $component => $value) {
                $componentNames = [
                    'new_product' => '新品',
                    'hot_product' => '爆品',
                    'priority' => '推荐值',
                    'purchase_history' => '购买记录',
                    'same_pet_preference' => '同宠意向'
                ];
                echo sprintf("     %s: %.1f\n", $componentNames[$component], $value);
            }
            
            echo "   原始值:\n";
            foreach ($details['raw_values'] as $key => $value) {
                echo sprintf("     %s: %s\n", $key, $value);
            }
        } else {
            echo "   原因: " . ($scoreData['details']['reason'] ?? '得分为0') . "\n";
        }
        
        echo "\n";
    }
}

echo "\nAPI 返回示例:\n";
echo str_repeat("-", 40) . "\n";

// 模拟API返回格式
$apiResponse = [
    'status' => 'success',
    'data' => [
        'current_page' => 1,
        'data' => []
    ]
];

// 取第一个宠物的第一个推荐商品作为示例
$exampleRec = $recommendations[0];
$good = $exampleRec['good'];
$scoreData = $exampleRec['score_data'];

$apiResponse['data']['data'][] = [
    'id' => $good['id'],
    'name' => $good['name'],
    'price' => 299.00,
    'recommendation_score' => $scoreData['score'],
    'algorithm_details' => $scoreData['details'],
    'recommendation_reason' => '新品推荐，专为金毛犬设计，基于您的购买偏好'
];

echo json_encode($apiResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
