<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class StreamOutputTest extends TestCase
{
    /**
     * 测试食物分析流式响应输出
     *
     * @return void
     */
    public function testFoodAnalysisStreamOutput()
    {
        // 测试无图片情况下的食物分析
        $response = $this->post('/api/mobile/petChat/food', [
            'message' => '可乐能不能喝',
            'stream' => true,
        ]);

        // 检查响应状态码
        $response->assertStatus(200);
        
        // 获取响应内容
        $content = $response->getContent();
        
        // 记录响应内容到日志
        Log::info('Food Analysis Stream Response:', ['content' => $content]);
        
        // 输出响应内容
        echo "\n\nFood Analysis Stream Response:\n" . $content . "\n\n";
        
        // 检查响应内容是否包含标题
        $this->assertStringContainsString('data: 食物分析', $content, '响应应该包含标题');
        
        // 检查是否包含完成标记
        $this->assertStringContainsString('data: [DONE]', $content, '响应应该包含完成标记');
    }

    /**
     * 测试排泄物分析流式响应输出
     *
     * @return void
     */
    public function testStoolAnalysisStreamOutput()
    {
        // 测试无图片情况下的排泄物分析
        $response = $this->post('/api/mobile/petChat/stool', [
            'message' => '我家狗狗的便便有点黑色',
            'stream' => true,
        ]);

        // 检查响应状态码
        $response->assertStatus(200);
        
        // 获取响应内容
        $content = $response->getContent();
        
        // 记录响应内容到日志
        Log::info('Stool Analysis Stream Response:', ['content' => $content]);
        
        // 输出响应内容
        echo "\n\nStool Analysis Stream Response:\n" . $content . "\n\n";
        
        // 检查响应内容是否包含标题
        $this->assertStringContainsString('data: 排泄物分析', $content, '响应应该包含标题');
        
        // 检查是否包含完成标记
        $this->assertStringContainsString('data: [DONE]', $content, '响应应该包含完成标记');
    }

    /**
     * 测试健康分析流式响应输出
     *
     * @return void
     */
    public function testHealthAnalysisStreamOutput()
    {
        // 测试无图片情况下的健康分析
        $response = $this->post('/api/mobile/petChat/health', [
            'message' => '我家狗狗最近没精神',
            'stream' => true,
        ]);

        // 检查响应状态码
        $response->assertStatus(200);
        
        // 获取响应内容
        $content = $response->getContent();
        
        // 记录响应内容到日志
        Log::info('Health Analysis Stream Response:', ['content' => $content]);
        
        // 输出响应内容
        echo "\n\nHealth Analysis Stream Response:\n" . $content . "\n\n";
        
        // 检查响应内容是否包含标题
        $this->assertStringContainsString('data: 健康分析', $content, '响应应该包含标题');
        
        // 检查是否包含完成标记
        $this->assertStringContainsString('data: [DONE]', $content, '响应应该包含完成标记');
    }
}
