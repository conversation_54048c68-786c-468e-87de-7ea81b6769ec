<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class RefactorGoodsRecommendationSystem extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 1. 为商品表添加新的筛选字段
        Schema::table('goods', function (Blueprint $table) {
            $table->json('pet_types')->nullable()->comment('适用宠物类型ID数组 [1,2,3]');
            $table->json('pet_breeds')->nullable()->comment('适用宠物品种ID数组 [1,2,3]');
            $table->integer('age_range_min')->nullable()->comment('适用最小年龄(月)');
            $table->integer('age_range_max')->nullable()->comment('适用最大年龄(月)');
            $table->decimal('weight_range_min', 5, 2)->nullable()->comment('适用最小体重(kg)');
            $table->decimal('weight_range_max', 5, 2)->nullable()->comment('适用最大体重(kg)');
            $table->tinyInteger('recommendation_priority')->default(1)->comment('推荐优先级:1=低,2=中,3=高');
        });

        // 2. 添加索引优化查询性能
        DB::statement('CREATE INDEX idx_goods_age_range ON goods(age_range_min, age_range_max)');
        DB::statement('CREATE INDEX idx_goods_weight_range ON goods(weight_range_min, weight_range_max)');
        DB::statement('CREATE INDEX idx_goods_priority ON goods(recommendation_priority)');

        // 3. 迁移现有的商品-宠物类型关联数据
        $this->migrateExistingPetTypeData();

        // 4. 迁移现有的商品-宠物品种关联数据
        $this->migrateExistingPetBreedData();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 删除新增的字段和索引
        Schema::table('goods', function (Blueprint $table) {
            $table->dropColumn([
                'pet_types',
                'pet_breeds', 
                'age_range_min',
                'age_range_max',
                'weight_range_min',
                'weight_range_max',
                'recommendation_priority'
            ]);
        });

        DB::statement('DROP INDEX IF EXISTS idx_goods_age_range ON goods');
        DB::statement('DROP INDEX IF EXISTS idx_goods_weight_range ON goods');
        DB::statement('DROP INDEX IF EXISTS idx_goods_priority ON goods');
    }

    /**
     * 迁移现有的商品-宠物类型关联数据
     */
    private function migrateExistingPetTypeData()
    {
        // 获取所有商品的宠物类型关联
        $goodsPetTypes = DB::table('goods_pet_types')
            ->select('good_id', DB::raw('GROUP_CONCAT(pet_type_id) as pet_type_ids'))
            ->groupBy('good_id')
            ->get();

        foreach ($goodsPetTypes as $relation) {
            $petTypeIds = explode(',', $relation->pet_type_ids);
            $petTypeIds = array_map('intval', $petTypeIds);
            
            DB::table('goods')
                ->where('id', $relation->good_id)
                ->update([
                    'pet_types' => json_encode($petTypeIds)
                ]);
        }
    }

    /**
     * 迁移现有的商品-宠物品种关联数据
     */
    private function migrateExistingPetBreedData()
    {
        // 获取所有商品的宠物品种关联
        $goodsPetBreeds = DB::table('goods_pet_breeds')
            ->select('good_id', DB::raw('GROUP_CONCAT(pet_breed_id) as pet_breed_ids'))
            ->groupBy('good_id')
            ->get();

        foreach ($goodsPetBreeds as $relation) {
            $petBreedIds = explode(',', $relation->pet_breed_ids);
            $petBreedIds = array_map('intval', $petBreedIds);
            
            DB::table('goods')
                ->where('id', $relation->good_id)
                ->update([
                    'pet_breeds' => json_encode($petBreedIds)
                ]);
        }
    }
}
