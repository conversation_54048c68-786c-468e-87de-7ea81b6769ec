<?php

namespace App\Listeners;

use App\Events\ConsultBookAcceptedEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultBookAcceptedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultBookAcceptedEvent $event
     * @return void
     */
    public function handle(ConsultBookAcceptedEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_CONSULT_BOOK_ACCEPTED,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定用户
            $channel = GoEasyServiceProvider::getChannelForUser($consult->user_id);
            $notification = ConsultNotificationService::getConsultBookAcceptedMessage();
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to user failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            //推送给所有营养师
            $channel = GoEasyServiceProvider::getChannelForDietitian(0);
            $res = $goEasy->publish($channel, $json);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to dietitian failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            Log::info('Consult book accepted notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult book accepted notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
