<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->json('pet_filter_tags')->nullable()->comment('宠物筛选标签JSON格式:["pet_type:1","pet_breed:15","size:large","hair:long","age:1-7","life:adult"]');
            $table->integer('matching_pets_count')->default(0)->comment('匹配的宠物数量');
            $table->index('matching_pets_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex(['matching_pets_count']);
            $table->dropColumn(['pet_filter_tags', 'matching_pets_count']);
        });
    }
};
