<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Laravel\Passport\HasApiTokens;

/**
 * @mixin Builder
 */
class Merchant extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;


    protected $fillable = [
        'uid',
        'username',
        'phone',
        'avatar',
        'status',
        'area',
        'memo',
        'status',
        'following_count',
        'fans_count',
        'hosting_count',
        'qrcode_url',
        'chinese_name',
        'english_name',
        'email',
        'gender',
        'birthday',
        'background_image',
        'is_admin'
    ];
    protected $hidden = [
        'updated_at',
        'deleted_at'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function generateToken(Merchant $merchant)
    {
        return $merchant->createToken('MerchantToken', []);
    }

    public static function getDetailById(string $id)
    {
        return self::where('id', $id)->first();
    }

    public static function getDetailByUId(string $uid)
    {
        return self::where('uid', $uid)->first();
    }

    public static function updateMerchantInfo(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $merchant = self::find($id);

            if (!$merchant) {
                return [false, '商户不存在'];
            }
            // 更新商户信息
            $merchant->fill($data);
            $merchant->save();

            DB::commit();
            return [true, '商户信息更新成功', $merchant];

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("更新商户失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return [false, '更新商户失败'];
        }
    }
}
