<?php

namespace App\Models;

use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class GoodCart extends Model
{
    protected $table = 'goods_carts';

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function addShoppingCart($data = array())
    {
        try {
            $cartKey = "shoppingCart:{$data['user_id']}";
            $itemKey = "{$data['good_id']}:{$data['good_sku_id']}";
            $maxLen = 100;
            if (count(redis()->hKeys($itemKey)) > $maxLen) {
                throw new Exception('购物车中商品数量已达上限');
            }
            $info = redis()->hGet($cartKey, $itemKey);
            if ($info) {
                $info = json_decode($info, true);
                $number = $info['number'] + $data['number'];
                $info = array_merge($info, $data);
                $info['number'] = $number;
                $info['updated_at'] = date('Y-m-d H:i:s');
            } else {
                $info = $data;
                $info['created_at'] = date('Y-m-d H:i:s');
            }
            if ($info['number'] > 999) {
                throw new Exception('商品购买数量已达上限');
            }
            if (!empty($info['pet_ids'])) {
                $petIds = explode(',', $info['pet_ids']);
                foreach ($petIds as $petId) {
                    if (!PetMaster::isMaster($petId, $data['user_id'])) {
                        throw new Exception('您不是该宠物的家人：' . $petId);
                    }
                }
            }
            $success = redis()->hSet($cartKey, $itemKey, json_encode($info, JSON_UNESCAPED_UNICODE)) !== false;
            if ($success) {
                $success = true;
                $record = json_decode(redis()->hGet($cartKey, $itemKey), true);
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
        } catch (\Exception $e) {
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function updatePetIds($data = array())
    {
        try {
            $cartKey = "shoppingCart:{$data['user_id']}";
            $itemKey = "{$data['good_id']}:{$data['good_sku_id']}";
            $info = redis()->hGet($cartKey, $itemKey);
            if (!$info) {
                throw new Exception('购物车中没有该商品');
            }
            $info['pet_ids'] = $data['pet_ids'] ?? '';
            if (!empty($info['pet_ids'])) {
                $petIds = explode(',', $info['pet_ids']);
                foreach ($petIds as $petId) {
                    if (!PetMaster::isMaster($petId, $data['user_id'])) {
                        throw new Exception('您不是该宠物的家人：' . $petId);
                    }
                }
            }
            $success = redis()->hSet($cartKey, $itemKey, json_encode($info, JSON_UNESCAPED_UNICODE)) !== false;
            if ($success) {
                $success = true;
                $record = json_decode(redis()->hGet($cartKey, $itemKey), true);
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
        } catch (\Exception $e) {
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function removeShoppingCart($data = array())
    {
        try {
            $cartKey = "shoppingCart:{$data['user_id']}";
            $itemKey = "{$data['good_id']}:{$data['good_sku_id']}";
            $info = redis()->hGet($cartKey, $itemKey);
            if (!$info) {
                throw new Exception('购物车中没有该商品');
            }
            $info = json_decode($info, true);
            if ($data['number'] >= $info['number']) {
                $success = redis()->hDel($cartKey, $itemKey);
            } else {
                $info['number'] -= $data['number'];
                $info['updated_at'] = date('Y-m-d H:i:s');
                $success = redis()->hSet($cartKey, $itemKey, json_encode($info, JSON_UNESCAPED_UNICODE)) !== false;
            }
            if ($success) {
                $success = true;
                $record = json_decode(redis()->hGet($cartKey, $itemKey), true);
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
        } catch (\Exception $e) {
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function clearShoppingCart($userId)
    {
        try {
            $cartKey = "shoppingCart:{$userId}";
            $success = redis()->del($cartKey);
            if ($success) {
                $success = true;
                $record = [];
                $message = '清空成功';
            } else {
                throw new Exception('清空失敗');
            }
        } catch (\Exception $e) {
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function getShoppingCart($userId)
    {
        $cartKey = "shoppingCart:{$userId}";
        $cartItems = redis()->hGetAll($cartKey);

        if (empty($cartItems)) {
            return [];
        }

        $formattedItems = [];
        foreach ($cartItems as $info) {
            $cart = json_decode($info, true);
            $formattedItems[] = $cart;
        }

        //根据created_at排序，先添加的放最后面
        usort($formattedItems, function ($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        $goodIds = array_column($formattedItems, 'good_id');
        $goodSkuIds = array_filter(array_column($formattedItems, 'good_sku_id'));
        $goods = Good::withTrashed()->whereIn('id', $goodIds)->select('id', 'name', 'name_tc', 'name_en', 'image', 'price', 'inventory', 'is_show', 'deleted_at')->get()->toArray();
        $goods = array_column($goods, null, 'id');
        $goodSkus = $goodSkuIds ? GoodSku::withTrashed()->whereIn('id', $goodSkuIds)->select('id', 'good_id', 'image', 'price', 'inventory', 'product_sku', 'deleted_at')->get()->toArray() : [];
        $goodSkus = array_column($goodSkus, null, 'id');

        foreach ($formattedItems as &$item) {
            $good = $goods[$item['good_id']];
            $item['now_price'] = $good['price'];  //用于对比价格
            $item['inventory'] = $good['inventory'];
            $item['invalid'] = $good['deleted_at'] || $good['is_show'] != Good::GOOD_SHOW_PUTAWAY;
            if ($item['good_sku_id'] > 0) {
                $goodSku = $goodSkus[$item['good_sku_id']];
                $item['now_price'] = $goodSku['price'];  //用于对比价格
                $item['inventory'] = $goodSku['inventory'];
                $item['product_sku'] = $goodSku['product_sku'];
                $item['invalid'] = $item['invalid'] || $goodSku['deleted_at'];
            }
            $petIds = array_filter(explode(',', $item['pet_ids'] ?? ''));
            $item['pets'] = Pet::getPetsByIdsWithOriginalOrder($petIds, $userId);
        }

        return $formattedItems;
    }

    public static function getCartCount($userId)
    {
        $cartKey = "shoppingCart:{$userId}";
        $carts = redis()->hGetAll($cartKey);
        $count = array_reduce($carts, function ($carry, $item) {
            $carry += json_decode($item, true)['number'];
            return $carry;
        }, 0);
        return ['count' => $count];
    }
}
