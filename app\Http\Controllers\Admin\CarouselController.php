<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Carousel;
use Illuminate\Http\Request;

class CarouselController extends Controller
{
    /**
     * 获取轮播图列表
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'position'  => 'nullable|string|max:50',
            'status'    => 'nullable|in:0,1',
            'sort_name' => 'nullable|in:id,title,sort_order,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);

        $formData = $request->all();
        $records = Carousel::getList($formData);

        return ResponseHelper::success($records);
    }

    /**
     * 获取轮播图详情
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $detail = Carousel::getDetail($id);

        return ResponseHelper::success($detail);
    }

    /**
     * 保存轮播图
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'         => 'sometimes|integer|gte:0',
            'title'      => 'required|string|max:100',
            'image_url'  => 'required|url|pic|trust_host',
            'link_type'  => 'required|integer|in:1,2,3',
            'link_url'   => 'nullable|required_if:link_type,2,3|string|max:255',
            'position'   => 'required|string|max:50',
            'sort_order' => 'nullable|integer',
            'status'     => 'required|in:0,1',
            'start_time' => 'nullable|date_format:Y-m-d H:i:s',
            'end_time'   => 'nullable|date_format:Y-m-d H:i:s|after_or_equal:start_time',
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = Carousel::saveDetail($formData);

        return ResponseHelper::result(...$result);
    }

    /**
     * 删除轮播图
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $carousel = Carousel::findOrFail($id);
        $carousel->delete();

        return ResponseHelper::success(['id' => $id], '删除成功');
    }
}
