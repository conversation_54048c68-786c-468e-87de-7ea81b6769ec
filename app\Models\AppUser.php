<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Laravel\Passport\HasApiTokens;
use OSS\OssClient;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * @mixin Builder
 */
class AppUser extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    protected $appends = [];

    protected $fillable = [
        'uid',
        'username',
        'phone',
        'avatar',
        'status',
        'area',
        'memo',
        'status',
        'following_count',
        'fans_count',
        'hosting_count',
        'qrcode_url',
        'chinese_name',
        'english_name',
        'email',
        'gender',
        'birthday',
        'background_image',
        'is_admin',
        'longitude',
        'latitude',
        'is_dietitian',
        'is_customer_service',
        'certificates',
        'work_year',
        'work_clock_start',
        'work_clock_end',
        'set_online_status',
    ];
    protected $hidden = [
        'updated_at',
        'deleted_at'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function followers()
    {
        return $this->belongsToMany(AppUser::class, 'app_users_follows', 'follow_user_id', 'app_user_id');
    }

    public function following()
    {
        return $this->belongsToMany(AppUser::class, 'app_users_follows', 'app_user_id', 'follow_user_id');
    }

    public static function generateToken(AppUser $user)
    {
        return $user->createToken('AppUserToken', []);
    }

    public static function getDetailById(string $id)
    {
        return self::where('id', $id)->first();
    }

    public static function getDetailByUId(string $uid)
    {
        return self::where('uid', $uid)->first();
    }

    /**
     * 获取二维码
     * @param $user
     */
    public static function renderQrCode($user)
    {
        if (!$user->qrcode_url) {
            //创建二维码
            list($qrcode_success, $qrcode_record, $qrcode_message) = AppUser::createQRCode($user->id);
            if ($qrcode_success === false) {
                logErr($user->id . '二维码生成失败：' . $qrcode_message);
            } else {
                $user->qrcode_url = $qrcode_record['qrcode_url'];
                $user->save();
            }
        }
    }

    /**
     *
     * 创建二维码
     */
    public static function createQRCode($user_id = 0)
    {
        if (!$user_id) {
            return [false, (object)[], '請傳入id'];
        }
        try {
            $extra_filename = 'png';
            /*
             * https://www.simplesoftware.io/#/docs/simple-qrcode/zh-cn
             * */
            $image_data = QrCode::format($extra_filename)
                ->size(500)
                ->margin(1)
                ->color(0, 0, 0)
                ->backgroundColor(255, 255, 255)
                ->errorCorrection('H')
                ->generate(json_encode(['id' => $user_id]));
            //如果不存在保存路径,则生成文件夹
            $folder_dir = 'images/qrcodes';
            Storage::makeDirectory($folder_dir);
            $filename = $folder_dir . '/app_user-' . $user_id . '.' . $extra_filename;
            Storage::put($filename, $image_data);
            //获取指向public目录下的storage完整路径
            $public_path = public_path('storage/' . $filename);
            //上传到oss
            $accessKeyId = config('services.alioss.key_id');
            $accessKeySecret = config('services.alioss.key_secret');
            $endpoint = 'https://' . config('services.alioss.server');
            $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $bucket = config('services.alioss.bucket');
            if (config('app.env') == 'production') {
                $env = 'master';
            } else {
                $env = 'develop';
            }
            $fileName = $env . '/images/qrcode/app_user-' . $user_id . '.' . $extra_filename;
            $options = array(
                OssClient::OSS_CONTENT_TYPE => 'image/' . $extra_filename, // 设置Content-Type
            );
            $record = $ossClient->uploadFile($bucket, $fileName, $public_path, $options);

            return [true, ['qrcode_url' => $record['oss-request-url']], '生成二維碼成功'];
        } catch (\Exception $e) {
            return [false, (object)[], $e->getMessage()];
        }
    }

    public static function updateUserInfo(string $id, array $data)
    {
        DB::beginTransaction();
        try {
            $user = self::find($id);

            if (!$user) {
                DB::rollBack();
                return [false, '用户不存在'];
            }

            if (!empty($user->longitude) && empty($data['longitude'])) {
                unset($data['longitude']);
            }
            if (!empty($user->latitude) && empty($data['latitude'])) {
                unset($data['latitude']);
            }

            // 更新用户信息
            $user->fill($data);
            $user->save();

            DB::commit();
            return [true, '用户信息更新成功', $user];

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("更新用户失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return [false, '更新用户失败'];
        }
    }


    /**
     * 用户注销账号（硬注销 + deleted_at记录）
     *
     * @param string $userId 用户ID
     * @param string $reason 注销原因
     * @return array [success, message, data]
     */
    public static function deleteUserAccount(string $userId, string $reason = '用户主动注销'): array
    {
        DB::beginTransaction();
        try {
            $user = self::find($userId);

            if (!$user) {
                DB::rollBack();
                return [false, '用户不存在'];
            }

            // 检查用户状态
            if ($user->status == -1) {
                DB::rollBack();
                return [false, '账号已经注销'];
            }

            if ($user->status == 0) {
                DB::rollBack();
                return [false, '账号已被禁用，无法注销'];
            }

            // 撤销所有token
            $user->tokens()->delete();

            // 设置用户状态为注销
            $deletedAt = now();
            $user->status = -1;
            $user->save();

            // 记录注销日志
            \Log::info("用户注销账号", [
                'user_id'    => $userId,
                'phone'      => $user->phone,
                'username'   => $user->username,
                'reason'     => $reason,
                'deleted_at' => $deletedAt->format('Y-m-d H:i:s')
            ]);

            DB::commit();
            return [
                true,
                '账号注销成功',
                [
                    'user_id'    => $userId,
                    'deleted_at' => $deletedAt->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            logErr("用户注销失败：{$e->getMessage()}" . PHP_EOL . $e->getTraceAsString());
            return [false, '注销失败，请稍后重试'];
        }
    }

    //增加关注用户数量
    public static function incrementFollower($appUserId, $followingUserId)
    {
        $bool = self::where('id', $appUserId)->increment('following_count', 1);
        $bool && $bool = self::where('id', $followingUserId)->increment('fans_count', 1);
        return $bool;
    }

    //减少关注用户数量
    public static function decrementFollower($appUserId, $followingUserId)
    {
        $bool = self::withTrashed()->where('id', $appUserId)->decrement('following_count', 1);
        $bool && $bool = self::withTrashed()->where('id', $followingUserId)->decrement('fans_count', 1);
        return $bool;
    }

    //增加收藏分享数量
    public static function incrementFavoriteContent($appUserId)
    {
        return self::where('id', $appUserId)->increment('favorite_content_count', 1);
    }

    //减少收藏分享数量
    public static function decrementFavoriteContent($appUserId)
    {
        return self::withTrashed()->where('id', $appUserId)->decrement('favorite_content_count', 1);
    }

    //增加收藏分享数量(公开展示)
    public static function incrementPublicFavoriteContent($appUserId)
    {
        return self::where('id', $appUserId)->increment('public_favorite_content_count', 1);
    }

    //减少收藏分享数量(公开展示)
    public static function decrementPublicFavoriteContent($appUserId)
    {
        return self::withTrashed()->where('id', $appUserId)->decrement('public_favorite_content_count', 1);
    }

    public static function getBaseList($userIds)
    {
        return self::whereIn('id', $userIds)->select('id', 'chinese_name', 'english_name', 'avatar', 'gender')->get()->toArray();
    }

    //增加提交咨询数量
    public static function incrementSubmitConsult($appUserId)
    {
        return self::where('id', $appUserId)->increment('submit_consult_count', 1);
    }

    //减少提交咨询数量
    public static function decrementSubmitConsult($appUserId)
    {
        return self::withTrashed()->where('id', $appUserId)->decrement('submit_consult_count', 1);
    }

    //增加接单咨询数量
    public static function incrementAcceptConsult($appUserId)
    {
        return self::where('id', $appUserId)->increment('accept_consult_count', 1);
    }

    /**
     * 获取客服身份状态名称
     */
    public function getIsCustomerServiceTextAttribute()
    {
        return $this->is_customer_service == 1 ? '是' : '否';
    }

    /**
     * 检查用户是否为客服
     */
    public function isCustomerService()
    {
        return $this->is_customer_service == 1;
    }

    /**
     * 获取用户身份标签
     */
    public function getUserRoleLabelsAttribute()
    {
        $roles = [];

        if ($this->is_admin == 1) {
            $roles[] = '管理员';
        }

        if ($this->is_dietitian > 0) {
            $roles[] = $this->is_dietitian == 1 ? '营养师' : '认证营养师';
        }

        if ($this->is_customer_service == 1) {
            $roles[] = '客服';
        }

        return empty($roles) ? ['普通用户'] : $roles;
    }

    /**
     * 获取用户身份标签字符串
     */
    public function getUserRoleStringAttribute()
    {
        return implode('、', $this->user_role_labels);
    }
}
