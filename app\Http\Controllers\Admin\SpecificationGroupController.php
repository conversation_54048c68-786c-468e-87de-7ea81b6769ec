<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\SpecificationGroup;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * @group specificationGroup
 * 规格组管理
 * Class SpecificationGroupController
 * @package App\Http\Controllers\Admin
 */
class SpecificationGroupController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $records = SpecificationGroup::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(SpecificationGroup::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'           => 'sometimes|integer|gte:0',
            'name'         => 'required|max:30',
            'name_tc'      => 'required|max:30',
            'name_en'      => 'required|max:255',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = SpecificationGroup::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = SpecificationGroup::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
