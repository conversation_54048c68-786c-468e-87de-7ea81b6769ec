<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Requests\SubmitBrowseRequest;
use App\Models\Browse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * browse
 * 浏览记录
 * Class BrowseController
 * @package App\Http\Controllers\Mobile
 */
class BrowseController extends Controller
{
    /**
     * BrowseList
     * 浏览记录列表
     * @param Request $request
     * @return string
     * @queryParam  limit int 每页显示条数
     * @queryParam  sort string 排序
     * @queryParam  page string 页码
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
        ]);
        $formData = $request->all();
        $formData['user_id'] = $request->user()->id;
        $records = Browse::getList($formData);
        return ResponseHelper::success($records);
    }

    /**
     * BrowseCreate
     * 创建浏览记录
     */
    public function save(Request $request)
    {
        $rules = [
            'good_id' => 'required|integer|gt:0',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $result = Browse::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }
}
