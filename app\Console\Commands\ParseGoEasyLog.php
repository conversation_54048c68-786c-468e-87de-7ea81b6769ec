<?php

namespace App\Console\Commands;

use App\Providers\GoEasyServiceProvider;
use Illuminate\Console\Command;

class ParseGoEasyLog extends Command
{
    protected $signature = 'parse_go_easy_log';

    protected $description = '解析GoEasy日志';

    public function handle()
    {
        $logPath = storage_path('logs/im-*.log');
        $files = glob($logPath);
        $res = 0;
        foreach ($files as $file) {
            $lines = file($file);
            foreach ($lines as $line) {
                //找到第一个花括号的位置
                $start = strpos($line, '{');
                if ($start === false) {
                    continue;
                }
                $json = substr($line, $start);
                $arr = json_decode($json, true);
                if (empty($arr['content'])) {
                    continue;
                }
                $res += GoEasyServiceProvider::handleMessage($arr['content']);
            }
        }
        $this->info("处理了{$res}条数据");
        return self::SUCCESS;
    }
}
