<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Consult;
use Illuminate\Http\Request;

class ConsultController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'book_status'   => 'nullable|in:0,1,2,3',  //预约状态:0=非预约,1=预约中,2=预约成功,3=预约失败
            'status'        => 'nullable|in:1,2,3,4',  //会话状态:1=待接入,2=已接入,3=已结束,4=已取消
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'book_start'    => 'nullable|required_with:book_end|date_format:"Y-m-d H:i:s"',
            'book_end'      => 'nullable|required_with:book_start|date_format:"Y-m-d H:i:s"|after:book_start',
            'pet_id'        => 'nullable|integer|exists:pets,id',
            'user_id'       => 'nullable|integer|gt:0|exists:users,id',
            'dietitian_id'  => 'nullable|integer|gt:0|exists:users,id',
        ]);
        $formData = $request->all();
        $records = Consult::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Consult::getDetail($id));
    }
}
