<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class IngredientAnalysisService extends BaseAnalysisService
{
    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);

        $this->type = 'ingredient';
        $this->name = '宠物粮成分表分析';
        $this->description = '分析宠物粮成分对宠物的宠物食品中的添加剂及其潜在健康风险';
    }

    /**
     * 执行成分表分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位（用于健康分析）
     * @param array|null $symptoms 症状列表（用于健康分析）
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        try {
            // 记录开始时间
            $startTime = microtime(true);
            // 获取宠物信息
            $petInfo = $this->getPetInfo($petId);

            // 记录提示词构建开始时间
            $promptStartTime = microtime(true);

            // 构建提示词，使用模板格式并传入多宠物信息
            // 始终使用模板格式，不考虑stream参数
            $prompt = $this->buildIngredientAnalysisPrompt($petInfo, $petsInfo, $images);

            // 记录提示词构建耗时
            $promptBuildTime = microtime(true) - $promptStartTime;

            // 添加用户查询
            $finalPrompt = $prompt . "\n\n用户问题: " . $query;

            // 记录AI调用开始时间
            $aiCallStartTime = microtime(true);

            // 调用AI服务，根据参数决定是否使用流式响应
            // 不使用上下文，只传递提示词和图片，加快响应速度
            $response = $this->aiService->chat(
                $finalPrompt,
                $images,
                $sessionId,
                0.7,
                $stream // 根据参数决定是否使用流式响应
            );

            // 记录AI调用耗时
            $aiCallTime = microtime(true) - $aiCallStartTime;

            // 如果是流式响应，直接返回响应对象
            if ($stream) {
                return $response;
            }

            // 如果不是流式响应，处理响应内容
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return [
                    'status' => 'error',
                    'message' => '获取分析结果失败',
                    'analysis_type' => $this->getType()
                ];
            }

            // 记录结束时间和耗时
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            $promptBuildTime = round($promptBuildTime ?? 0, 2);
            $aiCallTime = round($aiCallTime, 2);

            \Illuminate\Support\Facades\Log::info("IngredientAnalysisService 耗时分析", [
                'total_time' => $duration,
                'prompt_build_time' => $promptBuildTime,
                'ai_call_time' => $aiCallTime,
                'other_time' => round($duration - $promptBuildTime - $aiCallTime, 2),
                'has_images' => !empty($images),
                'image_count' => count($images ?? [])
            ]);

            // 返回格式化的响应，保持与流式响应相同的格式
            return [
                'status' => 'success',
                'message' => '分析成功',
                'data' => [
                    'content' => $content,
                    'session_id' => $response['session_id'] ?? null,
                    'is_new_session' => $response['is_new_session'] ?? false
                ],
                // 移除pet_info字段，由控制器统一添加
                'analysis_type' => $this->getType(),
                'performance' => [
                    'total_time' => $duration,
                    'prompt_build_time' => $promptBuildTime,
                    'ai_call_time' => $aiCallTime
                ]
            ];

        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常
            throw $e;
        } catch (\Exception $e) {
            Log::error('成分表分析失败: ' . $e->getMessage());
            // 将异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '成分表分析失败: ' . $e->getMessage(),
                'ingredient_analysis_error',
                $sessionId ?? null,
                [
                    'original_error' => $e->getMessage(),
                    'analysis_type' => $this->getType()
                ]
            );
        }
    }

    /**
     * 构建成分表分析提示词
     *
     * @param array|null $petInfo
     * @param array|null $petsInfo 多宠物信息数组
     * @return string
     */
    protected function buildIngredientAnalysisPrompt(?array $petInfo, ?array $petsInfo = null, array $images = []): string
    {
        $prompt = '';

        return $prompt;
    }
}
