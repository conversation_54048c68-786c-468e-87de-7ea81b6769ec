<?php

namespace App\Listeners;

use App\Events\ParentRemovedEvent;
use App\Models\AppUser;
use App\Models\NotificationSetting;
use App\Models\Pet;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendParentRemovedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param  \App\Events\ParentRemovedEvent  $event
     * @return void
     */
    public function handle(ParentRemovedEvent $event)
    {
        try {
            // 获取宠物信息
            $pet = Pet::find($event->petId);

            if (!$pet) {
                Log::error('Parent removed notification failed: Pet not found', [
                    'pet_id' => $event->petId,
                    'owner_id' => $event->ownerId,
                    'parent_id' => $event->parentId
                ]);
                return;
            }

            // 获取被移除的家长信息
            $parent = AppUser::find($event->parentId);

            if (!$parent) {
                Log::error('Parent removed notification failed: Parent not found', [
                    'pet_id' => $event->petId,
                    'parent_id' => $event->parentId
                ]);
                return;
            }

            // 获取宠物所有者信息
            $owner = AppUser::find($event->ownerId);

            if (!$owner) {
                Log::error('Parent removed notification failed: Owner not found', [
                    'pet_id' => $event->petId,
                    'owner_id' => $event->ownerId
                ]);
                return;
            }

            // 向宠物所有者发送通知
            NotificationService::createNotification(
                $event->ownerId, // 接收通知的用户ID（宠物所有者）
                NotificationSetting::TYPE_PARENT_REMOVED, // 通知类型
                '移除家长通知',
                "您已成功将 {$parent->username} 从宠物 {$pet->name} 的家长中移除",
                null, // 发送者ID（系统通知，无发送者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            // 向被移除的家长发送通知
            NotificationService::createNotification(
                $event->parentId, // 接收通知的用户ID（被移除的家长）
                NotificationSetting::TYPE_PARENT_REMOVED, // 通知类型
                '移除家长通知',
                "您已被 {$owner->username} 从宠物 {$pet->name} 的家长中移除",
                $event->ownerId, // 发送者ID（宠物所有者）
                $event->petId, // 相关ID（宠物ID）
                NotificationSetting::RELATION_TYPE_PET // 相关类型
            );

            Log::info('Parent removed notification sent', [
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId,
                'parent_id' => $event->parentId
            ]);
        } catch (\Exception $e) {
            Log::error('Parent removed notification failed', [
                'error' => $e->getMessage(),
                'pet_id' => $event->petId,
                'owner_id' => $event->ownerId,
                'parent_id' => $event->parentId
            ]);
        }
    }
}
