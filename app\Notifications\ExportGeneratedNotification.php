<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Symfony\Component\Mime\Email;

/**
 * 发送任务通知类
 */
class ExportGeneratedNotification extends Notification
{
    use Queueable;

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject(pathinfo($notifiable->file_name, PATHINFO_FILENAME))
            ->attach(storage_path('app' . DIRECTORY_SEPARATOR . 'public' . DIRECTORY_SEPARATOR . $notifiable->file_name))
            ->view('emails.export_task', ['filename' => $notifiable->file_name])
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $message->getHeaders()->addTextHeader('X-Task-TYPE', get_class($notifiable));
                $message->getHeaders()->addTextHeader('X-Task-ID', $notifiable->id);
            });
    }
}
