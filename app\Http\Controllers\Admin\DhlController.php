<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Models\Dhl;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

/**
 * @group   dhl
 * 快递公司管理
 * Class DhlController
 * @package App\Http\Controllers\Admin
 */
class DhlController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
        ]);
        $formData = $request->all();
        $records = Dhl::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Dhl::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'           => 'sometimes|integer|gte:0',
            'name'         => 'required|max:60',
            'name_tc'      => 'required|max:60',
            'name_en'      => 'required|max:255',
            'abbreviation' => 'required|string|max:80',
            'sort_order'   => 'required|integer',
            'state'        => 'required|integer',
            'is_default'   => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = Dhl::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Dhl::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
