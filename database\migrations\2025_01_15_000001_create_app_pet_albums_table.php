<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /* 表名: app_pet_albums 业务描述: 宠物相册表 */
        Schema::create('app_pet_albums', function (Blueprint $table) {
            $table->id()->comment('主键');
            $table->unsignedBigInteger('user_id')->comment('上传用户ID');
            $table->unsignedBigInteger('pet_id')->comment('关联宠物ID');
            $table->string('image_url', 500)->comment('图片OSS地址');
            $table->string('image_type', 50)->default('daily')->comment('图片类型：daily=日常照片,medical=医疗记录,growth=成长记录,other=其他');
            $table->string('description', 255)->nullable()->comment('图片描述');
            $table->json('image_info')->nullable()->comment('图片信息：宽高、大小等');
            $table->tinyInteger('status')->default(1)->comment('状态：1=正常,0=隐藏');
            $table->timestamps();
            $table->softDeletes();

            // 索引
            $table->index('user_id');
            $table->index('pet_id');
            $table->index(['pet_id', 'image_type']);
            $table->index('created_at');
        });

        DB::statement("ALTER TABLE `app_pet_albums` COMMENT='宠物相册表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('app_pet_albums');
    }
};
