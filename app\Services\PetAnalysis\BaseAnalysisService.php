<?php

namespace App\Services\PetAnalysis;

use App\Models\Pet;
use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;

abstract class BaseAnalysisService implements AnalysisServiceInterface
{
    /**
     * AI服务实例
     *
     * @var AliBailianService
     */
    protected $aiService;

    /**
     * 分析类型
     *
     * @var string
     */
    protected $type;

    /**
     * 服务名称
     *
     * @var string
     */
    protected $name;

    /**
     * 服务描述
     *
     * @var string
     */
    protected $description;

    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        $this->aiService = $aliBailianService;
    }

    /**
     * 获取分析类型
     *
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * 获取分析名称
     *
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * 获取分析描述
     *
     * @return string
     */
    public function getDescription(): string
    {
        return $this->description;
    }

    /**
     * 获取宠物信息
     *
     * @param int|null $petId
     * @return array|null
     */
    protected function getPetInfo(?int $petId): ?array
    {
        if (!$petId) return null;

        $pet = Pet::with(['petType', 'petBreed'])->find($petId);
        if (!$pet) return null;
        return [
            'id' => $pet->id,
            'name' => $pet->name,
            'type' => $pet->petType ? $pet->petType->name : null,
            'type_en' => $pet->petType ? $pet->petType->name_en : null,
            'breed' => $pet->petBreed ? $pet->petBreed->name : null,
            'sex' => $pet->sex == 1 ? '公' : '母',
            'age' => $pet->birthday ? now()->diffInYears($pet->birthday) : null,
            'weight' => $pet->weight,
            'weight_status' => $this->getWeightStatusText($pet->weight_status),
            'neutered' => $pet->neutered == 1,
            'is_pregnant' => $pet->is_pregnant == 1,
            'is_ill' => $pet->is_ill == 1,
            'special_conditions' => $this->getSpecialConditions($pet)
        ];
    }

    /**
     * 获取体重状态文本（9分制BCS系统 - 5个阶段）
     *
     * @param int|null $status
     * @return string|null
     */
    protected function getWeightStatusText(?int $status): ?string
    {
        if ($status === null) return null;

        // 简化为5个阶段
        if ($status >= 1 && $status <= 2) return '非常瘦';
        if ($status >= 3 && $status <= 4) return '瘦';
        if ($status == 5) return '正常';
        if ($status >= 6 && $status <= 7) return '胖';
        if ($status >= 8 && $status <= 9) return '非常胖';

        return '未知';
    }

    /**
     * 获取宠物特殊情况
     *
     * @param Pet $pet
     * @return array
     */
    protected function getSpecialConditions(Pet $pet): array
    {
        $conditions = [];

        if ($pet->is_pregnant == 1) {
            $conditions[] = '怀孕中';
        }

        if ($pet->is_ill == 1) {
            $conditions[] = '生病中';
        }

        // 使用新的9分制BCS系统
        if ($pet->weight_status >= 1 && $pet->weight_status <= 2) {
            $conditions[] = '非常瘦';
        } elseif ($pet->weight_status >= 3 && $pet->weight_status <= 4) {
            $conditions[] = '瘦';
        } elseif ($pet->weight_status >= 6 && $pet->weight_status <= 7) {
            $conditions[] = '胖';
        } elseif ($pet->weight_status >= 8 && $pet->weight_status <= 9) {
            $conditions[] = '非常胖';
        }

        if ($pet->active_status == 1) {
            $conditions[] = '久坐不动';
        }

        if ($pet->active_status == 2) {
            $conditions[] = '运动量大';
        }

        return $conditions;
    }
}
