<?php

namespace App\Listeners;

use App\Events\ConsultCancelledEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultCancelledNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultCancelledEvent $event
     * @return void
     */
    public function handle(ConsultCancelledEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_NEW_CONSULT,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //非预约推送给所有营养师 / 预约取消则给指定营养师
            $channel = GoEasyServiceProvider::getChannelForDietitian(intval($consult->dietitain_id));
            $notification = $consult->dietitain_id > 0 ? ConsultNotificationService::getConsultCanceledMessage() : [];
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            Log::info('Consult cancelled notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult cancelled notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
