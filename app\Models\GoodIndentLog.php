<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin Builder
 */
class GoodIndentLog extends Model
{
    protected $table = 'goods_indents_logs';

    protected $fillable = [
        'good_indent_id',
        'user_id',
        'user_name',
        'operate_type',
        'content',
    ];

    const OPERATE_TYPE_CANCEL = 1;   //取消订单
    const OPERATE_TYPE_SHIPMENT = 2; //发货
    const OPERATE_TYPE_RECEIPT = 3;  //收货
    const OPERATE_TYPE_REFUND = 4;   //退款
    const OPERATE_TYPE_INVOICE = 5;  //开票
    const OPERATE_TYPE_DELETE = 6;  //删除
    const OPERATE_TYPE_RECEIVING = 7;  //延长收货时间
    const OPERATE_TYPE_PAID = 8;  //设为已支付

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function saveByGoodIndent($GoodIndent, $operator, $operateType, $content)
    {
        $log = new self;
        $log->good_indent_id = $GoodIndent->id;
        $log->user_id = $operator['id'];
        $log->user_name = $operator['nickname'];
        $log->operate_type = $operateType;
        $log->content = $content;
        $log->save() or throw_if(true, 'RuntimeException', '日志保存失败');
    }
}
