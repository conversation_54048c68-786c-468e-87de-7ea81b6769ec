<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Version;
use Illuminate\Http\Request;

class VersionController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at',
            'sort_by'   => 'nullable|in:asc,desc',
            'app_type'  => 'nullable|in:0,1',

        ]);
        $formData = $request->all();
        $records = Version::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(Version::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'       => 'sometimes|integer|gte:0',
            'name'     => 'required|max:32',
            'descs'    => 'nullable',
            'type'     => 'required|in:1,2',
            'app_type' => 'nullable|in:0,1',
            'android_url' => 'nullable|string|url',
            'ios_url' => 'nullable|string|url',
            'wgt_url' => 'nullable|string|url',
            'edition_issue'=>'nullable|integer|gte:0',
            'edition_name'=>'nullable|string',

        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = Version::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Version::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
