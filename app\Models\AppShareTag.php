<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * 应用分享标签模型
 * @package App\Models
 */
class AppShareTag extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_share_tags';

    protected $fillable = [
        'name',
        'color',
        'description',
        'use_count',
        'sort_order',
        'status',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 与分享内容的多对多关系
     */
    public function appContents()
    {
        return $this->belongsToMany(AppContent::class, 'app_contents_tags', 'app_share_tag_id', 'app_content_id');
    }

    /**
     * 获取标签列表
     */
    public static function getList($search_data = [])
    {
        $page = $search_data['page'] ?? 1;
        $perPage = $search_data['per_page'] ?? 20;
        $keyword = $search_data['keyword'] ?? '';
        $status = $search_data['status'] ?? '';
        $sortName = $search_data['sort_name'] ?? 'sort_order';
        $sortBy = $search_data['sort_by'] ?? 'desc';

        $query = self::query();

        if ($keyword) {
            $query->where('name', 'like', "%{$keyword}%");
        }

        if ($status !== '') {
            $query->where('status', $status);
        }

        $query->orderBy($sortName, $sortBy);

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 获取标签详情
     */
    public static function getDetail($id)
    {
        return self::find($id);
    }

    /**
     * 保存标签详情
     */
    public static function saveDetail($data)
    {
        try {
            $id = $data['id'] ?? 0;

            if ($id) {
                $tag = self::find($id);
                if (!$tag) {
                    return [false, [], '标签不存在'];
                }
                $tag->update($data);
                $record = $tag;
            } else {
                $record = self::create($data);
            }

            return [true, $record, $id ? '更新成功' : '创建成功'];
        } catch (\Exception $e) {
            logErr('标签保存失败：' . $e->getMessage());
            return [false, [], '保存失败'];
        }
    }

    /**
     * 删除标签
     */
    public static function del($id)
    {
        try {
            $tag = self::find($id);
            if (!$tag) {
                return [false, [], '标签不存在'];
            }

            // 检查是否有关联的分享内容
            $contentCount = $tag->appContents()->count();

            if ($contentCount > 0) {
                return [false, [], "该标签已被 {$contentCount} 个分享使用，无法删除"];
            }

            $tag->delete();
            return [true, [], '删除成功'];
        } catch (\Exception $e) {
            logErr('标签删除失败：' . $e->getMessage());
            return [false, [], '删除失败'];
        }
    }

    /**
     * 增加使用次数
     */
    public static function incrementUseCount($tagId)
    {
        return self::where('id', $tagId)->increment('use_count');
    }

    /**
     * 减少使用次数
     */
    public static function decrementUseCount($tagId)
    {
        return self::where('id', $tagId)->where('use_count', '>', 0)->decrement('use_count');
    }

    /**
     * 获取热门标签
     */
    public static function getPopularTags($limit = 10)
    {
        return self::where('status', 1)
            ->orderBy('use_count', 'desc')
            ->orderBy('sort_order', 'desc')
            ->limit($limit)
            ->get(['id', 'name', 'color', 'use_count']);
    }
}
