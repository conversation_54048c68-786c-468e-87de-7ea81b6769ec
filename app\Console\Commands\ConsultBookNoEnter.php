<?php

namespace App\Console\Commands;

use App\Events\ConsultBookNoEnterEvent;
use App\Models\Consult;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ConsultBookNoEnter extends Command
{
    protected $signature = 'consult:book-no-enter';

    protected $description = '咨询预约用户未进入对话提醒';

    public function handle()
    {
        Consult::query()
            ->select('id')
            ->where('is_book', 1)
            ->where('book_status', Consult::CONSULT_BOOK_STATUS_SUCCESS)
            ->whereIn('book_notify', [0, 1, 2])
            ->where('status', Consult::CONSULT_STATUS_ACCEPTED)
            ->where('accept_time', '<=', now()->subMinutes(5))  //5分鐘未回覆
            ->whereNull('first_ask_time')
            ->where('created_at', '>=', now()->subDays(30))  //只查詢一個月內的
            ->chunk(100, function ($consults) {
                foreach ($consults as $consult) {
                    try {
                        event(new ConsultBookNoEnterEvent($consult->id));
                        Consult::where('id', $consult->id)->update([
                            'book_notify' => 3,
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Consult book no enter notification failed', [
                            'error'      => $e->getMessage(),
                            'consult_id' => $consult->id,
                        ]);
                    }
                }
            });
    }
}
