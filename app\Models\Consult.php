<?php

namespace App\Models;

use App\Events\ConsultCreatedEvent;
use App\Helpers\RedisLock;
use App\Services\Consult\ConsultService;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Consult extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'questionnaire',
        'pet_id',
        'user_id',
        'dietitian_id',
        'is_book',
        'book_time',
        'book_notify',
        'accept_time',
        'end_time',
        'is_transfer',
        'transfer_time',
        'first_ask_time',
        'last_ask_time',
        'first_answer_time',
        'last_answer_time',
        'summary',
        'book_status',
        'status',
    ];

    const SUBMIT_COUNT_PER_WEEK = 1; //每周可提交咨询数量
    const CONSULT_ACCEPT_MAX_PEOPLE = 99; //营养师一次最多接待人数
    const CONSULT_AUTO_CLOSE_TIME_LIMIT = 30;  //等待最大时长（分钟）
    const CONSULT_CONVERSATION_TIME_LIMIT = 15;  //会话最大时长（分钟）
    const CONSULT_REPLY_TIME_LIMIT = 5;  //营养师必须在多少时间内回复（分钟）

    const CONSULT_STATUS_WAITING = 1; //状态：待接入
    const CONSULT_STATUS_ACCEPTED = 2; //状态：已接入
    const CONSULT_STATUS_END = 3; //状态：已结束
    const CONSULT_STATUS_CANCEL = 4; //状态：已取消

    const CONSULT_BOOK_STATUS_NOPE = 0; //状态：非预约
    const CONSULT_BOOK_STATUS_WAIT = 1; //状态：预约中
    const CONSULT_BOOK_STATUS_SUCCESS = 2; //状态：预约成功
    const CONSULT_BOOK_STATUS_FAIL = 3; //状态：预约失败

    const STATUS_TEXT_LIST = [
        self::CONSULT_STATUS_WAITING  => '待接入',
        self::CONSULT_STATUS_ACCEPTED => '已接入',
        self::CONSULT_STATUS_END      => '已结束',
        self::CONSULT_STATUS_CANCEL   => '已取消',
    ];

    const BOOK_STATUS_TEXT_LIST = [
        self::CONSULT_BOOK_STATUS_NOPE    => '非预约',
        self::CONSULT_BOOK_STATUS_WAIT    => '预约中',
        self::CONSULT_BOOK_STATUS_SUCCESS => '预约成功',
        self::CONSULT_BOOK_STATUS_FAIL    => '预约失败',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public function pet()
    {
        return $this->belongsTo(Pet::class, 'pet_id');
    }

    public function user()
    {
        return $this->belongsTo(AppUser::class, 'user_id');
    }

    public function dietitian()
    {
        return $this->belongsTo(AppUser::class, 'dietitian_id');
    }

    public static function getList($search_data = array(), $requestAppUserId = null)
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $status = $search_data['status'] ?? "";
        $bookStatus = $search_data['book_status'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";
        $bookStart = $search_data['book_start'] ?? "";
        $bookEnd = $search_data['book_end'] ?? "";
        $userId = $search_data['user_id'] ?? "";
        $dietitianId = $search_data['dietitian_id'] ?? "";

        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {

            });
        })
            ->when($status, function (Builder $query) use ($status) {
                $query->where('status', $status);
            })
            ->when($bookStatus, function (Builder $query) use ($bookStatus) {
                $query->where('book_status', $bookStatus);
            })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($dietitianId, function (Builder $query) use ($dietitianId) {
                if ($dietitianId == -1) {
                    $query->where('dietitian_id', '>', 0); //营养师查看全部
                } else {
                    $query->where('dietitian_id', $dietitianId);
                }
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($bookStart && $bookEnd, function (Builder $query) use ($bookStart, $bookEnd) {
                $query->whereBetween('book_time', [$bookStart, $bookEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->select([
                'id',
                'pet_id',
                'user_id',
                'dietitian_id',
                'is_book',
                'book_time',
                'accept_time',
                'end_time',
                'is_transfer',
                'transfer_time',
                'first_ask_time',
                'first_answer_time',
                'last_ask_time',
                'last_answer_time',
                'summary',
                'book_status',
                'status',
                'created_at',
            ])
            ->when(isset($search_data['_waiting']), function (Builder $query) use ($requestAppUserId, $search_data) {
                $statusWaiting = self::CONSULT_STATUS_WAITING;
                $statusAccepted = self::CONSULT_STATUS_ACCEPTED;
                if ($search_data['_waiting'] == 'dietitian') {
                    $query->whereRaw("status = {$statusWaiting} and (is_book = 0 or (is_book = 1 and dietitian_id is null))");
                } else {
                    $query->whereIn('status', [$statusWaiting, $statusAccepted]);
                }
                $query->addSelect([
                    DB::raw("JSON_EXTRACT(questionnaire,'$.additional_question') AS additional_question")
                ]);
            })
            ->when(isset($search_data['_booking']), function (Builder $query) use ($requestAppUserId, $dietitianId) {
                $query->where('book_status', self::CONSULT_BOOK_STATUS_SUCCESS)
                    ->where('status', self::CONSULT_STATUS_WAITING);
                if ($dietitianId == -1) {
                    $query->where('dietitian_id', '>', 0); //营养师查看全部
                } else {
                    $query->where('dietitian_id', $dietitianId);
                }
            })
            ->with([
                'pet'       => function (Builder $q) {
                    $q->select('id', 'name', 'sex', 'avatar', 'type_id', 'breed_id', 'birthday', 'weight')
                        ->with('petType', function (Builder $query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        })
                        ->with('petBreed', function (Builder $query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        });
                },
                'user'      => function (Builder $q) {
                    $q->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                },
                'dietitian' => function (Builder $q) {
                    $q->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                },
            ])
            ->orderBy('id', 'desc');

        $list = $query->paginate($limit, ['*'], 'page', $page);

        // 处理返回数据，整理封面图和轮播图
        $list->getCollection()->transform(function ($item) use ($requestAppUserId) {
            $item = $item->toArray();
            self::renderItem($item, $requestAppUserId);
            return $item;
        });

        return $list;
    }

    public static function renderItem(&$item, $requestAppUserId)
    {
        //app用户访问，追加收藏状态
        if ($requestAppUserId) {
            self::renderTimeAgo($item);
        }
    }

    // 添加时间差处理
    public static function renderTimeAgo(&$item)
    {
        if (!$item['created_at']) {
            $item['time_ago'] = '';
            return;
        }
        $createTime = Carbon::parse($item['created_at']);
        $now = now();
        $diffInMinutes = $createTime->diffInMinutes($now);
        $diffInHours = $createTime->diffInHours($now);
        $diffInDays = $createTime->diffInDays($now);
        $diffInWeeks = $createTime->diffInWeeks($now);
        $diffInMonths = $createTime->diffInMonths($now);
        $diffInYears = $createTime->diffInYears($now);
        if ($diffInMinutes < 1) {
            $item['time_ago'] = '剛剛';
        } elseif ($diffInMinutes < 60) {
            $item['time_ago'] = $diffInMinutes . '分鐘前';
        } elseif ($diffInHours < 24) {
            $item['time_ago'] = $diffInHours . '小時前';
        } elseif ($diffInDays < 7) {
            $item['time_ago'] = $diffInDays . '天前';
        } elseif ($diffInWeeks < 4) {
            $item['time_ago'] = $diffInWeeks . '週前';
        } elseif ($diffInMonths < 12) {
            $item['time_ago'] = $diffInMonths . '月前';
        } elseif ($diffInYears >= 1) {
            $item['time_ago'] = $diffInYears . '年前';
        }
    }

    public static function getDetail(int $id, $where = [])
    {
        return self::where('id', $id)
            ->when(!empty($where), function (Builder $query) use ($where) {
                if (isset($where['dietitian_id'])) {
                    $statusWaiting = self::CONSULT_STATUS_WAITING;
                    $query->whereRaw("(
                        (
                            status = {$statusWaiting} and dietitian_id is null
                        ) or (
                            dietitian_id = {$where['dietitian_id']}
                        )
                    )");
                } else {
                    $query->where($where);
                }
            })
            ->with([
                'pet'       => function (Builder $q) {
                    $q->select('id', 'name', 'sex', 'avatar', 'type_id', 'breed_id', 'birthday', 'weight')
                        ->with('petType', function (Builder $query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        })
                        ->with('petBreed', function (Builder $query) {
                            $query->select('id', 'name', 'name_tc', 'name_en');
                        });
                },
                'user'      => function (Builder $q) {
                    $q->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                },
                'dietitian' => function (Builder $q) {
                    $q->select('id', 'username', 'chinese_name', 'english_name', 'avatar');
                },
            ])
            ->first();
    }

    public static function getSubmitCountThisWeek($userId)
    {
        return self::withTrashed()->where('user_id', $userId)
            ->whereIn('status', [
                self::CONSULT_STATUS_WAITING,
                self::CONSULT_STATUS_ACCEPTED,
                self::CONSULT_STATUS_END,
            ])
            ->where('created_at', '>=', now()->subDays(7))
            ->count();
    }

    public static function createConsult($data = array())
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $lock = RedisLock::lock($redis, 'CreateConsult_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            if (!PetMaster::isMaster($data['pet_id'], $data['user_id'])) {
                throw new Exception("无权限为宠物ID {$data['pet_id']} 提交咨询");
            }
            $data['questionnaire'] = json_encode($data['questionnaire'], JSON_UNESCAPED_UNICODE);
            //本周该用户是否有提交记录
            $count = self::getSubmitCountThisWeek($data['user_id']);
            if ($count >= self::SUBMIT_COUNT_PER_WEEK) {
                //throw new Exception("本周您提交的咨詢已達上限，請下周再來");
            }
            if ($data['is_book'] == 1) {
                $data['book_status'] = self::CONSULT_BOOK_STATUS_WAIT;
            } else {
                $data['book_time'] = null;
            }
            $result = self::create($data);
            $id = $result->id;
            AppUser::incrementSubmitConsult($userId);
            $success = true;
            if ($success) {
                $record['id'] = $id;
                $message = '保存成功';
                event(new ConsultCreatedEvent($id));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
            logErr($data['user_id'] . '用户咨询失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'CreateConsult_' . $userId);
        }
        return [$success, $record, $message];
    }

    // 是否是咨询的拥有者
    public static function isOwner($consultId, $userId)
    {
        return self::where('id', $consultId)->where('user_id', $userId)->first();
    }

    //还需等待多少人
    public static function getLineupCount($id, $userId)
    {
        $zero = strtotime(date('Y-m-d'));
        $startTime = date('Y-m-d H:i:s', $zero);
        $endTime = date('Y-m-d H:i:s', $zero + 86400);
        $waitingNum = self::where('id', '<', $id)
            ->where('user_id', '<>', $userId)
            ->where('status', self::CONSULT_STATUS_WAITING)
            ->whereRaw('(is_book = 0 or (is_book=2 and book_time between ? and ?))', [$startTime, $endTime])
            ->count();
        $consultingNum = self::where('status', self::CONSULT_STATUS_ACCEPTED)->count();
        return $waitingNum + min($consultingNum, self::CONSULT_ACCEPT_MAX_PEOPLE);
    }

    public static function cancel($id, $user)
    {
        $service = new ConsultService($id, $user);
        return [$service->cancel(), $service->getData(), $service->getMessage()];
    }

    //判断是否可以接单
    public static function isConsultTimeConflict($id, $user)
    {
        //获取预约时间
        $consult = self::where('id', $id)->select(['is_book', 'book_time', 'first_ask_time'])->first();
        if (!$consult) {
            return [false, []];
        }
        [$startTime, $endTime] = self::getConsultTimeRange($consult);
        //查询营养师的所有咨询
        $consulting = self::where('dietitian_id', $user->id)
            ->whereIn('status', [self::CONSULT_STATUS_ACCEPTED, self::CONSULT_STATUS_WAITING])
            ->where('id', '<>', $id)
            ->where('created_at', '>=', now()->subDays(30))
            ->select(['id', 'is_book', 'book_time', 'first_ask_time'])
            ->get();
        $bool = true;
        $intersect = [];
        foreach ($consulting as $item) {
            [$itemStartTime, $itemEndTime] = self::getConsultTimeRange($item);
            //判断是否存在交集
            if ($itemStartTime < $endTime && $itemEndTime > $startTime) {
                $bool = false;
                $intersect = $item;
                break;
            }
        }
        return [$bool, $intersect];
    }

    //获取咨询持续时间
    public static function getConsultTimeRange($consult)
    {
        if ($consult['is_book'] == 1) {
            $startTime = strtotime($consult['book_time']);
        } else {
            $startTime = time();
        }
        if ($consult->first_ask_time) {
            $endTime = strtotime($consult['first_ask_time']) + self::CONSULT_CONVERSATION_TIME_LIMIT;
        } else {
            $endTime = $startTime + self::CONSULT_AUTO_CLOSE_TIME_LIMIT + self::CONSULT_CONVERSATION_TIME_LIMIT;
        }
        return [$startTime, $endTime];
    }

    public static function acceptBook($id, $user)
    {
        $service = new ConsultService($id, $user);
        return [$service->acceptBook(), $service->getData(), $service->getMessage()];
    }

    //正在接入的咨询
    public static function getConsultingNum($dietitianId, $exceptId = 0)
    {
        return self::where('dietitian_id', $dietitianId)
            ->where('status', self::CONSULT_STATUS_ACCEPTED)
            ->where('id', '<>', $exceptId)
            ->count();
    }

    public static function acceptConsult($id, $user)
    {
        $service = new ConsultService($id, $user);
        return [$service->acceptConsult(), $service->getData(), $service->getMessage()];
    }

    public static function end($id, $user)
    {
        $service = new ConsultService($id, $user);
        return [$service->end(), $service->getData(), $service->getMessage()];
    }

    public static function setSummary($data, $user)
    {
        $service = new ConsultService($data['id'], $user);
        return [$service->setSummary($data), $service->getData(), $service->getMessage()];
    }

    public static function del($id, $user = null)
    {
        $service = new ConsultService($id, $user);
        return [$service->del(), $service->getData(), $service->getMessage()];
    }
}
