<?php

namespace App\Services\PetAnalysis;

use App\Services\AliBailian\AliBailianService;
use Illuminate\Support\Facades\Log;

class StoolAnalysisService extends BaseAnalysisService
{
    /**
     * 构造函数
     *
     * @param AliBailianService $aliBailianService
     */
    public function __construct(AliBailianService $aliBailianService)
    {
        parent::__construct($aliBailianService);

        $this->type = 'stool';
        $this->name = '排泄物分析';
        $this->description = '分析宠物排泄物状况判断健康问题';
    }

    /**
     * 执行排泄物分析
     *
     * @param string $query 用户查询
     * @param array|null $images 图片数组
     * @param int|null $petId 宠物ID
     * @param string|null $sessionId 会话ID
     * @param bool $stream 是否使用流式响应
     * @param string|null $bodyPart 分析的身体部位（用于健康分析）
     * @param array|null $symptoms 症状列表（用于健康分析）
     * @param array|null $petsInfo 多宠物信息数组（用于多宠物分析）
     * @return mixed 分析结果
     */
    public function analyze(string $query, ?array $images = null, ?int $petId = null, ?string $sessionId = null, bool $stream = false, ?string $bodyPart = null, ?array $symptoms = null, ?array $petsInfo = null): mixed
    {
        try {
            // 获取宠物信息
            $petInfo = $this->getPetInfo($petId);

            // 构建提示词，始终使用模板格式，不考虑stream参数
            $prompt = $this->buildStoolAnalysisPrompt($petInfo, $petsInfo, $images);

            // 添加用户查询
            $finalPrompt = $prompt . "\n\n用户问题: " . $query;

            // 调用AI服务，根据参数决定是否使用流式响应
            $response = $this->aiService->chat(
                $finalPrompt,
                $images,
                $sessionId,
                0.7,
                $stream // 根据参数决定是否使用流式响应
            );

            // 如果是流式响应，直接返回响应对象
            if ($stream) {
                return $response;
            }

            // 如果不是流式响应，处理响应内容
            $content = $response['choices'][0]['message']['content'] ?? null;

            if (empty($content)) {
                return [
                    'status' => 'error',
                    'message' => '获取分析结果失败',
                    'analysis_type' => $this->getType()
                ];
            }

            // 返回格式化的响应，保持与流式响应相同的格式
            return [
                'status' => 'success',
                'message' => '分析成功',
                'data' => [
                    'content' => $content,
                    'session_id' => $response['session_id'] ?? null,
                    'is_new_session' => $response['is_new_session'] ?? false
                ],
                // 移除pet_info字段，由控制器统一添加
                'analysis_type' => $this->getType()
            ];

        } catch (\App\Exceptions\AIServiceException $e) {
            // 直接抛出AI服务异常
            throw $e;
        } catch (\Exception $e) {
            Log::error('排泄物分析失败: ' . $e->getMessage());
            // 将异常转换为AI服务异常并抛出
            throw new \App\Exceptions\AIServiceException(
                '排泄物分析失败: ' . $e->getMessage(),
                'stool_analysis_error',
                $sessionId ?? null,
                [
                    'original_error' => $e->getMessage(),
                    'analysis_type' => $this->getType()
                ]
            );
        }
    }

    /**
     * 构建排泄物分析提示词
     *
     * @param array|null $petInfo
     * @param array|null $petsInfo 多宠物信息数组
     * @return string
     */
    protected function buildStoolAnalysisPrompt(?array $petInfo, ?array $petsInfo = null, array $images = []): string
    {
        // 根据是否有图片来调整提示词
        if (!empty($images)) {
            $prompt = "你是一个专业的宠物健康专家，请分析用户描述和上传图片中的宠物排泄物或呕吐物状况，判断可能的健康问题。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式。\n\n如果用户提供了多只宠物的信息，请根据不同宠物的情况给出各自的分析结果，并注意区分不同宠物的健康状况。\n\n";
        } else {
            $prompt = "你是一个专业的宠物健康专家，请根据用户描述的宠物排泄物或呕吐物状况，判断可能的健康问题。请使用纯正的香港粤语（广东话）回复，不要使用普通话或书面语。回答要自然流畅，符合香港本地人的表达方式。\n\n如果用户提供了多只宠物的信息，请根据不同宠物的情况给出各自的分析结果，并注意区分不同宠物的健康状况。\n\n";
        }

        // 添加颜色标注的模板格式
        $prompt .= "请按照以下格式回复，使用纯正的香港粤语（广东话）：\n\n";

        $prompt .= "<span style=\"color: #2196F3;\">查证对象: </span>[全部/宠物名称列表]\n\n";

        // 根据是否有图片来调整样本描述
        if (!empty($images)) {
            $prompt .= "<span style=\"color: #9C27B0;\">图片中的样本: </span>[排泄物/呕吐物类型]\n\n";
        } else {
            $prompt .= "<span style=\"color: #9C27B0;\">描述的样本: </span>[排泄物/呕吐物类型]\n\n";
        }

        $prompt .= "<span style=\"color: #FF9800;\">样本特征: </span>\n";
        $prompt .= " ● [样本的颜色、质地、形状等特征描述]\n";
        $prompt .= " ● [更多特征描述]\n\n";

        $prompt .= "<span style=\"color: #4CAF50;\">健康状况评估: </span>\n";
        $prompt .= " ● [宠物名称]--<span style=\"color: #4CAF50;\">[正常]</span>/<span style=\"color: #FFC107;\">[轻度异常]</span>/<span style=\"color: #FF9800;\">[中度异常]</span>/<span style=\"color: #F44336;\">[严重异常]</span>\n";
        $prompt .= " ● [其他宠物的健康状况评估]\n\n";

        $prompt .= "<span style=\"color: #FF9800;\">可能原因: </span>\n";
        $prompt .= " ● [宠物名称]--[可能原因1]\n";
        $prompt .= " ● [宠物名称]--[可能原因2]\n";
        $prompt .= " ● [其他宠物的可能原因]\n\n";

        $prompt .= "<span style=\"color: #F44336;\">是否需要就医: </span>\n";
        $prompt .= " ● [宠物名称]--<span style=\"color: #4CAF50;\">[不需要]</span>/<span style=\"color: #FFC107;\">[观察几天]</span>/<span style=\"color: #FF9800;\">[尽快就医]</span>/<span style=\"color: #F44336;\">[立即就医]</span>\n";
        $prompt .= " ● [其他宠物的就医建议]\n\n";

        $prompt .= "<span style=\"color: #9C27B0;\">建议措施: </span>\n";
        $prompt .= " ● [具体的建议措施，每条使用黑点标记]\n";
        $prompt .= " ● [更多建议措施]\n\n";

        $prompt .= "<span style=\"color: #009688;\">饮食调整建议: </span>\n";
        $prompt .= " ● [宠物名称]--[具体的饮食调整建议]\n";
        $prompt .= " ● [其他宠物的饮食调整建议]\n\n";

        $prompt .= "<span style=\"color: #2196F3;\">生活护理建议: </span>\n";
        $prompt .= " ● [宠物名称]--[具体的生活护理建议]\n";
        $prompt .= " ● [其他宠物的生活护理建议]\n\n";

        $prompt .= "<span style=\"color: #673AB7;\">总结: </span>\n";
        $prompt .= "[对所有宠物的健康状况的总体评估和建议]\n\n";

        // 以下是给AI的指导，使用特殊标记包裹，确保AI不会直接返回这些内容
        $prompt .= "<instructions>\n";
        $prompt .= "请注意：\n";
        if (!empty($images)) {
            $prompt .= "1. 如果无法识别样本，请在图片中的样本部分回答\"无法识别样本: 原因\"。\n";
        } else {
            $prompt .= "1. 如果无法识别样本，请在描述的样本部分回答\"无法识别样本: 原因\"。\n";
        }
        $prompt .= "2. 健康状况评估请使用：正常/轻度异常/中度异常/严重异常 四个等级之一。\n";
        $prompt .= "3. 是否需要就医请使用：不需要/观察几天/尽快就医/立即就医 四个选项之一。\n";
        $prompt .= "4. 如果可以识别样本但无法评估某些具体特性，请在相应项目中注明\"无法评估: 原因\"。\n";
        $prompt .= "5. **多宠物统一回复规则（重要）：**\n";
        $prompt .= "   - 当所有宠物健康状况相同时，统一回复：'所有宠物--[健康状态]'\n";
        $prompt .= "   - 当所有宠物就医建议相同时，统一回复：'所有宠物都[不需要/需要观察/需要就医]'\n";
        $prompt .= "   - 只有在宠物之间有差异时才分别说明个别情况\n";
        $prompt .= "   - 例如：所有宠物都是正常状态时，回复'所有宠物--正常'\n";
        $prompt .= "6. 请确保回复的格式与模板一致，以便前端可以正确展示。\n";
        $prompt .= "7. 请使用亲切、关心的语气，就像在和好朋友聊天一样。\n";
        $prompt .= "8. 请为重要内容添加颜色标注：\n";
        $prompt .= "   - 查证对象和生活护理建议：<span style=\"color: #2196F3;\">蓝色</span>\n";
        $prompt .= "   - 健康状况评估：<span style=\"color: #4CAF50;\">绿色</span>\n";
        $prompt .= "   - 可能原因和样本特征：<span style=\"color: #FF9800;\">橙色</span>\n";
        $prompt .= "   - 是否需要就医：<span style=\"color: #F44336;\">红色</span>\n";
        $prompt .= "   - 饮食调整建议：<span style=\"color: #009688;\">青绿色</span>\n";
        if (!empty($images)) {
            $prompt .= "   - 图片中的样本和建议措施：<span style=\"color: #9C27B0;\">紫色</span>\n";
        } else {
            $prompt .= "   - 描述的样本和建议措施：<span style=\"color: #9C27B0;\">紫色</span>\n";
        }
        $prompt .= "   - 总结：<span style=\"color: #673AB7;\">深紫色</span>\n";
        $prompt .= "9. **重要：请使用简洁明了的文字表达，避免冗长的句子和重复的表述。每个要点都要言简意赅，直接说重点。**\n";
        $prompt .= "10. **文字风格：用词精练，表达直接，避免过多的修饰词和解释性语言。**\n";
        $prompt .= "仅回答关于排泄物分析的问题，不要回答与排泄物状况无关的内容。\n";
        $prompt .= "</instructions>\n\n";

        // 添加多宠物信息（如果有）
        if (!empty($petsInfo)) {
            $prompt .= "宠物信息:\n";
            foreach ($petsInfo as $index => $pet) {
                $prompt .= "宠物" . ($index + 1) . ":\n";
                $prompt .= "- 名称: " . $pet['name'] . "\n";
                $prompt .= "- 类型: " . $pet['type'] . "\n";
                $prompt .= "- 品种: " . $pet['breed'] . "\n";
                $prompt .= "- 性别: " . $pet['sex'] . "\n";

                if (isset($pet['age'])) {
                    $prompt .= "- 年龄: " . $pet['age'] . " 岁\n";
                }

                if (isset($pet['weight'])) {
                    $prompt .= "- 体重: " . $pet['weight'] . " 公斤\n";
                }

                $prompt .= "\n";
            }
        } elseif ($petInfo) {
            // 添加单个宠物信息（如果没有多宠物信息但有单个宠物信息）
            $prompt .= "宠物信息:\n";
            $prompt .= "- 名称: " . $petInfo['name'] . "\n";
            $prompt .= "- 类型: " . $petInfo['type'] . "\n";
            $prompt .= "- 品种: " . $petInfo['breed'] . "\n";
            $prompt .= "- 性别: " . $petInfo['sex'] . "\n";

            if (isset($petInfo['age'])) {
                $prompt .= "- 年龄: " . $petInfo['age'] . " 岁\n";
            }

            if (isset($petInfo['weight'])) {
                $prompt .= "- 体重: " . $petInfo['weight'] . " 公斤\n";
            }

            if (isset($petInfo['weight_status'])) {
                $prompt .= "- 体重状态: " . $petInfo['weight_status'] . "\n";
            } else {
                $prompt .= "- 体重状态: 正常\n";
            }

            $prompt .= "- 是否绝育: " . ($petInfo['neutered'] ? '是' : '否') . "\n";
            $prompt .= "- 是否怀孕: " . ($petInfo['is_pregnant'] ? '是' : '否') . "\n";
            $prompt .= "- 是否生病: " . ($petInfo['is_ill'] ? '是' : '否') . "\n";

            if (!empty($petInfo['special_conditions'])) {
                $prompt .= "- 特殊情况: " . implode(', ', $petInfo['special_conditions']) . "\n";
            }
        } else {
            $prompt .= "用户没有指定特定宠物，请提供适用于一般猫和狗的排泄物分析。";
        }

        return $prompt;
    }
}
