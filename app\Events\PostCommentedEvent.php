<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostCommentedEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 帖子 ID
     *
     * @var int
     */
    public $postId;

    /**
     * 帖子所有者 ID
     *
     * @var int
     */
    public $postOwnerId;

    /**
     * 评论 ID
     *
     * @var int
     */
    public $commentId;

    /**
     * 评论者 ID
     *
     * @var int
     */
    public $commenterId;

    /**
     * Create a new event instance.
     *
     * @param int $postId 帖子 ID
     * @param int $postOwnerId 帖子所有者 ID
     * @param int $commentId 评论 ID
     * @param int $commenterId 评论者 ID
     * @return void
     */
    public function __construct($postId, $postOwnerId, $commentId, $commenterId)
    {
        $this->postId = $postId;
        $this->postOwnerId = $postOwnerId;
        $this->commentId = $commentId;
        $this->commenterId = $commenterId;
    }
}
