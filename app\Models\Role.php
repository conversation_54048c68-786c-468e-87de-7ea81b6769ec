<?php

namespace App\Models;

use App\Events\DataChanged;
use App\Exceptions\NoPermissionException;
use App\Helpers\Tree;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Database\Query\Builder;

/**
 * @mixin Builder
 */
class Role extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'status',
        'system_menu_ids',
        'pid',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    public static function getChildrenIds($user, $withself = true)
    {
        //只能查看 role_id = 自己，且 pid = 自己的role_id 的角色列表
        $roleIds = self::select(['id', 'pid'])->get()->toArray();
        $tree = Tree::instance()->init($roleIds, 'pid');
        return $tree->getChildrenIds($user->role_id, $withself);
    }

    public static function getList($search_data = array(), $user = null)
    {
        //遍历筛选条件
        $keyword = $search_data['keyword'] ?? "";
        $status = $search_data['status'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";

        $roleWhere = null;
        if ($user->role_id != 1) {  //校验权限
            $childrenIds = self::getChildrenIds($user);
            $roleWhere = function (Builder $query) use ($childrenIds) {
                $query->whereIn('id', $childrenIds);
            };
        }

        $list= self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%");
            });
        })
            ->when($status, function (Builder $query) use ($status) {
                $query->where('status', $status);
            })
            ->when($roleWhere, $roleWhere)
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->orderBy('id', 'asc')
            ->get()->toArray();
        $tree = Tree::instance()->init($list, 'pid');
        if ($user->role_id == 1) {
            $result = $tree->getTreeList($tree->getTreeArray(0));
        } else {
            $result = $tree->getTreeList($tree->getTreeArray($user['pid']));
        }
        return $result;
    }

    public static function getDetail(int $id, $user = null)
    {
        if ($user->role_id != 1) {  //校验权限
            $childrenIds = self::getChildrenIds($user);
            if (!in_array($id, $childrenIds)) {
                throw new NoPermissionException();
            }
        }
        return self::where('id', $id)->first();
    }

    public static function saveDetail($data = array(), $user = null)
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('name', $data['name'])->where('id', '!=', $id)->exists()) {
                throw new Exception('角色名稱已存在');
            }
            if ($user->role_id != 1) {  //校验权限
                $childrenIds = self::getChildrenIds($user, false);  //不包含自身的pid
                if ($id > 0 && !in_array($id, $childrenIds)) {
                    throw new NoPermissionException('role id error');  //只能修改自己下级角色的权限
                }
                if (!in_array($data['pid'], $childrenIds)) {
                    throw new NoPermissionException('role pid error');  //勾选的父级角色不能是和自己同级，只能是下级
                }
            }
            if ($data['pid'] > 0) {
                $parentSystemMenuIds = array_filter(explode(',', self::where('id', $data['pid'])->value('system_menu_ids')));
                $systemMenuIds = array_filter(explode(',', $data['system_menu_ids']));
                // $systemMenuIds 的范围不能超出 $parentSystemMenuIds
                if (!empty(array_diff($systemMenuIds, $parentSystemMenuIds))) {
                    throw new NoPermissionException('role system_menu_ids error');
                }
            }
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "角色：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('更新失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $record = array();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id, $user)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::getDetail($id, $user) or throw_if(true, 'RuntimeException', "角色：{$id}不存在或已删除");
            $success = $info->where('id', $id)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
