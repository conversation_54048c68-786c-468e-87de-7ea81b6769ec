<?php

namespace App\Models;

use App\Events\DataChanged;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class Article extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'category_id',
        'title',
        'title_tc',
        'title_en',
        'content',
        'content_tc',
        'content_en',
        'image',
        'images',
        'good_id',
        'status',
        'price',
        'line_price',
        'sort_order'
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    /**
     * 分类
     */
    public function category()
    {
        return $this->belongsTo(ArticleCategory::class);
    }

    /**
     * 商品
     */
    public function good()
    {
        return $this->belongsTo(Good::class);
    }

    public static function getList($search_data = array(), $requestAppUserId = null)
    {
        // 遍歷篩選條件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $status = $search_data['status'] ?? "";
        $categoryId = $search_data['category_id'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $createdStart = $search_data['created_start'] ?? "";
        $createdEnd = $search_data['created_end'] ?? "";

        $query = self::when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($status, function (Builder $query) use ($status) {
                $query->where('status', $status);
            })
            ->when($categoryId, function (Builder $query) use ($categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->when($createdStart && $createdEnd, function (Builder $query) use ($createdStart, $createdEnd) {
                $query->whereBetween('created_at', [$createdStart, $createdEnd]);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->select([
                'id',
                'title',
                'title_tc',
                'title_en',
                'image',
                'images',
                'category_id',
                'good_id',
                'price',
                'sort_order',
                'view_count',
                'created_at',
                'updated_at'
            ])
            ->with([
                'good'         => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en', 'image', 'inventory', 'market_price', 'cost_price', 'price');
                },
                'good.goodSku' => function (Builder $q) {
                    $q->select('id', 'good_id', 'image', 'price', 'inventory', 'product_sku');
                },
                'category'     => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en');
                },
            ])
            ->orderBy('id', 'desc');

        $list = $query->paginate($limit, ['*'], 'page', $page);

        // 处理返回数据，整理封面图和轮播图
        $list->getCollection()->transform(function ($item) use ($requestAppUserId) {
            $item = $item->toArray();
            self::renderItem($item, $requestAppUserId);
            return $item;
        });

        return $list;
    }

    public static function renderItem(&$item, $requestAppUserId)
    {
        $item['images'] = array_filter(explode(',', $item['images']));
        //app用户访问，追加收藏状态
        if ($requestAppUserId) {
            AppContent::renderTimeAgo($item);
            self::renderRealtimeDetail($item, $requestAppUserId);
        }
    }

    //todo app文章列表应该需要缓存，缓存的view_count肯定是需要实时读取redis的
    public static function renderRealtimeDetail(&$article, $appUserId)
    {
        if (!$article['id']) {
            return;
        }
        $article['view_count'] = ArticleView::getViewCount($article['id']);
    }

    //增加访问数量
    public static function incrementView($articleId)
    {
        return self::where('id', $articleId)->increment('view_count', 1);
    }

    public static function getDetail(int $id)
    {
        return self::where('id', $id)
            ->with([
                'good'         => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en', 'image', 'inventory', 'market_price', 'cost_price', 'price');
                },
                'good.goodSku'            => function (Builder $q) {
                    $q->select('id', 'good_id', 'image', 'market_price', 'cost_price', 'price', 'inventory', 'weight', 'product_sku', 'created_at', 'updated_at');
                },
                'category'     => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en');
                },
            ])
            ->first();
    }

    public static function getDetailForApp(int $id, $requestAppUserId)
    {
        $article= self::where('id', $id)
            ->with([
                'good'         => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en', 'image', 'inventory', 'market_price', 'cost_price', 'price');
                },
                'good.goodSku'            => function (Builder $q) {
                    $q->select('id', 'good_id', 'image', 'market_price', 'cost_price', 'price', 'inventory', 'weight', 'product_sku', 'created_at', 'updated_at');
                },
                'category'     => function (Builder $q) {
                    $q->select('id', 'name', 'name_tc', 'name_en');
                },
            ])
            ->first();

        if (!$article) {
            return [false, [], '文章不存在或已下架'];
        }
        $article = $article->toArray();

        ArticleView::addPvuv($requestAppUserId, $id);
        self::renderItem($article, $requestAppUserId);

        return [true, $article, '获取成功'];
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        try {
            $id = $data['id'] ?? 0;
            if (self::where('title', $data['title'])->where('id', '!=', $id)->exists()) {
                throw new Exception('文章已存在');
            }
            if (!empty($data['images']) && is_array($data['images'])) {
                $data['images'] = implode(',', $data['images']);
            } else {
                $data['images'] = '';  // 如果没有图片就设为空字符串
            }
            if (empty($id)) {
                $result = self::create($data);
                $id = $result->id;
                $success = true;
                $oldData = null;
                $newData = array_merge($data, ['id' => $id]);
            } else {
                $oldData = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "文章：{$id}不存在或已删除");
                $oldData = $oldData->toArray();
                $success = self::where('id', $id)->update($data);
                $newData = array_merge($oldData, $data);
            }
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $id;
                $message = '保存成功';
                event(new DataChanged(static::class, $newData, $oldData));
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }

    public static function del($id)
    {
        DB::beginTransaction();
        $record['id'] = $id;
        try {
            $info = self::where('id', $id)->first() or throw_if(true, 'RuntimeException', "文章：{$id}不存在或已删除");
            $success = $info->delete();
            if ($success) {
                $success = true;
                $message = '删除成功';
                event(new DataChanged(static::class, null, $info->toArray()));
            } else {
                throw new Exception('删除失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $message = $e->getMessage();
        }
        return [$success, $record, $message];
    }
}
