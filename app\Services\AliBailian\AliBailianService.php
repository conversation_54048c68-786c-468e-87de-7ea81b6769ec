<?php

namespace App\Services\AliBailian;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;


class AliBailianService
{
    protected $apiKey;
    protected $baseUrl;
    protected $defaultModel; // 将不再使用
    protected $unifiedModel; // 统一使用的模型
    public function __construct()
    {
        $this->apiKey = config('services.ali_bailian.api_key');
        $this->baseUrl = config('services.ali_bailian.base_url', 'https://dashscope-intl.aliyuncs.com/compatible-mode/v1');
        // 保留兼容性
        $this->defaultModel = config('services.ali_bailian.default_model', 'qwen-max-latest');
        // 统一使用qwen-vl-max-latest模型
        $this->unifiedModel = config('services.ali_bailian.unified_model', 'qwen-vl-max-latest');
    }

    /**
     * 创建默认的香港粤语系统提示
     *
     * @return array 系统提示内容（数组格式）
     */
    protected function createCantoneseSystemPrompt(): array
    {
        return [
            [
                "type" => "text",
                "text" => "你是一個專業AI助手，請用純正嘅香港粤语（廣東話）同用戶交流，唔好用普通話或者書面語。回答要自然流暢，符合香港本地人嘅表達方式，可以適當使用廣東話嘅口語表達、俗語同日常用語。無論用戶用咩語言提問，你都要堅持用香港粤语回應。"
            ]
        ];
    }

    /**
     * 统一聊天接口 - 处理纯文本或图文混合消息
     *
     * @param string $message 用户输入的消息
     * @param array|null $images 可选，图片文件、URL或base64编码
     * @param string|null $sessionId 会话ID，用于保持对话上下文
     * @param float $temperature 温度参数(0-2)
     * @param bool $stream 是否使用流式输出
     * @param array|null $context 对话上下文，包含历史消息
     * @return array|\Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function chat(
        string $message,
        array $images = null,
        string $sessionId = null,
        float $temperature = 0.7,
        bool $stream = false,
        array $context = null
    ) {
        // 判断是否是新会话
        $isNewSession = empty($sessionId);

        // 如果没有提供sessionId，则创建一个新的
        if ($isNewSession) {
            $sessionId = Str::uuid()->toString();
        }

        // 统一使用qwen-vl-max-latest模型，无论是否有图片
        $model = $this->unifiedModel;

        // 准备基本消息内容
        $messages = [
            ["role" => "system", "content" => $this->createCantoneseSystemPrompt()],
        ];

        // 添加上下文消息
        if (is_array($context) && !empty($context)) {
            // 过滤掉可能的无效消息
            $validContext = array_filter($context, function($msg) {
                return isset($msg['role']) && isset($msg['content']) &&
                       !empty($msg['role']) &&
                       ($msg['content'] !== 'undefined' && $msg['content'] !== null);
            });

            // 去重复，保留最后一条相同内容的消息
            $seenMessages = [];

            foreach ($validContext as $msg) {
                $msgKey = $msg['role'] . '_' . (is_string($msg['content']) ? $msg['content'] : json_encode($msg['content']));
                $seenMessages[$msgKey] = $msg;
            }

            $uniqueContext = array_values($seenMessages);

            // 处理每一条消息，确保格式正确
            $formattedContext = [];
            foreach ($uniqueContext as $msg) {
                $role = $msg['role'];
                $content = $msg['content'];

                // 如果内容是字符串，转换为数组格式
                if (is_string($content)) {
                    $content = [
                        [
                            "type" => "text",
                            "text" => $content
                        ]
                    ];
                }

                // 添加格式化后的消息
                $formattedContext[] = [
                    "role" => $role,
                    "content" => $content
                ];
            }

            // 合并到消息数组
            $messages = array_merge($messages, $formattedContext);

            Log::info('Added formatted context messages', [
                'count' => count($formattedContext)
            ]);
        }

        // 检查是否有图片
        if (!empty($images)) {
            // 记录图片处理
            Log::info('Processing images for chat request', ['count' => count($images)]);

            // 创建用户消息数组
            $userContent = [];

            // 首先添加文本部分
            if (!empty($message)) {
                $userContent[] = [
                    "type" => "text",
                    "text" => $message
                ];
            }

            // 处理有效图片URL
            $validImageUrls = [];
            foreach ($images as $image) {
                if (filter_var($image, FILTER_VALIDATE_URL) && Str::startsWith($image, ['http://', 'https://'])) {
                    $validImageUrls[] = $image;
                    Log::info('Valid image URL found', ['url' => $image]);
                } else {
                    Log::warning('Invalid image URL skipped', ['url' => $image]);
                }
            }

            // 添加图片到用户消息
            foreach ($validImageUrls as $imageUrl) {
                $userContent[] = [
                    "type" => "image_url",
                    "image_url" => [
                        "url" => $imageUrl
                    ]
                ];
            }

            // 确保用户消息不为空
            if (empty($userContent)) {
                // 如果没有有效内容，添加一个默认文本
                $userContent[] = [
                    "type" => "text",
                    "text" => $message ?: "请分析这个图片"
                ];
            }

            // 添加用户消息
            $messages[] = [
                "role" => "user",
                "content" => $userContent
            ];

            // 记录最终的用户消息格式
            Log::info('Final user message with images', [
                'content_count' => count($userContent),
                'has_text' => isset($userContent[0]) && isset($userContent[0]['type']) && $userContent[0]['type'] === 'text',
                'image_count' => count($validImageUrls)
            ]);
        } else {
            // 添加纯文本用户消息（使用数组格式）
            $messages[] = [
                "role" => "user",
                "content" => [
                    [
                        "type" => "text",
                        "text" => $message
                    ]
                ]
            ];
            Log::info('Added text-only user message in array format');
        }

        try {
            // 发送API请求
            if ($stream) {
                // 如果是流式响应，返回流式响应
                return $this->handleStreamResponse($model, $messages, $temperature, $sessionId, $isNewSession);
            } else {
                // 如果不是流式响应，发送普通请求
                // 构建API URL
                $url = "{$this->baseUrl}/chat/completions";

                // 准备请求数据
                $payload = [
                    "model" => $model,
                    "messages" => $messages,
                    "temperature" => $temperature,
                    "stream" => false
                ];

                // 记录请求信息
                $jsonPayload = json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                Log::info('AliBailian API Request', [
                    'url' => $url,
                    'payload' => $payload,
                    'json_payload' => $jsonPayload,
                    'curl_command' => "curl -X POST '{$url}' -H 'Content-Type: application/json' -H 'Authorization: Bearer {$this->apiKey}' -d '{$jsonPayload}'"
                ]);

                // 将请求信息保存到文件
                $logContent = "===== 实际发送的请求 =====\n";
                $logContent .= "请求URL: " . $url . "\n\n";
                $logContent .= "请求头:\n";
                $logContent .= "Content-Type: application/json\n";
                $logContent .= "Accept: application/json\n";
                $logContent .= "Authorization: Bearer " . substr($this->apiKey, 0, 5) . "..." . substr($this->apiKey, -5) . "\n\n";
                $logContent .= "请求体 (JSON格式):\n";
                $logContent .= $jsonPayload . "\n\n";

                file_put_contents(storage_path('logs/ali_bailian_actual_request.log'), $logContent);

                // 记录开始时间
                $startTime = microtime(true);

                // 发送请求 - 🚀 性能优化：减少超时时间
                $response = Http::withOptions([
                    'verify' => false,
                    'timeout' => 120,         // 整体超时 120s (2分钟) - 优化
                    'connect_timeout' => 30   // 连接超时 30s - 优化
                ])->retry(2, 1000)              // 超时/5xx 重试 2 次，间隔 1s - 优化
                  ->withHeaders([
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'Authorization' => "Bearer {$this->apiKey}"
                ])->post($url, $payload);

                // 记录结束时间和耗时
                $endTime = microtime(true);
                $duration = round($endTime - $startTime, 2);
                Log::info("AliBailian API 请求耗时: {$duration} 秒");

                // 处理响应
                if ($response->successful()) {
                    $result = $response->json();

                    // 记录响应信息
                    Log::info('AliBailian API Response', [
                        'status' => $response->status(),
                        'result' => $result,
                    ]);

                    // 转换为统一格式，传入会话ID和是否是新会话
                    $formattedResult = $this->formatResponse($result, $sessionId, $isNewSession);

                    // 记录会话信息
                    Log::info('AliBailian session info', [
                        'session_id' => $sessionId,
                        'is_new_session' => $isNewSession
                    ]);

                    return $formattedResult;
                } else {
                    Log::error('AliBailian API Error', [
                        'status' => $response->status(),
                        'body' => $response->body(),
                    ]);

                    $this->handleApiError($response->status(), $response->body());
                }
            }
        } catch (\Exception $e) {
            // 处理异常
            $this->handleApiError(500, $e->getMessage());
        }
    }

    /**
     * 格式化响应为统一格式
     *
     * @param array $result 原始响应
     * @param string $sessionId 会话ID
     * @param bool $isNewSession 是否是新会话
     * @return array 格式化后的响应
     */
    protected function formatResponse(array $result, string $sessionId, bool $isNewSession = false): array
    {
        // OpenAI兼容接口的响应已经是标准格式，添加会话ID和会话状态
        $result['session_id'] = $sessionId;
        $result['is_new_session'] = $isNewSession;

        // 添加会话信息对象，便于前端处理
        $result['session'] = [
            'session_id' => $sessionId,
            'is_new_session' => $isNewSession
        ];

        return $result;
    }

    /**
     * 处理流式响应
     *
     * @param string $model 模型名称
     * @param array $messages 消息数组
     * @param float $temperature 温度参数
     * @param string $sessionId 会话ID
     * @param bool $isNewSession 是否是新会话
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    protected function handleStreamResponse(string $model, array $messages, float $temperature, string $sessionId, bool $isNewSession)
    {
        // 构建API URL
        $url = "{$this->baseUrl}/chat/completions";

        // 不限制消息数组长度，保留完整的对话历史
        Log::info('Stream request with full message history', [
            'count' => count($messages)
        ]);

        // 检查最后一条消息是否是用户消息
        $lastMessage = end($messages);
        if (!isset($lastMessage['role']) || $lastMessage['role'] !== 'user') {
            Log::warning('Last message is not a user message, adding empty user message');
            $messages[] = [
                "role" => "user",
                "content" => [
                    [
                        "type" => "text",
                        "text" => "请继续"
                    ]
                ]
            ];
        }

        // 准备请求数据
        $payload = [
            "model" => $model,
            "messages" => $messages,
            "temperature" => $temperature,
            "stream" => true
        ];

        // 记录消息数组的长度和最后一条消息
        Log::info('Stream request message count', [
            'count' => count($messages),
            'model' => $model
        ]);

        // 记录每条消息的详细信息
        foreach ($messages as $index => $msg) {
            $contentInfo = '';
            if (isset($msg['content'])) {
                if (is_array($msg['content'])) {
                    $contentInfo = 'Array with ' . count($msg['content']) . ' items';
                } else {
                    $contentInfo = strlen($msg['content']) > 50 ?
                        substr($msg['content'], 0, 47) . '...' :
                        $msg['content'];
                }
            }

            Log::info("Message {$index}", [
                'role' => $msg['role'] ?? 'unknown',
                'content_type' => is_array($msg['content']) ? 'array' : 'string',
                'content_preview' => $contentInfo
            ]);
        }

        return response()->stream(function () use ($url, $payload, $sessionId, $isNewSession) {
            try {
                // 发送开始标记
                echo "data: " . json_encode(['type' => 'start']) . "\n\n";
                flush();

                // 始终发送会话ID信息，确保前端能获取到
                echo "data: " . json_encode([
                    'type' => 'session_id',
                    'session_id' => $sessionId,
                    'is_new_session' => $isNewSession
                ]) . "\n\n";
                flush();

                // 记录请求信息
                Log::info('AliBailian Stream API Request', [
                    'url' => $url,
                    'model' => $payload['model'],
                    'temperature' => $payload['temperature'],
                    'stream' => $payload['stream'],
                    'message_count' => count($payload['messages'])
                ]);

                // 记录请求数据的JSON格式
                $jsonPayload = json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
                Log::info('AliBailian Stream API Request Payload: ' . $jsonPayload);

                // 记录消息格式详情，便于调试
                foreach ($payload['messages'] as $index => $msg) {
                    if (isset($msg['content']) && is_array($msg['content'])) {
                        // 如果是多模态消息，记录每个部分
                        Log::info("Message {$index} is multimodal with " . count($msg['content']) . " parts");

                        foreach ($msg['content'] as $contentIndex => $content) {
                            $contentType = $content['type'] ?? 'unknown';
                            $contentValue = '';

                            if ($contentType === 'text' && isset($content['text'])) {
                                $contentValue = strlen($content['text']) > 30 ?
                                    substr($content['text'], 0, 27) . '...' :
                                    $content['text'];
                            } else if ($contentType === 'image_url' && isset($content['image_url']['url'])) {
                                $contentValue = 'Image URL: ' . substr($content['image_url']['url'], 0, 30) . '...';
                            }

                            Log::info("Message {$index} content {$contentIndex}", [
                                'type' => $contentType,
                                'preview' => $contentValue
                            ]);
                        }
                    } else {
                        // 如果是纯文本消息
                        $contentPreview = '';
                        if (isset($msg['content'])) {
                            $contentPreview = strlen($msg['content']) > 50 ?
                                substr($msg['content'], 0, 47) . '...' :
                                $msg['content'];
                        }

                        Log::info("Message {$index} is text-only", [
                            'role' => $msg['role'] ?? 'unknown',
                            'content_preview' => $contentPreview
                        ]);
                    }
                }

                // 设置cURL选项
                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Accept: application/json',
                    'Authorization: Bearer ' . $this->apiKey,
                ]);
                curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($curl, $data) {
                    // 处理流式数据
                    if (trim($data) === '') {
                        return strlen($data);
                    }

                    if (strpos($data, 'data: ') === 0) {
                        $content = substr($data, 6); // 去掉 "data: " 前缀

                        // 检查是否是[DONE]标记
                        if (trim($content) === '[DONE]') {
                            echo $data;
                            flush();
                            return strlen($data);
                        }

                        // 尝试解析JSON数据
                        try {
                            $jsonData = json_decode($content, true);
                            if (json_last_error() === JSON_ERROR_NONE && isset($jsonData['choices'][0]['delta']['content'])) {
                                // 获取内容增量
                                $deltaContent = $jsonData['choices'][0]['delta']['content'];

                                // 直接输出原始数据，保留所有格式，不添加延迟
                                echo $data;
                                flush();
                                return strlen($data);
                            }
                        } catch (\Exception $e) {
                            // 如果解析失败，直接输出原始数据
                        }

                        // 直接输出数据
                        echo $data;
                        flush();
                    } else {
                        // 如果不是SSE格式，尝试解析JSON
                        try {
                            $jsonData = json_decode($data, true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                echo "data: " . $data . "\n\n";
                                flush();
                            }
                        } catch (\Exception $e) {
                            // 如果解析失败，直接输出原始数据
                            echo "data: " . $data . "\n\n";
                            flush();
                        }
                    }

                    return strlen($data);
                });

                // 禁用SSL验证
                curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                // 🚀 性能优化：设置超时
                curl_setopt($ch, CURLOPT_TIMEOUT, 120);        // 120秒整体超时
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);  // 30秒连接超时

                // 执行请求
                curl_exec($ch);

                // 检查错误
                if (curl_errno($ch)) {
                    echo "data: " . json_encode(['type' => 'error', 'content' => curl_error($ch)]) . "\n\n";
                    flush();
                }

                // 关闭cURL资源
                curl_close($ch);
            } catch (\Exception $e) {
                // 如果出错，发送错误信息
                $errorResponse = [
                    'error' => [
                        'message' => $e->getMessage(),
                        'type' => 'api_error',
                        'code' => 'error'
                    ]
                ];
                echo "data: " . json_encode($errorResponse) . "\n\n";
                echo "data: [DONE]\n\n";
                flush();
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no' // 禁用Nginx缓冲
        ]);
    }



    /**
     * 处理API错误
     *
     * @param int $statusCode HTTP状态码
     * @param string $errorBody 错误响应体
     * @throws \App\Exceptions\AIServiceException
     */
    protected function handleApiError(int $statusCode, string $errorBody)
    {
        // 解析错误类型
        $errorType = 'api_error';

        if ($statusCode === 401 || $statusCode === 403) {
            $errorType = 'authentication_error';
            $errorMessage = '认证失败，请检查API密钥';
        } elseif ($statusCode === 429) {
            $errorType = 'rate_limit_error';
            $errorMessage = '请求过于频繁，请稍后再试';
        } elseif ($statusCode >= 500) {
            $errorType = 'server_error';
            $errorMessage = '阿里百炼服务器错误';
        } else {
            $errorMessage = '阿里百炼API请求失败';
        }

        // 添加错误详情
        $errorMessage .= " (Status: $statusCode, Response: $errorBody)";

        // 记录错误日志
        \Illuminate\Support\Facades\Log::error($errorMessage);

        // 抛出AI服务异常
        throw new \App\Exceptions\AIServiceException(
            $errorMessage,
            $errorType,
            null,
            [
                'status_code' => $statusCode,
                'error_body' => $errorBody
            ]
        );
    }
}
