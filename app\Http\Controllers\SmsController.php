<?php

namespace App\Http\Controllers;

use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\SendMessageToGlobeRequest;
use Illuminate\Http\Request;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Dysmsapi;
use AlibabaCloud\SDK\Dysmsapi\V20180501\Models\BatchSendMessageToGlobeRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use Darabonba\OpenApi\Models\Config;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Helpers\ResponseHelper;
use Illuminate\Support\Facades\Log;

class SmsController extends Controller
{
    private $client;

    public function __construct()
    {
        $this->client = $this->createClient();
    }

    private function createClient()
    {
        $config = new Config([
            "accessKeyId"     => config('services.alioss.key_id'),
            "accessKeySecret" => config('services.alioss.key_secret')
        ]);
        $config->endpoint = "dysmsapi.ap-southeast-1.aliyuncs.com";
        return new Dysmsapi($config);
    }

    /**
     * 发送短信
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendSms(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'phone' => 'required|string',
        ]);

        $phoneNumber = $request->input('phone');
        $cacheKey = 'sms_' . $phoneNumber;

        // 检查是否在1分钟内已发送过验证码
        if (redis()->exists($cacheKey . '_lock')) {
            return ResponseHelper::error('You can only request a code once per minute.');
        }

        // 生成6位数验证码
        $verificationCode = rand(100000, 999999);
        $message = 'Your verification code is ' . $verificationCode . '. The code is valid for 5 minutes.';

        $sendMessageToGlobeRequest = new SendMessageToGlobeRequest([
            "from"    => "Pixie",
            "to"      => $phoneNumber,
            "message" => $message
        ]);

        try {
            $response = $this->client->sendMessageToGlobeWithOptions($sendMessageToGlobeRequest, new RuntimeOptions([]));

            if ($response->body->responseCode != 'OK') {
                throw new \Exception($response->body->responseDescription); //短信发送失败
            }

            // 成功发送短信后，将验证码和锁缓存
            redis()->set($cacheKey, $verificationCode, 'EX', 300); // 设置验证码有效期为5分钟
            redis()->set($cacheKey . '_lock', true, 'EX', 60); // 设置1分钟的调用锁
            $data = \App::isLocal() ? ['response' => $response, 'verification_code' => $verificationCode] : [];

            return ResponseHelper::success($data);
        } catch (\Exception $error) {
            if (!($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            logErr($error);
            return ResponseHelper::error('Failed to send SMS: ' . $error->message);
        }
    }
}
