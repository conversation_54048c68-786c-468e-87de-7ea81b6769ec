<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LogController extends Controller
{
    protected $allowTypes = ['error', 'laravel', 'sql', 'cli'];

    public function getList(Request $request)
    {
        $request->validate([
            'type' => 'nullable|in:' . implode(',', $this->allowTypes),
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|between:1,100'
        ]);

        $type = $request->input('type');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        // 如果有type就查指定类型,否则查所有
        $path = 'logs/' . ($type ? $type . '-*.log' : '*.log');
        $files = array_filter(glob(storage_path($path)), 'is_file');

        $allFiles  = array_map(function ($file) {
            $fileSize = filesize($file);
            $fileModified = date('Y-m-d H:i:s', filemtime($file));
            return [
                'fileName'     => basename($file),
                'fileSize'     => bytesToHuman($fileSize),
                'fileModified' => $fileModified,
            ];
        }, $files);

        // 按修改时间倒序排序
        usort($allFiles, function($a, $b) {
            return strtotime($b['fileModified']) - strtotime($a['fileModified']);
        });

        // 手动分页
        $total = count($allFiles);
        $offset = ($page - 1) * $perPage;
        $files = array_slice($allFiles, $offset, $perPage);

        return ResponseHelper::success([
            'data' => $files,
            'total' => $total,
            'current_page' => $page,
            'per_page' => $perPage,
            'last_page' => ceil($total / $perPage)
        ]);

    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'fileName' => 'required|max:255',
        ]);
        $fileName = $request->input('fileName');
        $filePath = storage_path('logs/' . $fileName);
        //解析日志类型
        $type = substr($fileName, 0, strpos($fileName, '-'));
        if (!in_array($type, $this->allowTypes)) {
            return ResponseHelper::error('文件类型有误');
        }
        if (!file_exists($filePath)) {
            return ResponseHelper::error('文件不存在');
        }
        //文件大小超过3MB，仅显示最新的10000行数据
        $fileSize = filesize($filePath);
        if ($fileSize > 3 * 1024 * 1024) {
            $lines = array_slice(file($filePath), -10000);
            $content = implode('', $lines);
        } else {
            $content = file_get_contents($filePath);
        }
        //替换 Bearer xxx 避免token泄露
        $content = preg_replace('/\[\"Bearer.+?\\"]/', '["Bearer ***"]', $content);
        return ResponseHelper::success(['fileSize' => $fileSize, 'content' => $content]);
    }

}
