<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use App\Services\PetMatchingService;

/**
 * 商品推荐模型 - 业务逻辑层
 */
class GoodRecommendation extends Model
{
    // 不使用数据库表
    protected $table = null;
    public $timestamps = false;

    /**
     * 获取用户宠物的推荐商品
     *
     * @param int $userId 用户ID
     * @param array $petIds 宠物ID数组
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array
     */
    public static function getRecommendationsForUser($userId, $petIds, $page = 1, $perPage = 15)
    {
        // 获取用户有权限访问的宠物信息（包括拥有的和有master权限的）
        $pets = Pet::whereIn('id', $petIds)
            ->where(function($query) use ($userId) {
                $query->where('owner_id', $userId)
                      ->orWhereIn('id', function($subQuery) use ($userId) {
                          $subQuery->select('pet_id')
                                   ->from('pets_masters')
                                   ->where('user_id', $userId)
                                   ->whereNull('deleted_at');
                      });
            })
            ->with(['petType', 'petBreed'])
            ->get();

        if ($pets->isEmpty()) {
            return [
                'data' => [],
                'total' => 0,
                'current_page' => $page,
                'per_page' => $perPage,
                'last_page' => 1,
                'message' => '宠物信息不存在',
                'pets_info' => []
            ];
        }

        // 第一阶段：基于SKU的pet_filter_tags筛选匹配的商品
        $matchedGoods = self::filterGoodsByPetMatching($pets);

        if ($matchedGoods->isEmpty()) {
            return [
                'data' => [],
                'total' => 0,
                'current_page' => $page,
                'per_page' => $perPage,
                'last_page' => 1,
                'message' => '暂无适合您宠物的商品推荐',
                'pets_info' => self::formatPetsInfo($pets)
            ];
        }

        // 第二阶段：对筛选后的商品应用推荐算法
        $recommendedGoods = self::applyRecommendationAlgorithm($matchedGoods, $pets, $userId);

        // 第三阶段：为商品添加SKU匹配信息
        $goodsWithSkuMatching = self::addSkuMatchingData($recommendedGoods, $userId);

        // 分页处理
        $total = $goodsWithSkuMatching->count();
        $lastPage = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;
        $paginatedGoods = $goodsWithSkuMatching->slice($offset, $perPage)->values();

        return [
            'data' => $paginatedGoods,
            'total' => $total,
            'current_page' => $page,
            'per_page' => $perPage,
            'last_page' => $lastPage,
            'pets_info' => self::formatPetsInfo($pets),
            'algorithm_info' => self::getAlgorithmInfo()
        ];
    }

    /**
     * 第一阶段：基于SKU的pet_filter_tags筛选匹配的商品（优化版本）
     *
     * @param Collection $pets 宠物集合
     * @return Collection
     */
    private static function filterGoodsByPetMatching($pets)
    {
        $petMatchingService = new PetMatchingService();
        $userPetIds = $pets->pluck('id')->toArray();

        // 优化：先获取有空标签的商品ID（通用商品）
        $universalGoodIds = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->whereHas('goodSku', function($query) {
                $query->where(function($subQuery) {
                    $subQuery->whereNull('pet_filter_tags')
                            ->orWhere('pet_filter_tags', '')
                            ->orWhere('pet_filter_tags', '[]');
                });
            })
            ->pluck('id')
            ->toArray();

        // 获取有筛选标签的商品，进行精确匹配
        $filteredGoods = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->whereHas('goodSku', function($query) {
                $query->whereNotNull('pet_filter_tags')
                      ->where('pet_filter_tags', '!=', '')
                      ->where('pet_filter_tags', '!=', '[]');
            })
            ->with(['goodSku', 'category'])
            ->get()
            ->filter(function($good) use ($pets, $petMatchingService, $userPetIds) {
                // 检查商品是否有任何SKU匹配用户宠物
                return $good->goodSku->some(function($sku) use ($pets, $petMatchingService, $userPetIds) {
                    // 跳过空标签的SKU（已在上面处理）
                    if (empty($sku->pet_filter_tags) ||
                        $sku->pet_filter_tags === '[]' ||
                        $sku->pet_filter_tags === '') {
                        return false;
                    }

                    try {
                        // 使用PetMatchingService检查是否有任何宠物匹配这个SKU
                        $matchResult = $petMatchingService->calculateMatchingPets($sku->pet_filter_tags);
                        $matchedPetIds = collect($matchResult['pets'])->pluck('id')->toArray();

                        // 检查用户的宠物是否在匹配列表中
                        return !empty(array_intersect($userPetIds, $matchedPetIds));

                    } catch (\Exception $e) {
                        // 如果匹配服务出错，记录日志但不影响推荐
                        \Log::warning('SKU宠物匹配失败', [
                            'sku_id' => $sku->id,
                            'pet_filter_tags' => $sku->pet_filter_tags,
                            'error' => $e->getMessage()
                        ]);
                        return false;
                    }
                });
            });

        $filteredGoodIds = $filteredGoods->pluck('id')->toArray();

        // 合并通用商品和匹配的商品
        $allMatchedGoodIds = array_unique(array_merge($universalGoodIds, $filteredGoodIds));

        // 返回最终的商品集合
        return Good::whereIn('id', $allMatchedGoodIds)
            ->where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->with(['goodSku', 'category'])
            ->get();
    }

    /**
     * 第二阶段：对筛选后的商品应用推荐算法
     *
     * @param Collection $goods 商品集合
     * @param Collection $pets 宠物集合
     * @param int $userId 用户ID
     * @return Collection
     */
    private static function applyRecommendationAlgorithm($goods, $pets, $userId)
    {
        $config = self::getActiveAlgorithmConfig();

        return $goods->map(function($good) use ($pets, $userId, $config) {
            // 计算推荐得分
            $scoreData = self::calculateGoodRecommendationScore($good, $pets, $userId, $config);

            // 添加推荐信息到商品对象
            $good->recommendation_score = $scoreData['score'];
            $good->algorithm_details = $scoreData;
            $good->recommendation_reason = self::generateRecommendationReason($good, $scoreData);

            return $good;
        })
        ->sortByDesc('recommendation_score') // 按推荐得分排序
        ->values();
    }

    /**
     * 第三阶段：为商品添加SKU匹配信息
     *
     * @param Collection $goods 商品集合
     * @param int $userId 用户ID
     * @return Collection
     */
    private static function addSkuMatchingData($goods, $userId)
    {
        if ($goods->isEmpty()) {
            return $goods;
        }

        // 收集所有需要处理的SKU
        $allSkus = [];
        foreach ($goods as $good) {
            foreach ($good->goodSku as $sku) {
                $allSkus[] = [
                    'id' => $sku->id,
                    'pet_filter_tags' => $sku->pet_filter_tags ?? []
                ];
            }
        }

        // 批量处理SKU匹配
        $skuMatchingData = [];
        if (!empty($allSkus)) {
            $petMatchingService = new PetMatchingService();
            $skuMatchingData = $petMatchingService->batchMatchUserPetsForSkus($allSkus, $userId);
        }

        // 为每个商品的SKU添加匹配信息
        return $goods->map(function($good) use ($skuMatchingData, $userId) {
            // 转换为数组以便修改
            $goodArray = $good->toArray();

            // 为每个SKU添加匹配的用户宠物信息
            if (isset($goodArray['good_sku'])) {
                foreach ($goodArray['good_sku'] as &$sku) {
                    // 添加匹配信息
                    $sku['matched_pets'] = $skuMatchingData[$sku['id']] ?? [];

                    // 如果SKU没有筛选标签，显示为适合所有宠物
                    if (empty($sku['pet_filter_tags']) ||
                        $sku['pet_filter_tags'] === '[]' ||
                        $sku['pet_filter_tags'] === '') {
                        $sku['matched_pets'] = self::getAllUserPetsForUniversalSku($userId);
                        $sku['is_universal'] = true;
                        $sku['matching_reason'] = '适合所有宠物';
                    } else {
                        $sku['is_universal'] = false;
                        $sku['matching_reason'] = !empty($sku['matched_pets']) ? '符合筛选条件' : '不符合筛选条件';
                    }

                    $sku['has_matched_pets'] = !empty($sku['matched_pets']);
                }
                unset($sku); // 解除引用
            }

            // 转换回对象格式，保持推荐算法添加的属性
            $goodObject = (object) $goodArray;
            $goodObject->recommendation_score = $good->recommendation_score;
            $goodObject->algorithm_details = $good->algorithm_details;
            $goodObject->recommendation_reason = $good->recommendation_reason;

            return $goodObject;
        });
    }

    /**
     * 获取用户所有宠物信息（用于通用SKU）
     *
     * @param int $userId 用户ID
     * @return array
     */
    private static function getAllUserPetsForUniversalSku($userId)
    {
        return Pet::where('owner_id', $userId)
            ->where('deleted_at', null)
            ->with(['petType:id,name', 'petBreed:id,name'])
            ->get()
            ->map(function($pet) {
                return [
                    'id' => $pet->id,
                    'name' => $pet->name,
                    'avatar' => $pet->avatar,
                    'type_name' => $pet->petType->name ?? '',
                    'breed_name' => $pet->petBreed->name ?? '',
                ];
            })
            ->toArray();
    }

    /**
     * 计算商品推荐得分
     *
     * @param Good $good 商品
     * @param Collection $pets 宠物集合
     * @param int $userId 用户ID
     * @param RecommendationAlgorithmConfig $config 算法配置
     * @return array
     */
    private static function calculateGoodRecommendationScore($good, $pets, $userId, $config)
    {
        $totalScore = 0;
        $details = [];

        // 1. 新品因子
        if ($config->enable_is_new) {
            $newScore = $good->is_new == 1 ? 1 : 0;
            $weightedScore = $newScore * $config->weight_is_new;
            $totalScore += $weightedScore;
            $details['is_new'] = ['raw' => $newScore, 'weighted' => round($weightedScore, 2)];
        }

        // 2. 热销因子
        if ($config->enable_is_hot) {
            $hotScore = $good->is_hot == 1 ? 1 : 0;
            $weightedScore = $hotScore * $config->weight_is_hot;
            $totalScore += $weightedScore;
            $details['is_hot'] = ['raw' => $hotScore, 'weighted' => round($weightedScore, 2)];
        }

        // 3. 销量因子
        if ($config->enable_sales_volume) {
            $totalSales = ($good->sales_actual ?? 0) + ($good->sales_initial ?? 0);
            $salesScore = min($totalSales / $config->hot_sales_threshold, 10); // 标准化到0-10
            $weightedScore = $salesScore * $config->weight_sales_volume;
            $totalScore += $weightedScore;
            $details['sales_volume'] = ['raw' => round($salesScore, 2), 'weighted' => round($weightedScore, 2)];
        }

        // 4. 用户购买历史
        if ($config->enable_user_purchase) {
            $purchaseScore = self::calculateUserPurchaseScore($userId, $good) * 10; // 扩展到0-10
            $weightedScore = $purchaseScore * $config->weight_user_purchase;
            $totalScore += $weightedScore;
            $details['user_purchase'] = ['raw' => round($purchaseScore, 2), 'weighted' => round($weightedScore, 2)];
        }

        // 5. 同类宠物偏好
        if ($config->enable_same_pet_preference) {
            $samePetScore = self::calculateSamePetPreferenceScore($pets, $good) * 10; // 扩展到0-10
            $weightedScore = $samePetScore * $config->weight_same_pet_preference;
            $totalScore += $weightedScore;
            $details['same_pet_preference'] = ['raw' => round($samePetScore, 2), 'weighted' => round($weightedScore, 2)];
        }

        // 应用随机因子
        $randomFactor = mt_rand($config->random_factor_min * 100, $config->random_factor_max * 100) / 100;
        $finalScore = $totalScore * $randomFactor;

        return [
            'score' => round($finalScore, 2),
            'base_score' => round($totalScore, 2),
            'random_factor' => $randomFactor,
            'config_id' => $config->id,
            'config_name' => $config->name,
            'details' => $details
        ];
    }

    /**
     * 计算用户购买历史得分
     *
     * @param int $userId 用户ID
     * @param Good $good 商品
     * @return float 0-1之间的得分
     */
    private static function calculateUserPurchaseScore($userId, $good)
    {
        // 获取用户在该商品分类下的购买次数（最近180天）
        $purchaseCount = GoodIndent::where('user_id', $userId)
            ->whereHas('Commodities', function($query) use ($good) {
                $query->whereHas('good', function($subQuery) use ($good) {
                    $subQuery->where('category_id', $good->category_id);
                });
            })
            ->where('created_at', '>=', now()->subDays(180))
            ->count();

        // 标准化到0-1，购买5次以上得满分
        return min($purchaseCount / 5, 1.0);
    }

    /**
     * 计算同类宠物偏好得分
     *
     * @param Collection $pets 宠物集合
     * @param Good $good 商品
     * @return float 0-1之间的得分
     */
    private static function calculateSamePetPreferenceScore($pets, $good)
    {
        $totalScore = 0;
        $petCount = 0;

        foreach ($pets as $pet) {
            // 获取同品种宠物主人对该商品的购买情况
            $samePetOwners = Pet::where('breed_id', $pet->breed_id)
                ->where('owner_id', '!=', $pet->owner_id)
                ->pluck('owner_id')
                ->unique();

            if ($samePetOwners->isEmpty()) {
                continue;
            }

            // 计算同品种宠物主人对该商品的购买次数
            $purchaseCount = GoodIndent::whereIn('user_id', $samePetOwners)
                ->whereHas('Commodities', function($query) use ($good) {
                    $query->where('good_id', $good->id);
                })
                ->where('created_at', '>=', now()->subDays(180))
                ->count();

            // 标准化得分
            $score = min($purchaseCount / $samePetOwners->count(), 1.0);
            $totalScore += $score;
            $petCount++;
        }

        return $petCount > 0 ? $totalScore / $petCount : 0;
    }

    /**
     * 生成推荐理由
     *
     * @param Good $good 商品
     * @param array $scoreData 得分数据
     * @return string
     */
    private static function generateRecommendationReason($good, $scoreData)
    {
        $reasons = [];
        $details = $scoreData['details'] ?? [];

        // 商品特性
        if (isset($details['is_new']) && $details['is_new']['raw'] == 1) {
            $reasons[] = "新品推荐";
        }

        if (isset($details['is_hot']) && $details['is_hot']['raw'] == 1) {
            $reasons[] = "热销商品";
        }

        // 销量因子
        if (isset($details['sales_volume']) && $details['sales_volume']['raw'] > 5) {
            $reasons[] = "销量领先";
        }

        // 用户偏好
        if (isset($details['user_purchase']) && $details['user_purchase']['raw'] > 5) {
            $reasons[] = "基于您的购买偏好";
        }

        // 同类宠物偏好
        if (isset($details['same_pet_preference']) && $details['same_pet_preference']['raw'] > 3) {
            $reasons[] = "同类宠物主人都在买";
        }

        // 如果没有特殊理由，使用通用理由
        if (empty($reasons)) {
            $reasons[] = "为您的宠物精选";
        }

        return implode('，', $reasons);
    }

    /**
     * 获取当前生效的算法配置
     *
     * @return RecommendationAlgorithmConfig
     */
    private static function getActiveAlgorithmConfig()
    {
        $config = RecommendationAlgorithmConfig::getActiveConfig();

        if (!$config) {
            // 返回默认配置
            return self::getDefaultAlgorithmConfig();
        }

        return $config;
    }

    /**
     * 获取默认算法配置
     *
     * @return object
     */
    private static function getDefaultAlgorithmConfig()
    {
        return (object) [
            'id' => 0,
            'name' => '默认配置',
            'enable_is_new' => true,
            'enable_is_hot' => true,
            'enable_sales_volume' => true,
            'enable_user_purchase' => true,
            'enable_same_pet_preference' => true,
            'weight_is_new' => 2.00,
            'weight_is_hot' => 2.00,
            'weight_sales_volume' => 1.00,
            'weight_user_purchase' => 1.50,
            'weight_same_pet_preference' => 1.50,
            'random_factor_min' => 0.80,
            'random_factor_max' => 1.20,
            'hot_sales_threshold' => 100
        ];
    }

    /**
     * 格式化宠物信息
     *
     * @param Collection $pets 宠物集合
     * @return array
     */
    private static function formatPetsInfo($pets)
    {
        return $pets->map(function($pet) {
            return [
                'id' => $pet->id,
                'name' => $pet->name,
                'type' => $pet->petType->name ?? '',
                'breed' => $pet->petBreed->name ?? ''
            ];
        })->toArray();
    }

    /**
     * 获取算法信息
     *
     * @return array
     */
    private static function getAlgorithmInfo()
    {
        $config = self::getActiveAlgorithmConfig();

        return [
            'config_id' => $config->id,
            'config_name' => $config->name,
            'algorithm_version' => 'v3.0',
            'enabled_factors' => [
                'is_new' => $config->enable_is_new,
                'is_hot' => $config->enable_is_hot,
                'sales_volume' => $config->enable_sales_volume,
                'user_purchase' => $config->enable_user_purchase,
                'same_pet_preference' => $config->enable_same_pet_preference
            ],
            'weights' => [
                'is_new' => $config->weight_is_new,
                'is_hot' => $config->weight_is_hot,
                'sales_volume' => $config->weight_sales_volume,
                'user_purchase' => $config->weight_user_purchase,
                'same_pet_preference' => $config->weight_same_pet_preference
            ]
        ];
    }

    /**
     * 清除推荐缓存
     *
     * @param array|null $petIds 宠物ID数组
     */
    public static function clearCache($petIds = null)
    {
        try {
            $redis = redis(); // 使用项目的redis()辅助函数

            if ($petIds) {
                // 清除特定宠物组合的缓存
                if (is_array($petIds)) {
                    $sortedPetIds = $petIds;
                    sort($sortedPetIds);
                    $petIdsStr = implode(',', $sortedPetIds);
                    $pattern = "pets_recommendations_v3:{$petIdsStr}:*";
                } else {
                    $pattern = "pets_recommendations_v3:*{$petIds}*";
                }

                $keys = $redis->keys($pattern);
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                // 清除所有推荐缓存
                $patterns = ["pets_recommendations_v3:*"];

                foreach ($patterns as $pattern) {
                    $keys = $redis->keys($pattern);
                    if (!empty($keys)) {
                        $redis->del($keys);
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('清除推荐缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试筛选逻辑 - 用于验证SKU匹配是否正确
     *
     * @param int $userId 用户ID
     * @param array $petIds 宠物ID数组
     * @return array 测试结果
     */
    public static function testFilterLogic($userId, $petIds)
    {
        // 获取用户有权限访问的宠物信息（包括拥有的和有master权限的）
        $pets = Pet::whereIn('id', $petIds)
            ->where(function($query) use ($userId) {
                $query->where('owner_id', $userId)
                      ->orWhereIn('id', function($subQuery) use ($userId) {
                          $subQuery->select('pet_id')
                                   ->from('pets_masters')
                                   ->where('user_id', $userId)
                                   ->whereNull('deleted_at');
                      });
            })
            ->with(['petType', 'petBreed'])
            ->get();

        if ($pets->isEmpty()) {
            return ['error' => '宠物信息不存在'];
        }

        // 统计各类商品数量
        $totalGoods = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)->count();

        $universalGoods = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->whereHas('goodSku', function($query) {
                $query->where(function($subQuery) {
                    $subQuery->whereNull('pet_filter_tags')
                            ->orWhere('pet_filter_tags', '')
                            ->orWhere('pet_filter_tags', '[]');
                });
            })
            ->count();

        $taggedGoods = Good::where('is_show', Good::GOOD_SHOW_PUTAWAY)
            ->whereHas('goodSku', function($query) {
                $query->whereNotNull('pet_filter_tags')
                      ->where('pet_filter_tags', '!=', '')
                      ->where('pet_filter_tags', '!=', '[]');
            })
            ->count();

        // 执行筛选
        $matchedGoods = self::filterGoodsByPetMatching($pets);

        // 添加SKU匹配信息
        $goodsWithSkuMatching = self::addSkuMatchingData($matchedGoods->take(5), $userId);

        return [
            'pets_info' => self::formatPetsInfo($pets),
            'statistics' => [
                'total_goods' => $totalGoods,
                'universal_goods' => $universalGoods,
                'tagged_goods' => $taggedGoods,
                'matched_goods' => $matchedGoods->count()
            ],
            'sample_matched_goods' => $goodsWithSkuMatching->map(function($good) {
                return [
                    'id' => $good->id,
                    'name' => $good->name,
                    'category' => $good->category->name ?? '',
                    'sku_matching_info' => collect($good->good_sku ?? [])->map(function($sku) {
                        return [
                            'sku_id' => $sku['id'],
                            'pet_filter_tags' => $sku['pet_filter_tags'] ?: '空标签(通用)',
                            'is_universal' => $sku['is_universal'] ?? false,
                            'matching_reason' => $sku['matching_reason'] ?? '',
                            'matched_pets_count' => count($sku['matched_pets'] ?? []),
                            'matched_pets' => array_slice($sku['matched_pets'] ?? [], 0, 3) // 只显示前3个匹配的宠物
                        ];
                    })->toArray()
                ];
            })->toArray()
        ];
    }
}
