<?php

namespace App\Models;

use App\Helpers\RedisLock;
use DateTimeInterface;
use Exception;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class GoodCollect extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'goods_collects';

    protected $fillable = ['user_id', 'good_id'];

    /**
     * Prepare a date for array / JSON serialization.
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    // 商品
    public function Good()
    {
        return $this->hasOne(Good::class, 'id', 'good_id');
    }

    public static function getList($search_data = array())
    {
        // 遍历筛选条件
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $keyword = $search_data['keyword'] ?? "";
        $sortName = $search_data['sort_name'] ?? "";
        $sortBy = $search_data['sort_by'] ?? "desc";
        $userId = $search_data['user_id'] ?? "";

        return self::when($keyword, function (Builder $query) use ($keyword) {
            $query->whereIn('good_id', function ($subQuery) use ($keyword) {
                $subQuery->select('id')->from('goods')
                    ->where('name', 'like', "%$keyword%")
                    ->orWhere('name_tc', 'like', "%$keyword%")
                    ->orWhere('name_en', 'like', "%$keyword%");
            });
        })
            ->when($userId, function (Builder $query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->when($sortName, function (Builder $query) use ($sortName, $sortBy) {
                $query->orderBy($sortName, $sortBy);
            })
            ->with([
                'Good' => function ($q) {
                    $q->select('id', 'order_price', 'name', 'image');
                }
            ])
            ->orderBy('id', 'desc')
            ->paginate($limit, array('*'), 'page', $page);
    }

    public static function saveDetail($data = array())
    {
        DB::beginTransaction();
        $redis = redis();
        try {
            $userId = $data['user_id'];
            $lock = RedisLock::lock($redis, 'browse_' . $userId);
            if (!$lock) {
                throw new Exception('业务繁忙，请稍后再试');
            }
            // 浏览记录方式一：没有浏览记录的新增，有的更新
            $Browse = Browse::where('user_id', $userId)->where('good_id', $data['good_id'])->first();
            if (!$Browse) {
                $Browse = new Browse();
            } else {
                $Browse->updated_at = now()->toDateTimeString();
            }
            $Browse->user_id = $userId;
            $Browse->good_id = $data['good_id'];
            $success = $Browse->save();
            // 浏览记录方式二：所有的浏览记录都添加，用于后期的用户行为分析
            //            $Browse = new Browse();
            //            $Browse->user_id = $user_id;
            //            $Browse->good_id = $request->id;
            //            $Browse->save();
            if ($success) {
                $success = true;
                $record = $data;
                $record['id'] = $Browse->id;
                $message = '保存成功';
            } else {
                throw new Exception('保存失敗');
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $success = false;
            $record = array();
            $message = $e->getMessage();
        } finally {
            empty($lock) or RedisLock::unlock($redis, 'browse_' . $userId);
        }
        return [$success, $record, $message];
    }

    //将数据库中的数据同步到redis
    public static function initRedis($goodId)
    {
        //商品收藏列表
        $cacheGoodCollectCount = "good:{$goodId}:collect_count";
        redis()->del($cacheGoodCollectCount);
        if ($goodCollectList = self::where('good_id', $goodId)->select('id', 'user_id', 'created_at')->get()->toArray()) {
            redis()->incrBy($cacheGoodCollectCount, count($goodCollectList));
            //用商品收藏-用于判断某内容是否已收藏
            foreach ($goodCollectList as $v) {
                $appUserId = $v['user_id'];
                $cacheMyGoodCollectId = "app_user:{$appUserId}:good:{$goodId}";
                redis()->set($cacheMyGoodCollectId, $v['id']);
            }
        }
        //更新为初始化
        $cacheInitKey = "good:{$goodId}:init_collect";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($goodId)
    {
        $cacheInitKey = "good:{$goodId}:init_collect";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($goodId); //数据有问题时把key删除，重新初始化
    }

    public static function getGoodCollectCount($goodId)
    {
        self::ensureInitRedis($goodId);
        $cacheGoodCollectCount = "good:{$goodId}:collect_count";
        return (int)redis()->get($cacheGoodCollectCount);
    }

    public static function isCollected($appUserId, $goodId)
    {
        self::ensureInitRedis($goodId);
        $cacheMyGoodCollectId = "app_user:{$appUserId}:good:{$goodId}";
        return redis()->get($cacheMyGoodCollectId);
    }

    public static function collect($appUserId, $goodId)
    {
        self::ensureInitRedis($goodId);
        if (self::isCollected($appUserId, $goodId)) {
            return [false, [], '不能重複收藏该商品'];
        }
        $today = date('Y-m-d');
        $cacheGoodCollectCountToday = "app_user:{$appUserId}:good_collect_count:{$today}";
        if (!redis()->exists($cacheGoodCollectCountToday)) {
            redis()->setex($cacheGoodCollectCountToday, 86400, self::withTrashed()->where('user_id', $appUserId)->where('created_at', '>=', $today)->count());
        }
        if (redis()->get($cacheGoodCollectCountToday) >= 1000) {
            logErr('商品收藏数量告警：' . $appUserId);
            return [false, [], '今日收藏次數已達上限'];
        }
        if (!$good = Good::where('id', $goodId)->first()) {
            return [false, [], '商品不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            //用户今日内容次数
            redis()->incr($cacheGoodCollectCountToday);
            //更新到数据库
            $goodCollect = new self();
            $goodCollect->user_id = $appUserId;
            $goodCollect->good_id = $good->id;
            $goodCollect->save();
            //商品收藏次数
            $cacheGoodCollectCount = "good:{$goodId}:collect_count";
            redis()->incr($cacheGoodCollectCount);
            //商品收藏-用于判断某内容是否已收藏
            $cacheMyGoodCollectId = "app_user:{$appUserId}:good:{$goodId}";
            redis()->set($cacheMyGoodCollectId, $goodCollect->id);
            DB::commit();
            return [true, ['good' => $goodCollect->id], '收藏成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('收藏失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '收藏失敗'];
        }
    }

    public static function cancelCollect($appUserId, $goodId)
    {
        if (!$goodCollectId = self::isCollected($appUserId, $goodId)) {
            return [false, [], '還沒收藏该商品'];
        }
        DB::beginTransaction();
        try {
            //商品收藏次数
            $cacheGoodCollectCount = "good:{$goodId}:collect_count";
            redis()->decr($cacheGoodCollectCount);
            //用商品收藏
            $cacheMyGoodCollectId = "app_user:{$appUserId}:good:{$goodId}";
            redis()->del($cacheMyGoodCollectId);
            //更新到数据库
            self::where('id', $goodCollectId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            DB::commit();
            return [true, [], '取消收藏成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取消收藏失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取消收藏失敗'];
        }
    }
}
