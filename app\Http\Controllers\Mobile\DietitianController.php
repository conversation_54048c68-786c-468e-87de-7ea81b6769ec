<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Models\Consult;
use App\Models\ConsultAccessToken;
use App\Models\ConsultOTP;
use App\Models\Dietitian;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class DietitianController extends Controller
{
    public function getDietitianList(Request $request)
    {
        $request->validate([
            'is_dietitian' => 'required|integer|gt:0',
        ]);
        $formData = $request->all();
        if ($request->user()->is_dietitian == 1 && $formData['is_dietitian'] == 1) {
            return ResponseHelper::error(__('common.no_permission'));
        }
        $list = Dietitian::getDietitianList($formData);
        return ResponseHelper::success($list);
    }

    public function waitingConsultList(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
        ]);
        $formData = $request->all();
        $formData['_waiting'] = 'dietitian';
        $records = Consult::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function bookingConsultList(Request $request)
    {
        $request->validate([
            'page'     => 'sometimes|integer|gt:0',
            'per_page' => 'sometimes|integer|between:1,200',
        ]);
        $formData = $request->all();
        $formData['_booking'] = 1;
        if ($request->user()->is_dietitian == 2) {
            $formData['dietitian_id'] = -1; //营养师查看全部
        } else {
            $formData['dietitian_id'] = $request->user()->id; //助理仅查看自己
        }
        $records = Consult::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function getConsultList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,created_at,updated_at',
            'sort_by'       => 'nullable|in:asc,desc',
            'book_status'   => 'nullable|in:0,1,2,3',  //预约状态:0=非预约,1=预约中,2=预约成功,3=预约失败
            'status'        => 'nullable|in:1,2,3,4',  //会话状态:1=待接入,2=已接入,3=已结束，4=已取消
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'book_start'    => 'nullable|required_with:book_end|date_format:"Y-m-d H:i:s"',
            'book_end'      => 'nullable|required_with:book_start|date_format:"Y-m-d H:i:s"|after:book_start',
            'pet_id'        => 'nullable|integer|exists:pets,id',
        ]);
        $formData = $request->all();
        if ($request->user()->is_dietitian == 2) {
            $formData['dietitian_id'] = -1; //营养师查看全部
        } else {
            $formData['dietitian_id'] = $request->user()->id; //助理仅查看自己
        }
        $records = Consult::getList($formData, $request->user()->id);
        return ResponseHelper::success($records);
    }

    public function getConsultDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        if ($request->user()->is_dietitian == 2) {
            $where = [];
        } else {
            $where = ['dietitian_id' => $request->user()->id];
        }
        $result = Consult::getDetail($request->input('id'), $where);
        return ResponseHelper::success($result);
    }

    public function getOTP(Request $request)
    {
        $rules = [
            'consult_id' => 'nullable|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $formData['role'] = $request->user()->is_dietitian == 1 ? 2 : 3;
        $result = ConsultOTP::createOTP($formData);
        return ResponseHelper::result(...$result);
    }

    public function getAccessToken(Request $request)
    {
        $rules = [
            'consult_id' => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $formData['user_id'] = $request->user()->id;
        $formData['role'] = $request->user()->is_dietitian == 1 ? 2 : 3;
        $result = ConsultAccessToken::createAccessToken($formData);
        return ResponseHelper::result(...$result);
    }

    public function setOnlineStatus(Request $request)
    {
        $request->validate([
            'online_status' => 'required|integer|in:0,1',
        ]);
        $result = Dietitian::setOnlineStatus($request->user(), $request->input('online_status'));
        return ResponseHelper::success($result);
    }

    public function acceptBook(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::acceptBook($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function acceptConsult(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::acceptConsult($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function endConsult(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = Consult::end($request->input('id'), $request->user());
        return ResponseHelper::result(...$result);
    }

    public function setSummary(Request $request)
    {
        $rules = [
            'id'      => 'required|integer|gt:0',
            'summary' => 'required|string|max:255',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = Consult::setSummary($formData, $request->user());
        return ResponseHelper::result(...$result);
    }
}
