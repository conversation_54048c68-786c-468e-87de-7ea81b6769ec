<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AppContentComment;
use Illuminate\Http\Request;

class AppContentCommentController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'created_start' => 'nullable|required_with:created_end|date_format:"Y-m-d H:i:s"',
            'created_end'   => 'nullable|required_with:created_start|date_format:"Y-m-d H:i:s"|after:created_start',
            'sort_name'     => 'nullable|in:id,created_at,updated_at,like_count',
            'sort_by'       => 'nullable|in:asc,desc',
        ]);

        $records = AppContentComment::getList($request->all());
        return ResponseHelper::success($records);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = AppContentComment::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }


}
