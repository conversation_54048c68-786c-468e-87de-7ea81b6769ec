<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Pet;
use App\Models\PetMedicalRecord;
use App\Models\PetVaccineRecord;
use App\Models\PetCertificate;
use App\Services\PetRecordDeletionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * 移动端 - 疫苗记录 / 医疗档案 接口
 * - 严格使用模型进行数据库操作
 * - 控制器负责校验与响应，权限检查使用 Pet::hasEditPermission
 */
class PetRecordController extends Controller
{
    protected PetRecordDeletionService $deletionService;

    public function __construct(PetRecordDeletionService $deletionService)
    {
        $this->deletionService = $deletionService;
    }
    /**
     * 疫苗记录列表
     */
    public function getVaccineList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id' => 'required|integer|exists:pets,id',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,50',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $petId = (int) $request->input('pet_id');
        $perPage = (int) $request->input('per_page', 20);

        if (!Pet::hasEditPermission($petId, $userId)) {
            return ResponseHelper::error('沒有查看權限');
        }

        $query = PetVaccineRecord::with(['pet.vaccineCard'])
            ->where('pet_id', $petId)
            ->orderBy('vaccine_date')
            ->orderBy('id');

        $list = $query->paginate($perPage);
        // 为每条疫苗记录附带疫苗卡图片（若存在），返回首图与完整数组
        $list->setCollection(
            $list->getCollection()->map(function ($record) {
                $images = [];
                if ($record->pet && $record->pet->vaccineCard) {
                    // 优先使用 image_urls 辅助属性，若无则回退到 image_url（cast为array）
                    $images = $record->pet->vaccineCard->image_urls ?? $record->pet->vaccineCard->image_url ?? [];
                    if (!is_array($images)) {
                        // 兜底：字符串或其他情况
                        $decoded = is_string($images) ? json_decode($images, true) : [];
                        $images = json_last_error() === JSON_ERROR_NONE && is_array($decoded) ? $decoded : (empty($images) ? [] : [$images]);
                    }
                }
                $firstImage = !empty($images) ? $images[0] : null;
                $item = $record->toArray();
                $item['vaccine_image'] = $firstImage; // 单张首图（便于列表展示）
                $item['vaccine_images'] = $images;    // 全部疫苗卡图片（便于详情使用）
                return $item;
            })
        );

        return ResponseHelper::success($list);
    }

    /**
     * 删除疫苗针卡
     * 规则：疫苗针卡审核通过后不可删除，审核未通过可以删除
     */
    public function deleteVaccineCard(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'certificate_id' => 'required|integer|exists:pets_certificates,id',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $certificateId = (int) $request->input('certificate_id');

        // 检查删除权限
        $permissionCheck = $this->deletionService->canDeleteVaccineCardById($certificateId, $userId);
        if (!$permissionCheck['can_delete']) {
            return ResponseHelper::error($permissionCheck['message']);
        }

        // 执行删除
        $deleteResult = $this->deletionService->deleteVaccineCard($permissionCheck['certificate']);
        if (!$deleteResult['success']) {
            return ResponseHelper::error($deleteResult['message']);
        }

        return ResponseHelper::success(null, $deleteResult['message']);
    }

    /**
     * 医疗档案列表
     */
    public function getMedicalList(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id' => 'required|integer|exists:pets,id',
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|between:1,50',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $petId = (int) $request->input('pet_id');
        $perPage = (int) $request->input('per_page', 20);

        if (!Pet::hasEditPermission($petId, $userId)) {
            return ResponseHelper::error('沒有查看權限');
        }

        $query = PetMedicalRecord::where('pet_id', $petId)
            ->orderBy('record_date')
            ->orderBy('id');

        $list = $query->paginate($perPage);
        return ResponseHelper::success($list);
    }

    /**
     * 删除医疗档案
     * 规则：医疗档案无论审核状态都可以删除
     */
    public function deleteMedicalRecord(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'record_id' => 'required|integer|exists:pets_medical_records,id',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $recordId = (int) $request->input('record_id');

        // 检查删除权限
        $permissionCheck = $this->deletionService->canDeleteMedicalRecord($recordId, $userId);
        if (!$permissionCheck['can_delete']) {
            return ResponseHelper::error($permissionCheck['message']);
        }

        // 执行删除
        $deleteResult = $this->deletionService->deleteMedicalRecord($permissionCheck['record']);
        if (!$deleteResult['success']) {
            return ResponseHelper::error($deleteResult['message']);
        }

        return ResponseHelper::success(null, $deleteResult['message']);
    }

    /**
     * 删除出生证明
     * 规则：出生证明审核通过后不可删除，审核未通过可以删除
     */
    public function deleteBirthCertificate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id' => 'required|integer|exists:pets,id',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $petId = (int) $request->input('pet_id');

        // 检查删除权限
        $permissionCheck = $this->deletionService->canDeleteBirthCertificate($petId, $userId);
        if (!$permissionCheck['can_delete']) {
            return ResponseHelper::error($permissionCheck['message']);
        }

        // 执行删除
        $deleteResult = $this->deletionService->deleteBirthCertificate($permissionCheck['certificate']);
        if (!$deleteResult['success']) {
            return ResponseHelper::error($deleteResult['message']);
        }

        return ResponseHelper::success(null, $deleteResult['message']);
    }

    /**
     * 删除晶片编码
     * 规则：晶片编码审核通过后不可删除，审核未通过可以删除
     */
    public function deleteChipCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'pet_id' => 'required|integer|exists:pets,id',
        ]);
        if ($validator->fails()) {
            return ResponseHelper::error($validator->errors()->first());
        }

        $userId = $request->user()->id;
        $petId = (int) $request->input('pet_id');

        // 检查删除权限
        $permissionCheck = $this->deletionService->canDeleteChipCode($petId, $userId);
        if (!$permissionCheck['can_delete']) {
            return ResponseHelper::error($permissionCheck['message']);
        }

        // 执行删除
        $deleteResult = $this->deletionService->deleteChipCode(
            $permissionCheck['certificate'],
            $permissionCheck['pet']
        );
        if (!$deleteResult['success']) {
            return ResponseHelper::error($deleteResult['message']);
        }

        return ResponseHelper::success(null, $deleteResult['message']);
    }





}

