<?php

// 加载Laravel环境
require __DIR__ . '/../vendor/autoload.php';
$app = require_once __DIR__ . '/../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// 测试食物分析流式响应
echo "\n\n===== 测试食物分析流式响应 =====\n";
$request = Illuminate\Http\Request::create(
    '/api/mobile/petChat/food',
    'POST',
    ['stream' => true],
    [],
    [],
    ['CONTENT_TYPE' => 'application/json'],
    json_encode(['message' => '可乐能不能喝'])
);

$response = $kernel->handle($request);
echo "状态码: " . $response->getStatusCode() . "\n";
echo "响应头: " . json_encode($response->headers->all()) . "\n";
echo "响应内容:\n" . $response->getContent() . "\n";

// 测试排泄物分析流式响应
echo "\n\n===== 测试排泄物分析流式响应 =====\n";
$request = Illuminate\Http\Request::create(
    '/api/mobile/petChat/stool',
    'POST',
    ['stream' => true],
    [],
    [],
    ['CONTENT_TYPE' => 'application/json'],
    json_encode(['message' => '我家狗狗的便便有点黑色'])
);

$response = $kernel->handle($request);
echo "状态码: " . $response->getStatusCode() . "\n";
echo "响应头: " . json_encode($response->headers->all()) . "\n";
echo "响应内容:\n" . $response->getContent() . "\n";

// 测试健康分析流式响应
echo "\n\n===== 测试健康分析流式响应 =====\n";
$request = Illuminate\Http\Request::create(
    '/api/mobile/petChat/health',
    'POST',
    ['stream' => true],
    [],
    [],
    ['CONTENT_TYPE' => 'application/json'],
    json_encode(['message' => '我家狗狗最近没精神'])
);

$response = $kernel->handle($request);
echo "状态码: " . $response->getStatusCode() . "\n";
echo "响应头: " . json_encode($response->headers->all()) . "\n";
echo "响应内容:\n" . $response->getContent() . "\n";
