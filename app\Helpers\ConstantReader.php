<?php

namespace App\Helpers;

use ReflectionClass;
use Illuminate\Support\Arr;

trait ConstantReader
{
    /**
     * 获取类中所有常量及其值
     *
     * @param string $className 类名
     * @return array
     */
    public static function getConstantsWithValues($className = null)
    {
        $class = $className ?: static::class;
        $reflection = new ReflectionClass($class);
        return $reflection->getConstants();
    }

    /**
     * 获取类中所有常量及其注释
     *
     * @param string $className 类名
     * @return array
     */
    public static function getConstantsWithComments($className = null)
    {
        $class = $className ?: static::class;
        $reflection = new ReflectionClass($class);
        $constants = $reflection->getConstants();
        $result = [];

        foreach ($constants as $name => $value) {
            $constantReflection = $reflection->getReflectionConstant($name);
            $docComment = $constantReflection->getDocComment();

            $result[$name] = [
                'value' => $value,
                'comment' => $docComment ? self::parseDocComment($docComment) : '',
            ];
        }

        return $result;
    }

    /**
     * 解析註釋內容
     *
     * @param string $docComment
     * @return string
     */
    protected static function parseDocComment($docComment)
    {
        // 移除註釋的開始和結束標記
        $docComment = preg_replace('/^\/\*\*/', '', $docComment);
        $docComment = preg_replace('/\*\/$/', '', $docComment);

        // 按行分割
        $lines = explode("\n", $docComment);
        $description = '';

        foreach ($lines as $line) {
            // 移除行首的星號和空格
            $line = trim(preg_replace('/^\s*\*\s?/', '', $line));

            // 跳過空行和註解標籤行
            if (!empty($line) && !str_starts_with($line, '@')) {
                $description = $line;
                break;
            }
        }

        return $description;
    }

    /**
     * 获取指定前缀的常量
     *
     * @param string $prefix 常量前缀
     * @param string $className 类名
     * @return array
     */
    public static function getConstantsByPrefix($prefix, $className = null)
    {
        $constants = self::getConstantsWithValues($className);
        return Arr::where($constants, function ($value, $key) use ($prefix) {
            return str_starts_with($key, $prefix);
        });
    }
}
