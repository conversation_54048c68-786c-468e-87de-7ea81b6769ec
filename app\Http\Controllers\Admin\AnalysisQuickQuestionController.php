<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\AnalysisQuickQuestion;
use Illuminate\Http\Request;

class AnalysisQuickQuestionController extends Controller
{
    /**
     * 获取快捷问题列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getList(Request $request)
    {
        $request->validate([
            'page'          => 'sometimes|integer|gt:0',
            'per_page'      => 'sometimes|integer|between:1,200',
            'keyword'       => 'nullable|max:255',
            'sort_name'     => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'       => 'nullable|in:asc,desc',
            'analysis_type' => 'nullable|in:food,stool,health',
            'pet_type'      => 'nullable|in:general,dog,cat',
            'status'        => 'nullable|in:0,1',
        ]);

        $query = AnalysisQuickQuestion::query();

        // 关键词搜索
        if ($request->has('keyword') && $request->input('keyword')) {
            $keyword = $request->input('keyword');
            $query->where(function($q) use ($keyword) {
                $q->where('question', 'like', "%{$keyword}%");
            });
        }

        // 分析类型筛选
        if ($request->has('analysis_type') && $request->input('analysis_type')) {
            $query->where('analysis_type', $request->input('analysis_type'));
        }

        // 宠物类型筛选
        if ($request->has('pet_type') && $request->input('pet_type')) {
            $query->where('pet_type', $request->input('pet_type'));
        }

        // 状态筛选
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('status', $request->input('status'));
        }

        // 排序
        $sortName = $request->input('sort_name', 'sort_order');
        $sortBy = $request->input('sort_by', 'asc');
        $query->orderBy($sortName, $sortBy);

        // 使用标准分页
        $perPage = $request->input('per_page', 15);
        $records = $query->paginate($perPage);

        return ResponseHelper::success($records);
    }

    /**
     * 获取快捷问题详情
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $detail = AnalysisQuickQuestion::find($id);

        if (!$detail) {
            return ResponseHelper::error('快捷问题不存在');
        }

        return ResponseHelper::success($detail);
    }

    /**
     * 保存快捷问题信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveDetail(Request $request)
    {
        $rules = [
            'id'            => 'sometimes|integer|gte:0',
            'analysis_type' => 'required|in:food,stool,health',
            'question'      => 'required|string|max:255',
            'icon'          => 'nullable|string|max:255',
            'pet_type'      => 'required|in:general,dog,cat',
            'sort_order'    => 'nullable|integer|gte:0',
            'status'        => 'required|in:0,1',
        ];

        $request->validate($rules);
        $formData = $request->only(array_keys($rules));

        // 保存数据
        if (isset($formData['id']) && $formData['id'] > 0) {
            $model = AnalysisQuickQuestion::find($formData['id']);
            if (!$model) {
                return ResponseHelper::error('快捷问题不存在');
            }
            $model->update($formData);
        } else {
            $model = AnalysisQuickQuestion::create($formData);
        }

        return ResponseHelper::success($model);
    }

    /**
     * 删除快捷问题
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);

        $id = $request->input('id');
        $model = AnalysisQuickQuestion::find($id);

        if (!$model) {
            return ResponseHelper::error('快捷问题不存在');
        }

        $model->delete();

        return ResponseHelper::success(true);
    }
}
