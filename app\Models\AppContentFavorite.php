<?php

namespace App\Models;

use App\Events\PostFavoritedEvent;
use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;

/**
 * @mixin Builder
 */
class AppContentFavorite extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_contents_favorites';

    protected $fillable = [
        'content_id',
        'content_user_id',
        'favorite_user_id',
        'is_private',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($appUserId)
    {
        //我的收藏列表
        $cacheFavoriteOfMine = "app_user:{$appUserId}:content_favorites";
        redis()->del($cacheFavoriteOfMine);
        //公开的收藏列表
        $cacheFavoriteOfMineForPublic = "app_user:{$appUserId}:content_favorites_public";
        redis()->del($cacheFavoriteOfMineForPublic);
        //私密的收藏列表
        $cacheFavoriteOfMineForPrivate = "app_user:{$appUserId}:content_favorites_private";
        redis()->del($cacheFavoriteOfMineForPrivate);
        $favoriteList = self::where('favorite_user_id', $appUserId)->select('content_id', 'is_private', 'created_at')->get()->toArray();
        foreach ($favoriteList as $v) {
            $timeOfMine = strtotime($v['created_at']);
            redis()->zAdd($cacheFavoriteOfMine, $timeOfMine, $v['content_id']);
            if (!$v['is_private']) {
                redis()->zAdd($cacheFavoriteOfMineForPublic, $timeOfMine, $v['content_id']);
            } else {
                redis()->zAdd($cacheFavoriteOfMineForPrivate, $timeOfMine, $v['content_id']);
            }
        }
        //更新为初始化
        $cacheInitKey = "app_user:{$appUserId}:init_content_favorite";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    //收藏数量
    public static function getFavoriteCount($contentId)
    {
        $cacheFavoriteCount = "app_content:{$contentId}:favorite_count";
        if (!redis()->exists($cacheFavoriteCount)) {
            redis()->set($cacheFavoriteCount, self::where('content_id', $contentId)->count());
        }
        return (int)redis()->get($cacheFavoriteCount);
    }

    public static function ensureInitRedis($appUserId)
    {
        $cacheInitKey = "app_user:{$appUserId}:init_content_favorite";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($appUserId); //数据有问题时把key删除，重新初始化
    }

    //判断是否关注
    public static function isFavorite($appUserId, $contentId)
    {
        self::ensureInitRedis($appUserId);
        $cacheFavoriteOfMine = "app_user:{$appUserId}:content_favorites";
        return redis()->zScore($cacheFavoriteOfMine, $contentId) > 0;
    }

    public static function setFavorite($appUserId, $contentId)
    {
        if (self::isFavorite($appUserId, $contentId)) {
            return [false, [], '不能重複收藏'];
        }
        $cacheFavoriteOfMine = "app_user:{$appUserId}:content_favorites";
        if (redis()->sCard($cacheFavoriteOfMine) >= 10000) {
            logErr('收藏数量告警：' . $appUserId);
            return [false, [], '收藏數量已達上限'];
        }
        if (!AppContent::where('id', $contentId)->value('id')) {
            return [false, [], '内容不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            //我的收藏列表
            $time = time();
            redis()->zAdd($cacheFavoriteOfMine, $time, $contentId);
            //公开的收藏列表
            $cacheFavoriteOfMineForPublic = "app_user:{$appUserId}:content_favorites_public";
            redis()->zAdd($cacheFavoriteOfMineForPublic, $time, $contentId);
            //活动收藏数量
            $cacheFavoriteCount = "app_content:{$contentId}:favorite_count";
            redis()->incr($cacheFavoriteCount);
            //更新到数据库
            $favorite = new self();
            $favorite->content_id = $contentId;
            $favorite->content_user_id = AppContent::where('id', $contentId)->value('user_id');
            $favorite->favorite_user_id = $appUserId;
            $favorite->save();
            //更新统计
            AppUser::incrementFavoriteContent($appUserId);
            AppUser::incrementPublicFavoriteContent($appUserId);
            AppContent::incrementFavorite($contentId);

            // 触发帖子收藏事件（只有当收藏者和帖子所有者不是同一人时才触发）
            $contentUserId = $favorite->content_user_id;
            if ($appUserId != $contentUserId) {
                event(new PostFavoritedEvent($contentId, $contentUserId, $appUserId));
            }

            DB::commit();
            return [true, [], '收藏成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('收藏失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '收藏失敗'];
        }
    }

    public static function cancelFavorite($appUserId, $contentId)
    {
        if (!self::isFavorite($appUserId, $contentId)) {
            return [false, [], '還沒收藏該内容'];
        }
        DB::beginTransaction();
        try {
            //我的收藏列表
            $cacheFavoriteOfMine = "app_user:{$appUserId}:content_favorites";
            redis()->zRem($cacheFavoriteOfMine, $contentId);
            //公开的收藏列表
            $cacheFavoriteOfMineForPublic = "app_user:{$appUserId}:content_favorites_public";
            redis()->zRem($cacheFavoriteOfMineForPublic, $contentId);
            //活动收藏数量
            $cacheFavoriteCount = "app_content:{$contentId}:favorite_count";
            redis()->decr($cacheFavoriteCount);
            //更新到数据库
            self::where('favorite_user_id', $appUserId)->where('content_id', $contentId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            //更新统计
            AppUser::decrementFavoriteContent($appUserId);
            AppUser::decrementPublicFavoriteContent($appUserId);
            AppContent::decrementFavorite($contentId);
            DB::commit();
            return [true, [], '取消收藏成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取消收藏失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取消收藏失敗'];
        }
    }

    public static function isPrivate($appUserId, $contentId)
    {
        //公开的收藏列表
        $cacheFavoriteOfMineForPublic = "app_user:{$appUserId}:content_favorites_public";
        return !redis()->zScore($cacheFavoriteOfMineForPublic, $contentId);
    }

    public static function setIsPrivate($appUserId, $contentId, $isPrivate)
    {
        if (!self::isFavorite($appUserId, $contentId)) {
            return [false, [], '還沒收藏該内容'];
        }
        if ($isPrivate && self::isPrivate($appUserId, $contentId)) {
            return [false, [], '該内容已設置私密'];
        }
        if (!$isPrivate && !self::isPrivate($appUserId, $contentId)) {
            return [false, [], '該内容已設置公開'];
        }
        DB::beginTransaction();
        try {
            //我的收藏列表
            $cacheFavoriteOfMine = "app_user:{$appUserId}:content_favorites";
            $score = redis()->zScore($cacheFavoriteOfMine, $contentId);
            //公开的收藏列表
            $cacheFavoriteOfMineForPublic = "app_user:{$appUserId}:content_favorites_public";
            //私密的收藏列表
            $cacheFavoriteOfMineForPrivate = "app_user:{$appUserId}:content_favorites_private";
            if ($isPrivate) {
                redis()->zRem($cacheFavoriteOfMineForPublic, $contentId);
                redis()->zAdd($cacheFavoriteOfMineForPrivate, $score, $contentId);
            } else {
                redis()->zRem($cacheFavoriteOfMineForPrivate, $contentId);
                redis()->zAdd($cacheFavoriteOfMineForPublic, $score, $contentId);
            }
            //更新到数据库
            self::where('favorite_user_id', $appUserId)->where('content_id', $contentId)->update(['is_private' => $isPrivate]);
            //更新统计
            if ($isPrivate) {
                AppUser::decrementPublicFavoriteContent($appUserId);
            } else {
                AppUser::incrementPublicFavoriteContent($appUserId);
            }
            DB::commit();
            return [true, [], '更新成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('更新失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '更新失敗'];
        }
    }

    public static function getFavoriteList($search_data = array(), $requestAppUserId = null)
    {
        $page = $search_data['page'] ?? 1;
        $limit = $search_data['per_page'] ?? 15;
        $appUserId = $search_data['user_id'];
        $isPrivate = $search_data['is_private'] ?? null;
        self::ensureInitRedis($appUserId);
        if (!AppUser::where('id', $appUserId)->value('id')) {
            return [false, [], '用戶不存在或已刪除'];
        }
        $fromSelf = $appUserId == $requestAppUserId;
        if (!$fromSelf) {
            //公开的收藏列表
            $redisKey = "app_user:{$appUserId}:content_favorites_public";
        } else {
            //我的收藏列表
            $redisKey = "app_user:{$appUserId}:content_favorites";

            if (is_numeric($isPrivate)) {
                if ($isPrivate == 1) {
                    //私密的收藏列表
                    $redisKey = "app_user:{$appUserId}:content_favorites_private";
                } else {
                    //公开的收藏列表
                    $redisKey = "app_user:{$appUserId}:content_favorites_public";
                }
            }
        }
        return [true, self::buildList($redisKey, $page, $limit, $fromSelf), '獲取成功'];
    }

    protected static function buildList($redisKey, $page, $limit, $fromSelf)
    {
        $totalCount = redis()->zCard($redisKey);
        $startIndex = max(0, $totalCount - ($page * $limit));
        $endIndex = min($totalCount, $startIndex + $limit);
        list(, $appUserId) = explode(':', $redisKey);

        $contentIds = redis()->zRevRange($redisKey, $startIndex, $endIndex);
        //todo redis存储活动基本信息
        $search_data = ['ids' => $contentIds, 'per_page' => -1];
        $contents = !$contentIds ? [] : AppContent::getList($search_data);
        $contents = array_column($contents, null, 'id');

        $list = [];
        foreach ($contentIds as $id) {
            $item = $contents[$id] ?? [
                'id'         => 0,
                'title'      => '已删除',
                'content'    => null,
                'images'     => '',
                'created_at' => null,
            ];

            AppContent::renderItem($item, $appUserId);
            if ($fromSelf) {   //自己查看全部收藏列表，返回是否私密的字段，给前端用于判断更新
                $item['is_private'] = self::isPrivate($appUserId, $item['id']);
            }

            $list[] = $item;
        }

        return [
            'total'        => $totalCount,
            'per_page'     => $limit,
            'current_page' => $page,
            'last_page'    => max((int)ceil($totalCount / $limit), 1),
            'data'         => $list,
        ];
    }
}
