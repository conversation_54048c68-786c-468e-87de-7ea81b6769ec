<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Contracts\Database\Query\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use App\Events\PostLikedEvent;

/**
 * @mixin Builder
 */
class AppContentLike extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'app_contents_likes';

    protected $fillable = [
        'content_id',
        'content_user_id',
        'like_user_id',
        'is_like',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format($this->dateFormat ?: 'Y-m-d H:i:s');
    }

    //将数据库中的数据同步到redis
    public static function initRedis($contentId)
    {
        //活动内容点赞列表
        $cacheContentLikeCount = "app_content:{$contentId}:like_count";
        redis()->del($cacheContentLikeCount);
        if ($contentLikeList = self::where('content_id', $contentId)->select('id', 'like_user_id', 'created_at')->get()->toArray()) {
            redis()->incrBy($cacheContentLikeCount, count($contentLikeList));
            //用户内容点赞-用于判断某内容是否已点赞
            foreach ($contentLikeList as $v) {
                $appUserId = $v['like_user_id'];
                $cacheMyContentLikeId = "app_user:{$appUserId}:content_like_id:{$contentId}";
                redis()->set($cacheMyContentLikeId, $v['id']);
            }
        }
        //更新为初始化
        $cacheInitKey = "app_content:{$contentId}:init_like";
        return redis()->set($cacheInitKey, date('Y-m-d H:i:s'));
    }

    public static function ensureInitRedis($contentId)
    {
        $cacheInitKey = "app_content:{$contentId}:init_like";
        if (redis()->get($cacheInitKey)) {
            return true;
        }
        return self::initRedis($contentId); //数据有问题时把key删除，重新初始化
    }

    public static function getContentLikeCount($contentId)
    {
        self::ensureInitRedis($contentId);
        $cacheContentLikeCount = "app_content:{$contentId}:like_count";
        return (int)redis()->get($cacheContentLikeCount);
    }

    public static function isLike($appUserId, $contentId)
    {
        self::ensureInitRedis($contentId);
        $cacheMyContentLikeId = "app_user:{$appUserId}:content_like_id:{$contentId}";
        return redis()->get($cacheMyContentLikeId);
    }

    public static function likeContent($appUserId, $contentId)
    {
        self::ensureInitRedis($contentId);
        if (self::isLike($appUserId, $contentId)) {
            return [false, [], '不能重複點贊該分享'];
        }
        $today = date('Y-m-d');
        $cacheContentLikeCountToday = "app_user:{$appUserId}:content_like_count:{$today}";
        if (!redis()->exists($cacheContentLikeCountToday)) {
            redis()->setex($cacheContentLikeCountToday, 86400, self::withTrashed()->where('like_user_id', $appUserId)->where('created_at', '>=', $today)->count());
        }
        if (redis()->get($cacheContentLikeCountToday) >= 1000) {
            logErr('内容点赞数量告警：' . $appUserId);
            return [false, [], '今日點贊次數已達上限'];
        }
        if (!$content = AppContent::where('id', $contentId)->first()) {
            return [false, [], '分享不存在或已刪除'];
        }
        DB::beginTransaction();
        try {
            //用户今日内容次数
            redis()->incr($cacheContentLikeCountToday);
            //更新到数据库
            $contentLike = new self();
            $contentLike->content_id = $content->id;
            $contentLike->content_user_id = $content->user_id;
            $contentLike->like_user_id = $appUserId;
            $contentLike->is_like = 1;
            $contentLike->save();
            //内容点赞次数
            $cacheContentLikeCount = "app_content:{$contentId}:like_count";
            redis()->incr($cacheContentLikeCount);
            //用户内容点赞-用于判断某内容是否已点赞
            $cacheMyContentLikeId = "app_user:{$appUserId}:content_like_id:{$contentId}";
            redis()->set($cacheMyContentLikeId, $contentLike->id);
            //更新统计
            AppContent::incrementLike($contentId);

            // 触发点赞事件（只有当点赞者和内容所有者不是同一人时才触发）
            if ($appUserId != $content->user_id) {
                event(new PostLikedEvent($contentId, $content->user_id, $appUserId));
            }

            DB::commit();
            return [true, ['content_like_id' => $contentLike->id], '點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '點贊失敗'];
        }
    }

    public static function cancelLikeContent($appUserId, $contentId)
    {
        if (!$contentLikeId = self::isLike($appUserId, $contentId)) {
            return [false, [], '還沒點贊該分享'];
        }
        DB::beginTransaction();
        try {
            //内容点赞次数
            $cacheContentLikeCount = "app_content:{$contentId}:like_count";
            redis()->decr($cacheContentLikeCount);
            //用户内容点赞
            $cacheMyContentLikeId = "app_user:{$appUserId}:content_like_id:{$contentId}";
            redis()->del($cacheMyContentLikeId);
            //更新到数据库
            self::where('id', $contentLikeId)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            //更新统计
            AppContent::decrementLike($contentId);
            DB::commit();
            return [true, [], '取消點贊成功'];
        } catch (\Exception $e) {
            DB::rollBack();
            logErr('取消点赞失败：' . $e->getMessage() . PHP_EOL . $e->getTraceAsString());
            return [false, [], '取消點贊失敗'];
        }
    }
}
