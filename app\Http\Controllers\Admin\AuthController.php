<?php

namespace App\Http\Controllers\Admin;

use App\Events\DataChanged;
use App\Exceptions\NoPermissionException;
use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Http\Middleware\OnlineRefresh;
use App\Models\Role;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Contracts\Database\Query\Builder;
use Laravel\Passport\Token;

class AuthController extends Controller
{
    /**
     * 添加管理员
     */
    public function addUser(Request $request)
    {
        $request->validate([
            'name'     => 'required|max:255',
            'phone'    => 'required|mobile|unique:users',
            'email'    => 'required|email|unique:users',
            'password' => 'required|min:8|max:64',
            'role_id'  => 'required|integer|gt:0|exists:roles,id',
            'avatar'   => 'nullable',
        ]);
        DB::beginTransaction();
        try {
            $newData = [
                'name'     => $request->name,
                'phone'    => $request->phone,
                'email'    => $request->email,
                'role_id'  => $request->role_id,
                'password' => bcrypt($request->password),
                'avatar'   => $request->avatar,
            ];
            $user = User::create($newData);
            event(new DataChanged(User::class, ['id' => $user->id] + $newData));
            DB::commit();
            return ResponseHelper::success('保存成功');
        } catch (\Exception $e) {
            DB::rollBack();
            logErr($e);
            return ResponseHelper::error('保存失敗');
        }
    }

    /**
     * 登录流程
     */
    public function loginUser(Request $request)
    {
        $request->validate([
            'phone'    => 'required|mobile',
            'password' => 'required',
        ]);
        $user = User::where('phone', $request->phone)->first();
        if (!$user || !Hash::check($request->password, $user->password)) {
            return ResponseHelper::error("请检查账号或者密码是否正确");
        }
        if ($user->status != 1) {
            return ResponseHelper::error("账号已被禁用");
        }
        // 更新IP地址和时间
        $user->update([
            'last_login_ip' => $request->getClientIp(),
            'updated_at'    => now(), // 如果你有这个字段
        ]);
        $tokenResult = $user->createToken('UserToken');
        $expiration = $tokenResult->token->expires_at->diffInSeconds(Carbon::now());
        OnlineRefresh::addMember($user->id, 'admin');
        $user->role = $user->role()->select('id', 'name', 'system_menu_ids')->first();
        $user->setHidden(['created_at', 'updated_at', 'deleted_at', 'password', 'status']);
        return ResponseHelper::success([
            "token_type"   => "Bearer",
            "expires_in"   => $expiration,
            "access_token" => $tokenResult->accessToken,
            "user"         => $user,
        ], "登录成功", 200);
    }

    /**
     * 获取当前用户信息
     */
    public function profile(Request $request)
    {
        $user = User::where('id', $request->user()->id)->first();
        $user->role = $user->role()->select('id', 'name', 'system_menu_ids')->first();
        $user->setHidden(['created_at', 'updated_at', 'deleted_at', 'password', 'status']);
        return ResponseHelper::success($user);
    }

    /**
     * 获取用户详情
     */
    public function userDetail(Request $request)
    {
        return ResponseHelper::success(User::getDetail($request->id, $request->user()));
    }

    /**
     *登出
     */
    public function logout(Request $request)
    {
        OnlineRefresh::removeMember($request->user()->id, 'admin');
        $request->user()->token()->revoke();
        return ResponseHelper::success();
    }

    /**
     * 用户列表
     */
    public function getUsers(Request $request)
    {
        $query = User::query();

        $page = $request->input('page', 1);
        $limit = $request->input('per_page', 15);
        $keyword = $request->input('keyword', '');
        $sortName = $request->input('sort_name', 'id');
        $roleId = $request->input('role_id', '');
        $sortBy = $request->input('sort_by', 'desc');

        $roleWhere = null;
        if ($request->user()->role_id != 1) {  //校验权限
            $childrenIds = Role::getChildrenIds($request->user());
            if ($roleId > 0 && !in_array($roleId, $childrenIds)) {
                throw new NoPermissionException();
            }
            $roleWhere = function (Builder $query) use ($childrenIds) {
                $query->whereIn('role_id', $childrenIds);
            };
        }

        $query->when($keyword, function (Builder $query) use ($keyword) {
            $query->where(function (Builder $query) use ($keyword) {
                $query->where('name', 'like', "%$keyword%")
                    ->orWhere('email', 'like', "%$keyword%");
            });
        })
            ->when($roleId, function (Builder $query) use ($roleId) {
                $query->where('role_id', '=', $roleId);
            })
            ->when($roleWhere, $roleWhere)
            ->with('role', function (Builder $query) use ($roleId) {
                $query->select('id', 'name');
            })
            ->orderBy($sortName, $sortBy);

        $users = $query->paginate($limit, ['*'], 'page', $page);
        return ResponseHelper::success($users);
    }

    public function updateUser(Request $request)
    {
        DB::beginTransaction();
        try {
            $id = $request->id;
            $rules = [
                'name'    => [
                    'sometimes',
                    Rule::unique('users')->ignore($id),
                    'max:255'
                ],
                'status'  => 'sometimes|integer|in:1,2',
                'role_id' => 'nullable|integer|gt:0|exists:roles,id',
                'avatar'  => 'nullable',
            ];
            $validator = Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                throw new \Exception($validator->errors());
            }
            $user = User::getDetail($id, $request->user());
            if (!$user) {
                throw new \Exception('User not found');
            }
            $oldData = $user->toArray();
            $user->name = $request->get('name', $user->name);
            $user->password = $request->has('password') ? Hash::make($request->password) : $user->password;
            $user->status = $request->get('status', $user->status);
            $user->role_id = $request->get('role_id', $user->role_id);
            $user->avatar = $request->get('avatar', $user->avatar);
            $res = $user->save();
            $newData = $user->toArray();
            if (!$res) {
                throw new \Exception('保存失敗');
            }
            event(new DataChanged(User::class, $newData, $oldData));
            DB::commit();
            return ResponseHelper::success('保存成功');
        } catch (\Exception $e) {
            DB::rollBack();
            logErr($e);
            return ResponseHelper::error('保存失敗');
        }
    }

    /**
     * delete
     */
    public function deleteUser(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:1',
        ]);
        DB::beginTransaction();
        try {
            $currentUser = $request->user();
            $formData = $request->all();
            $id = $formData['id'];
            if ($currentUser->id == $id) {
                throw new \Exception("不能删除自己");
            }
            $deleteUser = User::getDetail($id, $currentUser);
            $success = $deleteUser->delete();
            if ($success) {
                $oldData = $deleteUser->toArray();
                $deleteUser->tokens->each(function (Token $token) {
                    $token->revoke();
                });
                OnlineRefresh::removeMember($id, 'admin');
                event(new DataChanged(User::class, null, $oldData));
                DB::commit();
                return ResponseHelper::success(['id' => $id], '删除成功');
            } else {
                throw new \Exception('删除失败');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            logErr($e);
            return ResponseHelper::error('删除失败');
        }
    }

    public function getOnlineUsers(Request $request)
    {
        $user_id = $request->user()->id;
        return ResponseHelper::success(User::getOnlineUsers($user_id));
    }

}
