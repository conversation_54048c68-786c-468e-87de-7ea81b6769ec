<?php

namespace App\Listeners;

use App\Events\ConsultBookNoEnterEvent;
use App\Helpers\GoEasy;
use App\Models\Consult;
use App\Providers\GoEasyServiceProvider;
use App\Services\Consult\ConsultNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SendConsultBookNoEnterNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 处理事件
     *
     * @param ConsultBookNoEnterEvent $event
     * @return void
     */
    public function handle(ConsultBookNoEnterEvent $event)
    {
        try {
            // 获取咨询信息
            $consult = Consult::getDetail($event->consultId);

            /**
             * 推送到GoEasy
             * @var GoEasy $goEasy
             */
            $goEasy = app()->make('goEasy');
            $data = [
                'event'   => GoEasyServiceProvider::EVENT_CONSULT_BOOK_NO_ENTER,
                'consult' => $consult->toArray(),
            ];
            $json = json_encode($data, JSON_UNESCAPED_UNICODE);

            //推送给指定用户
            $channel = GoEasyServiceProvider::getChannelForUser($consult->user_id);
            $notification = ConsultNotificationService::getConsultNoEnterMessage();
            $res = $goEasy->publish($channel, $json, $notification);
            if (!$res || $res['code'] != 200) {
                throw new \Exception('GoEasy publish to user failed, reason: ' . ($res['content'] ?? '未知错误'));
            }

            Log::info('Consult book no enter notification sent', [
                'consult_id' => $event->consultId,
            ]);
        } catch (\Exception $e) {
            Log::error('Consult book no enter notification failed', [
                'error'      => $e->getMessage(),
                'consult_id' => $event->consultId,
            ]);
        }
    }
}
