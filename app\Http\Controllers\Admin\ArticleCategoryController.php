<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\ArticleCategory;
use Illuminate\Http\Request;

class ArticleCategoryController extends Controller
{
    public function getList(Request $request)
    {
        $request->validate([
            'page'      => 'sometimes|integer|gt:0',
            'per_page'  => 'sometimes|integer|between:1,200',
            'keyword'   => 'nullable|max:255',
            'sort_name' => 'nullable|in:id,created_at,updated_at,sort_order',
            'sort_by'   => 'nullable|in:asc,desc',
            'status'    => 'nullable|in:0,1',
        ]);
        $formData = $request->all();
        $records = ArticleCategory::getList($formData);
        return ResponseHelper::success($records);
    }

    public function getDetail(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $id = $request->input('id');
        return ResponseHelper::success(ArticleCategory::getDetail($id));
    }

    public function saveDetail(Request $request)
    {
        $rules = [
            'id'         => 'sometimes|integer|gte:0',
            'name'       => 'required|max:32',
            'name_tc'    => 'required|max:32',
            'name_en'    => 'required|max:255',
            'status'     => 'required|integer|in:0,1',
            'sort_order' => 'required|integer',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));
        $result = ArticleCategory::saveDetail($formData);
        return ResponseHelper::result(...$result);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'id' => 'required|integer|gt:0',
        ]);
        $result = ArticleCategory::del($request->input('id'));
        return ResponseHelper::result(...$result);
    }
}
