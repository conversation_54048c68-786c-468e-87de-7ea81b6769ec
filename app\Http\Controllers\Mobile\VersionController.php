<?php

namespace App\Http\Controllers\Mobile;

use App\Helpers\ResponseHelper;
use App\Http\Controllers\Controller;
use App\Models\Version;
use Illuminate\Http\Request;

class VersionController extends Controller
{
    public function info(Request $request)
    {
        $rules = [
            'app_type' => 'nullable|in:0,1',
        ];
        $request->validate($rules);
        $formData = $request->only(array_keys($rules));

        // 设置默认值
        $appType = $formData['app_type'] ?? 0;

        // 根据 app_type 查询
        $version = Version::where('app_type', $appType)
            ->orderBy('id', 'desc')
            ->first();

        return ResponseHelper::success($version);
    }

}
